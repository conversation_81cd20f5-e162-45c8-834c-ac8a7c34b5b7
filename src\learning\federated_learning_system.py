"""
Professional-Grade Federated Learning System for AutoGPT Trader
Implements distributed learning across multiple trading sessions with privacy preservation
"""

import asyncio
import logging
import json
import hashlib
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from pathlib import Path
from collections import defaultdict, deque
import pickle
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)

# Neural network integration
try:
    import torch
    import torch.nn as nn
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logger.warning("PyTorch not available - federated learning will use basic aggregation")

@dataclass
class FederatedModel:
    """Federated model representation with privacy preservation"""
    model_id: str
    model_type: str  # 'lstm', 'transformer', 'reinforcement', 'ensemble'
    weights_hash: str
    encrypted_weights: bytes
    performance_metrics: Dict[str, float]
    training_rounds: int
    last_updated: datetime
    privacy_budget: float = 1.0  # Differential privacy budget
    contribution_score: float = 0.0
    
@dataclass
class LearningSession:
    """Individual learning session data"""
    session_id: str
    participant_id: str
    model_updates: List[Dict]
    performance_delta: float
    privacy_contribution: float
    timestamp: datetime
    market_regime: str
    strategy_type: str
    
@dataclass
class AggregationResult:
    """Result of federated aggregation"""
    aggregated_weights: Dict[str, Any]
    performance_improvement: float
    participants_count: int
    privacy_cost: float
    convergence_score: float
    timestamp: datetime

class PrivacyPreservingAggregator:
    """Privacy-preserving model aggregation using differential privacy"""
    
    def __init__(self, epsilon: float = 1.0, delta: float = 1e-5):
        self.epsilon = epsilon  # Privacy parameter
        self.delta = delta     # Privacy parameter
        self.noise_scale = self._calculate_noise_scale()
        
    def _calculate_noise_scale(self) -> float:
        """Calculate noise scale for differential privacy"""
        return np.sqrt(2 * np.log(1.25 / self.delta)) / self.epsilon
    
    def add_noise(self, weights: np.ndarray, sensitivity: float = 1.0) -> np.ndarray:
        """Add calibrated noise for differential privacy"""
        noise = np.random.laplace(0, sensitivity * self.noise_scale, weights.shape)
        return weights + noise
    
    def secure_aggregation(self, model_updates: List[Dict]) -> Dict[str, Any]:
        """Perform secure aggregation of model updates"""
        try:
            if not model_updates:
                return {}
            
            # Initialize aggregated weights
            aggregated = {}
            participant_count = len(model_updates)
            
            # Aggregate weights with privacy preservation
            for layer_name in model_updates[0].keys():
                layer_weights = []
                
                for update in model_updates:
                    if layer_name in update:
                        weights = np.array(update[layer_name])
                        # Add noise for privacy
                        noisy_weights = self.add_noise(weights)
                        layer_weights.append(noisy_weights)
                
                if layer_weights:
                    # Federated averaging
                    aggregated[layer_name] = np.mean(layer_weights, axis=0).tolist()
            
            logger.debug(f"Securely aggregated {participant_count} model updates")
            return aggregated
            
        except Exception as e:
            logger.error(f"Error in secure aggregation: {e}")
            return {}

class FederatedLearningCoordinator:
    """Coordinates federated learning across multiple trading instances"""
    
    def __init__(self, coordinator_id: str = "main_coordinator"):
        self.coordinator_id = coordinator_id
        self.active_participants = {}
        self.model_registry = {}
        self.learning_history = deque(maxlen=1000)
        self.aggregation_rounds = 0
        
        # Privacy and security
        self.privacy_aggregator = PrivacyPreservingAggregator()
        self.encryption_key = self._generate_encryption_key()
        
        # Performance tracking
        self.global_performance_metrics = {
            'accuracy': 0.0,
            'profit_factor': 0.0,
            'sharpe_ratio': 0.0,
            'win_rate': 0.0,
            'convergence_rate': 0.0
        }
        
        # Configuration
        self.min_participants = 2
        self.aggregation_frequency = timedelta(hours=1)
        self.last_aggregation = datetime.now()
        self.max_privacy_budget = 10.0
        
        logger.info(f"🤝 [FEDERATED] Coordinator {coordinator_id} initialized")
    
    def _generate_encryption_key(self) -> Fernet:
        """Generate encryption key for secure model transmission"""
        password = b"autogpt_federated_learning_2024"
        salt = b"trading_salt_2024"
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return Fernet(key)
    
    async def register_participant(self, participant_id: str, 
                                 capabilities: Dict[str, Any]) -> bool:
        """Register a new federated learning participant"""
        try:
            self.active_participants[participant_id] = {
                'id': participant_id,
                'capabilities': capabilities,
                'last_seen': datetime.now(),
                'contribution_score': 0.0,
                'privacy_budget_used': 0.0,
                'models_contributed': 0,
                'performance_history': deque(maxlen=100)
            }
            
            logger.info(f"🤝 [FEDERATED] Registered participant: {participant_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [FEDERATED] Error registering participant: {e}")
            return False
    
    async def submit_model_update(self, participant_id: str, 
                                model_update: Dict[str, Any]) -> bool:
        """Submit model update from participant"""
        try:
            if participant_id not in self.active_participants:
                logger.warning(f"⚠️ [FEDERATED] Unknown participant: {participant_id}")
                return False
            
            # Validate model update
            if not self._validate_model_update(model_update):
                logger.warning(f"⚠️ [FEDERATED] Invalid model update from {participant_id}")
                return False
            
            # Create learning session
            session = LearningSession(
                session_id=f"{participant_id}_{datetime.now().timestamp()}",
                participant_id=participant_id,
                model_updates=[model_update],
                performance_delta=model_update.get('performance_delta', 0.0),
                privacy_contribution=model_update.get('privacy_cost', 0.1),
                timestamp=datetime.now(),
                market_regime=model_update.get('market_regime', 'unknown'),
                strategy_type=model_update.get('strategy_type', 'general')
            )
            
            # Store session
            self.learning_history.append(session)
            
            # Update participant metrics
            participant = self.active_participants[participant_id]
            participant['last_seen'] = datetime.now()
            participant['models_contributed'] += 1
            participant['privacy_budget_used'] += session.privacy_contribution
            
            logger.debug(f"📊 [FEDERATED] Model update received from {participant_id}")
            
            # Check if aggregation should be triggered
            await self._check_aggregation_trigger()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [FEDERATED] Error submitting model update: {e}")
            return False
    
    def _validate_model_update(self, model_update: Dict[str, Any]) -> bool:
        """Validate model update structure and content"""
        required_fields = ['weights', 'performance_metrics', 'model_type']
        
        for field in required_fields:
            if field not in model_update:
                return False
        
        # Additional validation
        weights = model_update.get('weights', {})
        if not isinstance(weights, dict) or not weights:
            return False
        
        return True
    
    async def _check_aggregation_trigger(self):
        """Check if conditions are met for model aggregation"""
        try:
            # Check time-based trigger
            time_since_last = datetime.now() - self.last_aggregation
            
            # Check participant-based trigger
            recent_updates = [
                session for session in self.learning_history
                if (datetime.now() - session.timestamp) < timedelta(hours=2)
            ]
            
            should_aggregate = (
                time_since_last >= self.aggregation_frequency or
                len(recent_updates) >= self.min_participants * 3
            )
            
            if should_aggregate:
                await self.perform_federated_aggregation()
                
        except Exception as e:
            logger.error(f"❌ [FEDERATED] Error checking aggregation trigger: {e}")
    
    async def perform_federated_aggregation(self) -> Optional[AggregationResult]:
        """Perform federated model aggregation"""
        try:
            logger.info("🔄 [FEDERATED] Starting federated aggregation...")
            
            # Get recent model updates
            recent_sessions = [
                session for session in self.learning_history
                if (datetime.now() - session.timestamp) < timedelta(hours=6)
            ]
            
            if len(recent_sessions) < self.min_participants:
                logger.warning("⚠️ [FEDERATED] Insufficient participants for aggregation")
                return None
            
            # Group updates by model type
            updates_by_type = defaultdict(list)
            for session in recent_sessions:
                for update in session.model_updates:
                    model_type = update.get('model_type', 'general')
                    updates_by_type[model_type].append(update)
            
            aggregation_results = {}
            total_performance_improvement = 0.0
            
            # Aggregate each model type separately
            for model_type, updates in updates_by_type.items():
                if len(updates) >= self.min_participants:
                    # Extract weights for aggregation
                    weight_updates = [update['weights'] for update in updates]
                    
                    # Perform secure aggregation
                    aggregated_weights = self.privacy_aggregator.secure_aggregation(weight_updates)
                    
                    # Calculate performance improvement
                    performance_deltas = [update.get('performance_delta', 0.0) for update in updates]
                    avg_improvement = np.mean(performance_deltas)
                    
                    aggregation_results[model_type] = {
                        'weights': aggregated_weights,
                        'performance_improvement': avg_improvement,
                        'participants': len(updates)
                    }
                    
                    total_performance_improvement += avg_improvement
            
            # Create aggregation result
            result = AggregationResult(
                aggregated_weights=aggregation_results,
                performance_improvement=total_performance_improvement,
                participants_count=len(set(s.participant_id for s in recent_sessions)),
                privacy_cost=sum(s.privacy_contribution for s in recent_sessions),
                convergence_score=self._calculate_convergence_score(recent_sessions),
                timestamp=datetime.now()
            )
            
            # Update global metrics
            await self._update_global_metrics(result)
            
            # Distribute aggregated model
            await self._distribute_aggregated_model(result)
            
            self.aggregation_rounds += 1
            self.last_aggregation = datetime.now()
            
            logger.info(f"✅ [FEDERATED] Aggregation complete - Round {self.aggregation_rounds}")
            return result
            
        except Exception as e:
            logger.error(f"❌ [FEDERATED] Error in federated aggregation: {e}")
            return None

    def _calculate_convergence_score(self, sessions: List[LearningSession]) -> float:
        """Calculate convergence score based on performance consistency"""
        try:
            if len(sessions) < 2:
                return 0.0

            performance_deltas = [s.performance_delta for s in sessions]

            # Calculate variance in performance improvements
            variance = np.var(performance_deltas)
            mean_improvement = np.mean(performance_deltas)

            # Convergence score: higher when variance is low and mean improvement is positive
            if variance == 0:
                return 1.0 if mean_improvement > 0 else 0.0

            convergence = max(0, 1 - (variance / (abs(mean_improvement) + 1e-6)))
            return min(1.0, convergence)

        except Exception as e:
            logger.error(f"Error calculating convergence score: {e}")
            return 0.0

    async def _update_global_metrics(self, result: AggregationResult):
        """Update global performance metrics"""
        try:
            # Update performance metrics based on aggregation result
            improvement_factor = 1 + (result.performance_improvement / 100)

            self.global_performance_metrics['accuracy'] *= improvement_factor
            self.global_performance_metrics['profit_factor'] *= improvement_factor
            self.global_performance_metrics['convergence_rate'] = result.convergence_score

            # Update participant contribution scores
            for participant_id in self.active_participants:
                participant = self.active_participants[participant_id]
                participant['contribution_score'] += result.performance_improvement / result.participants_count

            logger.debug(f"📊 [FEDERATED] Global metrics updated")

        except Exception as e:
            logger.error(f"Error updating global metrics: {e}")

    async def _distribute_aggregated_model(self, result: AggregationResult):
        """Distribute aggregated model to participants"""
        try:
            # Encrypt aggregated weights
            encrypted_models = {}

            for model_type, model_data in result.aggregated_weights.items():
                weights_bytes = pickle.dumps(model_data['weights'])
                encrypted_weights = self.encryption_key.encrypt(weights_bytes)

                encrypted_models[model_type] = {
                    'encrypted_weights': base64.b64encode(encrypted_weights).decode(),
                    'performance_improvement': model_data['performance_improvement'],
                    'participants': model_data['participants'],
                    'timestamp': result.timestamp.isoformat()
                }

            # Store for participant retrieval
            self.model_registry[f"round_{self.aggregation_rounds}"] = encrypted_models

            logger.info(f"📤 [FEDERATED] Aggregated models distributed - Round {self.aggregation_rounds}")

        except Exception as e:
            logger.error(f"Error distributing aggregated model: {e}")

    async def get_latest_model(self, participant_id: str, model_type: str) -> Optional[Dict]:
        """Get latest aggregated model for participant"""
        try:
            if participant_id not in self.active_participants:
                return None

            # Get latest model round
            latest_round = f"round_{self.aggregation_rounds}"

            if latest_round not in self.model_registry:
                return None

            models = self.model_registry[latest_round]

            if model_type not in models:
                return None

            # Decrypt model for participant
            encrypted_data = models[model_type]
            encrypted_weights = base64.b64decode(encrypted_data['encrypted_weights'])
            weights_bytes = self.encryption_key.decrypt(encrypted_weights)
            weights = pickle.loads(weights_bytes)

            return {
                'weights': weights,
                'performance_improvement': encrypted_data['performance_improvement'],
                'participants': encrypted_data['participants'],
                'round': self.aggregation_rounds,
                'timestamp': encrypted_data['timestamp']
            }

        except Exception as e:
            logger.error(f"Error getting latest model: {e}")
            return None

    def get_federation_status(self) -> Dict[str, Any]:
        """Get comprehensive federation status"""
        try:
            active_count = len([
                p for p in self.active_participants.values()
                if (datetime.now() - p['last_seen']) < timedelta(hours=24)
            ])

            recent_sessions = [
                s for s in self.learning_history
                if (datetime.now() - s.timestamp) < timedelta(hours=24)
            ]

            return {
                'coordinator_id': self.coordinator_id,
                'active_participants': active_count,
                'total_participants': len(self.active_participants),
                'aggregation_rounds': self.aggregation_rounds,
                'last_aggregation': self.last_aggregation.isoformat(),
                'recent_sessions': len(recent_sessions),
                'global_metrics': self.global_performance_metrics,
                'privacy_budget_used': sum(
                    p['privacy_budget_used'] for p in self.active_participants.values()
                ),
                'convergence_trend': self._calculate_convergence_trend()
            }

        except Exception as e:
            logger.error(f"Error getting federation status: {e}")
            return {}

    def _calculate_convergence_trend(self) -> str:
        """Calculate convergence trend over recent rounds"""
        try:
            recent_sessions = list(self.learning_history)[-20:]  # Last 20 sessions

            if len(recent_sessions) < 5:
                return "insufficient_data"

            # Calculate trend in performance improvements
            improvements = [s.performance_delta for s in recent_sessions]

            # Simple linear trend
            x = np.arange(len(improvements))
            slope = np.polyfit(x, improvements, 1)[0]

            if slope > 0.01:
                return "improving"
            elif slope < -0.01:
                return "declining"
            else:
                return "stable"

        except Exception as e:
            logger.error(f"Error calculating convergence trend: {e}")
            return "unknown"

class FederatedLearningParticipant:
    """Individual participant in federated learning"""

    def __init__(self, participant_id: str, coordinator_url: Optional[str] = None):
        self.participant_id = participant_id
        self.coordinator_url = coordinator_url
        self.local_models = {}
        self.contribution_history = deque(maxlen=100)
        self.privacy_budget_remaining = 10.0

        # Local learning state
        self.local_performance_metrics = {
            'accuracy': 0.0,
            'profit_factor': 0.0,
            'trades_count': 0,
            'win_rate': 0.0
        }

        logger.info(f"🤝 [PARTICIPANT] {participant_id} initialized")

    async def contribute_model_update(self, model_type: str, weights: Dict[str, Any],
                                    performance_metrics: Dict[str, float]) -> bool:
        """Contribute local model update to federation"""
        try:
            # Calculate performance delta
            previous_performance = self.local_performance_metrics.get('profit_factor', 0.0)
            current_performance = performance_metrics.get('profit_factor', 0.0)
            performance_delta = current_performance - previous_performance

            # Calculate privacy cost
            privacy_cost = min(0.1, self.privacy_budget_remaining * 0.01)

            model_update = {
                'model_type': model_type,
                'weights': weights,
                'performance_metrics': performance_metrics,
                'performance_delta': performance_delta,
                'privacy_cost': privacy_cost,
                'participant_id': self.participant_id,
                'timestamp': datetime.now().isoformat(),
                'market_regime': self._detect_current_market_regime(),
                'strategy_type': 'adaptive'
            }

            # Update local state
            self.local_performance_metrics.update(performance_metrics)
            self.privacy_budget_remaining -= privacy_cost

            # Record contribution
            self.contribution_history.append({
                'timestamp': datetime.now(),
                'model_type': model_type,
                'performance_delta': performance_delta,
                'privacy_cost': privacy_cost
            })

            logger.info(f"📤 [PARTICIPANT] {self.participant_id} contributing {model_type} model update")
            return True

        except Exception as e:
            logger.error(f"❌ [PARTICIPANT] Error contributing model update: {e}")
            return False

    def _detect_current_market_regime(self) -> str:
        """Detect current market regime for context"""
        # Simplified market regime detection
        # In practice, this would use real market data
        return "normal_volatility"

    async def receive_aggregated_model(self, model_type: str,
                                     aggregated_weights: Dict[str, Any]) -> bool:
        """Receive and apply aggregated model from federation"""
        try:
            # Store aggregated model
            self.local_models[model_type] = {
                'weights': aggregated_weights,
                'received_at': datetime.now(),
                'applied': False
            }

            logger.info(f"📥 [PARTICIPANT] {self.participant_id} received {model_type} aggregated model")
            return True

        except Exception as e:
            logger.error(f"❌ [PARTICIPANT] Error receiving aggregated model: {e}")
            return False

    def get_participant_status(self) -> Dict[str, Any]:
        """Get participant status and metrics"""
        return {
            'participant_id': self.participant_id,
            'privacy_budget_remaining': self.privacy_budget_remaining,
            'contributions_count': len(self.contribution_history),
            'local_performance': self.local_performance_metrics,
            'models_received': len(self.local_models),
            'last_contribution': self.contribution_history[-1]['timestamp'].isoformat() if self.contribution_history else None
        }
