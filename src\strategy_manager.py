# Add proper position sizing and risk checks
# Implement real strategy switching

from strategies import BaseStrategy
from strategies.momentum import MomentumStrategy
from strategies.mean_reversion import MeanReversionStrategy
import logging
import numpy as np

class LiveStrategyManager:
    def __init__(self, capital: float):
        self.capital = capital
        self.strategies = {
            'momentum': MomentumStrategy(),
            'mean_reversion': MeanReversionStrategy()
        }
        self.active_strategies = {}
        
    def activate_strategy(self, name: str, risk_params: dict):
        """Activate strategy with live trading parameters"""
        if name not in self.strategies:
            raise ValueError(f"Strategy {name} not available")
            
        strategy = self.strategies[name]
        strategy.set_parameters(
            max_position_size=risk_params['max_position'],
            stop_loss=risk_params['stop_loss'],
            take_profit=risk_params['take_profit']
        )
        self.active_strategies[name] = strategy
        
    def deactivate_strategy(self, name: str):
        """Immediately disable strategy"""
        if name in self.active_strategies:
            del self.active_strategies[name]
            
    def get_live_signals(self, market_data: dict):
        """Process market data through active strategies"""
        signals = []
        for name, strategy in self.active_strategies.items():
            try:
                if strategy.check_market_conditions(market_data):
                    signals.append(strategy.generate_signal(market_data))
            except Exception as e:
                print(f"Strategy {name} failed: {str(e)}")
                self.deactivate_strategy(name)
                
        return signals

from jsonschema import Draft7Validator

class ConfigManager:
    def __init__(self):
        self.validator = Draft7Validator(self._load_schema())
        
    def validate(self, config: dict) -> list:
        """Return list of errors instead of raising"""
        return list(self.validator.iter_errors(config))

    async def emergency_shutdown(self):
        """More comprehensive than graceful_shutdown"""
        self.logger.critical("EMERGENCY SHUTDOWN INITIATED")
        
        try:
            # 1. Cancel all open orders
            await self.trader.cancel_all_orders()
            
            # 2. Close all positions if configured
            if self.config['trading']['risk'].get('auto_liquidate', False):
                await self.trader.close_all_positions()
            
            # 3. Send critical alert
            await self.send_alert("🛑 EMERGENCY SHUTDOWN ACTIVATED")
            
        finally:
            exit(1)  # Exit with error code

class MomentumStrategy:
    def __init__(self):
        self.rsi = RSI(period=14)
        self.ema20 = EMA(period=20)
        self.ema50 = EMA(period=50)
        
    def process_new_data(self, close_price: float):
        # Add new price
        self.rsi.add(close_price)
        self.ema20.add(close_price)
        self.ema50.add(close_price)
        
        # Generate signals
        signals = []
        if len(self.ema20) > 1 and len(self.ema50) > 1:
            # Golden cross
            if self.ema20[-1] > self.ema50[-1] and self.ema20[-2] <= self.ema50[-2]:
                signals.append('BUY')
            # Death cross
            elif self.ema20[-1] < self.ema50[-1] and self.ema20[-2] >= self.ema50[-2]:
                signals.append('SELL')
                
        if self.rsi[-1] > 70:
            signals.append('OVERBOUGHT')
        elif self.rsi[-1] < 30:
            signals.append('OVERSOLD')
            
        return signals

# Usage
strategy = MomentumStrategy()
prices = [100.5, 101.2, 102.3, 103.1, 101.8, 100.5]  # Sample data

for price in prices:
    signals = strategy.process_new_data(price)
    print(f"Price: {price} | Signals: {signals}")