#!/usr/bin/env python3
"""
Strategy Diversification Fix
Activates the existing sophisticated trading strategies instead of simple hardcoded ones
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Load environment
from dotenv import load_dotenv
load_dotenv()

logger = logging.getLogger(__name__)

class StrategyDiversificationFixer:
    """Fix strategy diversification by activating existing sophisticated strategies"""
    
    def __init__(self):
        self.strategies_activated = []
        self.strategy_manager = None
        
    async def fix_strategy_diversification(self):
        """Main fix for strategy diversification"""
        print("🎯 [STRATEGY-FIX] Starting strategy diversification fix...")
        
        # Step 1: Verify existing strategies are available
        await self._verify_existing_strategies()
        
        # Step 2: Initialize neural strategy manager
        await self._initialize_neural_strategy_manager()
        
        # Step 3: Test strategy rotation
        await self._test_strategy_rotation()
        
        # Step 4: Create strategy activation patch
        await self._create_strategy_activation_patch()
        
        print("✅ [STRATEGY-FIX] Strategy diversification fix completed!")
        return True
    
    async def _verify_existing_strategies(self):
        """Verify that sophisticated strategies exist and are importable"""
        print("\n📋 [VERIFY] Checking existing trading strategies...")
        
        strategies_to_check = [
            ('src.strategies.momentum', 'MomentumStrategy'),
            ('src.strategies.mean_reversion', 'MeanReversionStrategy'),
            ('src.strategies.adaptive_neural_strategy', 'AdaptiveNeuralStrategy'),
            ('src.strategies.neural_strategy_manager', 'NeuralStrategyManager'),
            ('src.strategies.photon', 'PhotonStrategy')
        ]
        
        available_strategies = []
        
        for module_path, class_name in strategies_to_check:
            try:
                module = __import__(module_path, fromlist=[class_name])
                strategy_class = getattr(module, class_name)
                available_strategies.append((module_path, class_name, strategy_class))
                print(f"✅ [VERIFY] {class_name} available")
            except ImportError as e:
                print(f"❌ [VERIFY] {class_name} not available: {e}")
            except AttributeError as e:
                print(f"❌ [VERIFY] {class_name} class not found: {e}")
        
        print(f"✅ [VERIFY] Found {len(available_strategies)} sophisticated strategies")
        self.available_strategies = available_strategies
        return len(available_strategies) > 0
    
    async def _initialize_neural_strategy_manager(self):
        """Initialize the neural strategy manager"""
        print("\n🧠 [NEURAL] Initializing neural strategy manager...")
        
        try:
            from src.strategies.neural_strategy_manager import NeuralStrategyManager
            
            # Configuration for strategy manager
            strategy_config = {
                'momentum': {
                    'lookback_period': 20,
                    'momentum_threshold': 0.02,
                    'max_position_size': 0.25
                },
                'mean_reversion': {
                    'lookback_period': 50,
                    'bollinger_period': 20,
                    'bollinger_std': 2.0,
                    'max_position_size': 0.20
                },
                'adaptive_neural': {
                    'learning_rate': 0.001,
                    'adaptation_frequency': 100,
                    'max_position_size': 0.30
                }
            }
            
            # Initialize strategy manager
            self.strategy_manager = NeuralStrategyManager(strategy_config)
            
            print("✅ [NEURAL] Neural strategy manager initialized")
            print(f"✅ [NEURAL] Available strategies: {list(self.strategy_manager.strategies.keys())}")
            print(f"✅ [NEURAL] Active strategy: {self.strategy_manager.active_strategy}")
            
            return True
            
        except Exception as e:
            print(f"❌ [NEURAL] Failed to initialize neural strategy manager: {e}")
            return False
    
    async def _test_strategy_rotation(self):
        """Test that strategy rotation works"""
        print("\n🔄 [ROTATION] Testing strategy rotation...")
        
        if not self.strategy_manager:
            print("❌ [ROTATION] No strategy manager available")
            return False
        
        try:
            # Create mock market context for testing
            from src.strategies.base import MarketContext
            
            mock_market_context = MarketContext(
                price_data={
                    'bybit': {
                        'BTC-USDT': {'price': 105000, 'volume': 1000000, 'change_24h': 0.02}
                    }
                },
                sentiment_data={'market_regime': 'trending_up'},
                arbitrage_opportunities=[],
                timestamp=datetime.now()
            )
            
            # Test getting trading signal
            signal = await self.strategy_manager.get_trading_signal(mock_market_context)
            
            if signal:
                print(f"✅ [ROTATION] Strategy manager generated signal: {signal.action} {signal.symbol}")
                print(f"✅ [ROTATION] Confidence: {signal.confidence:.2f}")
                print(f"✅ [ROTATION] Strategy: {signal.reasoning}")
            else:
                print("ℹ️ [ROTATION] No signal generated (normal for current market conditions)")
            
            # Test strategy switching
            original_strategy = self.strategy_manager.active_strategy
            print(f"✅ [ROTATION] Current active strategy: {original_strategy}")
            
            # Force strategy evaluation
            await self.strategy_manager._evaluate_and_switch_strategies(mock_market_context)
            
            new_strategy = self.strategy_manager.active_strategy
            print(f"✅ [ROTATION] Strategy after evaluation: {new_strategy}")
            
            return True
            
        except Exception as e:
            print(f"❌ [ROTATION] Strategy rotation test failed: {e}")
            return False
    
    async def _create_strategy_activation_patch(self):
        """Create a patch to activate sophisticated strategies in the main system"""
        print("\n🔧 [PATCH] Creating strategy activation patch...")
        
        patch_content = '''#!/usr/bin/env python3
"""
Strategy Activation Patch
Replaces simple hardcoded strategies with sophisticated neural strategy manager
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class AdvancedStrategyActivator:
    """Activates sophisticated trading strategies"""
    
    def __init__(self, components: Dict):
        self.components = components
        self.neural_strategy_manager = None
        self.strategy_signals = {}
        
    async def initialize_advanced_strategies(self):
        """Initialize advanced trading strategies"""
        try:
            # Initialize neural strategy manager if not already done
            if 'neural_strategy_manager' not in self.components:
                from src.strategies.neural_strategy_manager import NeuralStrategyManager
                
                strategy_config = {
                    'momentum': {
                        'lookback_period': 20,
                        'momentum_threshold': 0.02,
                        'max_position_size': 0.25,
                        'min_confidence_threshold': 0.6
                    },
                    'mean_reversion': {
                        'lookback_period': 50,
                        'bollinger_period': 20,
                        'bollinger_std': 2.0,
                        'max_position_size': 0.20,
                        'min_confidence_threshold': 0.6
                    },
                    'adaptive_neural': {
                        'learning_rate': 0.001,
                        'adaptation_frequency': 100,
                        'max_position_size': 0.30,
                        'min_confidence_threshold': 0.6
                    }
                }
                
                self.neural_strategy_manager = NeuralStrategyManager(strategy_config)
                self.components['neural_strategy_manager'] = self.neural_strategy_manager
                
                logger.info("✅ Advanced neural strategy manager activated")
                logger.info(f"Available strategies: {list(self.neural_strategy_manager.strategies.keys())}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize advanced strategies: {e}")
            return False
    
    async def get_diversified_trading_signals(self, market_data: Dict) -> Dict:
        """Get diversified trading signals from multiple strategies"""
        signals = {}
        
        try:
            if not self.neural_strategy_manager:
                await self.initialize_advanced_strategies()
            
            # Create market context
            from src.strategies.base import MarketContext
            
            market_context = MarketContext(
                price_data=market_data.get('price_data', {}),
                sentiment_data=market_data.get('sentiment_data', {'market_regime': 'sideways'}),
                arbitrage_opportunities=market_data.get('arbitrage_opportunities', []),
                timestamp=datetime.now()
            )
            
            # Get signal from neural strategy manager
            strategy_signal = await self.neural_strategy_manager.get_trading_signal(market_context)
            
            if strategy_signal:
                # Convert to format expected by trading system
                signal_key = f"advanced_strategy_{strategy_signal.symbol}"
                signals[signal_key] = {
                    'action': strategy_signal.action,
                    'symbol': strategy_signal.symbol,
                    'confidence': strategy_signal.confidence,
                    'quantity': strategy_signal.quantity,
                    'price': strategy_signal.price,
                    'stop_loss': strategy_signal.stop_loss,
                    'take_profit': strategy_signal.take_profit,
                    'strategy': f"neural_{self.neural_strategy_manager.active_strategy}",
                    'reasoning': strategy_signal.reasoning,
                    'priority': 1,  # High priority
                    'source': 'advanced_neural_strategy_manager',
                    'urgency': strategy_signal.urgency,
                    'neural_enhanced': True
                }
                
                logger.info(f"🎯 Advanced strategy signal: {strategy_signal.action} {strategy_signal.symbol}")
                logger.info(f"🎯 Active strategy: {self.neural_strategy_manager.active_strategy}")
                logger.info(f"🎯 Confidence: {strategy_signal.confidence:.2f}")
            
            return signals
            
        except Exception as e:
            logger.error(f"Error getting diversified trading signals: {e}")
            return {}

# Global instance for use in main system
_advanced_strategy_activator = None

def get_advanced_strategy_activator(components: Dict) -> AdvancedStrategyActivator:
    """Get or create advanced strategy activator"""
    global _advanced_strategy_activator
    if _advanced_strategy_activator is None:
        _advanced_strategy_activator = AdvancedStrategyActivator(components)
    return _advanced_strategy_activator

async def activate_advanced_strategies(components: Dict) -> bool:
    """Activate advanced strategies in the trading system"""
    activator = get_advanced_strategy_activator(components)
    return await activator.initialize_advanced_strategies()

async def get_advanced_trading_signals(components: Dict, market_data: Dict) -> Dict:
    """Get advanced trading signals"""
    activator = get_advanced_strategy_activator(components)
    return await activator.get_diversified_trading_signals(market_data)
'''
        
        # Save the patch
        patch_file = Path("src/strategies/strategy_activation_patch.py")
        patch_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(patch_file, 'w', encoding='utf-8') as f:
            f.write(patch_content)
        
        print(f"✅ [PATCH] Strategy activation patch created: {patch_file}")
        
        # Create integration instructions
        integration_instructions = '''
# Strategy Diversification Integration Instructions

## What was fixed:
1. The system had sophisticated trading strategies (momentum, mean reversion, adaptive neural) but was using simple hardcoded strategies
2. Neural strategy manager was initialized but not properly integrated into the main trading loop
3. Strategy rotation and diversification was not working

## How to integrate:
1. Import the strategy activation patch in main.py:
   ```python
   from src.strategies.strategy_activation_patch import activate_advanced_strategies, get_advanced_trading_signals
   ```

2. In the trading signal generation, replace simple strategies with advanced ones:
   ```python
   # Replace existing signal generation with:
   advanced_signals = await get_advanced_trading_signals(self.components, market_data)
   signals.update(advanced_signals)
   ```

3. Initialize advanced strategies during system startup:
   ```python
   await activate_advanced_strategies(self.components)
   ```

## Benefits:
- Multiple sophisticated trading strategies instead of simple BTC/USDT repetition
- Neural network-based strategy selection and adaptation
- Momentum, mean reversion, and adaptive neural strategies
- Automatic strategy switching based on market conditions
- Higher confidence thresholds and better risk management
'''
        
        instructions_file = Path("STRATEGY_DIVERSIFICATION_INTEGRATION.md")
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write(integration_instructions)
        
        print(f"✅ [PATCH] Integration instructions created: {instructions_file}")
        
        return True

async def main():
    """Main execution"""
    print("=" * 60)
    print("🎯 STRATEGY DIVERSIFICATION FIX")
    print("🚨 ACTIVATING SOPHISTICATED TRADING STRATEGIES")
    print("=" * 60)
    
    fixer = StrategyDiversificationFixer()
    
    try:
        success = await fixer.fix_strategy_diversification()
        
        if success:
            print("\n✅ [SUCCESS] Strategy diversification fix completed successfully!")
            print("✅ [RESULT] Sophisticated strategies are now available")
            print("✅ [NEXT] Apply the integration patch to activate in main system")
            return 0
        else:
            print("\n❌ [FAILED] Strategy diversification fix failed")
            return 1
            
    except Exception as e:
        print(f"\n❌ [ERROR] Fix failed: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Fix stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fix system error: {e}")
        sys.exit(1)
