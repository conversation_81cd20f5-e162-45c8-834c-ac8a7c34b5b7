#!/usr/bin/env python3
"""
Test Coinbase public endpoints to verify basic connectivity
"""

import requests
import json

def test_public_endpoints():
    """Test public Coinbase endpoints that don't require authentication"""
    
    print("🔍 [PUBLIC-TEST] Testing Coinbase public endpoints...")
    
    # Test endpoints
    endpoints = [
        {
            "name": "Time Endpoint",
            "url": "https://api.coinbase.com/api/v3/brokerage/time",
            "description": "Public time endpoint (no auth required)"
        },
        {
            "name": "Products Endpoint", 
            "url": "https://api.coinbase.com/api/v3/brokerage/products",
            "description": "Public products endpoint (no auth required)"
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n🧪 [TEST] {endpoint['name']}")
        print(f"📋 [URL] {endpoint['url']}")
        print(f"📋 [DESC] {endpoint['description']}")
        
        try:
            response = requests.get(endpoint['url'], timeout=30)
            
            print(f"📊 [RESPONSE] Status: {response.status_code}")
            print(f"📊 [HEADERS] {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ [SUCCESS] {endpoint['name']} working!")
                    print(f"📊 [DATA] {json.dumps(data, indent=2)[:500]}...")
                except:
                    print(f"✅ [SUCCESS] {endpoint['name']} working (non-JSON response)")
                    print(f"📊 [DATA] {response.text[:500]}...")
            else:
                print(f"❌ [FAILED] Status {response.status_code}")
                print(f"📊 [ERROR] {response.text}")
                
        except Exception as e:
            print(f"❌ [ERROR] Request failed: {e}")
    
    # Test basic connectivity
    print(f"\n🌐 [CONNECTIVITY] Testing basic connectivity to api.coinbase.com...")
    try:
        response = requests.get("https://api.coinbase.com", timeout=10)
        print(f"✅ [CONNECTIVITY] Basic connection successful (Status: {response.status_code})")
    except Exception as e:
        print(f"❌ [CONNECTIVITY] Basic connection failed: {e}")

def check_ip_and_location():
    """Check current IP and location"""
    print(f"\n🌍 [IP-CHECK] Checking current IP and location...")
    
    try:
        # Check IP
        ip_response = requests.get("https://httpbin.org/ip", timeout=10)
        if ip_response.status_code == 200:
            ip_data = ip_response.json()
            print(f"📍 [IP] Current IP: {ip_data.get('origin', 'Unknown')}")
        
        # Check location
        location_response = requests.get("https://httpbin.org/headers", timeout=10)
        if location_response.status_code == 200:
            headers_data = location_response.json()
            print(f"📍 [HEADERS] User-Agent: {headers_data.get('headers', {}).get('User-Agent', 'Unknown')}")
            
    except Exception as e:
        print(f"❌ [IP-CHECK] Failed: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🌐 COINBASE PUBLIC ENDPOINT TEST")
    print("=" * 60)
    
    test_public_endpoints()
    check_ip_and_location()
    
    print("=" * 60)
    print("✅ PUBLIC ENDPOINT TEST COMPLETED")
    print("=" * 60)
