{"adaptive_learning": {"enabled": true, "adaptation_frequency_hours": 1, "min_samples_for_adaptation": 50, "performance_threshold": 0.1, "exploration_phase_duration_hours": 24, "stability_period_required": 10}, "bayesian_optimization": {"enabled": true, "parameter_bounds": {"learning_rate": [1e-05, 0.1], "batch_size": [8, 128], "dropout": [0.0, 0.5], "weight_decay": [1e-06, 0.01], "momentum": [0.8, 0.99], "beta1": [0.8, 0.95], "beta2": [0.9, 0.999]}, "acquisition_function": "expected_improvement", "n_initial_points": 5, "n_calls": 50, "random_state": 42}, "online_learning": {"enabled": true, "learning_buffer_size": 1000, "performance_window_size": 100, "adaptation_threshold": 0.05, "batch_update_frequency_minutes": 15, "min_batch_size": 16, "max_batch_size": 64}, "strategy_evolution": {"enabled": true, "max_strategies": 10, "strategy_evaluation_window": 10, "performance_weight_decay": 0.95, "min_strategy_weight": 0.1, "strategy_selection_top_k": 3, "evolution_frequency_hours": 6}, "hyperparameter_optimization": {"methods": ["bayesian", "grid_search", "random_search"], "primary_method": "bayesian", "optimization_metric": "profit_factor", "secondary_metrics": ["sharpe_ratio", "win_rate", "max_drawdown"], "metric_weights": {"profit_factor": 0.4, "sharpe_ratio": 0.3, "win_rate": 0.2, "max_drawdown": 0.1}}, "model_selection": {"enabled": true, "selection_criteria": ["performance", "stability", "complexity"], "ensemble_size": 3, "model_rotation_enabled": true, "rotation_frequency_hours": 12, "performance_tracking_window": 100}, "meta_learning": {"enabled": true, "task_similarity_threshold": 0.8, "adaptation_steps": 5, "meta_learning_rate": 0.01, "inner_learning_rate": 0.001, "support_set_size": 10, "query_set_size": 5}, "continuous_learning": {"enabled": true, "catastrophic_forgetting_prevention": true, "elastic_weight_consolidation": true, "ewc_lambda": 0.4, "replay_buffer_size": 5000, "replay_frequency": 0.1, "knowledge_distillation_enabled": true}, "performance_metrics": {"primary_metrics": ["profit_factor", "sharpe_ratio", "win_rate", "max_drawdown", "calmar_ratio"], "secondary_metrics": ["sortino_ratio", "information_ratio", "treynor_ratio", "alpha", "beta"], "calculation_window_days": 30, "benchmark_symbol": "BTC/USDT", "risk_free_rate": 0.02}, "learning_schedules": {"warmup_period_hours": 6, "learning_rate_schedule": "cosine_annealing", "schedule_parameters": {"T_max": 100, "eta_min": 1e-06}, "adaptive_schedule": true, "performance_based_adjustment": true}, "regularization": {"l1_regularization": 0.0001, "l2_regularization": 0.001, "dropout_schedule": "adaptive", "batch_normalization": true, "layer_normalization": true, "gradient_clipping": true, "max_gradient_norm": 1.0}, "data_augmentation": {"enabled": true, "noise_injection": {"enabled": true, "noise_level": 0.01, "probability": 0.1}, "time_warping": {"enabled": true, "warp_factor": 0.1, "probability": 0.05}, "feature_dropout": {"enabled": true, "dropout_rate": 0.1, "probability": 0.1}}, "validation": {"cross_validation_folds": 5, "validation_split": 0.2, "time_series_split": true, "walk_forward_validation": true, "validation_frequency_hours": 24, "early_stopping": {"enabled": true, "patience": 10, "min_delta": 0.001, "restore_best_weights": true}}, "logging": {"adaptation_events": true, "hyperparameter_changes": true, "performance_metrics": true, "model_selection_events": true, "strategy_evolution": true, "detailed_metrics": false, "log_frequency_minutes": 30}}