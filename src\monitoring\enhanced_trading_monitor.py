#!/usr/bin/env python3
"""
Enhanced Trading Monitor - Comprehensive Real-time Monitoring and Debugging
Tracks signal generation, risk validation, position sizing, and order execution
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
from decimal import Decimal
import csv

logger = logging.getLogger(__name__)

class DecimalEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle Decimal objects"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

@dataclass
class TradingEvent:
    """Comprehensive trading event data structure"""
    timestamp: datetime
    event_type: str  # 'signal_generated', 'signal_validated', 'order_placed', 'order_filled', 'error'
    symbol: str
    action: str  # 'BUY', 'SELL', 'HOLD'
    amount: float
    price: float
    confidence: float
    strategy: str
    exchange: str
    order_id: Optional[str] = None
    execution_time_ms: Optional[float] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict] = None

@dataclass
class SystemMetrics:
    """Real-time system performance metrics"""
    timestamp: datetime
    total_signals_generated: int
    signals_validated: int
    orders_placed: int
    orders_filled: int
    total_pnl: float
    success_rate: float
    avg_execution_time_ms: float
    active_positions: int
    available_balance: float
    system_uptime_seconds: float

class EnhancedTradingMonitor:
    """
    Comprehensive real-time trading monitor with debugging capabilities
    """
    
    def __init__(self, log_dir: str = "logs/trading_monitor"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Event storage
        self.trading_events: List[TradingEvent] = []
        self.system_metrics_history: List[SystemMetrics] = []
        
        # Real-time counters
        self.counters = {
            'signals_generated': 0,
            'signals_validated': 0,
            'orders_placed': 0,
            'orders_filled': 0,
            'errors': 0,
            'total_pnl': 0.0
        }
        
        # Performance tracking
        self.execution_times: List[float] = []
        self.start_time = datetime.now()
        
        # File handles for real-time logging
        self.setup_logging_files()
        
        logger.info("Enhanced Trading Monitor initialized")
    
    def setup_logging_files(self):
        """Setup CSV files for real-time logging"""
        try:
            # Trading events log
            self.events_file = self.log_dir / f"trading_events_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            with open(self.events_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'event_type', 'symbol', 'action', 'amount', 'price',
                    'confidence', 'strategy', 'exchange', 'order_id', 'execution_time_ms',
                    'error_message', 'metadata'
                ])
            
            # System metrics log
            self.metrics_file = self.log_dir / f"system_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            with open(self.metrics_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'total_signals_generated', 'signals_validated', 'orders_placed',
                    'orders_filled', 'total_pnl', 'success_rate', 'avg_execution_time_ms',
                    'active_positions', 'available_balance', 'system_uptime_seconds'
                ])
                
        except Exception as e:
            logger.error(f"Failed to setup logging files: {e}")
    
    def log_signal_generated(self, signal_data: Dict, strategy: str = "unknown"):
        """Log when a trading signal is generated"""
        try:
            self.counters['signals_generated'] += 1
            
            event = TradingEvent(
                timestamp=datetime.now(),
                event_type='signal_generated',
                symbol=signal_data.get('symbol', 'UNKNOWN'),
                action=signal_data.get('action', 'UNKNOWN'),
                amount=float(signal_data.get('amount', 0)),
                price=float(signal_data.get('price', 0)),
                confidence=float(signal_data.get('confidence', 0)),
                strategy=strategy,
                exchange=signal_data.get('exchange', 'unknown'),
                metadata=signal_data
            )
            
            self._record_event(event)
            logger.info(f"📊 [SIGNAL] Generated {event.action} {event.symbol} - {strategy} (confidence: {event.confidence:.2f})")
            
        except Exception as e:
            logger.error(f"Error logging signal generation: {e}")
    
    def log_signal_validated(self, signal_data: Dict, validation_result: bool, reason: str = ""):
        """Log when a signal passes or fails validation"""
        try:
            if validation_result:
                self.counters['signals_validated'] += 1
            
            event = TradingEvent(
                timestamp=datetime.now(),
                event_type='signal_validated',
                symbol=signal_data.get('symbol', 'UNKNOWN'),
                action=signal_data.get('action', 'UNKNOWN'),
                amount=float(signal_data.get('amount', 0)),
                price=float(signal_data.get('price', 0)),
                confidence=float(signal_data.get('confidence', 0)),
                strategy=signal_data.get('strategy', 'unknown'),
                exchange=signal_data.get('exchange', 'unknown'),
                error_message=None if validation_result else reason,
                metadata={'validation_result': validation_result, 'reason': reason}
            )
            
            self._record_event(event)
            
            if validation_result:
                logger.info(f"✅ [VALIDATION] Approved {event.action} {event.symbol} (confidence: {event.confidence:.2f})")
            else:
                logger.warning(f"❌ [VALIDATION] Rejected {event.action} {event.symbol} - {reason}")
                
        except Exception as e:
            logger.error(f"Error logging signal validation: {e}")
    
    def log_order_placed(self, order_data: Dict, execution_time_ms: float = 0):
        """Log when an order is placed on an exchange"""
        try:
            self.counters['orders_placed'] += 1
            self.execution_times.append(execution_time_ms)
            
            event = TradingEvent(
                timestamp=datetime.now(),
                event_type='order_placed',
                symbol=order_data.get('symbol', 'UNKNOWN'),
                action=order_data.get('action', 'UNKNOWN'),
                amount=float(order_data.get('amount', 0)),
                price=float(order_data.get('price', 0)),
                confidence=float(order_data.get('confidence', 0)),
                strategy=order_data.get('strategy', 'unknown'),
                exchange=order_data.get('exchange', 'unknown'),
                order_id=order_data.get('order_id'),
                execution_time_ms=execution_time_ms,
                metadata=order_data
            )
            
            self._record_event(event)
            logger.warning(f"🚀 [ORDER] Placed {event.action} {event.amount} {event.symbol} on {event.exchange} "
                         f"(Order ID: {event.order_id}, Time: {execution_time_ms:.2f}ms)")
            
        except Exception as e:
            logger.error(f"Error logging order placement: {e}")
    
    def log_order_filled(self, fill_data: Dict, pnl: float = 0):
        """Log when an order is filled"""
        try:
            self.counters['orders_filled'] += 1
            self.counters['total_pnl'] += pnl
            
            event = TradingEvent(
                timestamp=datetime.now(),
                event_type='order_filled',
                symbol=fill_data.get('symbol', 'UNKNOWN'),
                action=fill_data.get('action', 'UNKNOWN'),
                amount=float(fill_data.get('amount', 0)),
                price=float(fill_data.get('fill_price', 0)),
                confidence=float(fill_data.get('confidence', 0)),
                strategy=fill_data.get('strategy', 'unknown'),
                exchange=fill_data.get('exchange', 'unknown'),
                order_id=fill_data.get('order_id'),
                metadata={'pnl': pnl, 'fill_data': fill_data}
            )
            
            self._record_event(event)
            pnl_str = f"+${pnl:.2f}" if pnl > 0 else f"${pnl:.2f}"
            logger.warning(f"💰 [FILLED] {event.action} {event.amount} {event.symbol} at ${event.price:.2f} "
                         f"(PnL: {pnl_str}, Order ID: {event.order_id})")
            
        except Exception as e:
            logger.error(f"Error logging order fill: {e}")
    
    def log_error(self, error_data: Dict, error_message: str):
        """Log trading errors and failures"""
        try:
            self.counters['errors'] += 1
            
            event = TradingEvent(
                timestamp=datetime.now(),
                event_type='error',
                symbol=error_data.get('symbol', 'UNKNOWN'),
                action=error_data.get('action', 'UNKNOWN'),
                amount=float(error_data.get('amount', 0)),
                price=float(error_data.get('price', 0)),
                confidence=float(error_data.get('confidence', 0)),
                strategy=error_data.get('strategy', 'unknown'),
                exchange=error_data.get('exchange', 'unknown'),
                error_message=error_message,
                metadata=error_data
            )
            
            self._record_event(event)
            logger.error(f"❌ [ERROR] {event.action} {event.symbol} on {event.exchange} - {error_message}")
            
        except Exception as e:
            logger.error(f"Error logging trading error: {e}")
    
    def _record_event(self, event: TradingEvent):
        """Record event to memory and file - CRITICAL FIX for character encoding"""
        try:
            # Add to memory
            self.trading_events.append(event)

            # Keep only last 1000 events in memory
            if len(self.trading_events) > 1000:
                self.trading_events = self.trading_events[-1000:]

            # CRITICAL FIX: Sanitize all string data for Windows console compatibility
            def sanitize_string(value):
                if isinstance(value, str):
                    # Replace problematic unicode characters
                    value = value.replace('\u2192', '->')  # Arrow character
                    value = value.replace('\u2190', '<-')  # Left arrow
                    value = value.replace('\u2191', '^')   # Up arrow
                    value = value.replace('\u2193', 'v')   # Down arrow
                    # Encode to ASCII with replacement for any other problematic characters
                    try:
                        value = value.encode('ascii', 'replace').decode('ascii')
                    except Exception:
                        value = str(value)  # Fallback to string conversion
                return value

            # Write to CSV file with proper encoding
            with open(self.events_file, 'a', newline='', encoding='utf-8', errors='replace') as f:
                writer = csv.writer(f)
                writer.writerow([
                    sanitize_string(event.timestamp.isoformat()),
                    sanitize_string(event.event_type),
                    sanitize_string(event.symbol),
                    sanitize_string(event.action),
                    event.amount,
                    event.price,
                    event.confidence,
                    sanitize_string(event.strategy),
                    sanitize_string(event.exchange),
                    sanitize_string(event.order_id),
                    event.execution_time_ms,
                    sanitize_string(event.error_message),
                    sanitize_string(json.dumps(event.metadata, cls=DecimalEncoder) if event.metadata else None)
                ])

        except Exception as e:
            logger.error(f"Error recording event: {e}")
    
    def get_real_time_metrics(self, available_balance: float = 0, active_positions: int = 0) -> SystemMetrics:
        """Get current system performance metrics"""
        try:
            uptime = (datetime.now() - self.start_time).total_seconds()
            success_rate = (self.counters['orders_filled'] / max(self.counters['orders_placed'], 1)) * 100
            avg_execution_time = sum(self.execution_times) / max(len(self.execution_times), 1)
            
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                total_signals_generated=self.counters['signals_generated'],
                signals_validated=self.counters['signals_validated'],
                orders_placed=self.counters['orders_placed'],
                orders_filled=self.counters['orders_filled'],
                total_pnl=self.counters['total_pnl'],
                success_rate=success_rate,
                avg_execution_time_ms=avg_execution_time,
                active_positions=active_positions,
                available_balance=available_balance,
                system_uptime_seconds=uptime
            )
            
            # Record metrics
            self.system_metrics_history.append(metrics)
            
            # Write to CSV with proper encoding
            with open(self.metrics_file, 'a', newline='', encoding='utf-8', errors='replace') as f:
                writer = csv.writer(f)
                writer.writerow([
                    metrics.timestamp.isoformat(),
                    metrics.total_signals_generated,
                    metrics.signals_validated,
                    metrics.orders_placed,
                    metrics.orders_filled,
                    metrics.total_pnl,
                    metrics.success_rate,
                    metrics.avg_execution_time_ms,
                    metrics.active_positions,
                    metrics.available_balance,
                    metrics.system_uptime_seconds
                ])
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting real-time metrics: {e}")
            return SystemMetrics(
                timestamp=datetime.now(),
                total_signals_generated=0, signals_validated=0, orders_placed=0,
                orders_filled=0, total_pnl=0.0, success_rate=0.0,
                avg_execution_time_ms=0.0, active_positions=0,
                available_balance=0.0, system_uptime_seconds=0.0
            )
    
    def print_performance_summary(self):
        """Print comprehensive performance summary"""
        try:
            metrics = self.get_real_time_metrics()
            
            print("\n" + "="*80)
            print("📊 ENHANCED TRADING MONITOR - PERFORMANCE SUMMARY")
            print("="*80)
            print(f"🕐 System Uptime: {metrics.system_uptime_seconds/3600:.2f} hours")
            print(f"📈 Signals Generated: {metrics.total_signals_generated}")
            print(f"✅ Signals Validated: {metrics.signals_validated} ({(metrics.signals_validated/max(metrics.total_signals_generated,1)*100):.1f}%)")
            print(f"🚀 Orders Placed: {metrics.orders_placed}")
            print(f"💰 Orders Filled: {metrics.orders_filled}")
            print(f"📊 Success Rate: {metrics.success_rate:.1f}%")
            print(f"⚡ Avg Execution Time: {metrics.avg_execution_time_ms:.2f}ms")
            print(f"💵 Total PnL: ${metrics.total_pnl:.2f}")
            print(f"💳 Available Balance: ${metrics.available_balance:.2f}")
            print(f"📍 Active Positions: {metrics.active_positions}")
            print(f"❌ Errors: {self.counters['errors']}")
            print("="*80)
            
        except Exception as e:
            logger.error(f"Error printing performance summary: {e}")
    
    def get_recent_events(self, limit: int = 10) -> List[TradingEvent]:
        """Get most recent trading events"""
        return self.trading_events[-limit:] if self.trading_events else []
    
    def export_data(self, export_path: str = None):
        """Export all trading data to JSON"""
        try:
            if not export_path:
                export_path = self.log_dir / f"trading_data_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            export_data = {
                'events': [asdict(event) for event in self.trading_events],
                'metrics_history': [asdict(metrics) for metrics in self.system_metrics_history],
                'counters': self.counters,
                'export_timestamp': datetime.now().isoformat()
            }
            
            with open(export_path, 'w') as f:
                json.dump(export_data, f, indent=2, cls=DecimalEncoder)
            
            logger.info(f"Trading data exported to {export_path}")
            return export_path
            
        except Exception as e:
            logger.error(f"Error exporting data: {e}")
            return None

# Global monitor instance
trading_monitor = None

def get_trading_monitor() -> EnhancedTradingMonitor:
    """Get global trading monitor instance"""
    global trading_monitor
    if trading_monitor is None:
        trading_monitor = EnhancedTradingMonitor()
    return trading_monitor
