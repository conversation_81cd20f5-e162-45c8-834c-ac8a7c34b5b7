"""
Centralized Logging Configuration for AutoGPT Trader
Ensures all logs are collected in /logs folder with daily structure
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional


class CentralizedLogger:
    """Centralized logging manager for the entire trading system"""
    
    def __init__(self, base_log_dir: str = "logs"):
        self.base_log_dir = Path(base_log_dir)
        self.current_date = datetime.now().strftime("%Y-%m-%d")
        self.daily_log_dir = self.base_log_dir / self.current_date
        self._setup_directories()
        self._configured_loggers = set()
    
    def _setup_directories(self):
        """Create the logging directory structure"""
        try:
            # Create base logs directory
            self.base_log_dir.mkdir(exist_ok=True)
            
            # Create daily subdirectory
            self.daily_log_dir.mkdir(exist_ok=True)
            
            # Create subdirectories for different log types
            (self.daily_log_dir / "trading").mkdir(exist_ok=True)
            (self.daily_log_dir / "neural").mkdir(exist_ok=True)
            (self.daily_log_dir / "exchanges").mkdir(exist_ok=True)
            (self.daily_log_dir / "monitoring").mkdir(exist_ok=True)
            (self.daily_log_dir / "errors").mkdir(exist_ok=True)
            (self.daily_log_dir / "system").mkdir(exist_ok=True)
            
        except Exception as e:
            print(f"Warning: Could not create log directories: {e}")
    
    def get_log_file_path(self, log_type: str, component: str) -> Path:
        """Get the appropriate log file path for a component"""
        # Check if date has changed and update directories
        current_date = datetime.now().strftime("%Y-%m-%d")
        if current_date != self.current_date:
            self.current_date = current_date
            self.daily_log_dir = self.base_log_dir / self.current_date
            self._setup_directories()
        
        timestamp = datetime.now().strftime("%H%M%S")
        filename = f"{component}_{timestamp}.log"
        
        return self.daily_log_dir / log_type / filename
    
    def setup_logger(self, name: str, log_type: str = "system", 
                    level: int = logging.INFO, 
                    console_output: bool = True) -> logging.Logger:
        """Setup a logger with centralized file and console handlers"""
        
        # Avoid duplicate configuration
        if name in self._configured_loggers:
            return logging.getLogger(name)
        
        logger = logging.getLogger(name)
        logger.setLevel(level)

        # Clear any existing handlers
        logger.handlers.clear()

        # Prevent propagation to root logger to avoid duplication
        logger.propagate = False
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - [%(levelname)s] - %(name)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # File handler with daily rotation
        try:
            log_file = self.get_log_file_path(log_type, name.replace('.', '_'))
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            print(f"Warning: Could not create file handler for {name}: {e}")
        
        # Console handler
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(level)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # Error handler (separate file for errors)
        try:
            error_file = self.get_log_file_path("errors", f"{name.replace('.', '_')}_errors")
            error_handler = logging.FileHandler(error_file, encoding='utf-8')
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(formatter)
            logger.addHandler(error_handler)
        except Exception as e:
            print(f"Warning: Could not create error handler for {name}: {e}")
        
        self._configured_loggers.add(name)
        return logger
    
    def setup_main_logger(self) -> logging.Logger:
        """Setup the main trading system logger"""
        return self.setup_logger("AutoGPT-LiveTrader", "trading", logging.INFO)
    
    def setup_neural_logger(self) -> logging.Logger:
        """Setup logger for neural components"""
        return self.setup_logger("Neural", "neural", logging.INFO)
    
    def setup_exchange_logger(self, exchange_name: str) -> logging.Logger:
        """Setup logger for exchange components"""
        return self.setup_logger(f"Exchange-{exchange_name}", "exchanges", logging.INFO)
    
    def setup_monitoring_logger(self) -> logging.Logger:
        """Setup logger for monitoring components"""
        return self.setup_logger("Monitoring", "monitoring", logging.INFO)
    
    def cleanup_old_logs(self, days_to_keep: int = 7):
        """Clean up log files older than specified days"""
        try:
            import time
            cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
            
            for date_dir in self.base_log_dir.iterdir():
                if date_dir.is_dir():
                    try:
                        # Check if directory is older than cutoff
                        dir_time = date_dir.stat().st_mtime
                        if dir_time < cutoff_time:
                            import shutil
                            shutil.rmtree(date_dir)
                            print(f"Cleaned up old log directory: {date_dir}")
                    except Exception as e:
                        print(f"Warning: Could not clean up {date_dir}: {e}")
                        
        except Exception as e:
            print(f"Warning: Log cleanup failed: {e}")


# Global instance
_centralized_logger = None

def get_centralized_logger() -> CentralizedLogger:
    """Get the global centralized logger instance"""
    global _centralized_logger
    if _centralized_logger is None:
        _centralized_logger = CentralizedLogger()
    return _centralized_logger

def setup_main_logging() -> logging.Logger:
    """Setup main logging for the trading system"""
    logger_manager = get_centralized_logger()
    return logger_manager.setup_main_logger()

def setup_component_logging(component_name: str, log_type: str = "system") -> logging.Logger:
    """Setup logging for a specific component"""
    logger_manager = get_centralized_logger()
    return logger_manager.setup_logger(component_name, log_type)

def cleanup_old_logs(days_to_keep: int = 7):
    """Clean up old log files"""
    logger_manager = get_centralized_logger()
    logger_manager.cleanup_old_logs(days_to_keep)


# Configure root logger to use centralized logging
def configure_root_logging():
    """Configure the root logger to use centralized logging"""
    try:
        logger_manager = get_centralized_logger()
        
        # Setup root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Add centralized handlers
        formatter = logging.Formatter(
            '%(asctime)s - [%(levelname)s] - %(name)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # File handler
        log_file = logger_manager.get_log_file_path("system", "root")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        print(f"✅ Centralized logging configured: {logger_manager.daily_log_dir}")
        
    except Exception as e:
        print(f"Warning: Could not configure centralized logging: {e}")


if __name__ == "__main__":
    # Test the centralized logging
    configure_root_logging()
    
    logger = setup_main_logging()
    logger.info("Testing centralized logging system")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Test component logging
    neural_logger = setup_component_logging("test_neural", "neural")
    neural_logger.info("Testing neural component logging")
    
    exchange_logger = setup_component_logging("test_coinbase", "exchanges")
    exchange_logger.info("Testing exchange component logging")
