import numpy as np
from decimal import Decimal

class LiveRiskManager:
    def __init__(self, capital: Decimal = Decimal('100000')):
        self.capital = capital
        self.positions = {}
        self.var_95 = Decimal('0.0')
        self.max_leverage = Decimal('3.0')

    def validate_order(self, signal: TradeSignal, price: Decimal) -> bool:
        position_size = Decimal(signal.amount_percent) * self.capital
        required_margin = position_size / self.max_leverage
        
        # Check VAR
        if position_size > self.capital * (Decimal('1') - self.var_95):
            return False
            
        # Check leverage
        current_leverage = sum(
            pos['size'] * pos['price'] / self.capital 
            for pos in self.positions.values()
        )
        if (current_leverage + position_size/self.capital) > self.max_leverage:
            return False
            
        return True

    def update_var(self, returns: List[float]):
        """Update Value-at-Risk using historical simulation"""
        returns = np.array(returns)
        self.var_95 = Decimal(str(np.percentile(returns, 5)))

from jsonschema import Draft7Validator

class ConfigManager:
    def __init__(self):
        self.validator = Draft7Validator(self._load_schema())
        
    def validate(self, config: dict) -> list:
        """Return list of errors instead of raising"""
        return list(self.validator.iter_errors(config))