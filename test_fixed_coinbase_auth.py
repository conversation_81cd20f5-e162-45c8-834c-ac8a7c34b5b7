#!/usr/bin/env python3
"""
Test the fixed Coinbase authentication
"""

import os
import sys
import time
import requests
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_fixed_coinbase_auth():
    """Test the fixed Coinbase authentication method"""
    print("🔧 [TEST] Testing fixed Coinbase authentication...")
    
    try:
        from dotenv import load_dotenv
        from utils.cryptography.secure_credentials import decrypt_value
        import jwt
        from cryptography.hazmat.primitives import serialization
        
        load_dotenv()
        
        # Load credentials
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        if not encrypted_api_key or not encrypted_private_key:
            print("❌ [TEST] Missing encrypted credentials")
            return False
        
        # Decrypt credentials
        api_key_name = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)
        
        # Extract key ID
        key_id = api_key_name.split("/apiKeys/")[1] if "/apiKeys/" in api_key_name else api_key_name
        
        print(f"✅ [TEST] Credentials loaded")
        print(f"📊 [TEST] Key ID: {key_id}")
        
        # Load private key
        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )
        
        # Test endpoints with fixed authentication
        endpoints = [
            '/api/v3/brokerage/time',
            '/api/v3/brokerage/products',
            '/api/v3/brokerage/accounts'
        ]
        
        for endpoint in endpoints:
            print(f"\n🔍 [TEST] Testing {endpoint}...")
            
            # Create JWT with working method
            now = int(time.time())
            payload = {
                'iss': 'cdp',
                'nbf': now,
                'exp': now + 120,
                'sub': api_key_name,
                'uri': f'GET {endpoint}',
                'aud': ['retail_rest_api_proxy']
            }
            
            token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': key_id})
            
            # Use working headers
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json',
                'CB-ACCESS-KEY': key_id,
                'CB-ACCESS-TIMESTAMP': str(now),
                'CB-VERSION': '2023-01-01'
            }
            
            try:
                response = requests.get(f'https://api.coinbase.com{endpoint}', headers=headers, timeout=15)
                
                if response.status_code == 200:
                    print(f"✅ [TEST] {endpoint} - SUCCESS")
                    
                    # Show some data for accounts endpoint
                    if 'accounts' in endpoint:
                        data = response.json()
                        accounts = data.get('accounts', [])
                        print(f"📊 [TEST] Found {len(accounts)} accounts")
                        
                        for account in accounts[:3]:  # Show first 3
                            currency = account.get('currency', 'Unknown')
                            balance = account.get('available_balance', {}).get('value', '0')
                            print(f"📊 [TEST] {currency}: {balance}")
                    
                elif response.status_code == 401:
                    print(f"❌ [TEST] {endpoint} - 401 Unauthorized")
                    return False
                else:
                    print(f"⚠️ [TEST] {endpoint} - {response.status_code}")
                    
            except Exception as e:
                print(f"❌ [TEST] {endpoint} - Error: {e}")
                return False
        
        print("\n✅ [TEST] Fixed Coinbase authentication working!")
        return True
        
    except Exception as e:
        print(f"❌ [TEST] Authentication test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 TESTING FIXED COINBASE AUTHENTICATION")
    print("=" * 60)
    
    success = test_fixed_coinbase_auth()
    
    if success:
        print("\n✅ COINBASE AUTHENTICATION FIXED")
        print("✅ Ready to restore hybrid trading system")
    else:
        print("\n❌ COINBASE AUTHENTICATION STILL FAILING")
        print("❌ Continue with Bybit-only trading")
