# backend/src/core/fix_engine.py
import asyncfix
from asyncfix import FIXMessage, FIXConnection
import logging
import uuid

class InstitutionalFIXEngine:
    def __init__(self, config: dict):
        self.logger = logging.getLogger('FIXEngine')
        self.config = config
        self.protocol = asyncfix.FIXProtocol44()
        self.connections = {}
        
        # FIX Session Parameters
        self.sender_comp_id = config['sender_comp_id']
        self.target_comp_id = config['target_comp_id']
        self.heartbeat_int = config.get('heartbeat', 30)
        
        # Order tracking
        self.order_register = {}

    async def connect(self, venue: str):
        """Establish FIX connection to trading venue"""
        conn = FIXConnection(
            self.protocol,
            sender_comp_id=self.sender_comp_id,
            target_comp_id=self.target_comp_id,
            heartbeat_int=self.heartbeat_int
        )
        
        try:
            await conn.connect(
                host=self.config['venues'][venue]['host'],
                port=self.config['venues'][venue]['port'],
                ssl=self.config['venues'][venue]['ssl']
            )
            self.connections[venue] = conn
            asyncio.create_task(self._message_loop(conn))
            
        except Exception as e:
            self.logger.error(f"FIX Connection failed: {str(e)}")
            raise

    async def send_order(self, venue: str, order: dict) -> str:
        """Send institutional order via FIX protocol"""
        if venue not in self.connections:
            await self.connect(venue)

        msg = FIXMessage(asyncfix.MsgType.ORDER_SINGLE)
        clord_id = f"INST_{uuid.uuid4().hex[:10]}"
        
        msg.set(asyncfix.Tag.ClOrdID, clord_id)
        msg.set(asyncfix.Tag.Symbol, order['symbol'])
        msg.set(asyncfix.Tag.Side, '1' if order['side'] == 'buy' else '2')
        msg.set(asyncfix.Tag.OrderQty, str(order['quantity']))
        msg.set(asyncfix.Tag.Price, str(order['price']))
        msg.set(asyncfix.Tag.TimeInForce, '1')  # GTC
        msg.set(asyncfix.Tag.OrdType, '2')  # Limit order
        msg.set(asyncfix.Tag.HandlInst, '1')  # Automated execution
        
        await self.connections[venue].send_msg(msg)
        self.order_register[clord_id] = order
        return clord_id

    async def _message_loop(self, conn: FIXConnection):
        """Handle incoming FIX messages"""
        try:
            async for msg in conn.iter_messages():
                if msg.msg_type == asyncfix.MsgType.EXECUTION_REPORT:
                    await self._handle_execution_report(msg)
                elif msg.msg_type == asyncfix.MsgType.ORDER_CANCEL_REJECT:
                    await self._handle_cancel_reject(msg)
                    
        except Exception as e:
            self.logger.error(f"FIX connection error: {str(e)}")

    async def _handle_execution_report(self, msg: FIXMessage):
        """Process order execution updates"""
        clord_id = msg.get(asyncfix.Tag.ClOrdID)
        exec_type = msg.get(asyncfix.Tag.ExecType)
        
        if exec_type == '0':  # New order
            self.logger.info(f"Order {clord_id} acknowledged")
        elif exec_type == '2':  # Filled
            await self._process_fill(msg)
            
    async def _process_fill(self, msg: FIXMessage):
        """Handle trade fills"""
        clord_id = msg.get(asyncfix.Tag.ClOrdID)
        fill_qty = Decimal(msg.get(asyncfix.Tag.LastQty))
        fill_price = Decimal(msg.get(asyncfix.Tag.LastPx))
        
        # Update order state
        self.order_register[clord_id].update({
            'filled_qty': fill_qty,
            'avg_price': fill_price,
            'status': 'filled'
        })
        
        # Forward to risk system
        await RiskSystem.report_fill(clord_id, fill_qty, fill_price)

# Updated LiveTradingSession with FIX and Distributed Locking
class LiveTradingSession:
    def __init__(self, config: dict):
        self.fix_engine = InstitutionalFIXEngine(config['fix'])
        self.lock_manager = DistributedLockManager(config['redis_uri'])
        # ... existing init
        
    async def execute_block_order(self, order: dict):
        """Institutional order handling"""
        async with self.lock_manager.acquire_lock(f"order_{order['symbol']}"):
            if order['type'] == 'INSTITUTIONAL':
                clord_id = await self.fix_engine.send_order(
                    order['venue'], order)
                return await self._track_institutional_order(clord_id)
            else:
                return await self.execute_retail_order(order)