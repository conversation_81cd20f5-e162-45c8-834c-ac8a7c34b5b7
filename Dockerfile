# Stage 1: Builder - install dependencies
FROM python:3.11-slim AS builder
ENV PYTHONUNBUFFERED=1 
WORKDIR /app

# Install build tools (if needed for any pip packages)
RUN apt-get update && apt-get install -y --no-install-recommends build-essential git && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies in builder
COPY requirements.txt .
RUN pip install --upgrade pip && pip install -r requirements.txt

# Stage 2: Final image
FROM python:3.11-slim
ENV PYTHONUNBUFFERED=1
WORKDIR /app

# Copy only needed files from builder (site-packages) and source code
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application code
COPY . .

# (Optional) If using NVIDIA CUDA base for GPU support:
# FROM nvidia/cuda:12.0.1-runtime-ubuntu20.04 as base
# ... (similar steps to install Python and deps)
# The final stage could then use `FROM base` instead of python:3.11-slim.

# Define default command (could be overridden in docker-compose)
CMD ["python", "-m", "quantum_trading.run"]
