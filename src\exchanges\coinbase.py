# src/exchanges/coinbase.py
import os
import logging
import time
from typing import Dict, Any, Optional
from decimal import Decimal

try:
    import cbpro
except ImportError:
    cbpro = None

try:
    import ccxt
except ImportError:
    ccxt = None

from src.vault_client import VaultClient

logger = logging.getLogger(__name__)

class CoinbaseProTrader:
    def __init__(self):
        vault = VaultClient()
        credentials = vault.get_exchange_secret('coinbase')
        
        self.client = cbpro.AuthenticatedClient(
            credentials['api_key'],
            credentials['api_secret'],
            passphrase=vault.crypto.decrypt_value(os.getenv('COINBASE_PASSPHRASE')),
            api_url="https://api.pro.coinbase.com"
        )

    def get_balance(self) -> dict:
        """Get real-time portfolio balances"""
        try:
            accounts = self.client.get_accounts()
            return {
                acc['currency']: {
                    'balance': float(acc['balance']),
                    'available': float(acc['available']),
                    'hold': float(acc['hold'])
                }
                for acc in accounts if float(acc['balance']) > 0
            }
        except cbpro.exceptions.CoinbaseAPIError as e:
            raise SystemError(f"Coinbase API Error: {str(e)}")

class TradingError(Exception):
    """Custom exception for trading errors"""
    pass

class CoinbaseClient:
    """Coinbase trading client for REAL money trading with CDP API integration"""
    
    def __init__(self, api_key: str = None, api_secret: str = None, passphrase: str = None, sandbox: bool = False):
        """Initialize Coinbase client for LIVE trading"""
        self.name = "coinbase"
        self.sandbox = False  # FORCE LIVE TRADING ONLY
        
        # Load credentials using the secure credential system
        from src.utils.cryptpography.hybrid import HybridCrypto
        crypto = HybridCrypto()
        decrypt_value = crypto.decrypt_value

        # Try to load CDP credentials first
        try:
            # Try encrypted versions first
            encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
            encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')

            if encrypted_api_key and not api_key:
                self.api_key_name = decrypt_value(encrypted_api_key)
            else:
                self.api_key_name = api_key or os.getenv("COINBASE_API_KEY_NAME")

            if encrypted_private_key and not api_secret:
                self.private_key_pem = decrypt_value(encrypted_private_key)
            else:
                self.private_key_pem = api_secret or os.getenv("COINBASE_PRIVATE_KEY")

        except Exception as e:
            logger.warning(f"⚠️ [MIGRATION] Failed to decrypt credentials: {e}")
            # Fallback to environment variables
            self.api_key_name = api_key or os.getenv("COINBASE_API_KEY_NAME")
            self.private_key_pem = api_secret or os.getenv("COINBASE_PRIVATE_KEY")

        # Legacy credentials for ccxt fallback
        api_key = api_key or os.getenv("COINBASE_API_KEY") or self.api_key_name
        api_secret = api_secret or os.getenv("COINBASE_API_SECRET") or self.private_key_pem
        passphrase = passphrase or os.getenv("COINBASE_API_PASSPHRASE", "NOT_USED_FOR_ADVANCED_TRADE_API")
        
        # Check if we have CDP credentials (preferred) - MIGRATED TO OFFICIAL SDK
        if self.api_key_name and "organizations/" in str(self.api_key_name) and self.private_key_pem and ("BEGIN EC PRIVATE KEY" in str(self.private_key_pem) or "BEGIN PRIVATE KEY" in str(self.private_key_pem)):
            logger.info("✅ [MIGRATION] Using Official Coinbase Advanced Trading API SDK")
            self.use_cdp = True
            # Initialize official Coinbase Advanced Trading API client
            try:
                from src.exchanges.coinbase_advanced_client import CoinbaseAdvancedClient
                self.cdp_client = CoinbaseAdvancedClient(
                    api_key_name=self.api_key_name,
                    private_key=self.private_key_pem
                )
                logger.info("✅ [MIGRATION] Official Coinbase Advanced Trading API client initialized")
            except ImportError as e:
                logger.error(f"❌ [MIGRATION] Official Coinbase SDK not available: {e}")
                logger.warning("⚠️ [MIGRATION] Falling back to ccxt (may have authentication issues)")
                self.use_cdp = False
            except Exception as e:
                logger.error(f"❌ [MIGRATION] Failed to initialize official Coinbase SDK: {e}")
                logger.warning("⚠️ [MIGRATION] Falling back to ccxt (may have authentication issues)")
                self.use_cdp = False
        else:
            logger.warning("⚠️ [MIGRATION] No CDP credentials found, using legacy ccxt (may have authentication issues)")
            self.use_cdp = False
        
        if not self.use_cdp:
            # Fallback to legacy ccxt format
            if not all([api_key, api_secret]):
                raise ValueError("Coinbase API credentials are required for live trading")
            
            # Check if credentials are encrypted and decrypt them
            if api_key and isinstance(api_key, str) and api_key.startswith("gAAAAA"):
                try:
                    from src.utils.cryptpography.hybrid import HybridCrypto
                    crypto = HybridCrypto()
                    api_key = crypto.decrypt_value(api_key)
                    api_secret = crypto.decrypt_value(api_secret)
                    passphrase = crypto.decrypt_value(passphrase)
                    logger.info("Successfully decrypted Coinbase API credentials")
                except Exception as e:
                    logger.error(f"Error decrypting Coinbase credentials: {e}")
            
            # Initialize ccxt client for LIVE trading - Use coinbaseadvanced for new API
            self.client = ccxt.coinbaseadvanced({
                'apiKey': api_key,
                'secret': api_secret,
                'passphrase': passphrase,
                'sandbox': False,  # LIVE TRADING ONLY
                'enableRateLimit': True,
                'options': {
                    'tradingType': 'spot',  # Use spot trading
                }
            })
        
        logger.warning(f"[LIVE] Coinbase client initialized for REAL MONEY trading ({'CDP' if self.use_cdp else 'ccxt'})")
    
    async def initialize(self) -> bool:
        """Initialize the Coinbase client and test connection with fallback support"""
        try:
            if self.use_cdp:
                # Initialize official Coinbase Advanced Trading API client with fallback
                result = await self.cdp_client.initialize()

                # Get authentication status
                auth_status = self.cdp_client.get_authentication_status()
                logger.info(f"🔄 [FALLBACK] Authentication method: {auth_status['current_method']}")
                logger.info(f"🔄 [FALLBACK] Capabilities: {auth_status['capabilities']}")

                if result:
                    if auth_status['read_only_mode']:
                        logger.warning("⚠️ [FALLBACK] Coinbase initialized in READ-ONLY mode - limited functionality")
                    else:
                        logger.info("✅ [FALLBACK] Coinbase initialized with full trading capabilities")

                    # Log error message if not fully functional
                    error_msg = self.cdp_client.get_error_message()
                    if "❌" in error_msg or "⚠️" in error_msg:
                        logger.warning(f"🔧 [FALLBACK] Status: {error_msg}")

                    return True
                else:
                    logger.error("❌ [FALLBACK] All Coinbase authentication methods failed")
                    logger.error("🔧 [FALLBACK] Run 'python test_coinbase_endpoints.py' for diagnosis")
                    return False
            else:
                # Test the connection by fetching account info (legacy ccxt)
                self.client.fetch_balance()
                logger.info("✅ Coinbase ccxt client initialized successfully for LIVE TRADING")
                return True
        except Exception as e:
            logger.error(f"Failed to initialize Coinbase client: {e}")
            return False

    async def test_connection(self) -> bool:
        """Test connection to Coinbase API"""
        try:
            if self.use_cdp:
                # Use official SDK connection test
                result = await self.cdp_client.test_connection()
                if result:
                    logger.info("✅ [MIGRATION] Official Coinbase Advanced Trading API connection test successful")
                else:
                    logger.error("❌ [MIGRATION] Official Coinbase Advanced Trading API connection test failed")
                return result
            else:
                # Simple connection test (legacy ccxt)
                self.client.fetch_balance()
                logger.info("✅ Coinbase ccxt connection test successful")
                return True
        except Exception as e:
            logger.error(f"Coinbase connection test failed: {e}")
            return False
    
    def get_balance(self, currency: str = "USD") -> Dict[str, Any]:
        """Get REAL account balance for any currency"""
        try:
            balance = self.client.fetch_balance()
            
            if currency.upper() in balance['total']:
                return {
                    "currency": currency,
                    "available": str(balance['free'][currency]),
                    "hold": str(balance['used'][currency]),
                    "total": str(balance['total'][currency])
                }
            else:                return {
                    "currency": currency,
                    "available": "0.00",
                    "hold": "0.00", 
                    "total": "0.00"
                }
        except Exception as e:
            raise TradingError(f"Coinbase balance check failed: {str(e)}")
    
    def get_all_balances(self) -> Dict[str, Any]:
        """Get ALL account balances for all currencies with fallback support"""
        try:
            if self.use_cdp:
                # Use official Coinbase Advanced Trading API SDK with fallback
                logger.info("💰 [FALLBACK] Getting balances using Coinbase Advanced Trading API with fallback authentication")
                result = self.cdp_client.get_all_balances()

                # Check if we're in read-only mode or have authentication issues
                if "error" in result:
                    auth_status = self.cdp_client.get_authentication_status()
                    if auth_status['read_only_mode']:
                        logger.warning("⚠️ [FALLBACK] Cannot retrieve balances - operating in read-only mode")
                        logger.warning("🔧 [FALLBACK] Check API key permissions and run diagnostic tests")
                    else:
                        logger.error("❌ [FALLBACK] Balance retrieval failed due to authentication issues")
                        logger.error("🔧 [FALLBACK] Run 'python test_coinbase_endpoints.py' for diagnosis")

                return result
            else:
                # Legacy ccxt method
                logger.warning("⚠️ [LEGACY] Using legacy ccxt method for balances (may have authentication issues)")
                balance = self.client.fetch_balance()

                # Filter out zero balances and return comprehensive data
                filtered_balances = {}
                for currency, amounts in balance['total'].items():
                    if float(amounts) > 0:
                        filtered_balances[currency] = {
                            'free': balance['free'].get(currency, 0),
                            'used': balance['used'].get(currency, 0),
                            'total': amounts
                        }

                logger.info(f"Coinbase detected {len(filtered_balances)} currencies with balances: {list(filtered_balances.keys())}")
                return {
                    "balances": filtered_balances,
                    "exchange": "coinbase"
                }
        except Exception as e:
            # Enhanced error handling with fallback guidance
            if self.use_cdp:
                auth_status = self.cdp_client.get_authentication_status()
                error_msg = self.cdp_client.get_error_message()
                logger.error(f"❌ [FALLBACK] Balance retrieval failed: {error_msg}")

                if not auth_status['authenticated']:
                    logger.error("🔧 [FALLBACK] Recommendation: Check API key permissions and run 'python test_coinbase_endpoints.py'")

            raise TradingError(f"Coinbase get_all_balances failed: {str(e)}")
    
    async def get_balances(self) -> Dict[str, Any]:
        """Get balances with async interface for compatibility with main.py"""
        try:
            return self.get_all_balances()
        except Exception as e:
            logger.error(f"Failed to get Coinbase balances: {e}")
            return {"balances": {}, "exchange": "coinbase"}

    def fetch_account(self) -> Dict[str, Any]:
        """Fetch comprehensive account information"""
        try:
            balance = self.client.fetch_balance()
            
            return {
                "balances": balance,
                "exchange": self.name
            }
        except Exception as e:
            logger.error(f"Coinbase fetch_account failed: {e}")
            return {"balances": {}, "exchange": self.name, "error": str(e)}

    def fetch_balance(self) -> Dict[str, Any]:
        """Fetch balance in ccxt format"""
        try:
            return self.client.fetch_balance()
        except Exception as e:
            logger.error(f"Coinbase fetch_balance failed: {e}")
            return {"free": {}, "used": {}, "total": {}}

    def get_ticker(self, symbol: str = "BTC-USD") -> Dict[str, Any]:
        """Get REAL ticker data"""
        try:
            ticker = self.client.fetch_ticker(symbol)
            return {
                "symbol": symbol,
                "price": str(ticker['last']),
                "volume": str(ticker['baseVolume']),
                "timestamp": ticker['datetime']
            }
        except Exception as e:
            raise TradingError(f"Coinbase ticker request failed: {str(e)}")
    
    def execute_order(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a REAL trading order with REAL MONEY"""
        try:
            symbol = action.get("symbol", "BTC-USD")
            side = action.get("action", "buy").lower()
            amount = action.get("quantity", 0)
            order_type = action.get("type", "market").lower()
            price = action.get("price")

            logger.warning(f"🚀 [LIVE TRADE] [MIGRATION] Executing {side} {amount} {symbol} on Coinbase with REAL MONEY")

            if self.use_cdp:
                # Use official Coinbase Advanced Trading API SDK
                logger.info("🚀 [MIGRATION] Using official Coinbase Advanced Trading API SDK for order execution")

                if order_type == "market":
                    result = self.cdp_client.execute_order(symbol, side, Decimal(str(amount)), "market")
                elif order_type == "limit" and price:
                    result = self.cdp_client.execute_order(symbol, side, Decimal(str(amount)), "limit", Decimal(str(price)))
                else:
                    raise ValueError("Invalid order type or missing price for limit order")

                logger.info(f"✅ [MIGRATION] [LIVE TRADE] Official Coinbase SDK order successful")
                return result
            else:
                # Legacy ccxt method
                logger.warning("⚠️ [LEGACY] Using legacy ccxt method for order execution (may have authentication issues)")

                if order_type == "market":
                    if side == "buy":
                        order = self.client.create_market_buy_order(symbol, amount)
                    else:
                        order = self.client.create_market_sell_order(symbol, amount)
                elif order_type == "limit" and price:
                    if side == "buy":
                        order = self.client.create_limit_buy_order(symbol, amount, price)
                    else:
                        order = self.client.create_limit_sell_order(symbol, amount, price)
                else:
                    raise ValueError("Invalid order type or missing price for limit order")

                logger.info(f"[LIVE TRADE] Coinbase order successful: {order.get('id')}")
                return {
                    "order_id": order.get("id"),
                    "status": order.get("status"),
                    "symbol": symbol,
                    "side": side,
                    "quantity": amount,
                    "price": order.get("price"),
                    "timestamp": order.get("datetime"),
                    "exchange": "coinbase"
                }
        except Exception as e:
            raise TradingError(f"Coinbase order execution failed: {str(e)}")

    def execute_trade(self, symbol: str, side: str, amount: Decimal, order_type: str = "market", price: Optional[Decimal] = None) -> Dict[str, Any]:
        """Execute trade with unified interface"""
        action = {
            "symbol": symbol,
            "action": side,
            "quantity": float(amount),
            "type": order_type
        }
        if price:
            action["price"] = float(price)
            
        return self.execute_order(action)
    
    def get_market_state(self) -> Dict[str, Any]:
        """Get current REAL market state"""
        try:
            btc_ticker = self.client.fetch_ticker("BTC-USD")
            eth_ticker = self.client.fetch_ticker("ETH-USD")
            
            return {
                "exchange": "coinbase",
                "status": "online",
                "btc_price": float(btc_ticker['last']),
                "eth_price": float(eth_ticker['last']),
                "timestamp": btc_ticker['datetime']
            }
        except Exception as e:
            logger.error(f"Failed to get market state: {e}")
            return {"exchange": "coinbase", "status": "error", "error": str(e)}
    
    def close(self):
        """Close client connection"""
        logger.info("Coinbase client connection closed")
