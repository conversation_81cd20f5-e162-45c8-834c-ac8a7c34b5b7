#!/usr/bin/env python3
"""
Minimal version of main.py to test trading system startup without heavy neural imports
"""

import os
import sys
import traceback
import signal
import asyncio
import logging
import time
from pathlib import Path
from datetime import datetime

# CRITICAL: Force X Drive mode and environment setup
def setup_x_drive_environment():
    """Setup X drive environment and paths"""
    os.environ['AUTOGPT_X_DRIVE_MODE'] = 'true'
    
    X_DRIVE_PROJECT = Path("X:/autogpt_trade_project/The_real_deal/autogpt-trader")
    X_DRIVE_SRC = X_DRIVE_PROJECT / "src"
    
    sys.path = [p for p in sys.path if not any(p.lower().startswith(pattern) for pattern in [
        'e:\\', 'e:/', 'e:\\$root', 'e:/$root'
    ])]
    
    x_drive_paths = [str(X_DRIVE_PROJECT), str(X_DRIVE_SRC)]
    for path in x_drive_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    os.environ["PROJECT_ROOT"] = str(X_DRIVE_PROJECT)
    os.environ["SRC_DIR"] = str(X_DRIVE_SRC)
    os.environ["PYTHONPATH"] = f"{X_DRIVE_SRC};{X_DRIVE_PROJECT};{os.environ.get('PYTHONPATH', '')}"
    
    print(f"[OK] X Drive environment configured")
    return X_DRIVE_PROJECT, X_DRIVE_SRC

PROJECT_ROOT, SRC_DIR = setup_x_drive_environment()

# LIVE TRADING ONLY - NO FALLBACKS
def enforce_live_trading_mode():
    """Enforce live trading mode - NO SIMULATION/MOCK/TEST allowed"""
    os.environ["LIVE_TRADING"] = "true"
    os.environ["REAL_MONEY_TRADING"] = "true" 
    os.environ["DEMO_MODE"] = "false"
    os.environ["DRY_RUN"] = "false"
    os.environ["TRADING_MODE"] = "live"
    os.environ["SANDBOX"] = "false"
    os.environ["TESTNET"] = "false"
    os.environ["ENVIRONMENT"] = "production"

enforce_live_trading_mode()

# BYBIT-ONLY MODE
def enforce_bybit_only_mode():
    """Force Bybit-only trading mode"""
    os.environ["BYBIT_ONLY_MODE"] = "true"
    os.environ["COINBASE_ENABLED"] = "false"
    os.environ["COINBASE_DISABLED"] = "true"
    os.environ["PRIMARY_EXCHANGE"] = "bybit"
    os.environ["SECONDARY_EXCHANGE"] = "none"
    os.environ["FORCE_BYBIT_TRADING"] = "true"
    print("🎯 [BYBIT-ONLY] Bybit-only mode enforced")

enforce_bybit_only_mode()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("AutoGPT-MinimalTrader")

# Load environment
from dotenv import load_dotenv
env_file = PROJECT_ROOT / ".env"
if env_file.exists():
    load_dotenv(env_file)
    print(f"[OK] Loaded .env from {env_file}")

class MinimalTradingSystem:
    """Minimal trading system to test core functionality"""
    
    def __init__(self):
        self.components = {}
        self.running = False
        logger.info("🚀 [MINIMAL] Initializing minimal trading system...")
    
    async def initialize_system(self):
        """Initialize minimal system components"""
        try:
            logger.info("🔧 [MINIMAL] Initializing core components...")
            
            # Initialize exchange client
            await self._init_exchange_client()
            
            # Initialize currency switcher (our fix)
            await self._init_currency_switcher()
            
            # Initialize trading engine
            await self._init_trading_engine()
            
            logger.info("✅ [MINIMAL] System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ [MINIMAL] System initialization failed: {e}")
            logger.error(traceback.format_exc())
            return False
    
    async def _init_exchange_client(self):
        """Initialize Bybit exchange client"""
        try:
            logger.info("🏦 [EXCHANGE] Initializing Bybit client...")
            
            from src.exchanges.bybit_client_fixed import BybitClientFixed
            
            # Get credentials
            api_key = os.getenv('BYBIT_API_KEY')
            api_secret = os.getenv('BYBIT_API_SECRET')
            
            if not api_key or not api_secret:
                logger.error("❌ [EXCHANGE] Bybit credentials not found")
                return
            
            # Initialize client
            bybit_client = BybitClientFixed(
                api_key=api_key,
                api_secret=api_secret,
                testnet=False
            )
            
            self.components['bybit_client'] = bybit_client
            logger.info("✅ [EXCHANGE] Bybit client initialized")
            
        except Exception as e:
            logger.error(f"❌ [EXCHANGE] Failed to initialize exchange client: {e}")
    
    async def _init_currency_switcher(self):
        """Initialize currency switcher (our fix)"""
        try:
            logger.info("🔄 [CURRENCY] Initializing currency switcher...")
            
            from src.trading.intelligent_currency_switcher import IntelligentCurrencySwitcher
            
            if 'bybit_client' in self.components:
                self.currency_switcher = IntelligentCurrencySwitcher(
                    exchange_client=self.components['bybit_client'],
                    min_thresholds={
                        'USDT': 5.0,
                        'BTC': 0.0001,
                        'ETH': 0.001,
                        'SOL': 0.01,
                    }
                )
                
                # Test the fix
                logger.info("🧪 [CURRENCY] Testing min_usdt_threshold fix...")
                if hasattr(self.currency_switcher, 'min_usdt_threshold'):
                    logger.info(f"✅ [CURRENCY] min_usdt_threshold = {self.currency_switcher.min_usdt_threshold}")
                else:
                    logger.error("❌ [CURRENCY] min_usdt_threshold missing!")
                
                self.components['currency_switcher'] = self.currency_switcher
                logger.info("✅ [CURRENCY] Currency switcher initialized")
            else:
                logger.warning("⚠️ [CURRENCY] No exchange client for currency switcher")
                
        except Exception as e:
            logger.error(f"❌ [CURRENCY] Failed to initialize currency switcher: {e}")
    
    async def _init_trading_engine(self):
        """Initialize trading engine"""
        try:
            logger.info("⚙️ [ENGINE] Initializing trading engine...")
            
            from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
            
            exchange_clients = {}
            if 'bybit_client' in self.components:
                exchange_clients['bybit'] = self.components['bybit_client']
            
            if exchange_clients:
                self.trading_engine = MultiCurrencyTradingEngine(
                    exchange_clients=exchange_clients,
                    config={
                        'min_usdt_threshold': 10.0,
                        'aggressive_trading': True,
                        'micro_trading': True,
                        'confidence_threshold': 0.60,
                        'max_balance_usage': 0.85,
                    }
                )
                
                self.components['trading_engine'] = self.trading_engine
                logger.info("✅ [ENGINE] Trading engine initialized")
            else:
                logger.warning("⚠️ [ENGINE] No exchange clients for trading engine")
                
        except Exception as e:
            logger.error(f"❌ [ENGINE] Failed to initialize trading engine: {e}")
    
    async def run_minimal_trading_loop(self):
        """Run minimal trading loop"""
        try:
            logger.info("🚀 [TRADING] Starting minimal trading loop...")
            self.running = True
            
            loop_count = 0
            while self.running and loop_count < 5:  # Run only 5 iterations for testing
                try:
                    loop_count += 1
                    logger.info(f"🔄 [TRADING] Loop {loop_count}/5")
                    
                    # Test currency switcher
                    if 'currency_switcher' in self.components:
                        try:
                            result = await self.components['currency_switcher'].should_switch_to_sell_mode()
                            logger.info(f"💱 [CURRENCY] Switch decision: {result}")
                        except Exception as e:
                            logger.error(f"❌ [CURRENCY] Switch test failed: {e}")
                    
                    # Test trading engine
                    if 'trading_engine' in self.components:
                        try:
                            # Just test that we can access the engine without errors
                            logger.info(f"⚙️ [ENGINE] Engine status: {type(self.components['trading_engine'])}")
                        except Exception as e:
                            logger.error(f"❌ [ENGINE] Engine test failed: {e}")
                    
                    await asyncio.sleep(2)  # 2 second intervals
                    
                except Exception as e:
                    logger.error(f"❌ [TRADING] Loop error: {e}")
                    await asyncio.sleep(1)
            
            logger.info("✅ [TRADING] Minimal trading loop completed successfully")
            
        except Exception as e:
            logger.error(f"❌ [TRADING] Trading loop failed: {e}")

async def main():
    """Main function"""
    logger.info("🚀 [MAIN] Starting minimal trading system...")
    
    system = MinimalTradingSystem()
    
    # Initialize system
    success = await system.initialize_system()
    if not success:
        logger.error("❌ [MAIN] System initialization failed")
        return
    
    # Run trading loop
    await system.run_minimal_trading_loop()
    
    logger.info("✅ [MAIN] Minimal trading system completed")

if __name__ == "__main__":
    asyncio.run(main())
