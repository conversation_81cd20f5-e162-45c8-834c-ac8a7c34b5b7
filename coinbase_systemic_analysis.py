#!/usr/bin/env python3
"""
Coinbase Systemic Issue Analysis
Deep investigation into account status, API configuration, and infrastructure issues
"""

import os
import sys
import time
import requests
import json
from datetime import datetime, timezone

# Add project root to path
sys.path.append('.')

# Load environment
from dotenv import load_dotenv
load_dotenv()

from src.utils.cryptography.secure_credentials import decrypt_value

def analyze_account_status():
    """Analyze Coinbase account and organization status"""
    print("=== COINBASE ACCOUNT STATUS ANALYSIS ===")
    
    try:
        # Get current credentials
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        api_key_name = decrypt_value(encrypted_api_key)
        
        # Extract organization ID
        if '/apiKeys/' in api_key_name:
            org_id = api_key_name.split('/')[1]
            key_id = api_key_name.split('/apiKeys/')[1]
            
            print(f"Organization ID: {org_id}")
            print(f"Current Key ID: {key_id}")
            print(f"Full API Key: {api_key_name}")
            
            # Check if this is a valid organization format
            if len(org_id) == 36 and org_id.count('-') == 4:
                print("✅ Organization ID format appears valid (UUID)")
            else:
                print("❌ Organization ID format appears invalid")
                
            return org_id, key_id
        else:
            print("❌ Invalid API key format - cannot extract organization ID")
            return None, None
            
    except Exception as e:
        print(f"❌ Error analyzing account status: {e}")
        return None, None

def test_api_endpoints_comprehensive():
    """Test various Coinbase API endpoints to identify access patterns"""
    print("\n=== COMPREHENSIVE API ENDPOINT TESTING ===")
    
    # Test different base URLs and API versions
    test_configs = [
        {
            "name": "Advanced Trade API v3",
            "base_url": "https://api.coinbase.com",
            "endpoints": [
                "/api/v3/brokerage/time",
                "/api/v3/brokerage/products", 
                "/api/v3/brokerage/accounts",
                "/api/v3/brokerage/portfolios"
            ]
        },
        {
            "name": "Legacy Pro API",
            "base_url": "https://api.pro.coinbase.com",
            "endpoints": [
                "/time",
                "/products",
                "/accounts"
            ]
        },
        {
            "name": "Exchange API v2",
            "base_url": "https://api.coinbase.com",
            "endpoints": [
                "/v2/time",
                "/v2/exchange-rates",
                "/v2/accounts"
            ]
        }
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n--- Testing {config['name']} ---")
        results[config['name']] = {}
        
        for endpoint in config['endpoints']:
            full_url = f"{config['base_url']}{endpoint}"
            
            try:
                # Test without authentication first
                response = requests.get(full_url, timeout=10)
                status = response.status_code
                
                print(f"  {endpoint}: {status}")
                results[config['name']][endpoint] = {
                    "status": status,
                    "response_size": len(response.text),
                    "auth_required": status == 401
                }
                
                if status == 200:
                    print(f"    ✅ Public access working")
                elif status == 401:
                    print(f"    🔐 Authentication required")
                elif status == 403:
                    print(f"    ❌ Forbidden - possible account restriction")
                elif status == 404:
                    print(f"    ❌ Not found - endpoint may not exist")
                else:
                    print(f"    ⚠️ Unexpected status: {response.text[:100]}")
                    
            except Exception as e:
                print(f"  {endpoint}: ERROR - {e}")
                results[config['name']][endpoint] = {"error": str(e)}
    
    return results

def test_network_infrastructure():
    """Test network connectivity and potential blocking"""
    print("\n=== NETWORK INFRASTRUCTURE ANALYSIS ===")
    
    # Test basic connectivity
    test_hosts = [
        "api.coinbase.com",
        "api.pro.coinbase.com", 
        "cloud.coinbase.com",
        "www.coinbase.com"
    ]
    
    for host in test_hosts:
        try:
            response = requests.get(f"https://{host}", timeout=5)
            print(f"✅ {host}: {response.status_code}")
        except requests.exceptions.Timeout:
            print(f"⏱️ {host}: Timeout")
        except requests.exceptions.ConnectionError:
            print(f"❌ {host}: Connection Error")
        except Exception as e:
            print(f"⚠️ {host}: {e}")
    
    # Check current IP and geolocation
    try:
        ip_response = requests.get('https://api.ipify.org?format=json', timeout=5)
        ip_data = ip_response.json()
        current_ip = ip_data['ip']
        
        print(f"\nCurrent IP: {current_ip}")
        
        # Get geolocation info
        geo_response = requests.get(f'http://ip-api.com/json/{current_ip}', timeout=5)
        geo_data = geo_response.json()
        
        print(f"Location: {geo_data.get('city', 'Unknown')}, {geo_data.get('country', 'Unknown')}")
        print(f"ISP: {geo_data.get('isp', 'Unknown')}")
        print(f"VPN/Proxy: {'Yes' if geo_data.get('proxy', False) else 'No'}")
        
        # Check if IP matches expected
        expected_ip = "************"
        if current_ip == expected_ip:
            print("✅ IP matches expected address")
        else:
            print(f"⚠️ IP differs from expected: {expected_ip}")
            print("   This could indicate VPN/proxy usage or IP change")
            
    except Exception as e:
        print(f"❌ IP analysis failed: {e}")

def analyze_jwt_token_details():
    """Analyze JWT token generation and structure"""
    print("\n=== JWT TOKEN ANALYSIS ===")
    
    try:
        import jwt
        from cryptography.hazmat.primitives import serialization
        
        # Get credentials
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        api_key_name = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)
        key_id = api_key_name.split('/apiKeys/')[1] if '/apiKeys/' in api_key_name else None
        
        # Load and analyze private key
        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )
        
        # Get key details
        key_size = private_key.key_size
        curve_name = private_key.curve.name
        
        print(f"Private Key Details:")
        print(f"  Algorithm: EC (Elliptic Curve)")
        print(f"  Key Size: {key_size} bits")
        print(f"  Curve: {curve_name}")
        
        # Generate and analyze JWT
        now = int(time.time())
        payload = {
            'iss': 'cdp',
            'nbf': now,
            'exp': now + 120,
            'sub': api_key_name,
            'uri': 'GET /api/v3/brokerage/accounts'
        }
        
        token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': key_id})
        
        print(f"\nJWT Token Analysis:")
        print(f"  Length: {len(token)} characters")
        print(f"  Algorithm: ES256")
        print(f"  Key ID: {key_id}")
        
        # Decode header and payload for inspection
        header = jwt.get_unverified_header(token)
        decoded_payload = jwt.decode(token, options={"verify_signature": False})
        
        print(f"  Header: {json.dumps(header, indent=2)}")
        print(f"  Payload: {json.dumps(decoded_payload, indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ JWT analysis failed: {e}")
        return False

def check_coinbase_service_status():
    """Check Coinbase service status and announcements"""
    print("\n=== COINBASE SERVICE STATUS CHECK ===")
    
    try:
        # Check Coinbase status page
        status_response = requests.get('https://status.coinbase.com/api/v2/status.json', timeout=10)
        status_data = status_response.json()
        
        overall_status = status_data.get('status', {}).get('description', 'Unknown')
        print(f"Overall Status: {overall_status}")
        
        # Check for incidents
        incidents_response = requests.get('https://status.coinbase.com/api/v2/incidents.json', timeout=10)
        incidents_data = incidents_response.json()
        
        recent_incidents = incidents_data.get('incidents', [])[:3]  # Last 3 incidents
        
        if recent_incidents:
            print("\nRecent Incidents:")
            for incident in recent_incidents:
                name = incident.get('name', 'Unknown')
                status = incident.get('status', 'Unknown')
                created_at = incident.get('created_at', 'Unknown')
                print(f"  - {name} ({status}) - {created_at}")
        else:
            print("No recent incidents reported")
            
    except Exception as e:
        print(f"❌ Service status check failed: {e}")

def generate_escalation_report(org_id, key_id, test_results):
    """Generate comprehensive report for Coinbase support escalation"""
    print("\n=== GENERATING ESCALATION REPORT ===")
    
    report = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "account_details": {
            "organization_id": org_id,
            "current_key_id": key_id,
            "ip_address": "************",
            "issue_duration": "Multiple days",
            "failed_key_generations": 3
        },
        "technical_details": {
            "error_pattern": "Consistent 401 Unauthorized on all private endpoints",
            "public_endpoints": "Mixed results - some work, some fail",
            "jwt_generation": "Successful",
            "credential_decryption": "Successful",
            "network_connectivity": "Normal"
        },
        "test_results": test_results,
        "recommended_actions": [
            "Manual account review by Coinbase support",
            "Verification of API access permissions at organization level",
            "Check for account restrictions or compliance holds",
            "Review IP whitelist configuration",
            "Investigate potential API rate limiting or abuse detection"
        ]
    }
    
    # Save report to file
    with open('coinbase_escalation_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print("✅ Escalation report saved to: coinbase_escalation_report.json")
    print("\nKey points for Coinbase support:")
    print("1. Multiple API key generations have failed")
    print("2. JWT tokens generate correctly but return 401")
    print("3. Issue persists across different endpoints")
    print("4. Account may have restrictions or compliance holds")
    print("5. Manual review of organization access required")

def main():
    print("🔍 COINBASE SYSTEMIC ISSUE ANALYSIS")
    print("=" * 60)
    print("Investigating deeper issues beyond credential problems...")
    print()
    
    # 1. Account status analysis
    org_id, key_id = analyze_account_status()
    
    # 2. Comprehensive endpoint testing
    test_results = test_api_endpoints_comprehensive()
    
    # 3. Network infrastructure testing
    test_network_infrastructure()
    
    # 4. JWT token analysis
    jwt_success = analyze_jwt_token_details()
    
    # 5. Service status check
    check_coinbase_service_status()
    
    # 6. Generate escalation report
    if org_id and key_id:
        generate_escalation_report(org_id, key_id, test_results)
    
    print("\n" + "=" * 60)
    print("🎯 ANALYSIS COMPLETE")
    print("\nRECOMMENDATION: Do NOT generate another API key.")
    print("Contact Coinbase Developer Support with the escalation report.")
    print("The issue appears to be at the account/organization level.")

if __name__ == "__main__":
    main()
