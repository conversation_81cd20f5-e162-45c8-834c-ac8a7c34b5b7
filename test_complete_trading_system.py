#!/usr/bin/env python3
"""
Complete Trading System Test
Tests real money trading with integrated backtesting and order validation
"""
import asyncio
import os
import sys
import time
from datetime import datetime
from dotenv import load_dotenv

sys.path.append('.')

async def test_complete_trading_system():
    """Test the complete trading system with real money and backtesting"""
    try:
        load_dotenv()
        
        print("🚀 [SYSTEM] Starting Complete Trading System Test...")
        print("=" * 80)
        
        # Test 1: Initialize all components
        print("\n🔍 [TEST 1] Initializing system components...")
        
        from pybit.unified_trading import HTTP
        from src.exchanges.bybit_order_validator import BybitOrderValidator
        from src.trading.backtesting import RealTimeBacktestingEngine
        
        # Initialize Bybit session
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not api_key or not api_secret:
            print("❌ [TEST 1] Bybit credentials not found")
            return False
        
        session = HTTP(
            api_key=api_key,
            api_secret=api_secret,
            testnet=False,
            recv_window=10000
        )
        
        # Initialize order validator
        validator = BybitOrderValidator(session)
        
        # Initialize backtesting engine
        backtest_config = {
            'initial_balance': 10000.0,
            'risk_per_trade': 0.02,
            'enable_learning': True
        }
        backtest_engine = RealTimeBacktestingEngine(backtest_config)
        
        print("✅ [TEST 1] All components initialized successfully")
        
        # Test 2: Verify current balance
        print("\n🔍 [TEST 2] Verifying current balance...")
        
        balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
        if balance_response.get('retCode') == 0:
            balance_data = balance_response['result']['list'][0]['coin'][0]
            current_balance = float(balance_data.get('walletBalance', '0'))
            print(f"💰 [TEST 2] Current USDT balance: ${current_balance:.2f}")
            
            if current_balance < 6.0:
                print(f"⚠️ [TEST 2] Balance may be insufficient for testing (need $6.00 minimum)")
                return False
        else:
            print(f"❌ [TEST 2] Failed to get balance: {balance_response}")
            return False
        
        # Test 3: Test order validation
        print("\n🔍 [TEST 3] Testing order validation...")
        
        test_symbol = 'BTCUSDT'
        test_amount = 6.0  # Safe amount above minimum
        
        is_valid, order_params = await validator.validate_and_adjust_order(
            symbol=test_symbol,
            side="Buy",
            amount=test_amount,
            order_type="Market"
        )
        
        if is_valid:
            print(f"✅ [TEST 3] Order validation successful")
            print(f"   Symbol: {order_params['symbol']}")
            print(f"   Qty: {order_params['qty']}")
            print(f"   Calculated Value: ${order_params['calculated_value']:.2f}")
            print(f"   Price: ${order_params['price']:.2f}")
        else:
            print(f"❌ [TEST 3] Order validation failed: {order_params}")
            return False
        
        # Test 4: Execute real money trade
        print("\n🔍 [TEST 4] Executing real money trade...")
        print("🚨 [WARNING] This will execute a REAL trade with REAL money!")
        
        try:
            order_result = session.place_order(
                category=order_params['category'],
                symbol=order_params['symbol'],
                side=order_params['side'],
                orderType=order_params['orderType'],
                qty=order_params['qty'],
                isLeverage=order_params['isLeverage'],
                orderFilter=order_params.get('orderFilter', 'Order')
            )
            
            print(f"📊 [TEST 4] Order result: {order_result}")
            
            if order_result.get('retCode') == 0:
                order_id = order_result['result']['orderId']
                print(f"✅ [TEST 4] REAL MONEY TRADE SUCCESSFUL!")
                print(f"   Order ID: {order_id}")
                print(f"   Amount: ${test_amount:.2f}")
                print(f"   Symbol: {test_symbol}")
                
                # Verify balance change
                await asyncio.sleep(3)  # Wait for order to process
                
                new_balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
                if new_balance_response.get('retCode') == 0:
                    new_balance = float(new_balance_response['result']['list'][0]['coin'][0]['walletBalance'])
                    balance_change = current_balance - new_balance
                    
                    print(f"💰 [VERIFICATION] Balance change: ${balance_change:.2f}")
                    print(f"💰 [VERIFICATION] New balance: ${new_balance:.2f}")
                    
                    if balance_change > 0:
                        print(f"✅ [VERIFICATION] REAL MONEY TRADING CONFIRMED!")
                    else:
                        print(f"⚠️ [VERIFICATION] No balance change detected")
                
            else:
                error_code = order_result.get('retCode')
                error_msg = order_result.get('retMsg', 'Unknown error')
                print(f"❌ [TEST 4] Order failed: Code {error_code}, Message: {error_msg}")
                
                if error_code == 170140:
                    print(f"❌ [CRITICAL] ErrCode 170140 still occurring!")
                    return False
                
        except Exception as e:
            print(f"❌ [TEST 4] Order execution exception: {e}")
            return False
        
        # Test 5: Test backtesting simulation
        print("\n🔍 [TEST 5] Testing backtesting simulation...")
        
        # Simulate a trading signal
        test_signal = {
            'symbol': 'BTCUSDT',
            'action': 'BUY',
            'confidence': 0.75,
            'price': order_params['price'],
            'strategy': 'test_strategy',
            'timestamp': datetime.now()
        }
        
        # Process signal for backtesting
        await backtest_engine._process_signal_for_backtest(test_signal)
        
        if len(backtest_engine.simulated_trades) > 0:
            simulated_trade = backtest_engine.simulated_trades[0]
            print(f"✅ [TEST 5] Backtesting simulation successful")
            print(f"   Simulated Trade: {simulated_trade['action']} {simulated_trade['amount']:.6f} {simulated_trade['symbol']}")
            print(f"   Simulated Price: ${simulated_trade['price']:.2f}")
            print(f"   Simulated Balance: ${simulated_trade['balance_after']:.2f}")
        else:
            print(f"❌ [TEST 5] No simulated trades generated")
            return False
        
        # Test 6: Performance metrics
        print("\n🔍 [TEST 6] Checking performance metrics...")
        
        metrics = backtest_engine.performance_metrics
        print(f"📊 [METRICS] Total Trades: {metrics['total_trades']}")
        print(f"📊 [METRICS] Total P&L: ${metrics['total_pnl']:.2f}")
        print(f"📊 [METRICS] Current Simulated Balance: ${backtest_engine.simulated_balance:.2f}")
        
        if metrics['total_trades'] > 0:
            print(f"✅ [TEST 6] Performance metrics working")
        else:
            print(f"⚠️ [TEST 6] No performance data yet")
        
        # Test 7: Continuous operation test
        print("\n🔍 [TEST 7] Testing continuous operation (30 seconds)...")
        
        start_time = time.time()
        operation_count = 0
        
        while time.time() - start_time < 30:  # Run for 30 seconds
            try:
                # Simulate continuous trading signals
                test_signal = {
                    'symbol': 'BTCUSDT',
                    'action': 'BUY' if operation_count % 2 == 0 else 'SELL',
                    'confidence': 0.65 + (operation_count % 3) * 0.1,
                    'price': order_params['price'] * (1 + (operation_count % 5 - 2) * 0.001),  # Small price variations
                    'strategy': f'continuous_test_{operation_count}',
                    'timestamp': datetime.now()
                }
                
                # Process for backtesting
                await backtest_engine._process_signal_for_backtest(test_signal)
                operation_count += 1
                
                await asyncio.sleep(2)  # Process every 2 seconds
                
            except Exception as e:
                print(f"⚠️ [TEST 7] Operation error: {e}")
        
        print(f"✅ [TEST 7] Continuous operation completed")
        print(f"   Operations processed: {operation_count}")
        print(f"   Simulated trades: {len(backtest_engine.simulated_trades)}")
        print(f"   Final simulated balance: ${backtest_engine.simulated_balance:.2f}")
        
        # Final summary
        print("\n" + "=" * 80)
        print("🎯 [COMPLETE] COMPREHENSIVE TRADING SYSTEM TEST RESULTS")
        print("=" * 80)
        print(f"✅ Real Money Trading: WORKING")
        print(f"✅ Order Validation: WORKING (ErrCode 170140 FIXED)")
        print(f"✅ Balance Management: WORKING")
        print(f"✅ Backtesting Engine: WORKING")
        print(f"✅ Continuous Operation: WORKING")
        print(f"💰 Live Balance: ${new_balance:.2f} USDT")
        print(f"📊 Simulated Balance: ${backtest_engine.simulated_balance:.2f} USDT")
        print(f"📈 Total Simulated Trades: {len(backtest_engine.simulated_trades)}")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ [ERROR] Complete system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_complete_trading_system())
    if success:
        print(f'\n🎯 [RESULT] COMPLETE TRADING SYSTEM TEST SUCCESSFUL!')
        print(f'🚀 [READY] System ready for continuous live trading with backtesting!')
    else:
        print(f'\n❌ [RESULT] COMPLETE TRADING SYSTEM TEST FAILED!')
