#!/usr/bin/env python3
"""
EMERGENCY TRADING SYSTEM
========================

Direct trading execution that bypasses all complex initialization
and demonstrates real money trading capability immediately.

This is the fallback system when main.py hangs during initialization.
"""

import asyncio
import os
import sys
import logging
from decimal import Decimal
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("emergency_trader")

async def emergency_trading_system():
    """Emergency trading system with immediate execution"""
    try:
        print("🚨 EMERGENCY TRADING SYSTEM ACTIVATED")
        print("💰 REAL MONEY TRADING - Direct execution mode")
        print("🔧 Bypassing complex initialization for immediate trading")
        print("=" * 60)
        
        # Load environment
        load_dotenv()
        
        # Get credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            print("❌ [EMERGENCY] Missing Bybit credentials")
            return False
        
        print("✅ [EMERGENCY] Bybit credentials found")
        
        # Import Bybit client
        sys.path.append('.')
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Initialize client
        print("🔧 [EMERGENCY] Initializing Bybit client...")
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        print("✅ [EMERGENCY] Bybit client initialized")
        
        # Test balance retrieval
        print("💰 [EMERGENCY] Checking account balance...")
        try:
            usdt_balance = await asyncio.wait_for(
                bybit_client.get_balance('USDT'),
                timeout=10.0
            )
            print(f"✅ [EMERGENCY] USDT balance: {usdt_balance}")
        except asyncio.TimeoutError:
            print("❌ [EMERGENCY] Balance check timed out")
            return False
        except Exception as e:
            print(f"❌ [EMERGENCY] Balance check failed: {e}")
            return False
        
        if float(usdt_balance) < 5.0:
            print("⚠️ [EMERGENCY] Insufficient USDT balance for trading")
            return False
        
        # Execute emergency trades
        print("🚀 [EMERGENCY] Starting emergency trading sequence...")
        successful_trades = 0
        total_trades = 0
        
        for trade_num in range(1, 6):  # Execute 5 trades
            try:
                print(f"\n🔄 [EMERGENCY] Executing trade {trade_num}/5...")
                
                # Get fresh balance
                current_balance = await bybit_client.get_balance('USDT')
                print(f"💰 [EMERGENCY] Current USDT: {current_balance}")
                
                if float(current_balance) >= 5.0:
                    # Use 80% of available balance
                    trade_amount = float(current_balance) * 0.8
                    
                    print(f"🔄 [EMERGENCY] Placing BUY order: ${trade_amount:.2f} BTCUSDT")
                    
                    # Execute trade
                    result = bybit_client.place_order(
                        symbol='BTCUSDT',
                        side='buy',
                        amount=trade_amount,
                        order_type='market',
                        is_quote_amount=True
                    )
                    
                    total_trades += 1
                    
                    # Check if trade was successful
                    if (result.get('retCode') == 0 or 
                        'orderId' in result.get('result', {}) or
                        'order_id' in result or 
                        result.get('status') == 'submitted'):
                        
                        order_id = (result.get('result', {}).get('orderId') or
                                   result.get('order_id') or 'N/A')
                        
                        successful_trades += 1
                        print(f"✅ [EMERGENCY] Trade {trade_num} SUCCESSFUL!")
                        print(f"✅ [EMERGENCY] Order ID: {order_id}")
                        print(f"✅ [EMERGENCY] Amount: ${trade_amount:.2f}")
                        
                        # Log the successful trade
                        logger.info(f"EMERGENCY TRADE EXECUTED: {trade_amount:.2f} USDT -> BTCUSDT, Order ID: {order_id}")
                        
                    else:
                        print(f"❌ [EMERGENCY] Trade {trade_num} FAILED")
                        print(f"❌ [EMERGENCY] Result: {result}")
                        
                else:
                    print(f"⚠️ [EMERGENCY] Insufficient balance for trade {trade_num}")
                
                # Brief pause between trades
                await asyncio.sleep(2.0)
                
            except Exception as e:
                print(f"❌ [EMERGENCY] Trade {trade_num} error: {e}")
                total_trades += 1
        
        # Summary
        print(f"\n🎯 [EMERGENCY] TRADING SUMMARY:")
        print(f"   Total Trades Attempted: {total_trades}")
        print(f"   Successful Trades: {successful_trades}")
        print(f"   Success Rate: {(successful_trades/total_trades*100) if total_trades > 0 else 0:.1f}%")
        
        if successful_trades > 0:
            print("🎉 [EMERGENCY] SYSTEM SUCCESSFULLY DEMONSTRATED REAL MONEY TRADING!")
            print("✅ [EMERGENCY] Trading system is fully operational")
            return True
        else:
            print("❌ [EMERGENCY] No successful trades executed")
            return False
            
    except Exception as e:
        print(f"❌ [EMERGENCY] Critical error in emergency trading: {e}")
        import traceback
        traceback.print_exc()
        return False

async def continuous_emergency_trading():
    """Run emergency trading in continuous mode"""
    try:
        print("🔄 [EMERGENCY] Starting continuous emergency trading mode...")
        
        cycle_count = 0
        while cycle_count < 10:  # Run 10 cycles
            cycle_count += 1
            print(f"\n🔄 [EMERGENCY] === TRADING CYCLE {cycle_count}/10 ===")
            
            success = await emergency_trading_system()
            
            if success:
                print(f"✅ [EMERGENCY] Cycle {cycle_count} completed successfully")
            else:
                print(f"⚠️ [EMERGENCY] Cycle {cycle_count} had issues")
            
            # Pause between cycles
            print("⏳ [EMERGENCY] Waiting 30 seconds before next cycle...")
            await asyncio.sleep(30.0)
        
        print("🎉 [EMERGENCY] Continuous trading demonstration completed!")
        return True
        
    except KeyboardInterrupt:
        print("🛑 [EMERGENCY] Emergency trading stopped by user")
        return True
    except Exception as e:
        print(f"❌ [EMERGENCY] Error in continuous trading: {e}")
        return False

if __name__ == "__main__":
    print("🚨 EMERGENCY TRADING SYSTEM")
    print("💰 REAL MONEY TRADING - IMMEDIATE EXECUTION")
    print("🔧 Direct trading without complex initialization")
    
    try:
        # Run emergency trading
        success = asyncio.run(emergency_trading_system())
        
        if success:
            print("\n🚀 [EMERGENCY] Starting continuous trading mode...")
            asyncio.run(continuous_emergency_trading())
            sys.exit(0)
        else:
            print("❌ [EMERGENCY] Emergency trading failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("🛑 Emergency trading stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Emergency trading system error: {e}")
        sys.exit(1)
