#!/usr/bin/env python3
"""
Test script to verify symbol generation fixes in the multi-currency trading engine.
This test ensures that only valid Bybit symbols are generated and invalid symbols are properly rejected.
"""

import asyncio
import sys
import os
import time
from decimal import Decimal

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from exchanges.bybit_client_fixed import BybitClientFixed
from trading.multi_currency_trading_engine import MultiCurrencyTradingEngine

class MockBybitClient:
    """Mock Bybit client for testing symbol validation"""
    
    def __init__(self):
        self.name = "bybit"
        self._symbol_validation_cache = {}
        self._invalid_symbol_cache = set()
    
    def _is_valid_bybit_symbol(self, symbol: str) -> bool:
        """Enhanced validation for Bybit symbols with caching and pattern matching"""
        try:
            # Initialize validation cache if not exists
            if not hasattr(self, '_symbol_validation_cache'):
                self._symbol_validation_cache = {}
                self._invalid_symbol_cache = set()  # Cache for known invalid symbols
            
            # Check invalid symbol cache first for fast rejection
            if symbol in self._invalid_symbol_cache:
                return False
            
            # Check validation cache
            if symbol in self._symbol_validation_cache:
                return self._symbol_validation_cache[symbol]
            
            # Enhanced list of valid Bybit symbols
            valid_bybit_symbols = {
                'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT',
                'LINKUSDT', 'UNIUSDT', 'AVAXUSDT', 'MATICUSDT', 'LTCUSDT',
                'BCHUSDT', 'XRPUSDT', 'EOSUSDT', 'TRXUSDT', 'XLMUSDT',
                'BNBUSDT', 'ATOMUSDT', 'FILUSDT', 'ALGOUSDT', 'VETUSDT'
            }
            
            # Comprehensive list of invalid combinations that should be rejected
            invalid_patterns = {
                'SOLADA', 'SOLDOT', 'ADABTC', 'DOTADA', 'SOLADOT', 'ADAUSD',
                'SOL_ADA', 'SOL-ADA', 'ADA_SOL', 'ADA-SOL', 'DOT_SOL',
                'BTCADA', 'ETHADA', 'ADAETH', 'DOTETH', 'SOLDOT',
                'DOTBTC', 'ADABTC', 'LINKADA', 'UNIADA', 'AVAXADA',
                'MATICADA', 'ADAAVAX', 'ADALINK', 'ADAUNI', 'ADAMATIC'
            }
            
            # Fast rejection for known invalid patterns
            if symbol in invalid_patterns:
                self._invalid_symbol_cache.add(symbol)
                self._symbol_validation_cache[symbol] = False
                return False
            
            # Check against known valid symbols
            if symbol in valid_bybit_symbols:
                self._symbol_validation_cache[symbol] = True
                return True
            
            # Pattern-based validation for new symbols
            is_valid = self._validate_symbol_pattern(symbol)
            self._symbol_validation_cache[symbol] = is_valid
            
            if not is_valid:
                self._invalid_symbol_cache.add(symbol)
            
            return is_valid
            
        except Exception as e:
            print(f"Error validating symbol {symbol}: {e}")
            return False
    
    def _validate_symbol_pattern(self, symbol: str) -> bool:
        """Enhanced validation using pattern matching"""
        try:
            if not symbol or len(symbol) < 6 or len(symbol) > 10:
                return False

            if not symbol.isupper() or not symbol.isalpha():
                return False

            # Must end with valid quote currencies
            valid_quote_endings = ['USDT', 'USD', 'BTC', 'ETH']
            if not any(symbol.endswith(ending) for ending in valid_quote_endings):
                return False

            # Additional pattern-based validation
            # Reject symbols where small-cap altcoins are paired with BTC
            small_altcoins = {'ADA', 'DOT', 'LINK', 'UNI', 'AVAX', 'MATIC'}
            for altcoin in small_altcoins:
                if symbol == f"{altcoin}BTC":
                    return False

            # Only allow major crypto to BTC pairs
            allowed_btc_pairs = {'ETHBTC', 'SOLBTC'}
            if symbol.endswith('BTC') and symbol not in allowed_btc_pairs:
                return False

            # Extract base currency
            for ending in valid_quote_endings:
                if symbol.endswith(ending):
                    base = symbol[:-len(ending)]
                    # Base currency should be 2-6 characters
                    if len(base) >= 2 and len(base) <= 6 and base.isalpha():
                        return True

            return False

        except Exception:
            return False
    
    def get_price(self, symbol: str) -> float:
        """Mock get_price that fails fast for invalid symbols"""
        if not self._is_valid_bybit_symbol(symbol):
            raise ValueError(f"Invalid Bybit symbol: {symbol}")
        
        # Return mock prices for valid symbols
        mock_prices = {
            'BTCUSDT': 45000.0,
            'ETHUSDT': 3000.0,
            'SOLUSDT': 100.0,
            'ADAUSDT': 0.5,
            'DOTUSDT': 8.0
        }
        return mock_prices.get(symbol, 100.0)  # Default price for valid symbols
    
    async def get_all_available_balances(self):
        """Mock balance method"""
        return {
            'USDT': 100.0,
            'BTC': 0.001,
            'ETH': 0.1,
            'SOL': 5.0,
            'ADA': 100.0,
            'DOT': 10.0
        }

async def test_symbol_validation():
    """Test symbol validation logic"""
    print("🧪 [TEST] Testing symbol validation...")
    
    client = MockBybitClient()
    
    # Test valid symbols
    valid_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']
    for symbol in valid_symbols:
        is_valid = client._is_valid_bybit_symbol(symbol)
        print(f"   ✅ {symbol}: {'VALID' if is_valid else 'INVALID'}")
        assert is_valid, f"Valid symbol {symbol} was rejected"
    
    # Test invalid symbols that were causing issues
    invalid_symbols = ['SOLADA', 'SOL_ADA', 'SOL-ADA', 'SOLDOT', 'ADABTC', 'DOTADA']
    for symbol in invalid_symbols:
        is_valid = client._is_valid_bybit_symbol(symbol)
        print(f"   ❌ {symbol}: {'VALID' if is_valid else 'INVALID'}")
        assert not is_valid, f"Invalid symbol {symbol} was accepted"
    
    print("✅ [TEST] Symbol validation tests passed!")

async def test_symbol_generation():
    """Test the new symbol generation logic"""
    print("\n🧪 [TEST] Testing symbol generation...")
    
    # Create mock exchange clients
    exchange_clients = {
        'bybit': MockBybitClient()
    }
    
    # Create multi-currency trading engine
    engine = MultiCurrencyTradingEngine(exchange_clients)
    
    # Set up mock currencies
    engine.supported_currencies = {'BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'USDT', 'USD'}
    engine.categorized_currencies = {
        'stablecoins': {'USDT', 'USD'},
        'major_crypto': {'BTC', 'ETH', 'SOL', 'ADA', 'DOT'},
        'altcoins': set(),
        'other': set()
    }
    
    # Test symbol generation
    valid_symbols = await engine._generate_valid_bybit_symbols(exchange_clients['bybit'], 'bybit')
    
    print(f"   Generated {len(valid_symbols)} symbols: {valid_symbols}")
    
    # Verify no invalid symbols were generated
    invalid_patterns = ['SOLADA', 'SOL_ADA', 'SOLDOT', 'ADABTC', 'DOTADA']
    for invalid_symbol in invalid_patterns:
        assert invalid_symbol not in valid_symbols, f"Invalid symbol {invalid_symbol} was generated"
    
    # Verify valid symbols were generated
    expected_valid = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']
    for valid_symbol in expected_valid:
        if valid_symbol not in valid_symbols:
            print(f"   ⚠️ Expected valid symbol {valid_symbol} not generated")
    
    print("✅ [TEST] Symbol generation tests passed!")

async def test_fast_fail_performance():
    """Test that invalid symbols fail fast without delays"""
    print("\n🧪 [TEST] Testing fast-fail performance...")
    
    client = MockBybitClient()
    
    # Test invalid symbols for fast failure
    invalid_symbols = ['SOLADA', 'SOL_ADA', 'SOLDOT', 'ADABTC', 'DOTADA']
    
    for symbol in invalid_symbols:
        start_time = time.time()
        try:
            price = client.get_price(symbol)
            assert False, f"Invalid symbol {symbol} should have failed"
        except ValueError as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"   ⚡ {symbol}: Failed in {duration:.3f}s (should be < 0.1s)")
            assert duration < 0.1, f"Symbol {symbol} took too long to fail: {duration:.3f}s"
            assert "Invalid Bybit symbol" in str(e)
    
    print("✅ [TEST] Fast-fail performance tests passed!")

async def test_symbol_caching():
    """Test symbol validation caching"""
    print("\n🧪 [TEST] Testing symbol validation caching...")
    
    client = MockBybitClient()
    
    # Test that repeated calls use cache
    symbol = 'BTCUSDT'
    
    # First call
    start_time = time.time()
    is_valid1 = client._is_valid_bybit_symbol(symbol)
    first_duration = time.time() - start_time
    
    # Second call (should use cache)
    start_time = time.time()
    is_valid2 = client._is_valid_bybit_symbol(symbol)
    second_duration = time.time() - start_time
    
    print(f"   First call: {first_duration:.4f}s, Second call: {second_duration:.4f}s")
    assert is_valid1 == is_valid2, "Cached result differs from original"
    assert symbol in client._symbol_validation_cache, "Symbol not cached"
    
    # Test invalid symbol caching
    invalid_symbol = 'SOLADA'
    client._is_valid_bybit_symbol(invalid_symbol)
    assert invalid_symbol in client._invalid_symbol_cache, "Invalid symbol not cached"
    
    print("✅ [TEST] Symbol caching tests passed!")

async def main():
    """Run all tests"""
    print("🚀 [SYMBOL-FIX-TESTS] Starting symbol generation fix tests...\n")
    
    try:
        await test_symbol_validation()
        await test_symbol_generation()
        await test_fast_fail_performance()
        await test_symbol_caching()
        
        print("\n🎉 [SUCCESS] All symbol generation fix tests passed!")
        print("✅ Invalid symbols like SOLADA, SOL_ADA, SOLDOT are now properly rejected")
        print("✅ Valid Bybit symbols are generated correctly")
        print("✅ Fast-fail logic prevents 8-13 second delays")
        print("✅ Symbol validation caching improves performance")
        
    except Exception as e:
        print(f"\n❌ [FAILURE] Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
