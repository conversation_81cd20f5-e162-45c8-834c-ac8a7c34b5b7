#!/usr/bin/env python3
"""
Enhanced Coinbase API Authentication Test
Tests multiple signature algorithms, retry logic, and comprehensive error handling
"""

import os
import sys
import time
import requests
import jwt
from dotenv import load_dotenv
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ec, ed25519

# Load environment
load_dotenv()

def detect_signature_algorithm(private_key_pem):
    """Detect the correct signature algorithm from private key"""
    try:
        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )
        
        if isinstance(private_key, ec.EllipticCurvePrivateKey):
            return 'ES256', private_key
        elif isinstance(private_key, ed25519.Ed25519PrivateKey):
            return 'EdDSA', private_key
        else:
            print(f"🔍 Unknown key type: {type(private_key)}")
            return 'ES256', private_key  # Default fallback
            
    except Exception as e:
        print(f"❌ Key detection failed: {e}")
        return 'ES256', None

def create_enhanced_jwt(api_key_name, private_key_obj, algorithm, endpoint, method='GET'):
    """Create JWT with enhanced payload and proper algorithm"""
    try:
        key_id = api_key_name.split('/apiKeys/')[1] if '/apiKeys/' in api_key_name else api_key_name
        
        now = int(time.time())
        
        # Enhanced JWT payload with all required fields
        payload = {
            'iss': 'cdp',
            'nbf': now,
            'exp': now + 120,
            'sub': api_key_name,
            'uri': f'{method} {endpoint}',
            'aud': ['retail_rest_api_proxy']  # Add audience
        }

        # Create JWT with detected algorithm
        token = jwt.encode(
            payload, 
            private_key_obj, 
            algorithm=algorithm, 
            headers={
                'kid': key_id,
                'typ': 'JWT',
                'alg': algorithm
            }
        )
        
        return token
        
    except Exception as e:
        print(f"❌ JWT creation failed: {e}")
        return None

def make_authenticated_request(token, endpoint, method='GET', retry_count=3):
    """Make authenticated request with retry logic and comprehensive error handling"""
    
    for attempt in range(retry_count):
        try:
            # Enhanced headers
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json',
                'User-Agent': 'AutoGPT-Trader/1.0',
                'Accept': 'application/json'
            }
            
            # Make the request with timeout
            base_url = "https://api.coinbase.com"
            full_url = f"{base_url}{endpoint}"
            
            if method.upper() == 'GET':
                response = requests.get(full_url, headers=headers, timeout=30)
            else:
                response = requests.request(method, full_url, headers=headers, timeout=30)
            
            print(f"🔍 {method} {endpoint} -> {response.status_code}")
            
            if response.status_code == 200:
                return True, response.json()
            elif response.status_code == 401:
                print(f"⚠️ 401 Unauthorized (attempt {attempt + 1}/{retry_count})")
                print(f"   Response: {response.text[:200]}")
                if attempt < retry_count - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                    continue
                else:
                    return False, {"error": f"401 Unauthorized after {retry_count} attempts"}
            elif response.status_code == 403:
                return False, {"error": "403 Forbidden - Check API permissions"}
            elif response.status_code == 429:
                print(f"⚠️ Rate limited (attempt {attempt + 1}/{retry_count})")
                if attempt < retry_count - 1:
                    time.sleep(5)  # Wait longer for rate limits
                    continue
                else:
                    return False, {"error": "Rate limited"}
            else:
                return False, {"error": f"HTTP {response.status_code}: {response.text[:200]}"}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed (attempt {attempt + 1}/{retry_count}): {e}")
            if attempt < retry_count - 1:
                time.sleep(2 ** attempt)
                continue
            else:
                return False, {"error": f"Request failed: {e}"}
        except Exception as e:
            print(f"❌ Unexpected error (attempt {attempt + 1}/{retry_count}): {e}")
            if attempt < retry_count - 1:
                time.sleep(2 ** attempt)
                continue
            else:
                return False, {"error": f"Unexpected error: {e}"}
    
    return False, {"error": "All retry attempts failed"}

def test_enhanced_authentication():
    """Test enhanced Coinbase authentication with multiple algorithms and endpoints"""
    print("=== ENHANCED COINBASE API AUTHENTICATION TEST ===\n")
    
    # Get credentials
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        if not encrypted_api_key or not encrypted_private_key:
            print("❌ Missing encrypted credentials")
            return False
        
        api_key_name = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)
        
        print(f"✅ API Key: {api_key_name[:50]}...")
        print(f"✅ Private Key: {len(private_key_pem)} characters")
        
    except Exception as e:
        print(f"❌ Credential decryption failed: {e}")
        return False
    
    # Detect signature algorithm
    print("\n1. SIGNATURE ALGORITHM DETECTION:")
    algorithm, private_key_obj = detect_signature_algorithm(private_key_pem)
    if not private_key_obj:
        print("❌ Failed to load private key")
        return False
    
    print(f"✅ Detected algorithm: {algorithm}")
    print(f"✅ Key type: {type(private_key_obj).__name__}")
    
    # Test multiple endpoints with enhanced authentication
    print("\n2. ENDPOINT TESTING WITH ENHANCED AUTH:")
    
    endpoints_to_test = [
        ('Public Products', 'GET', '/api/v3/brokerage/products'),
        ('Account Info', 'GET', '/api/v3/brokerage/accounts'),
        ('Portfolio Info', 'GET', '/api/v1/portfolios'),
        ('User Info', 'GET', '/api/v3/brokerage/user'),
        ('Time Info', 'GET', '/api/v3/brokerage/time')
    ]
    
    successful_endpoints = []
    failed_endpoints = []
    
    for name, method, endpoint in endpoints_to_test:
        print(f"\n   Testing {name} ({endpoint})...")
        
        # Create fresh JWT for each request
        token = create_enhanced_jwt(api_key_name, private_key_obj, algorithm, endpoint, method)
        if not token:
            print(f"      ❌ JWT creation failed")
            failed_endpoints.append(name)
            continue
        
        # Make authenticated request with retry logic
        success, result = make_authenticated_request(token, endpoint, method)
        
        if success:
            print(f"      ✅ SUCCESS - {name} works!")
            successful_endpoints.append(name)
            
            # Show some data for successful calls
            if isinstance(result, dict):
                if 'accounts' in result:
                    print(f"         Found {len(result['accounts'])} accounts")
                elif 'products' in result:
                    print(f"         Found {len(result['products'])} products")
                elif 'portfolios' in result:
                    print(f"         Found {len(result['portfolios'])} portfolios")
        else:
            print(f"      ❌ FAILED - {result.get('error', 'Unknown error')}")
            failed_endpoints.append(name)
    
    # Summary
    print(f"\n3. SUMMARY:")
    print(f"   ✅ Successful endpoints: {len(successful_endpoints)}")
    print(f"   ❌ Failed endpoints: {len(failed_endpoints)}")
    
    if successful_endpoints:
        print(f"   Working: {', '.join(successful_endpoints)}")
    
    if failed_endpoints:
        print(f"   Failed: {', '.join(failed_endpoints)}")
    
    # Recommendations
    print(f"\n4. RECOMMENDATIONS:")
    if len(successful_endpoints) > 0:
        print("   🎉 AUTHENTICATION IS WORKING!")
        print("   ✅ Your API key and JWT implementation are correct")
        print("   🔧 Update main.py to use the enhanced authentication method")
    else:
        print("   ❌ All endpoints failed - this indicates:")
        print("   1. Account verification required")
        print("   2. API permissions insufficient")
        print("   3. Geographic/IP restrictions")
        print("   4. Account compliance hold")
        print("   📞 Contact Coinbase Support with your Organization ID")
    
    return len(successful_endpoints) > 0

if __name__ == "__main__":
    success = test_enhanced_authentication()
    sys.exit(0 if success else 1)
