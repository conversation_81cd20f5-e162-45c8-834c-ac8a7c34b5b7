"""
Intelligent Currency Switching System for Multi-Currency Trading

This module implements advanced currency switching logic that automatically
switches between BUY and SELL orders based on available balances across
multiple currencies, preventing trading halts due to insufficient balance
in any single currency.
"""

import asyncio
import logging
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import time

logger = logging.getLogger(__name__)

@dataclass
class CurrencyBalance:
    """Represents a currency balance with metadata"""
    currency: str
    balance: Decimal
    usd_value: Decimal
    is_tradeable: bool
    min_order_value: Decimal
    exchange: str

@dataclass
class TradingDecision:
    """Represents a trading decision with currency switching logic"""
    action: str  # 'buy', 'sell', 'hold', 'switch_currency'
    currency: str
    amount: Decimal
    symbol: str
    reason: str
    confidence: float
    alternative_currencies: List[str]
    estimated_profit: Decimal

class IntelligentCurrencySwitcher:
    """
    Advanced currency switching system that enables continuous trading
    by automatically switching between currencies when balances are insufficient
    """
    
    def __init__(self, exchange_client, min_thresholds: Dict[str, float] = None):
        self.exchange_client = exchange_client
        self.min_thresholds = min_thresholds or {
            'USDT': 5.0,    # Bybit minimum
            'USD': 1.0,     # Coinbase minimum
            'BTC': 0.0001,  # Minimum BTC amount
            'ETH': 0.001,   # Minimum ETH amount
            'SOL': 0.01,    # Minimum SOL amount
        }
        # CRITICAL FIX: Add missing min_usdt_threshold attribute
        self.min_usdt_threshold = 10.0  # $10 USDT threshold for switching to SELL mode
        self.major_currencies = ['USDT', 'USD', 'BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX', 'MATIC']
        self.last_balance_check = 0
        self.balance_cache = {}
        self.cache_duration = 30  # 30 seconds cache
        
    async def get_all_tradeable_balances(self) -> List[CurrencyBalance]:
        """Get all tradeable balances across all supported currencies"""
        try:
            # Check cache first
            current_time = time.time()
            if current_time - self.last_balance_check < self.cache_duration and self.balance_cache:
                logger.debug("🔄 [CURRENCY-SWITCH] Using cached balance data")
                return self.balance_cache.get('balances', [])
            
            logger.info("🔍 [CURRENCY-SWITCH] Scanning all tradeable balances...")
            tradeable_balances = []
            
            # Get all available balances from exchange
            all_balances = await self.exchange_client.get_all_available_balances()
            
            for currency, balance in all_balances.items():
                if balance > 0:
                    balance_decimal = Decimal(str(balance))
                    
                    # Get USD value for this currency
                    usd_value = await self._get_usd_value(currency, balance_decimal)
                    
                    # Check if this balance is tradeable
                    min_threshold = self.min_thresholds.get(currency, 1.0)
                    is_tradeable = usd_value >= min_threshold
                    
                    currency_balance = CurrencyBalance(
                        currency=currency,
                        balance=balance_decimal,
                        usd_value=usd_value,
                        is_tradeable=is_tradeable,
                        min_order_value=Decimal(str(min_threshold)),
                        exchange=self.exchange_client.name
                    )
                    
                    tradeable_balances.append(currency_balance)
                    
                    if is_tradeable:
                        logger.info(f"✅ [TRADEABLE] {currency}: {balance:.6f} = ${usd_value:.2f}")
                    else:
                        logger.debug(f"⚠️ [TOO-SMALL] {currency}: {balance:.6f} = ${usd_value:.2f} < ${min_threshold:.2f}")
            
            # Cache the results
            self.balance_cache = {
                'balances': tradeable_balances,
                'timestamp': current_time
            }
            self.last_balance_check = current_time
            
            logger.info(f"🔍 [CURRENCY-SWITCH] Found {len([b for b in tradeable_balances if b.is_tradeable])} tradeable currencies")
            return tradeable_balances
            
        except Exception as e:
            logger.error(f"❌ [CURRENCY-SWITCH] Error getting tradeable balances: {e}")
            return []
    
    async def make_intelligent_trading_decision(self, symbol: str, intended_side: str, 
                                              intended_amount: float, current_price: float) -> TradingDecision:
        """
        Make intelligent trading decision with automatic currency switching
        """
        try:
            logger.info(f"🧠 [INTELLIGENT-DECISION] Analyzing {intended_side} {symbol} for ${intended_amount:.2f}")
            
            # Get all tradeable balances
            tradeable_balances = await self.get_all_tradeable_balances()
            
            # Extract base and quote currencies from symbol
            base_currency, quote_currency = self._extract_currencies_from_symbol(symbol)
            
            if intended_side.lower() == 'buy':
                return await self._decide_buy_strategy(symbol, intended_amount, current_price, 
                                                     quote_currency, tradeable_balances)
            else:
                return await self._decide_sell_strategy(symbol, intended_amount, current_price, 
                                                      base_currency, tradeable_balances)
                
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT-DECISION] Error making trading decision: {e}")
            return TradingDecision(
                action='hold',
                currency='USDT',
                amount=Decimal('0'),
                symbol=symbol,
                reason=f"Error in decision making: {str(e)}",
                confidence=0.0,
                alternative_currencies=[],
                estimated_profit=Decimal('0')
            )
    
    async def _decide_buy_strategy(self, symbol: str, intended_amount: float, current_price: float,
                                 quote_currency: str, tradeable_balances: List[CurrencyBalance]) -> TradingDecision:
        """Decide BUY strategy with intelligent currency switching"""
        try:
            # Find quote currency balance (usually USDT for BUY orders)
            quote_balance = None
            for balance in tradeable_balances:
                if balance.currency == quote_currency and balance.is_tradeable:
                    quote_balance = balance
                    break
            
            if quote_balance and quote_balance.usd_value >= intended_amount:
                # Direct BUY order possible
                buy_amount = min(intended_amount, float(quote_balance.usd_value * Decimal('0.85')))
                
                return TradingDecision(
                    action='buy',
                    currency=quote_currency,
                    amount=Decimal(str(buy_amount)),
                    symbol=symbol,
                    reason=f"Direct BUY with {quote_currency}: ${quote_balance.usd_value:.2f} available",
                    confidence=0.9,
                    alternative_currencies=[],
                    estimated_profit=Decimal('0')
                )
            
            # Quote currency insufficient - look for alternatives
            logger.info(f"🔄 [BUY-SWITCH] {quote_currency} insufficient, looking for alternatives...")
            
            # Strategy 1: Sell other crypto to get quote currency
            for balance in tradeable_balances:
                if balance.currency != quote_currency and balance.is_tradeable and balance.currency in self.major_currencies:
                    if balance.usd_value >= intended_amount * 1.1:  # 10% buffer
                        sell_amount = float(balance.balance * Decimal('0.85'))  # Use 85% of holdings
                        
                        return TradingDecision(
                            action='sell',
                            currency=balance.currency,
                            amount=Decimal(str(sell_amount)),
                            symbol=f"{balance.currency}{quote_currency}",
                            reason=f"Sell {balance.currency} to get {quote_currency} for BUY order",
                            confidence=0.8,
                            alternative_currencies=[quote_currency],
                            estimated_profit=Decimal('0')
                        )
            
            # Strategy 2: Cross-currency trading
            base_currency = symbol.replace(quote_currency, '').replace('USDT', '').replace('USD', '')
            for balance in tradeable_balances:
                if balance.currency != quote_currency and balance.currency != base_currency and balance.is_tradeable:
                    # Try direct pair trading (e.g., BTC/ETH instead of BTC/USDT)
                    cross_symbol = f"{base_currency}{balance.currency}"
                    
                    return TradingDecision(
                        action='buy',
                        currency=balance.currency,
                        amount=balance.balance * Decimal('0.85'),
                        symbol=cross_symbol,
                        reason=f"Cross-currency BUY: {base_currency} with {balance.currency}",
                        confidence=0.7,
                        alternative_currencies=[quote_currency],
                        estimated_profit=Decimal('0')
                    )
            
            return TradingDecision(
                action='hold',
                currency=quote_currency,
                amount=Decimal('0'),
                symbol=symbol,
                reason="No sufficient balance found for BUY order",
                confidence=0.0,
                alternative_currencies=[],
                estimated_profit=Decimal('0')
            )
            
        except Exception as e:
            logger.error(f"❌ [BUY-STRATEGY] Error deciding buy strategy: {e}")
            return TradingDecision(
                action='hold',
                currency='USDT',
                amount=Decimal('0'),
                symbol=symbol,
                reason=f"Error in buy strategy: {str(e)}",
                confidence=0.0,
                alternative_currencies=[],
                estimated_profit=Decimal('0')
            )
    
    async def _decide_sell_strategy(self, symbol: str, intended_amount: float, current_price: float,
                                  base_currency: str, tradeable_balances: List[CurrencyBalance]) -> TradingDecision:
        """Decide SELL strategy with intelligent currency switching"""
        try:
            # Find base currency balance for SELL orders
            base_balance = None
            for balance in tradeable_balances:
                if balance.currency == base_currency and balance.is_tradeable:
                    base_balance = balance
                    break
            
            if base_balance:
                # Direct SELL order possible
                sell_amount = min(intended_amount, float(base_balance.balance * Decimal('0.85')))
                
                return TradingDecision(
                    action='sell',
                    currency=base_currency,
                    amount=Decimal(str(sell_amount)),
                    symbol=symbol,
                    reason=f"Direct SELL with {base_currency}: {base_balance.balance:.6f} available",
                    confidence=0.9,
                    alternative_currencies=[],
                    estimated_profit=Decimal('0')
                )
            
            # Base currency insufficient - look for alternatives
            logger.info(f"🔄 [SELL-SWITCH] {base_currency} insufficient, looking for alternatives...")
            
            # Find other crypto holdings to sell instead
            for balance in tradeable_balances:
                if balance.currency != base_currency and balance.is_tradeable and balance.currency in self.major_currencies:
                    sell_amount = float(balance.balance * Decimal('0.85'))
                    alt_symbol = f"{balance.currency}USDT"
                    
                    return TradingDecision(
                        action='sell',
                        currency=balance.currency,
                        amount=Decimal(str(sell_amount)),
                        symbol=alt_symbol,
                        reason=f"Alternative SELL: {balance.currency} instead of {base_currency}",
                        confidence=0.8,
                        alternative_currencies=[base_currency],
                        estimated_profit=Decimal('0')
                    )
            
            return TradingDecision(
                action='hold',
                currency=base_currency,
                amount=Decimal('0'),
                symbol=symbol,
                reason="No sufficient crypto holdings found for SELL order",
                confidence=0.0,
                alternative_currencies=[],
                estimated_profit=Decimal('0')
            )
            
        except Exception as e:
            logger.error(f"❌ [SELL-STRATEGY] Error deciding sell strategy: {e}")
            return TradingDecision(
                action='hold',
                currency='USDT',
                amount=Decimal('0'),
                symbol=symbol,
                reason=f"Error in sell strategy: {str(e)}",
                confidence=0.0,
                alternative_currencies=[],
                estimated_profit=Decimal('0')
            )
    
    async def _get_usd_value(self, currency: str, amount: Decimal) -> Decimal:
        """Get USD value of a currency amount"""
        try:
            if currency in ['USD', 'USDT', 'USDC']:
                return amount
            
            # Get price from exchange
            symbol = f"{currency}USDT"
            price = await self._get_price_safely(symbol)
            
            if price > 0:
                return amount * Decimal(str(price))
            else:
                logger.warning(f"⚠️ [USD-VALUE] Could not get price for {currency}")
                return Decimal('0')
                
        except Exception as e:
            logger.error(f"❌ [USD-VALUE] Error getting USD value for {currency}: {e}")
            return Decimal('0')
    
    async def _get_price_safely(self, symbol: str) -> float:
        """Safely get price for a symbol"""
        try:
            if hasattr(self.exchange_client, 'get_price'):
                price = self.exchange_client.get_price(symbol)
                return float(price) if price else 0.0
            else:
                logger.warning(f"⚠️ [PRICE] Exchange client doesn't have get_price method")
                return 0.0
        except Exception as e:
            logger.debug(f"🔍 [PRICE] Error getting price for {symbol}: {e}")
            return 0.0
    
    def _extract_currencies_from_symbol(self, symbol: str) -> Tuple[str, str]:
        """Extract base and quote currencies from trading symbol"""
        # Common quote currencies in order of priority
        quote_currencies = ['USDT', 'USD', 'USDC', 'EUR', 'BTC', 'ETH']

        for quote in quote_currencies:
            if symbol.endswith(quote):
                base = symbol[:-len(quote)]
                return base, quote

        # Fallback - assume last 3-4 characters are quote
        if len(symbol) > 6:
            return symbol[:-4], symbol[-4:]
        else:
            return symbol[:-3], symbol[-3:]

    async def should_switch_to_sell_mode(self, exchange_name: str = None) -> Dict[str, Any]:
        """Determine if system should switch to SELL mode due to low USDT"""
        try:
            # Get current USDT balance
            usdt_balance = await self._get_balance_for_currency('USDT')

            # Check if below minimum threshold
            if usdt_balance < self.min_usdt_threshold:
                logger.warning(f"🔄 [CURRENCY-SWITCH] USDT balance ${usdt_balance:.2f} below threshold ${self.min_usdt_threshold:.2f}")

                # Find best crypto to sell
                best_crypto = await self._find_largest_crypto_holding()

                if best_crypto:
                    return {
                        "should_switch": True,
                        "reason": f"USDT balance ${usdt_balance:.2f} below ${self.min_usdt_threshold:.2f}",
                        "recommended_action": "sell",
                        "recommended_currency": best_crypto['currency'],
                        "available_amount": best_crypto['amount'],
                        "estimated_usdt_value": best_crypto['usdt_value'],
                        "trading_mode": "SELL"
                    }
                else:
                    return {
                        "should_switch": False,
                        "reason": "No suitable crypto holdings found for selling",
                        "usdt_balance": usdt_balance,
                        "trading_mode": "INSUFFICIENT_FUNDS"
                    }
            else:
                return {
                    "should_switch": False,
                    "reason": f"USDT balance ${usdt_balance:.2f} above threshold",
                    "usdt_balance": usdt_balance,
                    "trading_mode": "BUY"
                }

        except Exception as e:
            logger.error(f"❌ [CURRENCY-SWITCH] Error checking sell mode: {e}")
            return {"should_switch": False, "error": str(e), "trading_mode": "ERROR"}

    async def maintain_continuous_trading_cycle(self) -> Dict[str, Any]:
        """Maintain continuous trading cycle between BUY and SELL modes"""
        try:
            logger.info("🔄 [TRADING-CYCLE] Maintaining continuous trading cycle...")

            # Check current trading mode
            mode_check = await self.should_switch_to_sell_mode()
            current_mode = mode_check.get("trading_mode", "BUY")

            logger.info(f"🔄 [TRADING-CYCLE] Current mode: {current_mode}")

            if current_mode == "BUY":
                # BUY mode - look for crypto purchase opportunities
                buy_opportunities = await self._find_buy_opportunities()

                return {
                    "trading_mode": "BUY",
                    "action": "buy_crypto",
                    "opportunities": buy_opportunities,
                    "usdt_balance": mode_check.get("usdt_balance", 0),
                    "reason": "Sufficient USDT for buying crypto"
                }

            elif current_mode == "SELL":
                # SELL mode - execute crypto sales
                sell_opportunity = {
                    "currency": mode_check["recommended_currency"],
                    "amount": mode_check["available_amount"],
                    "estimated_value": mode_check["estimated_usdt_value"]
                }

                return {
                    "trading_mode": "SELL",
                    "action": "sell_crypto",
                    "opportunity": sell_opportunity,
                    "reason": mode_check["reason"]
                }

            elif current_mode == "INSUFFICIENT_FUNDS":
                # Try to find alternative trading opportunities
                alternatives = await self._find_alternative_trading_opportunities()

                return {
                    "trading_mode": "ALTERNATIVE",
                    "action": "alternative_trading",
                    "alternatives": alternatives,
                    "reason": "Insufficient USDT and no suitable crypto for selling"
                }

            else:
                return {
                    "trading_mode": "ERROR",
                    "action": "none",
                    "reason": "Error determining trading mode"
                }

        except Exception as e:
            logger.error(f"❌ [TRADING-CYCLE] Error maintaining trading cycle: {e}")
            return {
                "trading_mode": "ERROR",
                "action": "none",
                "error": str(e)
            }

    async def _find_largest_crypto_holding(self) -> Optional[Dict[str, Any]]:
        """Find the largest crypto holding for selling"""
        try:
            tradeable_balances = await self.get_all_tradeable_balances()

            largest_holding = None
            largest_value = Decimal('0')

            for balance in tradeable_balances:
                if (balance.currency not in ['USDT', 'USD', 'USDC'] and
                    balance.is_tradeable and
                    balance.usd_value > largest_value):
                    largest_value = balance.usd_value
                    largest_holding = {
                        'currency': balance.currency,
                        'amount': balance.balance,
                        'usdt_value': balance.usd_value
                    }

            if largest_holding and largest_value >= Decimal('5.0'):  # Minimum $5 value
                logger.info(f"🔍 [LARGEST-HOLDING] Found: {largest_holding['currency']} = ${largest_value:.2f}")
                return largest_holding
            else:
                logger.warning("⚠️ [LARGEST-HOLDING] No suitable crypto holdings found")
                return None

        except Exception as e:
            logger.error(f"❌ [LARGEST-HOLDING] Error finding largest holding: {e}")
            return None

    async def _find_buy_opportunities(self) -> List[Dict[str, Any]]:
        """Find cryptocurrency buying opportunities"""
        try:
            opportunities = []

            # Get available USDT balance
            usdt_balance = await self._get_balance_for_currency('USDT')

            if usdt_balance < self.min_usdt_threshold:
                return opportunities

            # Calculate usable amount (with buffer)
            usable_amount = usdt_balance * Decimal('0.85')  # Use 85% of available USDT

            # Find cryptocurrencies to buy
            target_currencies = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX']

            for currency in target_currencies:
                try:
                    symbol = f"{currency}USDT"

                    # Get current price
                    price = await self._get_price_safely(symbol)
                    if not price or price <= 0:
                        continue

                    # Calculate potential purchase amount
                    purchase_amount = usable_amount / len(target_currencies)  # Distribute evenly
                    crypto_amount = purchase_amount / Decimal(str(price))

                    if purchase_amount >= Decimal('5.0'):  # Minimum $5 order
                        opportunities.append({
                            "currency": currency,
                            "symbol": symbol,
                            "price": price,
                            "usdt_amount": float(purchase_amount),
                            "crypto_amount": float(crypto_amount),
                            "action": "buy"
                        })

                except Exception as e:
                    logger.debug(f"Error evaluating {currency}: {e}")
                    continue

            # Sort by potential value
            opportunities.sort(key=lambda x: x['usdt_amount'], reverse=True)

            logger.info(f"🔍 [BUY-OPPORTUNITIES] Found {len(opportunities)} buy opportunities")
            return opportunities[:5]  # Return top 5

        except Exception as e:
            logger.error(f"❌ [BUY-OPPORTUNITIES] Error finding opportunities: {e}")
            return []

    async def _find_alternative_trading_opportunities(self) -> List[Dict[str, Any]]:
        """Find alternative trading opportunities when both USDT and crypto are insufficient"""
        try:
            alternatives = []

            # Get all tradeable balances
            tradeable_balances = await self.get_all_tradeable_balances()

            # Look for cross-currency trading opportunities
            for balance in tradeable_balances:
                if (balance.currency not in ['USDT', 'USD'] and
                    balance.is_tradeable and
                    balance.usd_value >= Decimal('5.0')):  # Minimum $5 value

                    # Look for trading pairs with this currency
                    trading_pairs = await self._find_trading_pairs_for_currency(balance.currency)

                    if trading_pairs:
                        alternatives.append({
                            "base_currency": balance.currency,
                            "balance": float(balance.balance),
                            "usd_value": float(balance.usd_value),
                            "trading_pairs": trading_pairs,
                            "action": "cross_currency_trade"
                        })

            # Sort by USD value
            alternatives.sort(key=lambda x: x['usd_value'], reverse=True)

            logger.info(f"🔍 [ALTERNATIVES] Found {len(alternatives)} alternative opportunities")
            return alternatives[:3]  # Return top 3

        except Exception as e:
            logger.error(f"❌ [ALTERNATIVES] Error finding alternatives: {e}")
            return []

    async def _find_trading_pairs_for_currency(self, currency: str) -> List[str]:
        """Find available trading pairs for a specific currency"""
        try:
            pairs = []

            # Common quote currencies to pair with (USDT is primary on Bybit)
            quote_currencies = ['USDT', 'USD', 'BTC', 'ETH']

            for quote in quote_currencies:
                if quote == currency:
                    continue

                # Generate symbol in CORRECT order (base/quote)
                # For stablecoins as base, skip (e.g., don't create USDTBTC)
                if currency in ['USDT', 'USD', 'USDC'] and quote in ['BTC', 'ETH', 'SOL', 'ADA', 'DOT']:
                    continue  # Skip invalid combinations like USDTBTC

                # Primary format: currency as base, quote as quote (e.g., BTCUSDT)
                symbol = f"{currency}{quote}"

                try:
                    price = await self._get_price_safely(symbol)
                    if price and price > 0:
                        pairs.append(symbol)
                except Exception:
                    # Only try reverse if the primary format failed AND it makes sense
                    if quote not in ['USDT', 'USD', 'USDC']:  # Don't reverse with stablecoins
                        try:
                            reverse_symbol = f"{quote}{currency}"
                            price = await self._get_price_safely(reverse_symbol)
                            if price and price > 0:
                                pairs.append(reverse_symbol)
                        except Exception:
                            continue

            return pairs

        except Exception as e:
            logger.error(f"❌ [TRADING-PAIRS] Error finding pairs for {currency}: {e}")
            return []

    async def _get_balance_for_currency(self, currency: str) -> Decimal:
        """Get balance for a specific currency"""
        try:
            if hasattr(self.exchange_client, 'get_balance'):
                balance = await self.exchange_client.get_balance(currency)
                return Decimal(str(balance)) if balance else Decimal('0')
            else:
                # Fallback to get_all_available_balances
                if hasattr(self.exchange_client, 'get_all_available_balances'):
                    balances = await self.exchange_client.get_all_available_balances()
                    return Decimal(str(balances.get(currency, 0)))
                else:
                    logger.warning(f"⚠️ [BALANCE] No balance method available for {currency}")
                    return Decimal('0')
        except Exception as e:
            logger.error(f"❌ [BALANCE] Error getting balance for {currency}: {e}")
            return Decimal('0')
