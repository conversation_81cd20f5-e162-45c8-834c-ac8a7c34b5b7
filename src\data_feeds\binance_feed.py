from binance import AsyncClient, BinanceSocketManager
import numpy as np

class BinanceDataFeed:
    def __init__(self, api_key: str, api_secret: str):
        self.client = AsyncClient(api_key, api_secret)
        self.socket_manager = BinanceSocketManager(self.client)
        
    async def get_historical_data(self, symbol: str, interval: str, limit: int = 500):
        """Fetch historical candlestick data"""
        klines = await self.client.get_klines(
            symbol=symbol,
            interval=interval,
            limit=limit
        )
        return {
            "prices": np.array([float(k[4]) for k in klines]),  # Closing prices
            "volumes": np.array([float(k[5]) for k in klines])
        }

    async def live_data_stream(self, symbols: list, callback):
        """WebSocket stream for real-time data"""
        streams = [f"{symbol.lower()}@kline_1m" for symbol in symbols]
        async with self.socket_manager.multiplex_socket(streams) as ts:
            while True:
                res = await ts.recv()
                await callback(res)