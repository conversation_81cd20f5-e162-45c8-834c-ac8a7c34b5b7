"""
Live Trading Learning System
Learns from actual trading results and continuously improves decision making
Integrates with neural network components for advanced learning
"""

import asyncio
import logging
import json
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

# Neural network integration - PROFESSIONAL-GRADE COMPONENTS
try:
    from ..neural.hybrid_agent import HybridTradingAgent
    from ..neural.lstm_processor import LSTMProcessor
    from ..neural.reinforcement_learning import ReinforcementLearningAgent
    # Professional-grade neural components
    from ..neural.advanced_lstm_processor import AdvancedLSTMProcessor
    from ..neural.transformer_trading_model import TransformerTradingModel, TransformerConfig
    from ..neural.variational_autoencoder import MarketVAE, VAEConfig
    from ..neural.performance_optimizer import InferenceOptimizer, OptimizationConfig
    # Federated learning integration
    from .federated_learning_system import FederatedLearningParticipant
    from .adaptive_learning_framework import AdaptiveLearningFramework, LearningMetrics
    import torch
    NEURAL_AVAILABLE = True
    TORCH_AVAILABLE = True
    PROFESSIONAL_COMPONENTS_AVAILABLE = True
except ImportError as e:
    NEURAL_AVAILABLE = False
    TORCH_AVAILABLE = False
    PROFESSIONAL_COMPONENTS_AVAILABLE = False
    logger.warning(f"Neural components not available - using basic learning only: {e}")

@dataclass
class LiveTradeResult:
    """Data structure for live trading results"""
    trade_id: str
    timestamp: datetime
    symbol: str
    exchange: str
    action: str  # BUY/SELL
    amount: Decimal
    entry_price: Decimal
    exit_price: Optional[Decimal] = None
    exit_timestamp: Optional[datetime] = None
    pnl: Optional[Decimal] = None
    fees: Decimal = Decimal('0')
    strategy_name: str = ""
    confidence: float = 0.0
    market_conditions: Optional[Dict] = None
    reasoning: str = ""
    duration_minutes: Optional[int] = None
    success: Optional[bool] = None

    def __post_init__(self):
        if self.market_conditions is None:
            self.market_conditions = {}

@dataclass
class StrategyPerformance:
    """Strategy performance metrics"""
    strategy_name: str
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_pnl: Decimal = Decimal('0')
    win_rate: float = 0.0
    avg_win: Decimal = Decimal('0')
    avg_loss: Decimal = Decimal('0')
    profit_factor: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: Decimal = Decimal('0')
    avg_trade_duration: float = 0.0
    confidence_accuracy: float = 0.0
    last_updated: Optional[datetime] = None

class LiveTradingLearner:
    """
    Live Trading Learning System
    Learns from actual trading results to improve future decisions
    """
    
    def __init__(self, db_path: str = "data/live_trading_results.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Performance tracking
        self.strategy_performance = {}
        self.recent_trades = deque(maxlen=1000)
        self.learning_insights = {}

        # Enhanced learning parameters
        self.min_trades_for_learning = 5  # Faster learning with fewer trades
        self.learning_window_days = 30
        self.confidence_threshold = 0.6
        self.pattern_memory_size = 5000
        self.long_term_memory_days = 90
        self.real_time_learning_enabled = True
        self.adaptive_learning_rate = True

        # Real-time learning capabilities
        self.learning_queue = deque(maxlen=100)
        self.continuous_learning_active = False
        self.learning_session_metrics = {
            'trades_processed': 0,
            'patterns_learned': 0,
            'accuracy_improvements': 0,
            'last_learning_update': None
        }

        # Enhanced memory systems
        self.pattern_memory = None
        self.long_term_strategies = {}
        self.market_regime_memory = deque(maxlen=1000)
        self.strategy_adaptation_history = {}

        # Neural network integration for enhanced learning
        self.neural_learning_active = False
        self.neural_feedback_loop = {}
        self.strategy_optimization_queue = deque(maxlen=50)

        # Neural network integration - PROFESSIONAL-GRADE
        self.neural_agent = None
        self.rl_agent = None
        self.professional_components = {}
        self.federated_participant = None
        self.adaptive_learner = None

        if NEURAL_AVAILABLE:
            self._init_neural_components()
            self._init_enhanced_memory_systems()

        if PROFESSIONAL_COMPONENTS_AVAILABLE:
            self._init_professional_components()

        # Initialize database
        self._init_database()
        self._load_historical_performance()
        self._load_long_term_strategies()

        logger.info("Enhanced LiveTradingLearner initialized - learning from real trading results with memory")

    def _init_neural_components(self):
        """Initialize neural network components for advanced learning"""
        try:
            # Initialize reinforcement learning agent for strategy optimization
            self.rl_agent = ReinforcementLearningAgent(
                state_size=20,  # Market features + performance metrics
                action_size=5,  # Different strategy adjustments
                learning_rate=0.001
            )

            # Initialize hybrid trading agent for pattern recognition
            neural_config = {
                'learning_rate': 0.001,
                'batch_size': 32,
                'memory_size': 10000
            }
            self.neural_agent = HybridTradingAgent(neural_config)

            logger.info("Neural components initialized for live trading learning")

        except Exception as e:
            logger.error(f"Error initializing neural components: {e}")
            self.neural_agent = None
            self.rl_agent = None

    def _init_enhanced_memory_systems(self):
        """Initialize enhanced memory systems for pattern recognition and long-term learning"""
        try:
            # Initialize pattern memory using SOMemory
            from ..neural.som_memory import SOMemory
            self.pattern_memory = SOMemory(
                memory_size=self.pattern_memory_size,
                pattern_dim=50  # Comprehensive market state representation
            )

            # Initialize market regime detection
            self.market_regimes = {
                'bull_market': {'volatility_threshold': 0.02, 'trend_threshold': 0.05},
                'bear_market': {'volatility_threshold': 0.02, 'trend_threshold': -0.05},
                'sideways': {'volatility_threshold': 0.01, 'trend_threshold': 0.02},
                'high_volatility': {'volatility_threshold': 0.05, 'trend_threshold': None}
            }

            logger.info("Enhanced memory systems initialized")

        except Exception as e:
            logger.error(f"Error initializing enhanced memory systems: {e}")
            self.pattern_memory = None

    def _init_professional_components(self):
        """Initialize professional-grade neural components"""
        try:
            logger.info("🏛️ [PROFESSIONAL] Initializing professional-grade learning components...")

            # Advanced LSTM Processor for enhanced pattern recognition
            self.professional_components['advanced_lstm'] = AdvancedLSTMProcessor(
                input_size=20,
                hidden_size=64,  # Smaller for live trading performance
                num_layers=2,
                num_heads=4,
                use_attention=True,
                device='cpu'  # Use CPU for stability in live trading
            )

            # Transformer for multi-task learning
            transformer_config = TransformerConfig(
                d_model=128,  # Smaller for performance
                num_heads=4,
                num_layers=3,
                d_ff=256,
                dropout=0.1
            )
            self.professional_components['transformer'] = TransformerTradingModel(
                transformer_config, input_size=20, output_size=1
            )

            # VAE for anomaly detection
            vae_config = VAEConfig(
                input_dim=50,
                latent_dim=10,
                hidden_dims=[64, 32]
            )
            self.professional_components['vae'] = MarketVAE(vae_config)

            # Performance optimizer
            optimization_config = OptimizationConfig(
                target_inference_time_ms=50.0,  # Fast for live trading
                enable_gpu_acceleration=False,  # CPU for stability
                enable_quantization=False,      # Disable for compatibility
                enable_jit_compilation=True,
                precision='fp32'
            )
            self.professional_components['optimizer'] = InferenceOptimizer(optimization_config)

            # Federated learning participant
            participant_id = f"live_trader_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.federated_participant = FederatedLearningParticipant(participant_id)

            # Adaptive learning framework
            self.adaptive_learner = AdaptiveLearningFramework(
                models=self.professional_components,
                config_path="adaptive_config.json"
            )

            logger.info("✅ [PROFESSIONAL] Professional-grade components initialized successfully")

        except Exception as e:
            logger.error(f"❌ [PROFESSIONAL] Error initializing professional components: {e}")
            self.professional_components = {}
            self.federated_participant = None
            self.adaptive_learner = None

    def _load_long_term_strategies(self):
        """Load long-term strategy performance and adaptations"""
        try:
            # Load strategy adaptation history
            with sqlite3.connect(self.db_path) as conn:
                # Create table if it doesn't exist
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS long_term_strategies (
                        strategy_id TEXT PRIMARY KEY,
                        strategy_name TEXT NOT NULL,
                        creation_date TEXT NOT NULL,
                        last_adaptation TEXT,
                        adaptation_count INTEGER DEFAULT 0,
                        performance_score REAL DEFAULT 0,
                        market_regime TEXT,
                        parameters TEXT,
                        success_rate REAL DEFAULT 0,
                        total_trades INTEGER DEFAULT 0,
                        active INTEGER DEFAULT 1
                    )
                """)

                # Load existing strategies
                cursor = conn.execute("SELECT * FROM long_term_strategies WHERE active = 1")
                for row in cursor.fetchall():
                    strategy_id = row[0]
                    self.long_term_strategies[strategy_id] = {
                        'strategy_name': row[1],
                        'creation_date': datetime.fromisoformat(row[2]),
                        'last_adaptation': datetime.fromisoformat(row[3]) if row[3] else None,
                        'adaptation_count': row[4],
                        'performance_score': row[5],
                        'market_regime': row[6],
                        'parameters': json.loads(row[7]) if row[7] else {},
                        'success_rate': row[8],
                        'total_trades': row[9]
                    }

                conn.commit()

            logger.info(f"Loaded {len(self.long_term_strategies)} long-term strategies")

        except Exception as e:
            logger.error(f"Error loading long-term strategies: {e}")

    def _init_database(self):
        """Initialize SQLite database for trade results"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS live_trades (
                    trade_id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    exchange TEXT NOT NULL,
                    action TEXT NOT NULL,
                    amount REAL NOT NULL,
                    entry_price REAL NOT NULL,
                    exit_price REAL,
                    exit_timestamp TEXT,
                    pnl REAL,
                    fees REAL DEFAULT 0,
                    strategy_name TEXT,
                    confidence REAL DEFAULT 0,
                    market_conditions TEXT,
                    reasoning TEXT,
                    duration_minutes INTEGER,
                    success INTEGER
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    strategy_name TEXT PRIMARY KEY,
                    total_trades INTEGER DEFAULT 0,
                    winning_trades INTEGER DEFAULT 0,
                    losing_trades INTEGER DEFAULT 0,
                    total_pnl REAL DEFAULT 0,
                    win_rate REAL DEFAULT 0,
                    avg_win REAL DEFAULT 0,
                    avg_loss REAL DEFAULT 0,
                    profit_factor REAL DEFAULT 0,
                    sharpe_ratio REAL DEFAULT 0,
                    max_drawdown REAL DEFAULT 0,
                    avg_trade_duration REAL DEFAULT 0,
                    confidence_accuracy REAL DEFAULT 0,
                    last_updated TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS learning_insights (
                    insight_id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    insight_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    confidence REAL DEFAULT 0,
                    applied INTEGER DEFAULT 0
                )
            """)
            
            conn.commit()
    
    async def record_trade_entry(self, trade_result: LiveTradeResult):
        """Record a new trade entry"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO live_trades 
                    (trade_id, timestamp, symbol, exchange, action, amount, entry_price, 
                     strategy_name, confidence, market_conditions, reasoning)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    trade_result.trade_id,
                    trade_result.timestamp.isoformat(),
                    trade_result.symbol,
                    trade_result.exchange,
                    trade_result.action,
                    float(trade_result.amount),
                    float(trade_result.entry_price),
                    trade_result.strategy_name,
                    trade_result.confidence,
                    json.dumps(trade_result.market_conditions),
                    trade_result.reasoning
                ))
                conn.commit()
            
            self.recent_trades.append(trade_result)
            logger.info(f"Recorded trade entry: {trade_result.trade_id}")
            
        except Exception as e:
            logger.error(f"Error recording trade entry: {e}")
    
    async def record_trade_exit(self, trade_id: str, exit_price: Decimal,
                               exit_timestamp: Optional[datetime] = None):
        """Record trade exit and calculate results"""
        try:
            if exit_timestamp is None:
                exit_timestamp = datetime.now()
            
            # Get trade entry data
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT * FROM live_trades WHERE trade_id = ?
                """, (trade_id,))
                row = cursor.fetchone()
                
                if not row:
                    logger.error(f"Trade {trade_id} not found for exit recording")
                    return
                
                # Calculate results
                entry_price = Decimal(str(row[6]))  # entry_price column
                action = row[4]  # action column
                amount = Decimal(str(row[5]))  # amount column
                
                if action == "BUY":
                    pnl = (exit_price - entry_price) * amount
                else:  # SELL
                    pnl = (entry_price - exit_price) * amount
                
                # Calculate duration
                entry_time = datetime.fromisoformat(row[1])
                duration_minutes = int((exit_timestamp - entry_time).total_seconds() / 60)
                
                success = pnl > 0
                
                # Update trade record
                conn.execute("""
                    UPDATE live_trades 
                    SET exit_price = ?, exit_timestamp = ?, pnl = ?, 
                        duration_minutes = ?, success = ?
                    WHERE trade_id = ?
                """, (
                    float(exit_price),
                    exit_timestamp.isoformat(),
                    float(pnl),
                    duration_minutes,
                    1 if success else 0,
                    trade_id
                ))
                conn.commit()
            
            # Update strategy performance
            strategy_name = row[11]  # strategy_name column
            await self._update_strategy_performance(strategy_name)
            
            # Generate learning insights
            await self._generate_learning_insights(trade_id)

            # Enhanced neural network learning from trade result
            if NEURAL_AVAILABLE and self.rl_agent:
                await self._neural_learn_from_trade(trade_id, pnl, success)

            # Store pattern in memory for future reference
            if self.pattern_memory:
                await self._store_trade_pattern(trade_id, success)

            # Update long-term strategy performance
            await self._update_long_term_strategy_performance(strategy_name, success, float(pnl))

            # Detect and adapt to market regime changes
            await self._detect_market_regime_change(trade_id)

            # Real-time learning from trade result
            if self.real_time_learning_enabled:
                await self._real_time_learn_from_trade(trade_id, success, float(pnl))

            # Add to learning queue for continuous processing
            self.learning_queue.append({
                'trade_id': trade_id,
                'timestamp': exit_timestamp,
                'success': success,
                'pnl': float(pnl),
                'strategy': strategy_name,
                'processed': False
            })

            # Trigger continuous learning if queue is full
            if len(self.learning_queue) >= 10:
                await self._process_learning_queue()

            logger.info(f"Recorded trade exit: {trade_id}, P&L: {pnl}")

        except Exception as e:
            logger.error(f"Error recording trade exit: {e}")

    async def _store_trade_pattern(self, trade_id: str, success: bool):
        """Store trading pattern in memory for pattern recognition"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT market_conditions, confidence, strategy_name, duration_minutes, pnl
                    FROM live_trades WHERE trade_id = ?
                """, (trade_id,))
                trade_data = cursor.fetchone()

                if not trade_data:
                    return

                market_conditions_str, confidence, strategy_name, duration, pnl = trade_data
                market_conditions = json.loads(market_conditions_str) if market_conditions_str else {}

                # Create pattern vector from trade data
                pattern_vector = self._create_pattern_vector(
                    market_conditions, confidence, strategy_name, duration, pnl
                )

                # Calculate importance based on trade outcome and confidence
                importance = confidence * (2.0 if success else 0.5)
                if abs(float(pnl or 0)) > 0.01:  # Significant P&L
                    importance *= 1.5

                # Store pattern with metadata
                metadata = {
                    'trade_id': trade_id,
                    'strategy_name': strategy_name,
                    'success': success,
                    'pnl': float(pnl or 0),
                    'market_regime': self._detect_current_market_regime(market_conditions)
                }

                if TORCH_AVAILABLE and self.pattern_memory is not None:
                    self.pattern_memory.store_pattern(
                        pattern=torch.tensor(pattern_vector, dtype=torch.float32),
                        importance=importance,
                        metadata=metadata
                    )
                else:
                    # Fallback for when torch or pattern memory is not available
                    logger.debug("Pattern memory not available - skipping pattern storage")

                logger.debug(f"Stored trade pattern for {trade_id} with importance {importance:.2f}")

        except Exception as e:
            logger.error(f"Error storing trade pattern: {e}")

    async def analyze_trade_patterns(self, market_data: Dict) -> Dict:
        """Analyze current market data against learned patterns with trading impact"""
        try:
            if not self.pattern_memory:
                return {'patterns': [], 'confidence': 0.0, 'trading_recommendation': 'HOLD'}

            # Convert market data to pattern format
            pattern_features = self._extract_pattern_features(market_data)

            if not pattern_features:
                return {'patterns': [], 'confidence': 0.0, 'trading_recommendation': 'HOLD'}

            # ENHANCED: Get best patterns for current market conditions
            market_conditions = self._extract_market_conditions(market_data)
            best_patterns = self.pattern_memory.get_best_patterns_for_conditions(market_conditions, limit=10)

            # ENHANCED: Analyze pattern outcomes with trading impact
            pattern_analysis = []
            total_confidence = 0.0
            total_profit_expectation = 0.0
            buy_signals = 0
            sell_signals = 0

            for pattern_info in best_patterns:
                pattern = pattern_info['pattern']
                score = pattern_info['score']

                if isinstance(pattern, dict):
                    success_rate = pattern.get('success_rate', 0.0)
                    profit_correlation = pattern.get('profit_correlation', 0.0)
                    prediction_accuracy = pattern.get('prediction_accuracy', 0.0)
                    metadata = pattern.get('metadata', {})

                    # Determine signal direction from pattern
                    signal_type = metadata.get('signal_type', 'HOLD')
                    if signal_type == 'BUY' or profit_correlation > 10:
                        buy_signals += 1
                    elif signal_type == 'SELL' or profit_correlation < -10:
                        sell_signals += 1

                    pattern_analysis.append({
                        'success_rate': success_rate,
                        'profit_correlation': profit_correlation,
                        'prediction_accuracy': prediction_accuracy,
                        'metadata': metadata,
                        'confidence': score,
                        'signal_type': signal_type,
                        'expected_profit': profit_correlation * prediction_accuracy
                    })

                    total_confidence += score
                    total_profit_expectation += profit_correlation * prediction_accuracy

            avg_confidence = total_confidence / len(best_patterns) if best_patterns else 0.0
            avg_profit_expectation = total_profit_expectation / len(best_patterns) if best_patterns else 0.0

            # ENHANCED: Generate trading recommendation based on patterns
            trading_recommendation = self._generate_trading_recommendation(
                buy_signals, sell_signals, avg_profit_expectation, avg_confidence
            )

            # ENHANCED: Calculate position sizing recommendation
            position_size = self._calculate_position_size(avg_confidence, avg_profit_expectation)

            return {
                'patterns': pattern_analysis,
                'confidence': avg_confidence,
                'pattern_count': len(best_patterns),
                'trading_recommendation': trading_recommendation,
                'expected_profit': avg_profit_expectation,
                'position_size': position_size,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'market_regime': market_conditions.get('regime', 'unknown')
            }

        except Exception as e:
            logger.error(f"Error analyzing trade patterns: {e}")
            return {'patterns': [], 'confidence': 0.0, 'trading_recommendation': 'HOLD'}

    def _extract_market_conditions(self, market_data: Dict) -> Dict:
        """Extract market conditions for pattern matching"""
        try:
            conditions = {}

            # Extract from price data
            for exchange_data in market_data.get('price_data', {}).values():
                for symbol_data in exchange_data.values():
                    if isinstance(symbol_data, dict):
                        conditions.update({
                            'volatility': symbol_data.get('volatility', 0.02),
                            'volume': symbol_data.get('volume', 0),
                            'change': symbol_data.get('change', 0),
                            'trend': symbol_data.get('change', 0)  # Use change as trend proxy
                        })
                        break
                break

            # Extract from technical indicators
            tech_indicators = market_data.get('technical_indicators', {})
            for symbol_indicators in tech_indicators.values():
                if isinstance(symbol_indicators, dict):
                    conditions.update({
                        'rsi': symbol_indicators.get('rsi', 50),
                        'macd': symbol_indicators.get('macd', 0),
                        'bollinger_position': symbol_indicators.get('bollinger_position', 0.5)
                    })
                    break

            return conditions

        except Exception as e:
            logger.debug(f"Error extracting market conditions: {e}")
            return {}

    def _generate_trading_recommendation(self, buy_signals: int, sell_signals: int,
                                       profit_expectation: float, confidence: float) -> str:
        """Generate trading recommendation based on pattern analysis"""
        try:
            # Minimum confidence threshold
            if confidence < 0.3:
                return 'HOLD'

            # Strong buy conditions
            if (buy_signals > sell_signals * 2 and profit_expectation > 20 and confidence > 0.7):
                return 'STRONG_BUY'
            elif (buy_signals > sell_signals and profit_expectation > 10 and confidence > 0.5):
                return 'BUY'

            # Strong sell conditions
            elif (sell_signals > buy_signals * 2 and profit_expectation < -20 and confidence > 0.7):
                return 'STRONG_SELL'
            elif (sell_signals > buy_signals and profit_expectation < -10 and confidence > 0.5):
                return 'SELL'

            # Default to hold
            else:
                return 'HOLD'

        except Exception as e:
            logger.debug(f"Error generating trading recommendation: {e}")
            return 'HOLD'

    def _calculate_position_size(self, confidence: float, profit_expectation: float) -> float:
        """Calculate recommended position size based on confidence and expected profit"""
        try:
            # Base position size (as percentage of portfolio)
            base_size = 0.02  # 2% base position

            # Confidence multiplier (0.5x to 2x)
            confidence_multiplier = 0.5 + (confidence * 1.5)

            # Profit expectation multiplier (0.5x to 2x)
            profit_multiplier = max(0.5, min(2.0, 1.0 + profit_expectation / 100))

            # Calculate final position size
            position_size = base_size * confidence_multiplier * profit_multiplier

            # Cap at maximum 10% of portfolio
            return min(position_size, 0.10)

        except Exception as e:
            logger.debug(f"Error calculating position size: {e}")
            return 0.01  # Default 1%

    def _extract_pattern_features(self, market_data: Dict) -> List[float]:
        """Extract numerical features from market data for pattern matching"""
        try:
            features = []

            # Extract price and volume features
            for exchange_data in market_data.get('price_data', {}).values():
                for symbol_data in exchange_data.values():
                    if isinstance(symbol_data, dict):
                        features.extend([
                            symbol_data.get('last', 0.0),
                            symbol_data.get('volume', 0.0),
                            symbol_data.get('change', 0.0),
                            symbol_data.get('volatility', 0.02),
                            symbol_data.get('high', 0.0),
                            symbol_data.get('low', 0.0)
                        ])
                        break
                break

            # Extract technical indicator features
            tech_indicators = market_data.get('technical_indicators', {})
            for symbol_indicators in tech_indicators.values():
                if isinstance(symbol_indicators, dict):
                    features.extend([
                        symbol_indicators.get('rsi', 50.0),
                        symbol_indicators.get('macd', 0.0),
                        symbol_indicators.get('bollinger_position', 0.5),
                        symbol_indicators.get('momentum', 0.0),
                        symbol_indicators.get('trend_strength', 0.0)
                    ])
                    break

            # Pad or truncate to fixed size (20 features)
            target_size = 20
            if len(features) < target_size:
                features.extend([0.0] * (target_size - len(features)))
            elif len(features) > target_size:
                features = features[:target_size]

            return features

        except Exception as e:
            logger.debug(f"Error extracting pattern features: {e}")
            return [0.0] * 20  # Return default features

    def _create_pattern_vector(self, market_conditions: Dict, confidence: float,
                              strategy_name: str, duration: Optional[int], pnl: Optional[float]) -> List[float]:
        """Create a comprehensive pattern vector from trade data"""
        try:
            # Market condition features
            features = [
                market_conditions.get('volatility', 0.0),
                market_conditions.get('volume', 0.0),
                market_conditions.get('price_change', 0.0),
                market_conditions.get('rsi', 50.0) / 100.0,
                market_conditions.get('macd', 0.0),
                market_conditions.get('bollinger_position', 0.5),
                market_conditions.get('volume_ratio', 1.0),
                market_conditions.get('momentum', 0.0),
                market_conditions.get('support_distance', 0.0),
                market_conditions.get('resistance_distance', 0.0),
            ]

            # Trade-specific features
            features.extend([
                confidence,
                (duration or 0) / 1440.0,  # Normalize to days
                max(-1.0, min(1.0, (pnl or 0) * 100)),  # Normalized P&L
                hash(strategy_name) % 100 / 100.0,  # Strategy encoding
            ])

            # Time-based features
            now = datetime.now()
            features.extend([
                now.hour / 24.0,  # Hour of day
                now.weekday() / 7.0,  # Day of week
                (now.day - 1) / 31.0,  # Day of month
            ])

            # Market regime features
            regime = self._detect_current_market_regime(market_conditions)
            regime_encoding = [0.0] * 4  # 4 market regimes
            regime_map = {'bull_market': 0, 'bear_market': 1, 'sideways': 2, 'high_volatility': 3}
            if regime in regime_map:
                regime_encoding[regime_map[regime]] = 1.0
            features.extend(regime_encoding)

            # Pad or truncate to fixed size (50 features)
            while len(features) < 50:
                features.append(0.0)

            return features[:50]

        except Exception as e:
            logger.error(f"Error creating pattern vector: {e}")
            return [0.0] * 50

    def _detect_current_market_regime(self, market_conditions: Dict) -> str:
        """Detect current market regime based on market conditions"""
        try:
            volatility = market_conditions.get('volatility', 0.02)
            trend = market_conditions.get('price_change', 0.0)

            if volatility > 0.05:
                return 'high_volatility'
            elif trend > 0.05:
                return 'bull_market'
            elif trend < -0.05:
                return 'bear_market'
            else:
                return 'sideways'

        except Exception as e:
            logger.debug(f"Error detecting market regime: {e}")
            return 'sideways'

    async def _update_long_term_strategy_performance(self, strategy_name: str, success: bool, pnl: float):
        """Update long-term strategy performance and trigger adaptations if needed"""
        try:
            strategy_id = f"{strategy_name}_long_term"

            # Get or create long-term strategy record
            if strategy_id not in self.long_term_strategies:
                self.long_term_strategies[strategy_id] = {
                    'strategy_name': strategy_name,
                    'creation_date': datetime.now(),
                    'last_adaptation': None,
                    'adaptation_count': 0,
                    'performance_score': 0.0,
                    'market_regime': 'unknown',
                    'parameters': {},
                    'success_rate': 0.0,
                    'total_trades': 0
                }

            strategy = self.long_term_strategies[strategy_id]

            # Update performance metrics
            strategy['total_trades'] += 1
            old_success_rate = strategy['success_rate']
            strategy['success_rate'] = (old_success_rate * (strategy['total_trades'] - 1) + (1.0 if success else 0.0)) / strategy['total_trades']

            # Update performance score (weighted by recency and magnitude)
            score_update = (pnl * 10) + (0.1 if success else -0.1)
            strategy['performance_score'] = strategy['performance_score'] * 0.95 + score_update * 0.05

            # Check if adaptation is needed
            if strategy['total_trades'] >= 20 and strategy['total_trades'] % 10 == 0:
                await self._consider_strategy_adaptation(strategy_id)

            # Save to database
            await self._save_long_term_strategy(strategy_id)

        except Exception as e:
            logger.error(f"Error updating long-term strategy performance: {e}")

    async def _consider_strategy_adaptation(self, strategy_id: str):
        """Consider adapting strategy based on long-term performance"""
        try:
            strategy = self.long_term_strategies[strategy_id]

            # Check if adaptation is warranted
            should_adapt = False
            adaptation_reason = ""

            if strategy['success_rate'] < 0.4:
                should_adapt = True
                adaptation_reason = "Low success rate"
            elif strategy['performance_score'] < -0.5:
                should_adapt = True
                adaptation_reason = "Poor performance score"
            elif strategy['total_trades'] >= 50 and strategy['success_rate'] < 0.45:
                should_adapt = True
                adaptation_reason = "Sustained underperformance"

            if should_adapt:
                await self._adapt_strategy(strategy_id, adaptation_reason)

        except Exception as e:
            logger.error(f"Error considering strategy adaptation: {e}")

    async def _adapt_strategy(self, strategy_id: str, reason: str):
        """Adapt strategy parameters based on performance"""
        try:
            strategy = self.long_term_strategies[strategy_id]

            # Record adaptation
            strategy['last_adaptation'] = datetime.now()
            strategy['adaptation_count'] += 1

            # Simple adaptation logic (would be more sophisticated in practice)
            adaptations = {
                'confidence_threshold': max(0.1, strategy.get('confidence_threshold', 0.6) - 0.1),
                'position_size_multiplier': max(0.5, strategy.get('position_size_multiplier', 1.0) - 0.1),
                'risk_tolerance': max(0.01, strategy.get('risk_tolerance', 0.02) - 0.005)
            }

            strategy['parameters'].update(adaptations)

            # Save adaptation record
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO learning_insights
                    (insight_id, timestamp, insight_type, content, confidence)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    f"{strategy_id}_adaptation_{strategy['adaptation_count']}",
                    datetime.now().isoformat(),
                    'strategy_adaptation',
                    f"Adapted {strategy['strategy_name']} due to {reason}. "
                    f"New parameters: {json.dumps(adaptations)}",
                    0.9
                ))
                conn.commit()

            logger.info(f"Adapted strategy {strategy_id} due to {reason}")

        except Exception as e:
            logger.error(f"Error adapting strategy: {e}")

    async def _save_long_term_strategy(self, strategy_id: str):
        """Save long-term strategy to database"""
        try:
            strategy = self.long_term_strategies[strategy_id]

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO long_term_strategies
                    (strategy_id, strategy_name, creation_date, last_adaptation,
                     adaptation_count, performance_score, market_regime, parameters,
                     success_rate, total_trades, active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    strategy_id,
                    strategy['strategy_name'],
                    strategy['creation_date'].isoformat(),
                    strategy['last_adaptation'].isoformat() if strategy['last_adaptation'] else None,
                    strategy['adaptation_count'],
                    strategy['performance_score'],
                    strategy.get('market_regime', 'unknown'),
                    json.dumps(strategy['parameters']),
                    strategy['success_rate'],
                    strategy['total_trades'],
                    1
                ))
                conn.commit()

        except Exception as e:
            logger.error(f"Error saving long-term strategy: {e}")

    async def _detect_market_regime_change(self, trade_id: str):
        """Detect market regime changes and update memory"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT market_conditions FROM live_trades WHERE trade_id = ?
                """, (trade_id,))
                result = cursor.fetchone()

                if result:
                    market_conditions = json.loads(result[0]) if result[0] else {}
                    current_regime = self._detect_current_market_regime(market_conditions)

                    # Store in regime memory
                    regime_entry = {
                        'timestamp': datetime.now(),
                        'regime': current_regime,
                        'conditions': market_conditions
                    }
                    self.market_regime_memory.append(regime_entry)

                    # Check for regime change
                    if len(self.market_regime_memory) >= 10:
                        recent_regimes = [entry['regime'] for entry in list(self.market_regime_memory)[-10:]]
                        if len(set(recent_regimes)) == 1 and recent_regimes[0] != current_regime:
                            logger.info(f"Market regime change detected: {recent_regimes[-1]} -> {current_regime}")
                            await self._handle_regime_change(current_regime)

        except Exception as e:
            logger.error(f"Error detecting market regime change: {e}")

    async def _handle_regime_change(self, new_regime: str):
        """Handle market regime change by adapting strategies"""
        try:
            # Update all long-term strategies for new regime
            for strategy_id, strategy in self.long_term_strategies.items():
                strategy['market_regime'] = new_regime

                # Regime-specific adaptations
                if new_regime == 'high_volatility':
                    # Reduce position sizes in high volatility
                    strategy['parameters']['position_size_multiplier'] = 0.7
                elif new_regime == 'bull_market':
                    # Increase confidence threshold in bull markets
                    strategy['parameters']['confidence_threshold'] = 0.7
                elif new_regime == 'bear_market':
                    # Tighten risk management in bear markets
                    strategy['parameters']['risk_tolerance'] = 0.01

                await self._save_long_term_strategy(strategy_id)

            # Log regime change insight
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO learning_insights
                    (insight_id, timestamp, insight_type, content, confidence)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    f"regime_change_{int(datetime.now().timestamp())}",
                    datetime.now().isoformat(),
                    'market_regime_change',
                    f"Market regime changed to {new_regime}. Adapted all strategies accordingly.",
                    0.95
                ))
                conn.commit()

        except Exception as e:
            logger.error(f"Error handling regime change: {e}")

    async def _neural_learn_from_trade(self, trade_id: str, pnl: Decimal, success: bool):
        """Use neural networks to learn from trade results"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT strategy_name, confidence, market_conditions, duration_minutes
                    FROM live_trades WHERE trade_id = ?
                """, (trade_id,))
                trade_data = cursor.fetchone()

                if not trade_data:
                    return

                strategy_name, confidence, market_conditions_str, duration = trade_data
                market_conditions = json.loads(market_conditions_str) if market_conditions_str else {}

                # Prepare state vector for RL agent
                state = self._prepare_state_vector(market_conditions, confidence, duration)

                # Calculate reward based on P&L and other factors
                reward = float(pnl)
                if success:
                    reward += 0.1  # Bonus for successful trade
                if confidence > 0.8 and success:
                    reward += 0.2  # Extra bonus for high-confidence successful trades
                elif confidence > 0.8 and not success:
                    reward -= 0.3  # Penalty for overconfident failed trades

                # Train RL agent if available
                if self.rl_agent and hasattr(self.rl_agent, 'remember'):
                    action = 0  # This would be the action taken (strategy choice, etc.)
                    self.rl_agent.remember(state, action, reward, state, success)

                    # Train if we have enough experiences
                    if hasattr(self.rl_agent, 'memory') and len(self.rl_agent.memory) > 32:
                        self.rl_agent.replay(32)

                logger.debug(f"Neural learning from trade {trade_id}: reward={reward:.3f}")

        except Exception as e:
            logger.error(f"Error in neural learning from trade: {e}")

    def _prepare_state_vector(self, market_conditions: Dict, confidence: float,
                             duration: Optional[int]) -> np.ndarray:
        """Prepare state vector for neural network learning"""
        try:
            # Create feature vector from market conditions and trade data
            features = [
                market_conditions.get('volatility', 0.0),
                market_conditions.get('volume', 0.0),
                market_conditions.get('price_change', 0.0),
                market_conditions.get('rsi', 50.0) / 100.0,  # Normalize RSI
                market_conditions.get('macd', 0.0),
                confidence,
                (duration or 0) / 1440.0,  # Normalize duration to days
                # Add more features as needed
            ]

            # Pad or truncate to fixed size (20 features)
            while len(features) < 20:
                features.append(0.0)

            return np.array(features[:20], dtype=np.float32)

        except Exception as e:
            logger.error(f"Error preparing state vector: {e}")
            return np.zeros(20, dtype=np.float32)
    
    async def _update_strategy_performance(self, strategy_name: str):
        """Update performance metrics for a strategy"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get all completed trades for this strategy
                cursor = conn.execute("""
                    SELECT pnl, success, confidence, duration_minutes 
                    FROM live_trades 
                    WHERE strategy_name = ? AND pnl IS NOT NULL
                """, (strategy_name,))
                
                trades = cursor.fetchall()
                
                if not trades:
                    return
                
                # Calculate metrics
                total_trades = len(trades)
                winning_trades = sum(1 for t in trades if t[1] == 1)
                losing_trades = total_trades - winning_trades
                total_pnl = Decimal(str(sum(Decimal(str(t[0])) for t in trades)))
                win_rate = winning_trades / total_trades if total_trades > 0 else 0
                
                wins = [Decimal(str(t[0])) for t in trades if t[1] == 1]
                losses = [abs(Decimal(str(t[0]))) for t in trades if t[1] == 0]
                
                avg_win = Decimal(str(sum(wins) / len(wins))) if wins else Decimal('0')
                avg_loss = Decimal(str(sum(losses) / len(losses))) if losses else Decimal('0')
                
                profit_factor = float(avg_win) / float(avg_loss) if avg_loss > 0 else 0
                
                # Calculate confidence accuracy
                confident_trades = [t for t in trades if t[2] > self.confidence_threshold]
                confidence_accuracy = (
                    sum(1 for t in confident_trades if t[1] == 1) / len(confident_trades)
                    if confident_trades else 0
                )
                
                avg_duration = sum(t[3] for t in trades if t[3]) / total_trades
                
                # Update database
                conn.execute("""
                    INSERT OR REPLACE INTO strategy_performance 
                    (strategy_name, total_trades, winning_trades, losing_trades, 
                     total_pnl, win_rate, avg_win, avg_loss, profit_factor, 
                     avg_trade_duration, confidence_accuracy, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    strategy_name, total_trades, winning_trades, losing_trades,
                    float(total_pnl), win_rate, float(avg_win), float(avg_loss),
                    profit_factor, avg_duration, confidence_accuracy,
                    datetime.now().isoformat()
                ))
                conn.commit()
                
                # Update in-memory cache
                self.strategy_performance[strategy_name] = StrategyPerformance(
                    strategy_name=strategy_name,
                    total_trades=total_trades,
                    winning_trades=winning_trades,
                    losing_trades=losing_trades,
                    total_pnl=total_pnl,
                    win_rate=win_rate,
                    avg_win=avg_win,
                    avg_loss=avg_loss,
                    profit_factor=profit_factor,
                    avg_trade_duration=avg_duration,
                    confidence_accuracy=confidence_accuracy,
                    last_updated=datetime.now()
                )
                
                logger.info(f"Updated performance for {strategy_name}: "
                           f"Win rate: {win_rate:.2%}, P&L: {total_pnl}")
                
        except Exception as e:
            logger.error(f"Error updating strategy performance: {e}")
    
    def _load_historical_performance(self):
        """Load historical performance data from database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT * FROM strategy_performance")
                for row in cursor.fetchall():
                    strategy_name = row[0]
                    self.strategy_performance[strategy_name] = StrategyPerformance(
                        strategy_name=strategy_name,
                        total_trades=row[1],
                        winning_trades=row[2],
                        losing_trades=row[3],
                        total_pnl=Decimal(str(row[4])),
                        win_rate=row[5],
                        avg_win=Decimal(str(row[6])),
                        avg_loss=Decimal(str(row[7])),
                        profit_factor=row[8],
                        sharpe_ratio=row[9],
                        max_drawdown=Decimal(str(row[10])),
                        avg_trade_duration=row[11],
                        confidence_accuracy=row[12],
                        last_updated=datetime.fromisoformat(row[13]) if row[13] else None
                    )
            
            logger.info(f"Loaded performance data for {len(self.strategy_performance)} strategies")
            
        except Exception as e:
            logger.error(f"Error loading historical performance: {e}")

    async def _generate_learning_insights(self, trade_id: str):
        """Generate learning insights from completed trades"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get trade details
                cursor = conn.execute("""
                    SELECT * FROM live_trades WHERE trade_id = ?
                """, (trade_id,))
                trade = cursor.fetchone()

                if not trade or trade[9] is None:  # No P&L recorded
                    return

                strategy_name = trade[11]
                pnl = Decimal(str(trade[9]))
                confidence = trade[12]
                market_conditions = json.loads(trade[13]) if trade[13] else {}

                insights = []

                # Confidence vs Performance Analysis
                if confidence > self.confidence_threshold and pnl < 0:
                    insights.append({
                        'type': 'confidence_overestimation',
                        'content': f"High confidence ({confidence:.2f}) trade resulted in loss. "
                                 f"Strategy {strategy_name} may be overconfident in certain conditions.",
                        'confidence': 0.8
                    })
                elif confidence < self.confidence_threshold and pnl > 0:
                    insights.append({
                        'type': 'confidence_underestimation',
                        'content': f"Low confidence ({confidence:.2f}) trade was profitable. "
                                 f"Strategy {strategy_name} may be underestimating opportunities.",
                        'confidence': 0.7
                    })

                # Market Condition Analysis
                volatility = market_conditions.get('volatility', 0)
                if volatility > 0.05 and pnl < 0:  # High volatility loss
                    insights.append({
                        'type': 'volatility_sensitivity',
                        'content': f"Strategy {strategy_name} performed poorly in high volatility "
                                 f"({volatility:.3f}). Consider reducing position sizes or avoiding "
                                 f"trades during high volatility periods.",
                        'confidence': 0.9
                    })

                # Duration Analysis
                duration = trade[15]  # duration_minutes
                if duration and duration > 240 and pnl < 0:  # Long losing trade
                    insights.append({
                        'type': 'duration_risk',
                        'content': f"Long duration trade ({duration} minutes) resulted in loss. "
                                 f"Consider implementing tighter stop-losses or time-based exits.",
                        'confidence': 0.8
                    })

                # Store insights
                for insight in insights:
                    insight_id = f"{trade_id}_{insight['type']}"
                    conn.execute("""
                        INSERT OR REPLACE INTO learning_insights
                        (insight_id, timestamp, insight_type, content, confidence)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        insight_id,
                        datetime.now().isoformat(),
                        insight['type'],
                        insight['content'],
                        insight['confidence']
                    ))

                conn.commit()

                if insights:
                    logger.info(f"Generated {len(insights)} learning insights from trade {trade_id}")

        except Exception as e:
            logger.error(f"Error generating learning insights: {e}")

    async def get_strategy_recommendations(self, strategy_name: str) -> Dict[str, Any]:
        """Get recommendations for improving strategy performance"""
        try:
            if strategy_name not in self.strategy_performance:
                return {'recommendations': [], 'confidence': 0.0}

            perf = self.strategy_performance[strategy_name]
            recommendations = []

            # Win rate analysis
            if perf.total_trades >= self.min_trades_for_learning:
                if perf.win_rate < 0.4:
                    recommendations.append({
                        'type': 'win_rate_improvement',
                        'message': f"Win rate is low ({perf.win_rate:.1%}). Consider tightening entry criteria.",
                        'priority': 'high'
                    })
                elif perf.win_rate > 0.7:
                    recommendations.append({
                        'type': 'position_sizing',
                        'message': f"High win rate ({perf.win_rate:.1%}). Consider increasing position sizes.",
                        'priority': 'medium'
                    })

                # Profit factor analysis
                if perf.profit_factor < 1.2:
                    recommendations.append({
                        'type': 'profit_factor',
                        'message': f"Low profit factor ({perf.profit_factor:.2f}). "
                                 f"Focus on cutting losses faster or letting winners run longer.",
                        'priority': 'high'
                    })

                # Confidence accuracy analysis
                if perf.confidence_accuracy < 0.6:
                    recommendations.append({
                        'type': 'confidence_calibration',
                        'message': f"Confidence accuracy is low ({perf.confidence_accuracy:.1%}). "
                                 f"Recalibrate confidence scoring mechanism.",
                        'priority': 'medium'
                    })

            # Get recent insights
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT insight_type, content, confidence
                    FROM learning_insights
                    WHERE timestamp > ? AND applied = 0
                    ORDER BY confidence DESC LIMIT 5
                """, ((datetime.now() - timedelta(days=7)).isoformat(),))

                recent_insights = cursor.fetchall()
                for insight in recent_insights:
                    recommendations.append({
                        'type': insight[0],
                        'message': insight[1],
                        'priority': 'high' if insight[2] > 0.8 else 'medium'
                    })

            return {
                'recommendations': recommendations,
                'confidence': min(perf.total_trades / 50, 1.0),  # More trades = higher confidence
                'performance_summary': {
                    'total_trades': perf.total_trades,
                    'win_rate': perf.win_rate,
                    'total_pnl': float(perf.total_pnl),
                    'profit_factor': perf.profit_factor,
                    'confidence_accuracy': perf.confidence_accuracy
                }
            }

        except Exception as e:
            logger.error(f"Error getting strategy recommendations: {e}")
            return {'recommendations': [], 'confidence': 0.0}

    async def apply_learning_to_strategy(self, strategy_name: str) -> Dict[str, Any]:
        """Apply learning insights to improve strategy parameters"""
        try:
            recommendations = await self.get_strategy_recommendations(strategy_name)

            if not recommendations['recommendations']:
                return {'adjustments': [], 'message': 'No adjustments needed'}

            adjustments = []

            for rec in recommendations['recommendations']:
                if rec['type'] == 'win_rate_improvement':
                    adjustments.append({
                        'parameter': 'confidence_threshold',
                        'adjustment': 'increase',
                        'value': 0.1,
                        'reason': 'Increase confidence threshold to improve trade quality'
                    })
                elif rec['type'] == 'position_sizing':
                    adjustments.append({
                        'parameter': 'position_size_multiplier',
                        'adjustment': 'increase',
                        'value': 0.2,
                        'reason': 'Increase position sizes due to high win rate'
                    })
                elif rec['type'] == 'profit_factor':
                    adjustments.append({
                        'parameter': 'stop_loss_tightness',
                        'adjustment': 'increase',
                        'value': 0.1,
                        'reason': 'Tighten stop losses to improve profit factor'
                    })

            # Mark insights as applied
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE learning_insights
                    SET applied = 1
                    WHERE timestamp > ? AND applied = 0
                """, ((datetime.now() - timedelta(days=7)).isoformat(),))
                conn.commit()

            logger.info(f"Applied {len(adjustments)} learning adjustments to {strategy_name}")

            return {
                'adjustments': adjustments,
                'message': f'Applied {len(adjustments)} performance-based adjustments'
            }

        except Exception as e:
            logger.error(f"Error applying learning to strategy: {e}")
            return {'adjustments': [], 'message': f'Error: {e}'}

    async def _real_time_learn_from_trade(self, trade_id: str, success: bool, pnl: float):
        """Real-time learning from individual trade results"""
        try:
            # Get trade details
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT strategy_name, confidence, market_conditions, duration_minutes
                    FROM live_trades WHERE trade_id = ?
                """, (trade_id,))
                trade_data = cursor.fetchone()

                if not trade_data:
                    return

                strategy_name, confidence, market_conditions_str, duration = trade_data
                market_conditions = json.loads(market_conditions_str) if market_conditions_str else {}

            # Update learning session metrics
            self.learning_session_metrics['trades_processed'] += 1
            self.learning_session_metrics['last_learning_update'] = datetime.now()

            # Adaptive learning rate based on recent performance
            if self.adaptive_learning_rate:
                recent_success_rate = self._calculate_recent_success_rate()
                learning_multiplier = 1.5 if recent_success_rate < 0.5 else 1.0
            else:
                learning_multiplier = 1.0

            # Real-time strategy adjustment
            if strategy_name in self.strategy_performance:
                strategy_perf = self.strategy_performance[strategy_name]

                # Adjust confidence thresholds based on performance
                if strategy_perf.win_rate < 0.4 and strategy_perf.total_trades >= 10:
                    # Poor performing strategy - increase confidence threshold
                    self._adjust_strategy_confidence_threshold(strategy_name, increase=True)
                elif strategy_perf.win_rate > 0.7 and strategy_perf.total_trades >= 5:
                    # Well performing strategy - can lower confidence threshold
                    self._adjust_strategy_confidence_threshold(strategy_name, increase=False)

            # Neural network feedback if available
            if self.neural_agent and NEURAL_AVAILABLE:
                await self._provide_neural_feedback(trade_id, success, pnl, market_conditions)

            # Pattern learning enhancement
            if success and abs(pnl) > 0.01:  # Significant profitable trade
                self.learning_session_metrics['patterns_learned'] += 1
                await self._enhance_successful_pattern(trade_id, market_conditions)
            elif not success and abs(pnl) > 0.01:  # Significant losing trade
                await self._learn_from_failed_pattern(trade_id, market_conditions)

            logger.debug(f"Real-time learning completed for trade {trade_id}")

        except Exception as e:
            logger.error(f"Error in real-time learning from trade: {e}")

    async def _process_learning_queue(self):
        """Process accumulated trades in learning queue for batch learning"""
        try:
            if not self.learning_queue:
                return

            unprocessed_trades = [trade for trade in self.learning_queue if not trade.get('processed', False)]

            if len(unprocessed_trades) < 5:
                return  # Wait for more trades

            logger.info(f"Processing learning queue with {len(unprocessed_trades)} trades")

            # Batch analysis of trade patterns
            successful_trades = [t for t in unprocessed_trades if t['success']]
            failed_trades = [t for t in unprocessed_trades if not t['success']]

            # Strategy performance analysis
            strategy_performance = {}
            for trade in unprocessed_trades:
                strategy = trade['strategy']
                if strategy not in strategy_performance:
                    strategy_performance[strategy] = {'wins': 0, 'losses': 0, 'total_pnl': 0.0}

                if trade['success']:
                    strategy_performance[strategy]['wins'] += 1
                else:
                    strategy_performance[strategy]['losses'] += 1
                strategy_performance[strategy]['total_pnl'] += trade['pnl']

            # Generate batch learning insights
            insights = []
            for strategy, perf in strategy_performance.items():
                total_trades = perf['wins'] + perf['losses']
                win_rate = perf['wins'] / total_trades if total_trades > 0 else 0

                if total_trades >= 5:  # Enough data for insights
                    if win_rate < 0.3:
                        insights.append(f"Strategy '{strategy}' showing poor performance ({win_rate:.1%} win rate) - consider adjustment")
                    elif win_rate > 0.8:
                        insights.append(f"Strategy '{strategy}' performing excellently ({win_rate:.1%} win rate) - consider increasing allocation")

            # Store insights
            for insight in insights:
                await self._store_learning_insight('batch_analysis', insight, confidence=0.8)

            # Mark trades as processed
            for trade in unprocessed_trades:
                trade['processed'] = True

            # Neural network batch learning
            if self.neural_agent and NEURAL_AVAILABLE and len(unprocessed_trades) >= 10:
                await self._neural_batch_learning(unprocessed_trades)

            logger.info(f"Completed batch learning processing - generated {len(insights)} insights")

        except Exception as e:
            logger.error(f"Error processing learning queue: {e}")

    def _calculate_recent_success_rate(self, days: int = 7) -> float:
        """Calculate success rate for recent trades"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_trades = [t for t in self.recent_trades if t.timestamp >= cutoff_date and t.success is not None]

            if not recent_trades:
                return 0.5  # Default neutral rate

            successful = sum(1 for t in recent_trades if t.success)
            return successful / len(recent_trades)

        except Exception as e:
            logger.error(f"Error calculating recent success rate: {e}")
            return 0.5

    def _adjust_strategy_confidence_threshold(self, strategy_name: str, increase: bool):
        """Adjust confidence threshold for a strategy based on performance"""
        try:
            if strategy_name not in self.strategy_adaptation_history:
                self.strategy_adaptation_history[strategy_name] = {
                    'confidence_threshold': 0.6,
                    'adjustments': [],
                    'last_adjustment': None
                }

            current_threshold = self.strategy_adaptation_history[strategy_name]['confidence_threshold']

            if increase:
                new_threshold = min(0.9, current_threshold + 0.1)
                adjustment_type = 'increase'
            else:
                new_threshold = max(0.3, current_threshold - 0.05)
                adjustment_type = 'decrease'

            self.strategy_adaptation_history[strategy_name]['confidence_threshold'] = new_threshold
            self.strategy_adaptation_history[strategy_name]['adjustments'].append({
                'timestamp': datetime.now(),
                'type': adjustment_type,
                'old_threshold': current_threshold,
                'new_threshold': new_threshold
            })
            self.strategy_adaptation_history[strategy_name]['last_adjustment'] = datetime.now()

            logger.info(f"Adjusted confidence threshold for {strategy_name}: {current_threshold:.2f} -> {new_threshold:.2f}")

        except Exception as e:
            logger.error(f"Error adjusting strategy confidence threshold: {e}")

    def get_learning_metrics(self) -> Dict[str, Any]:
        """Get comprehensive learning metrics"""
        try:
            return {
                'learning_session_metrics': self.learning_session_metrics,
                'learning_queue_size': len(self.learning_queue),
                'pattern_memory_size': len(self.pattern_memory.patterns) if self.pattern_memory else 0,
                'strategy_adaptations': len(self.strategy_adaptation_history),
                'neural_learning_active': self.neural_learning_active,
                'real_time_learning_enabled': self.real_time_learning_enabled,
                'continuous_learning_active': self.continuous_learning_active,
                'neural_feedback_queue_size': len(self.neural_feedback_loop.get('recent_feedback', [])),
                'long_term_strategies': len(self.long_term_strategies),
                'market_regime_memory_size': len(self.market_regime_memory)
            }
        except Exception as e:
            logger.error(f"Error getting learning metrics: {e}")
            return {'error': str(e)}

    async def _provide_neural_feedback(self, trade_id: str, success: bool, pnl: float, market_conditions: Dict):
        """Provide feedback to neural networks for continuous learning"""
        try:
            if not self.neural_agent:
                return

            # Create feedback data
            feedback_data = {
                'trade_id': trade_id,
                'success': success,
                'pnl': pnl,
                'market_conditions': market_conditions,
                'timestamp': datetime.now()
            }

            # Store in neural feedback loop
            if 'recent_feedback' not in self.neural_feedback_loop:
                self.neural_feedback_loop['recent_feedback'] = deque(maxlen=100)

            self.neural_feedback_loop['recent_feedback'].append(feedback_data)

            # Trigger neural learning if enough feedback accumulated
            if len(self.neural_feedback_loop['recent_feedback']) >= 20:
                await self._trigger_neural_learning_update()

        except Exception as e:
            logger.error(f"Error providing neural feedback: {e}")

    async def _enhance_successful_pattern(self, trade_id: str, market_conditions: Dict):
        """Enhance learning from successful trading patterns"""
        try:
            # Store successful pattern with high importance
            if self.pattern_memory:
                pattern_vector = self._create_pattern_vector(market_conditions, 1.0, "successful", None, None)
                importance = 2.0  # High importance for successful patterns

                metadata = {
                    'trade_id': trade_id,
                    'pattern_type': 'successful',
                    'timestamp': datetime.now(),
                    'market_regime': self._detect_current_market_regime(market_conditions)
                }

                if TORCH_AVAILABLE:
                    self.pattern_memory.store_pattern(
                        pattern=torch.tensor(pattern_vector, dtype=torch.float32),
                        importance=importance,
                        metadata=metadata
                    )

            logger.debug(f"Enhanced successful pattern from trade {trade_id}")

        except Exception as e:
            logger.error(f"Error enhancing successful pattern: {e}")

    async def _learn_from_failed_pattern(self, trade_id: str, market_conditions: Dict):
        """Learn from failed trading patterns to avoid similar mistakes"""
        try:
            # Store failed pattern with negative importance
            if self.pattern_memory:
                pattern_vector = self._create_pattern_vector(market_conditions, 0.0, "failed", None, None)
                importance = -1.0  # Negative importance for failed patterns

                metadata = {
                    'trade_id': trade_id,
                    'pattern_type': 'failed',
                    'timestamp': datetime.now(),
                    'market_regime': self._detect_current_market_regime(market_conditions),
                    'avoid_pattern': True
                }

                if TORCH_AVAILABLE:
                    self.pattern_memory.store_pattern(
                        pattern=torch.tensor(pattern_vector, dtype=torch.float32),
                        importance=importance,
                        metadata=metadata
                    )

            logger.debug(f"Learned from failed pattern in trade {trade_id}")

        except Exception as e:
            logger.error(f"Error learning from failed pattern: {e}")

    async def _store_learning_insight(self, insight_type: str, content: str, confidence: float = 0.5):
        """Store learning insight in database"""
        try:
            insight_id = f"{insight_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO learning_insights
                    (insight_id, timestamp, insight_type, content, confidence)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    insight_id,
                    datetime.now().isoformat(),
                    insight_type,
                    content,
                    confidence
                ))
                conn.commit()

            logger.debug(f"Stored learning insight: {content}")

        except Exception as e:
            logger.error(f"Error storing learning insight: {e}")

    async def _trigger_neural_learning_update(self):
        """Trigger neural network learning update based on accumulated feedback"""
        try:
            if not self.neural_agent or not NEURAL_AVAILABLE:
                return

            feedback_data = list(self.neural_feedback_loop['recent_feedback'])

            # Prepare training data
            successful_trades = [f for f in feedback_data if f['success']]
            failed_trades = [f for f in feedback_data if not f['success']]

            if len(successful_trades) >= 5 and len(failed_trades) >= 5:
                # Trigger neural network retraining
                await self.neural_agent.update_from_feedback(feedback_data)

                # Clear processed feedback
                self.neural_feedback_loop['recent_feedback'].clear()

                logger.info("Triggered neural learning update with accumulated feedback")

        except Exception as e:
            logger.error(f"Error triggering neural learning update: {e}")

    async def _neural_batch_learning(self, trades: List[Dict]):
        """Perform batch learning with neural networks"""
        try:
            if not self.neural_agent or not NEURAL_AVAILABLE:
                return

            # Prepare batch training data
            training_data = []
            for trade in trades:
                training_data.append({
                    'success': trade['success'],
                    'pnl': trade['pnl'],
                    'strategy': trade['strategy'],
                    'timestamp': trade['timestamp']
                })

            # Trigger batch learning
            await self.neural_agent.batch_learn(training_data)

            logger.info(f"Completed neural batch learning with {len(trades)} trades")

        except Exception as e:
            logger.error(f"Error in neural batch learning: {e}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get overall performance summary across all strategies"""
        try:
            total_trades = sum(p.total_trades for p in self.strategy_performance.values())
            total_pnl = sum(p.total_pnl for p in self.strategy_performance.values())

            if total_trades == 0:
                return {'message': 'No trading data available yet'}

            weighted_win_rate = sum(
                p.win_rate * p.total_trades for p in self.strategy_performance.values()
            ) / total_trades

            best_strategy = max(
                self.strategy_performance.values(),
                key=lambda p: p.total_pnl if p.total_trades >= 5 else Decimal('-999999')
            )

            return {
                'total_trades': total_trades,
                'total_pnl': float(total_pnl),
                'overall_win_rate': weighted_win_rate,
                'best_strategy': {
                    'name': best_strategy.strategy_name,
                    'pnl': float(best_strategy.total_pnl),
                    'win_rate': best_strategy.win_rate,
                    'trades': best_strategy.total_trades
                },
                'strategies_count': len(self.strategy_performance),
                'learning_active': True
            }

        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {'message': f'Error: {e}'}

# Global instance for easy access
_live_learner = None

def get_live_learner() -> LiveTradingLearner:
    """Get or create global live trading learner instance"""
    global _live_learner
    if _live_learner is None:
        _live_learner = LiveTradingLearner()
    return _live_learner
