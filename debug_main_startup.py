#!/usr/bin/env python3
"""
Debug Main Startup - Find out why main.py is failing
"""

import os
import sys
import traceback
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def debug_main_startup():
    """Debug the main.py startup process"""
    print("🔧 [DEBUG] Starting main.py debug process...")
    
    # Set environment variables
    os.environ["BYBIT_ONLY_MODE"] = "true"
    os.environ["COINBASE_ENABLED"] = "false"
    os.environ["LIVE_TRADING"] = "true"
    os.environ["REAL_MONEY_TRADING"] = "true"
    
    print("✅ [DEBUG] Environment variables set")
    
    try:
        print("🔄 [DEBUG] Importing main module...")
        import main
        print("✅ [DEBUG] Main module imported successfully")
        
        print("🔄 [DEBUG] Checking main function...")
        if hasattr(main, 'main'):
            print("✅ [DEBUG] Main function found")
        else:
            print("❌ [DEBUG] Main function not found")
            return False
        
        print("🚀 [DEBUG] Starting main function...")
        
        # Try to run main with error handling
        try:
            import asyncio
            if asyncio.iscoroutinefunction(main.main):
                print("🔄 [DEBUG] Main is async, running with asyncio...")
                asyncio.run(main.main())
            else:
                print("🔄 [DEBUG] Main is sync, running directly...")
                main.main()
        except Exception as main_error:
            print(f"❌ [DEBUG] Main function failed: {main_error}")
            traceback.print_exc()
            return False
        
        print("✅ [DEBUG] Main function completed successfully")
        return True
        
    except ImportError as import_error:
        print(f"❌ [DEBUG] Import error: {import_error}")
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"❌ [DEBUG] Unexpected error: {e}")
        traceback.print_exc()
        return False

def test_basic_imports():
    """Test basic imports to see what's failing"""
    print("🔧 [TEST] Testing basic imports...")
    
    try:
        print("🔄 [TEST] Importing os...")
        import os
        print("✅ [TEST] os imported")
        
        print("🔄 [TEST] Importing sys...")
        import sys
        print("✅ [TEST] sys imported")
        
        print("🔄 [TEST] Importing asyncio...")
        import asyncio
        print("✅ [TEST] asyncio imported")
        
        print("🔄 [TEST] Importing dotenv...")
        from dotenv import load_dotenv
        print("✅ [TEST] dotenv imported")
        
        print("🔄 [TEST] Loading .env...")
        load_dotenv()
        print("✅ [TEST] .env loaded")
        
        print("🔄 [TEST] Importing pybit...")
        from pybit.unified_trading import HTTP
        print("✅ [TEST] pybit imported")
        
        print("🔄 [TEST] Testing Bybit connection...")
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        if api_key and api_secret:
            session = HTTP(api_key=api_key, api_secret=api_secret, testnet=False, recv_window=10000)
            balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
            if balance_response.get('retCode') == 0:
                balance = float(balance_response['result']['list'][0]['coin'][0]['walletBalance'])
                print(f"✅ [TEST] Bybit connection working - Balance: ${balance:.2f} USDT")
            else:
                print(f"⚠️ [TEST] Bybit connection issue: {balance_response}")
        else:
            print("⚠️ [TEST] Bybit credentials not found")
        
        return True
        
    except Exception as e:
        print(f"❌ [TEST] Basic import test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main debug execution"""
    print("=" * 60)
    print("🔧 MAIN.PY STARTUP DEBUG")
    print("🚨 FINDING WHY MAIN.PY FAILS")
    print("=" * 60)
    
    # Test 1: Basic imports
    print("\n🔍 [TEST 1] Testing basic imports...")
    basic_test = test_basic_imports()
    
    if not basic_test:
        print("❌ [FAILED] Basic imports failed")
        return False
    
    print("✅ [PASSED] Basic imports working")
    
    # Test 2: Main startup
    print("\n🔍 [TEST 2] Testing main.py startup...")
    main_test = debug_main_startup()
    
    if main_test:
        print("✅ [SUCCESS] Main.py startup successful")
        return True
    else:
        print("❌ [FAILED] Main.py startup failed")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 MAIN.PY DEBUG SUCCESSFUL")
    else:
        print("\n❌ MAIN.PY DEBUG FAILED")
    
    sys.exit(0 if success else 1)
