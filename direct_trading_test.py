#!/usr/bin/env python3
"""
Direct trading test that bypasses complex initialization
and focuses on executing actual trades
"""

import asyncio
import os
import sys
from decimal import Decimal
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def direct_trading_test():
    """Test direct trading functionality"""
    try:
        print("🚀 [DIRECT] Starting direct trading test...")
        
        # Import required components
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        from src.trading.balance_aware_order_manager import BalanceAwareOrderManager
        
        # Get credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            print("❌ [DIRECT] Missing Bybit credentials")
            return False
        
        print("✅ [DIRECT] Credentials available")
        
        # Initialize client with timeout
        print("🔧 [DIRECT] Initializing Bybit client...")
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        print("✅ [DIRECT] Bybit client created")
        
        # Test basic balance check with timeout
        print("💰 [DIRECT] Testing basic balance check...")
        try:
            usdt_balance = await asyncio.wait_for(
                bybit_client.get_balance('USDT'), 
                timeout=10.0
            )
            print(f"✅ [DIRECT] USDT balance: {usdt_balance}")
            
            if float(usdt_balance) < 5.0:
                print("⚠️ [DIRECT] Insufficient USDT balance for trading")
                return False
                
        except asyncio.TimeoutError:
            print("❌ [DIRECT] Balance check timed out")
            return False
        except Exception as e:
            print(f"❌ [DIRECT] Balance check error: {e}")
            return False
        
        # Initialize balance manager
        print("⚖️ [DIRECT] Initializing balance manager...")
        exchange_clients = {'bybit': bybit_client}
        
        balance_manager = BalanceAwareOrderManager(
            exchange_clients=exchange_clients,
            config={
                'balance_buffer': 0.05,
                'min_order_value': 5.0,
                'max_balance_usage': 0.8,
                'critical_threshold': 0.1,
                'sizing_strategy': 'dynamic',
                'aggressive_trading': True,
                'micro_trading': True,
                'balance_percentage': 0.8
            }
        )
        print("✅ [DIRECT] Balance manager created")
        
        # Test direct order execution
        print("🔄 [DIRECT] Testing direct order execution...")
        
        # Use 80% of USDT balance for a BUY order
        trade_amount = float(usdt_balance) * 0.8
        
        # Choose a popular trading pair
        symbol = 'BTCUSDT'
        side = 'buy'
        
        print(f"🔄 [DIRECT] Attempting to {side} ${trade_amount:.2f} of {symbol}")
        
        try:
            # Execute balance-aware order
            result = await asyncio.wait_for(
                balance_manager.execute_balance_aware_order(
                    symbol=symbol,
                    side=side,
                    amount=Decimal(str(trade_amount)),
                    exchange='bybit'
                ),
                timeout=30.0
            )
            
            if result.get('success', False):
                print("🎉 [DIRECT] Trade executed successfully!")
                print(f"✅ [DIRECT] Order result: {result}")
                
                # Check if it was a fallback strategy
                if 'fallback_strategy' in result:
                    print(f"🔄 [DIRECT] Used fallback strategy: {result['fallback_strategy']}")
                    print(f"🔄 [DIRECT] Original order: {result.get('original_order', 'N/A')}")
                    print(f"🔄 [DIRECT] Executed order: {result.get('executed_order', 'N/A')}")
                
                return True
            else:
                print(f"❌ [DIRECT] Trade failed: {result.get('error', 'Unknown error')}")
                
                # Check if alternatives were suggested
                if 'alternatives' in result:
                    print("🔄 [DIRECT] Suggested alternatives:")
                    for alt in result['alternatives']:
                        print(f"  💡 {alt}")
                
                return False
                
        except asyncio.TimeoutError:
            print("❌ [DIRECT] Trade execution timed out")
            return False
        except Exception as e:
            print(f"❌ [DIRECT] Trade execution error: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ [DIRECT] Error in direct trading test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(direct_trading_test())
    if success:
        print("🎉 [DIRECT] Direct trading test PASSED - System can execute real trades!")
    else:
        print("❌ [DIRECT] Direct trading test FAILED")
    sys.exit(0 if success else 1)
