#!/usr/bin/env python3
"""
Coinbase API Diagnostic Tool
Tests different API endpoints to identify the correct API type and permissions
"""
import os
import sys
import time
import jwt
import requests
from pathlib import Path
from cryptography.hazmat.primitives import serialization
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from src.utils.cryptography.secure_credentials import decrypt_value

def test_coinbase_endpoints():
    """Test multiple Coinbase API endpoints to identify the issue"""

    # Load environment
    load_dotenv()

    # Get encrypted credentials and decrypt them
    encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_CLIENT_API_KEY')
    encrypted_key_id = os.getenv('ENCRYPTED_COINBASE_KEY_ID')
    encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
    api_key_name = os.getenv('COINBASE_API_KEY')  # The organizations/xxx format (if set)

    # Decrypt credentials
    api_key = decrypt_value(encrypted_api_key) if encrypted_api_key else None
    key_id = decrypt_value(encrypted_key_id) if encrypted_key_id else None
    private_key_pem = decrypt_value(encrypted_private_key) if encrypted_private_key else None
    
    print("🔧 Coinbase API Diagnostic Tool")
    print("=" * 50)
    print(f"📋 Client API Key: {api_key[:20]}..." if api_key else "❌ No Client API Key")
    print(f"📋 Key ID: {key_id}" if key_id else "❌ No Key ID")
    print(f"📋 API Key Name: {api_key_name[:50]}..." if api_key_name else "❌ No API Key Name")
    print(f"📋 Private Key: {'✅ Present' if private_key_pem else '❌ Missing'}")
    print()

    # For CDP API, we need the organizations/xxx format as the API key name
    if not api_key_name and api_key:
        # Construct the API key name from the provided credentials
        api_key_name = f"organizations/7405b51f-cfea-4f54-a52d-02838b5cb217/apiKeys/{key_id}"
        print(f"🔧 Constructed API Key Name: {api_key_name}")

    if not all([api_key, key_id, private_key_pem]):
        print("❌ Missing required credentials!")
        return False

    try:
        # Load private key
        if not private_key_pem:
            print("❌ Private key is None after decryption!")
            return False

        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )
        print("✅ Private key loaded successfully")
        
        # Test different endpoints
        endpoints_to_test = [
            # Advanced Trade API endpoints
            {
                'name': 'Advanced Trade - Accounts',
                'path': '/api/v3/brokerage/accounts',
                'description': 'Coinbase Advanced Trade accounts endpoint'
            },
            {
                'name': 'Advanced Trade - Products', 
                'path': '/api/v3/brokerage/products',
                'description': 'Coinbase Advanced Trade products endpoint'
            },
            {
                'name': 'Advanced Trade - Portfolios',
                'path': '/api/v3/brokerage/portfolios',
                'description': 'Coinbase Advanced Trade portfolios endpoint'
            },
            # CDP API endpoints
            {
                'name': 'CDP - Portfolios',
                'path': '/api/v1/portfolios',
                'description': 'Coinbase Developer Platform portfolios'
            },
            # Legacy Exchange API endpoints
            {
                'name': 'Exchange - Accounts',
                'path': '/accounts',
                'description': 'Legacy Coinbase Pro/Exchange API'
            },
            {
                'name': 'Exchange - Products',
                'path': '/products',
                'description': 'Legacy Coinbase Pro/Exchange products'
            }
        ]
        
        print("\n🌐 Testing API Endpoints...")
        print("=" * 50)
        
        for endpoint in endpoints_to_test:
            print(f"\n🔍 Testing: {endpoint['name']}")
            print(f"📍 Path: {endpoint['path']}")
            print(f"📝 Description: {endpoint['description']}")
            
            success = test_single_endpoint(
                private_key,
                api_key_name,
                endpoint['path']
            )
            
            if success:
                print(f"✅ SUCCESS: {endpoint['name']} works!")
                print("🎯 This is the correct API type for your key")
            else:
                print(f"❌ FAILED: {endpoint['name']} - 401 Unauthorized")
        
        print("\n" + "=" * 50)
        print("📊 DIAGNOSTIC COMPLETE")
        print("💡 Check which endpoints returned SUCCESS above")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_single_endpoint(private_key, api_key_name, endpoint_path):
    """Test a single API endpoint with multiple authentication methods"""

    # Method 1: Try Coinbase Advanced Trade API format
    print("   🔧 Trying Advanced Trade API authentication...")
    success = test_advanced_trade_auth(private_key, api_key_name, endpoint_path)
    if success:
        return True

    # Method 2: Try CDP API format
    print("   🔧 Trying CDP API authentication...")
    success = test_cdp_auth(private_key, api_key_name, endpoint_path)
    if success:
        return True

    return False

def test_advanced_trade_auth(private_key, api_key_name, endpoint_path):
    """Test Coinbase Advanced Trade API authentication"""
    try:
        # Extract just the key ID from the full API key name
        key_id = api_key_name.split('/')[-1] if '/' in api_key_name else api_key_name

        # Create JWT for Advanced Trade API
        now = int(time.time())
        payload = {
            'iss': 'coinbase-cloud',
            'nbf': now,
            'exp': now + 120,  # 2 minutes
            'sub': key_id,  # Just the key ID, not the full path
            'uri': f'GET {endpoint_path}'
        }

        token = jwt.encode(
            payload,
            private_key,
            algorithm='ES256',
            headers={'kid': key_id, 'typ': 'JWT'}
        )

        return make_api_request(token, endpoint_path, "Advanced Trade")

    except Exception as e:
        print(f"   ❌ Advanced Trade auth failed: {e}")
        return False

def test_cdp_auth(private_key, api_key_name, endpoint_path):
    """Test CDP API authentication"""
    try:
        # Create JWT for CDP API
        now = int(time.time())
        payload = {
            'iss': 'cdp',
            'nbf': now,
            'exp': now + 120,  # 2 minutes
            'sub': api_key_name,  # Use the full organizations/xxx format
            'uri': f'GET {endpoint_path}'
        }

        token = jwt.encode(
            payload,
            private_key,
            algorithm='ES256',
            headers={'kid': api_key_name, 'typ': 'JWT'}
        )

        return make_api_request(token, endpoint_path, "CDP")

    except Exception as e:
        print(f"   ❌ CDP auth failed: {e}")
        return False

def make_api_request(token, endpoint_path, auth_type):
    """Make the actual API request"""
    try:
        # Test API call
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
            'User-Agent': f'AutoGPT-Trader-{auth_type}/1.0'
        }

        response = requests.get(
            f'https://api.coinbase.com{endpoint_path}',
            headers=headers,
            timeout=10
        )

        print(f"   📡 {auth_type} Status: {response.status_code}")

        if response.status_code == 200:
            print(f"   ✅ {auth_type} authentication SUCCESS!")
            return True
        elif response.status_code == 401:
            print(f"   ❌ {auth_type} authentication failed: 401 Unauthorized")
            return False
        else:
            print(f"   ⚠️  {auth_type} unexpected status: {response.status_code}")
            print(f"   📄 Response: {response.text[:100]}...")
            return False

    except Exception as e:
        print(f"   ❌ {auth_type} request failed: {e}")
        return False

if __name__ == "__main__":
    test_coinbase_endpoints()
