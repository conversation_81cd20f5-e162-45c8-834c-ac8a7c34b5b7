#!/usr/bin/env python3
"""
Debug Coinbase JWT to understand the exact issue
"""

import jwt
from cryptography.hazmat.primitives import serialization
import time
import secrets
import requests
import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_credentials():
    """Get and decrypt Coinbase credentials"""
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        # Get encrypted credentials
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        if not encrypted_api_key or not encrypted_private_key:
            print("❌ Missing encrypted credentials in .env file")
            return None, None, None
        
        # Decrypt credentials
        api_key = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)
        
        # Extract key ID
        if "/apiKeys/" in api_key:
            key_id = api_key.split("/apiKeys/")[1]
        else:
            print("❌ Cannot extract key ID from API key format")
            return None, None, None
        
        return api_key, private_key_pem, key_id
        
    except Exception as e:
        print(f"❌ Error loading credentials: {e}")
        return None, None, None

def test_different_jwt_formats():
    """Test different JWT formats to find the working one"""
    
    print("🔍 [DEBUG] Testing different JWT formats...")
    
    # Get credentials
    api_key, private_key_pem, key_id = get_credentials()
    if not api_key or not private_key_pem or not key_id:
        print("❌ Failed to load credentials")
        return False
    
    print(f"✅ [CREDENTIALS] API Key: {api_key}")
    print(f"✅ [CREDENTIALS] Key ID: {key_id}")
    print(f"✅ [CREDENTIALS] Private Key: {private_key_pem[:50]}...")
    
    # Load private key
    try:
        private_key = serialization.load_pem_private_key(private_key_pem.encode('utf-8'), password=None)
        print("✅ [PRIVATE-KEY] Loaded successfully")
    except Exception as e:
        print(f"❌ [PRIVATE-KEY] Failed to load: {e}")
        return False
    
    # Test parameters
    request_method = "GET"
    request_host = "api.coinbase.com"
    request_path = "/api/v3/brokerage/accounts"
    
    # Test different JWT formats
    test_cases = [
        {
            "name": "Support Format (kid=api_key)",
            "uri": f"{request_method} {request_host}{request_path}",
            "payload": {
                'sub': api_key,
                'iss': "cdp",
                'nbf': int(time.time()),
                'exp': int(time.time()) + 120,
                'uri': f"{request_method} {request_host}{request_path}",
            },
            "headers": {'kid': api_key, 'nonce': secrets.token_hex()}
        },
        {
            "name": "Support Format (kid=key_id)",
            "uri": f"{request_method} {request_host}{request_path}",
            "payload": {
                'sub': api_key,
                'iss': "cdp",
                'nbf': int(time.time()),
                'exp': int(time.time()) + 120,
                'uri': f"{request_method} {request_host}{request_path}",
            },
            "headers": {'kid': key_id, 'nonce': secrets.token_hex()}
        },
        {
            "name": "Original Format",
            "uri": f"{request_method} {request_path}",
            "payload": {
                'iss': 'cdp',
                'nbf': int(time.time()),
                'exp': int(time.time()) + 120,
                'sub': api_key,
                'uri': f"{request_method} {request_path}",
            },
            "headers": {'kid': key_id, 'typ': 'JWT', 'alg': 'ES256'}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 [TEST {i}] {test_case['name']}")
        print(f"📋 [URI] {test_case['uri']}")
        print(f"📋 [PAYLOAD] {json.dumps(test_case['payload'], indent=2)}")
        print(f"📋 [HEADERS] {json.dumps(test_case['headers'], indent=2)}")
        
        try:
            # Generate JWT
            jwt_token = jwt.encode(
                test_case['payload'],
                private_key,
                algorithm='ES256',
                headers=test_case['headers']
            )
            print(f"✅ [JWT] Generated: {jwt_token[:50]}...")
            
            # Make request
            headers = {
                'Authorization': f'Bearer {jwt_token}',
                'Content-Type': 'application/json'
            }
            
            full_url = f"https://{request_host}{request_path}"
            response = requests.get(full_url, headers=headers, timeout=30)
            
            print(f"📊 [RESPONSE] Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ [SUCCESS] Test case {i} WORKED!")
                data = response.json()
                print(f"📊 [DATA] {json.dumps(data, indent=2)}")
                return True
            else:
                print(f"❌ [FAILED] Status {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ [ERROR] Test case {i} failed: {e}")
    
    return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔍 COINBASE JWT DEBUG")
    print("=" * 60)
    
    success = test_different_jwt_formats()
    
    print("=" * 60)
    if success:
        print("✅ FOUND WORKING JWT FORMAT!")
    else:
        print("❌ NO WORKING JWT FORMAT FOUND")
    print("=" * 60)
