# src/crypto/decrypt.py
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.fernet import Fernet
import os

def decrypt_value(encrypted_value: str, private_key_path: str) -> str:
    """Hybrid decryption flow"""
    # Split into components
    encrypted_fernet_key_hex, encrypted_data = encrypted_value.split(":")
    encrypted_fernet_key = bytes.fromhex(encrypted_fernet_key_hex)
    
    # Load private key
    with open(private_key_path, "rb") as f:
        private_key = serialization.load_pem_private_key(
            f.read(),
            password=None  # Add password if key is encrypted
        )
    
    # Decrypt Fernet key
    fernet_key = private_key.decrypt(
        encrypted_fernet_key,
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
    )
    
    # Decrypt actual value
    return Fernet(fernet_key).decrypt(encrypted_data.encode()).decode()