#!/usr/bin/env python3
"""
Encrypt New ECDSA Coinbase API Credentials
==========================================

This script encrypts the new ECDSA-compatible Coinbase API credentials
and updates the .env file with the encrypted values.

New API Key Details:
- API Key ID: b71fc94b-f040-4d88-9435-7ee897421f33
- Full API Key: organizations/7405b51f-cfea-4f54-a52d-02838b5cb217/apiKeys/b71fc94b-f040-4d88-9435-7ee897421f33
- Algorithm: ECDSA (EC Private Key)
- Purpose: Replace Ed25519 credentials to match ECDSA infrastructure
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def encrypt_new_ecdsa_credentials():
    """Encrypt the new ECDSA Coinbase credentials and update .env file"""

    print("🔐 Encrypting New ECDSA Coinbase API Credentials")
    print("=" * 55)

    # New ECDSA credentials from Coinbase
    new_api_key_id = "b71fc94b-f040-4d88-9435-7ee897421f33"
    organization_id = "7405b51f-cfea-4f54-a52d-02838b5cb217"  # Same organization
    new_full_api_key = f"organizations/{organization_id}/apiKeys/{new_api_key_id}"
    new_secret = """**********************************************************************************************************************************************************************************************************************************"""

    print(f"📋 API Key ID: {new_api_key_id}")
    print(f"📋 Organization ID: {organization_id}")
    print(f"📋 Full API Key: {new_full_api_key}")
    print(f"📋 Secret: EC Private Key (ECDSA)")
    print(f"📋 Algorithm: ECDSA (Compatible with RSA infrastructure)")
    print()

    try:
        # Initialize crypto with RSA private key
        private_key_path = "src/utils/cryptography/private.pem"
        if not Path(private_key_path).exists():
            print(f"❌ Private key not found at {private_key_path}")
            print("   Make sure RSA private key is restored")
            return False

        from src.utils.cryptography.hybrid import HybridCrypto
        crypto = HybridCrypto(private_key_path)

        print(f"✅ Initialized HybridCrypto with {private_key_path}")

        # Encrypt the new credentials
        print("\n🔒 Encrypting credentials...")

        encrypted_full_api_key = crypto.encrypt_value(new_full_api_key)
        encrypted_secret = crypto.encrypt_value(new_secret)
        encrypted_client_api_key = crypto.encrypt_value(new_api_key_id)  # Just the key ID for client API
        encrypted_key_id = crypto.encrypt_value(new_api_key_id)  # Just the key ID

        print(f"✅ Encrypted Full API Key: {encrypted_full_api_key[:50]}...")
        print(f"✅ Encrypted Secret: {encrypted_secret[:50]}...")
        print(f"✅ Encrypted Client API Key: {encrypted_client_api_key[:50]}...")
        print(f"✅ Encrypted Key ID: {encrypted_key_id[:50]}...")

        # Update .env file
        print("\n📝 Updating .env file...")

        env_file = Path(".env")
        if not env_file.exists():
            print("❌ .env file not found")
            return False

        # Read current .env content
        with open(env_file, 'r') as f:
            content = f.read()

        # Replace the current encrypted values with new ones
        # Update the main API key and private key (lines 11-12)
        old_api_key_line = None
        old_private_key_line = None
        old_client_api_key_line = None
        old_key_id_line = None

        # Find current encrypted values
        for line in content.split('\n'):
            if line.startswith('ENCRYPTED_COINBASE_API_KEY_NAME='):
                old_api_key_line = line
            elif line.startswith('ENCRYPTED_COINBASE_PRIVATE_KEY='):
                old_private_key_line = line
            elif line.startswith('ENCRYPTED_COINBASE_CLIENT_API_KEY='):
                old_client_api_key_line = line
            elif line.startswith('ENCRYPTED_COINBASE_KEY_ID='):
                old_key_id_line = line

        # Replace with new encrypted values
        if old_api_key_line:
            content = content.replace(old_api_key_line, f"ENCRYPTED_COINBASE_API_KEY_NAME={encrypted_full_api_key}")
        if old_private_key_line:
            content = content.replace(old_private_key_line, f"ENCRYPTED_COINBASE_PRIVATE_KEY={encrypted_secret}")
        if old_client_api_key_line:
            content = content.replace(old_client_api_key_line, f"ENCRYPTED_COINBASE_CLIENT_API_KEY={encrypted_client_api_key}")
        if old_key_id_line:
            content = content.replace(old_key_id_line, f"ENCRYPTED_COINBASE_KEY_ID={encrypted_key_id}")

        # Write updated content
        with open(env_file, 'w') as f:
            f.write(content)

        print("✅ Successfully updated .env file with encrypted credentials")

        # Verify the encryption works by testing decryption
        print("\n🔍 Verifying encryption/decryption...")

        decrypted_full_api_key = crypto.decrypt_value(encrypted_full_api_key)
        decrypted_secret = crypto.decrypt_value(encrypted_secret)
        decrypted_client_api_key = crypto.decrypt_value(encrypted_client_api_key)
        decrypted_key_id = crypto.decrypt_value(encrypted_key_id)

        if (decrypted_full_api_key == new_full_api_key and
            decrypted_secret == new_secret and
            decrypted_client_api_key == new_api_key_id and
            decrypted_key_id == new_api_key_id):
            print("✅ Encryption/decryption verification successful!")
            print(f"   Decrypted Full API Key: {decrypted_full_api_key}")
            print(f"   Decrypted Secret: EC Private Key (verified)")
            print(f"   Decrypted Client API Key: {decrypted_client_api_key}")
            print(f"   Decrypted Key ID: {decrypted_key_id}")
        else:
            print("❌ Encryption/decryption verification failed!")
            return False

        print("\n🎉 SUCCESS! New ECDSA Coinbase credentials encrypted and stored")
        print("=" * 60)
        print("✅ Full API Key encrypted and stored in ENCRYPTED_COINBASE_API_KEY_NAME")
        print("✅ Secret encrypted and stored in ENCRYPTED_COINBASE_PRIVATE_KEY")
        print("✅ Client API Key encrypted and stored in ENCRYPTED_COINBASE_CLIENT_API_KEY")
        print("✅ Key ID encrypted and stored in ENCRYPTED_COINBASE_KEY_ID")
        print("✅ ECDSA algorithm matches RSA infrastructure")
        print("✅ Ready to test with your trading system")

        print("\nNext steps:")
        print("1. Run debug_jwt_token.py to test the new ECDSA authentication")
        print("2. The signature algorithm mismatch should now be resolved")
        print("3. You should see successful API calls instead of 401 errors")

        return True

    except Exception as e:
        print(f"❌ Error encrypting credentials: {e}")
        return False

if __name__ == "__main__":
    success = encrypt_new_ecdsa_credentials()
    sys.exit(0 if success else 1)