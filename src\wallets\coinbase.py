import os
import ccxt
import asyncio
from typing import Dict, Any
from cryptography.fernet import Fernet
from decimal import Decimal
import logging
import cbpro
from src.utils.rotate_keys import connect_vault

logger = logging.getLogger('CoinbaseTrader')

class CoinbaseTrader:
    def __init__(self):
        vault = connect_vault()
        secrets = vault.secrets.kv.v2.read_secret_version(
            path='coinbase/pro'
        )['data']['data']
        
        self.client = cbpro.AuthenticatedClient(
            secrets['api_key'],
            secrets['api_secret'],
            secrets['passphrase']
        )

    def get_balance(self):
        """Get REAL Coinbase Pro balances"""
        try:
            accounts = self.client.get_accounts()
            return {
                acc['currency']: float(acc['balance'])
                for acc in accounts if float(acc['balance']) > 0
            }
        except cbpro.exceptions.CoinbaseAPIError as e:
            raise ConnectionError(f"Coinbase API failure: {str(e)}")
        
    def _init_exchange(self) -> ccxt.coinbase:
        """Initialize authenticated exchange instance"""
        return ccxt.coinbase({
            'apiKey': self._decrypt('COINBASE_API_KEY'),
            'secret': self._decrypt('COINBASE_API_SECRET'),
            'password': self._decrypt('COINBASE_PASSPHRASE'),
            'enableRateLimit': True,
            'options': {
                'adjustForTimeDifference': True,
                'defaultType': 'spot'
            }
        })
    
    def _decrypt(self, env_var: str) -> str:
        encrypted = os.getenv(env_var)
        if not encrypted:
            raise ValueError(f"Missing {env_var} in environment")
        return self.fernet.decrypt(encrypted.encode()).decode()

    async def execute_order(self, symbol: str, side: str, amount: Decimal) -> Dict[str, Any]:
        """Execute order with full safety checks"""
        await self._validate_order(symbol, side, float(amount))  # Nu correct
        
        try:
            return await self.exchange.create_order(
                symbol=symbol,
                type='market',
                side=side,
                amount=float(amount))
        except ccxt.NetworkError as e:
            logger.error(f"Network error: {str(e)}")
            raise
        except ccxt.ExchangeError as e:
            logger.error(f"Exchange error: {str(e)}")
            raise
    
    async def _validate_order(self, symbol: str, side: str, amount: float):
        market = self.exchange.market(symbol)
        if not market['active']:
            raise ValueError(f"Market {symbol} not active")
            
        min_amount = float(market['limits']['amount']['min'])
        if amount < min_amount:
            raise ValueError(f"Amount {amount} below minimum {min_amount}")