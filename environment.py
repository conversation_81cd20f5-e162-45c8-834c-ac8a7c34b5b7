import numpy as np
import time

class TradingEnvironment:
    def __init__(self, market_symbol, exchange_client, sentiment_source=None):
        """
        market_symbol: e.g. 'BTC/USDT' or specific instrument identifier.
        exchange_client: an object with methods to get market data and execute trades (for Binance, Bybit, etc.).
        sentiment_source: optional object or function to get sentiment score.
        """
        self.symbol = market_symbol
        self.client = exchange_client
        self.sentiment_source = sentiment_source

        # Parameters for risk management
        self.volatility_window = 60  # in seconds, window to compute volatility
        self.price_history = deque(maxlen=self.volatility_window)
        self.last_price = None

    def get_observation(self):
        """
        Fetch latest market state and sentiment to form observation vector.
        This could include price history, volume, orderbook info, position, sentiment, etc.
        """
        # Example features: last price, short-term moving average, volatility, sentiment score
        price = self.client.get_price(self.symbol)  # current market price
        volume = self.client.get_volume(self.symbol)  # maybe current volume or last trade size
        self.price_history.append(price)
        if self.last_price is None:
            self.last_price = price
        # Compute short-term returns or price change
        price_change = price - self.last_price
        self.last_price = price
        # Compute volatility (std of price changes in window)
        vol = 0.0
        if len(self.price_history) > 1:
            prices = np.array(self.price_history)
            vol = float(np.std(prices))

        # Get sentiment score if available
        sentiment = 0.0
        if self.sentiment_source:
            try:
                sentiment = float(self.sentiment_source.get_sentiment(self.symbol))
            except Exception as e:
                sentiment = 0.0  # default if sentiment fetch fails

        # Example state vector [price, price_change, vol, sentiment]
        state = np.array([price, price_change, vol, sentiment], dtype=np.float32)
        return state

    def execute_action(self, action, agent: HFTAgent):
        """
        Execute the agent's action on the exchange, handle position and PnL.
        Returns reward and done flag.
        """
        reward = 0.0
        done = False  # in live trading, 'done' could signify a terminal condition or just end of episode placeholder

        # Determine trade parameters from action
        # If action is symbolic like 'BUY' or 'SELL':
        if isinstance(action, str):
            if action == "HOLD":
                # No trade, small negative reward to encourage finding opportunities
                reward = -0.001  # small penalty to avoid doing nothing (tunable)
            else:
                # Calculate position size:
                base_size = 1.0  # default base size (could be in quote currency or number of lots)
                # Adjust base size if needed (for example, a continuous action might dictate size)
                # ... If action is continuous target position, interpret accordingly ...

                # Risk throttle: adjust size based on volatility
                volatility_factor = 1.0
                # If volatility is high, reduce position
                state_vol = agent.balance * agent.risk_fraction  # using balance as proxy, refine as needed
                # Actually use environment volatility measure:
                if hasattr(self, 'price_history') and len(self.price_history) > 1:
                    prices = np.array(self.price_history)
                    current_vol = float(np.std(prices))
                    # Example threshold: if current volatility > 2% of price, scale down
                    if current_vol / (prices.mean() + 1e-9) > 0.02:
                        volatility_factor = 0.5  # reduce size by half in high volatility
                size = agent.scale_position(base_size, volatility_factor)

                # Execute trade via exchange client
                if action == "BUY":
                    # Place buy order of 'size'
                    fill_price = self.client.buy(self.symbol, size)
                    # Update agent position and balance
                    if agent.position_size == 0:
                        agent.position_price = fill_price
                    # accumulate position (for simplicity, treat as one position)
                    agent.position_size += size
                elif action == "SELL":
                    # Place sell order. If we have an open position, this might be closing it or shorting.
                    fill_price = self.client.sell(self.symbol, size)
                    # If there was an open long position, selling that size will reduce it or close it.
                    # For simplicity, assume we close any open position when sell is called.
                    if agent.position_size > 0:
                        # Calculate PnL from closing long position
                        pnl = (fill_price - agent.position_price) * min(size, agent.position_size)
                        agent.update_balance(pnl)
                        agent.position_size -= min(size, agent.position_size)
                        if agent.position_size == 0:
                            agent.position_price = None
                        reward = pnl  # reward is profit (could normalize or include transaction cost)
                    else:
                        # If no position (or already short), treat as entering a short (not detailed here)
                        agent.position_size -= size
                        agent.position_price = fill_price

        # If action is continuous (e.g., target position), we would interpret it as desired position size or similar.
        # ... (Implement logic to adjust current position towards target) ...

        # Optionally include risk-based termination: e.g., stop if balance drops too low (stop-loss of total strategy)
        if agent.balance <= 0:
            done = True

        return reward, done
