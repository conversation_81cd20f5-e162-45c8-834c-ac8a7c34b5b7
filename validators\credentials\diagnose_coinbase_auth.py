#!/usr/bin/env python3
"""
COINBASE AUTHENTICATION DIAGNOSTICS
===================================

Comprehensive diagnostics for Coinbase API authentication issues.

Author: AutoGPT Trader
Date: June 2025
"""

import os
import json
import requests
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("coinbase_diagnostics")

def check_ip_address():
    """Check current public IP address"""
    try:
        response = requests.get('https://api.ipify.org?format=json', timeout=10)
        ip_data = response.json()
        current_ip = ip_data.get('ip', 'Unknown')
        logger.info(f"🌐 [IP] Current public IP: {current_ip}")
        
        # Check if it matches the allowed IP
        allowed_ip = "************"
        if current_ip == allowed_ip:
            logger.info("✅ [IP] IP address matches Coinbase API restriction")
            return True
        else:
            logger.warning(f"⚠️ [IP] IP mismatch - Current: {current_ip}, Allowed: {allowed_ip}")
            return False
            
    except Exception as e:
        logger.error(f"❌ [IP] Failed to check IP address: {e}")
        return False

def diagnose_coinbase_credentials():
    """Comprehensive Coinbase credential diagnostics"""
    try:
        # Load credentials
        from credential_decryptor_fixed import setup_credentials
        
        logger.info("🔐 [DIAG] Loading encrypted credentials...")
        success = setup_credentials()
        
        if not success:
            logger.error("❌ [DIAG] Failed to load credentials")
            return False
        
        # Get credentials
        api_key_name = os.getenv('COINBASE_API_KEY_NAME')
        private_key = os.getenv('COINBASE_PRIVATE_KEY')
        
        if not api_key_name or not private_key:
            logger.error("❌ [DIAG] Coinbase credentials not found in environment")
            return False
        
        logger.info(f"✅ [DIAG] API Key Name: {api_key_name}")
        logger.info(f"✅ [DIAG] Private Key Length: {len(private_key)} characters")
        logger.info(f"✅ [DIAG] Private Key Format: {'✅ Valid PEM' if private_key.startswith('-----BEGIN') else '❌ Invalid'}")
        
        # Check API key format
        if "organizations/" in api_key_name and "/apiKeys/" in api_key_name:
            logger.info("✅ [DIAG] API key format is correct (CDP format)")
        else:
            logger.error("❌ [DIAG] API key format is incorrect")
            return False
        
        # Test different authentication methods
        logger.info("🧪 [TEST] Testing Coinbase authentication methods...")
        
        # Method 1: Direct REST client
        try:
            from coinbase.rest import RESTClient
            
            logger.info("📡 [TEST] Method 1: Direct REST client...")
            client = RESTClient(api_key=api_key_name, api_secret=private_key)
            
            # Test with a simple public endpoint first
            logger.info("🔍 [TEST] Testing public endpoint...")
            try:
                products = client.get_products(limit=1)
                logger.info("✅ [TEST] Public endpoint accessible")
            except Exception as e:
                logger.error(f"❌ [TEST] Public endpoint failed: {e}")
                return False
            
            # Test authenticated endpoint
            logger.info("🔐 [TEST] Testing authenticated endpoint...")
            try:
                accounts = client.get_accounts(limit=1)
                logger.info(f"✅ [TEST] Authenticated endpoint working - {len(accounts)} accounts found")
                
                # Show account details
                for account in accounts:
                    currency = account.get('currency', 'Unknown')
                    balance = account.get('available_balance', {}).get('value', '0')
                    logger.info(f"💰 [BALANCE] {currency}: {balance}")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ [TEST] Authenticated endpoint failed: {e}")
                
                # Check for specific error types
                error_str = str(e)
                if "401" in error_str or "Unauthorized" in error_str:
                    logger.error("🔑 [ERROR] Authentication failed - check API key permissions")
                elif "403" in error_str or "Forbidden" in error_str:
                    logger.error("🚫 [ERROR] Access forbidden - check IP restrictions")
                elif "429" in error_str:
                    logger.error("⏰ [ERROR] Rate limited - wait and try again")
                else:
                    logger.error(f"❓ [ERROR] Unknown error: {error_str}")
                
                return False
                
        except ImportError:
            logger.error("❌ [TEST] Coinbase library not available")
            return False
        except Exception as e:
            logger.error(f"❌ [TEST] REST client test failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ [DIAG] Diagnostics failed: {e}")
        return False

def check_coinbase_json_file():
    """Check the coinbase_cloud_api_key.json file"""
    try:
        json_file = Path("coinbase_cloud_api_key.json")
        if json_file.exists():
            with open(json_file, 'r') as f:
                data = json.load(f)
            
            logger.info("📄 [JSON] coinbase_cloud_api_key.json file found")
            logger.info(f"📄 [JSON] API Key: {data.get('name', 'Missing')}")
            logger.info(f"📄 [JSON] Private Key Length: {len(data.get('privateKey', ''))} chars")
            
            # Check if the JSON credentials match environment
            api_key_name = os.getenv('COINBASE_API_KEY_NAME')
            if data.get('name') == api_key_name:
                logger.info("✅ [JSON] JSON file matches environment credentials")
                return True
            else:
                logger.warning("⚠️ [JSON] JSON file doesn't match environment credentials")
                return False
        else:
            logger.warning("⚠️ [JSON] coinbase_cloud_api_key.json file not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ [JSON] Failed to check JSON file: {e}")
        return False

def main():
    """Main diagnostics function"""
    logger.info("🔍 [COINBASE] Starting comprehensive authentication diagnostics...")
    
    # Check IP address
    ip_ok = check_ip_address()
    
    # Check JSON file
    json_ok = check_coinbase_json_file()
    
    # Run credential diagnostics
    auth_ok = diagnose_coinbase_credentials()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 [SUMMARY] Coinbase Authentication Diagnostics Results:")
    logger.info(f"🌐 IP Address: {'✅ OK' if ip_ok else '❌ ISSUE'}")
    logger.info(f"📄 JSON File: {'✅ OK' if json_ok else '❌ ISSUE'}")
    logger.info(f"🔐 Authentication: {'✅ OK' if auth_ok else '❌ ISSUE'}")
    logger.info("="*60)
    
    if auth_ok:
        print("✅ Coinbase authentication is working correctly!")
        print("🚀 Ready for live trading with Coinbase Advanced API")
        return True
    else:
        print("❌ Coinbase authentication issues detected")
        if not ip_ok:
            print("🔧 SOLUTION: Update IP restriction at https://cloud.coinbase.com/access/api")
            print(f"   Add your current IP or use 0.0.0.0/0 for any IP")
        print("🔧 Check your API key permissions and IP restrictions")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
