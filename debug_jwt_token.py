#!/usr/bin/env python3
"""
JWT Token Debug Script
Analyzes the JWT token generation and structure to identify authentication issues
"""

import os
import sys
import time
import jwt
import json
import base64
from pathlib import Path
from cryptography.hazmat.primitives import serialization

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

def load_environment():
    """Load environment variables"""
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print(f"✅ Loaded environment from {env_file}")

def decode_jwt_token(token):
    """Decode JWT token without verification to inspect structure"""
    try:
        # Split token into parts
        header_b64, payload_b64, signature_b64 = token.split('.')
        
        # Decode header
        header_padded = header_b64 + '=' * (4 - len(header_b64) % 4)
        header = json.loads(base64.urlsafe_b64decode(header_padded))
        
        # Decode payload
        payload_padded = payload_b64 + '=' * (4 - len(payload_b64) % 4)
        payload = json.loads(base64.urlsafe_b64decode(payload_padded))
        
        return header, payload
    except Exception as e:
        print(f"❌ Failed to decode JWT: {e}")
        return None, None

def debug_jwt_generation():
    """Debug JWT token generation process"""
    print("🔍 JWT TOKEN GENERATION DEBUG")
    print("="*50)
    
    # Load credentials
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        api_key = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)
        
        print(f"📋 API Key: {api_key}")
        key_id = api_key.split("/apiKeys/")[1] if "/apiKeys/" in api_key else "UNKNOWN"
        print(f"📋 Key ID: {key_id}")
        
    except Exception as e:
        print(f"❌ Credential loading failed: {e}")
        return False
    
    # Analyze private key (this should be the Coinbase-provided EC private key)
    try:
        # The private_key_pem should contain the EC private key from Coinbase
        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )

        # Get key details
        key_size = private_key.key_size
        print(f"📋 Private Key Size: {key_size} bits")
        print(f"📋 Private Key Type: {type(private_key).__name__}")

        # Check if it's an EC key
        if hasattr(private_key, 'curve'):
            print(f"📋 Curve: {private_key.curve.name}")

        # Verify this is the Coinbase EC key, not our infrastructure key
        if not isinstance(private_key, type(private_key)) or not hasattr(private_key, 'curve'):
            print("⚠️ Warning: This doesn't appear to be an EC private key")
            print("   Make sure ENCRYPTED_COINBASE_PRIVATE_KEY contains the Coinbase EC private key")

    except Exception as e:
        print(f"❌ Private key analysis failed: {e}")
        print("💡 Make sure ENCRYPTED_COINBASE_PRIVATE_KEY contains the Coinbase-provided EC private key")
        return False
    
    # Generate and analyze JWT tokens
    now = int(time.time())
    
    # Test different JWT configurations
    test_configs = [
        {
            "name": "Coinbase Advanced Trading API",
            "payload": {
                'iss': 'cdp',
                'nbf': now,
                'exp': now + 120,
                'sub': api_key,
                'uri': 'GET /api/v3/brokerage/accounts'
            },
            "headers": {'kid': key_id}
        },
        {
            "name": "Coinbase Cloud API",
            "payload": {
                'iss': 'coinbase-cloud',
                'nbf': now,
                'exp': now + 120,
                'sub': api_key,
                'aud': ['retail_rest_api_proxy'],
                'uri': 'GET /api/v3/brokerage/accounts'
            },
            "headers": {'kid': key_id}
        },
        {
            "name": "CDP API with nonce",
            "payload": {
                'iss': 'cdp',
                'nbf': now,
                'exp': now + 120,
                'sub': api_key,
                'uri': 'GET /api/v3/brokerage/accounts'
            },
            "headers": {'kid': key_id, 'nonce': str(int(time.time() * 1000))}
        }
    ]
    
    for config in test_configs:
        print(f"\n🧪 Testing: {config['name']}")
        print("-" * 30)
        
        try:
            # Generate token
            token = jwt.encode(
                config['payload'], 
                private_key, 
                algorithm='ES256',
                headers=config['headers']
            )
            
            print(f"✅ Token generated successfully")
            print(f"📋 Token length: {len(token)} characters")
            
            # Decode and analyze token
            header, payload = decode_jwt_token(token)
            
            if header and payload:
                print(f"📋 Header: {json.dumps(header, indent=2)}")
                print(f"📋 Payload: {json.dumps(payload, indent=2)}")
                
                # Check token validity
                current_time = int(time.time())
                if payload.get('exp', 0) > current_time:
                    print(f"✅ Token is not expired (expires in {payload.get('exp', 0) - current_time} seconds)")
                else:
                    print(f"❌ Token is expired")
                
                if payload.get('nbf', 0) <= current_time:
                    print(f"✅ Token is valid now (nbf: {payload.get('nbf', 0)})")
                else:
                    print(f"❌ Token not valid yet")
            
            # Test token with a simple request
            import requests
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json',
                'User-Agent': 'AutoGPT-Trader/1.0'
            }
            
            # Try a simple endpoint
            try:
                response = requests.get(
                    'https://api.coinbase.com/api/v3/brokerage/accounts',
                    headers=headers,
                    timeout=10
                )
                
                print(f"📋 HTTP Response: {response.status_code} {response.reason}")
                
                if response.status_code == 401:
                    print(f"❌ Still getting 401 with this token format")
                    # Try to get more error details
                    try:
                        error_data = response.json()
                        print(f"📋 Error details: {json.dumps(error_data, indent=2)}")
                    except:
                        print(f"📋 Error text: {response.text}")
                elif response.status_code == 200:
                    print(f"✅ SUCCESS! This token format works!")
                    return True
                else:
                    print(f"⚠️ Unexpected response: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Request failed: {e}")
                
        except Exception as e:
            print(f"❌ Token generation failed: {e}")
    
    # Additional diagnostics
    print(f"\n🔍 ADDITIONAL DIAGNOSTICS")
    print("-" * 30)
    
    # Check if the API key format is exactly correct
    expected_format = "organizations/{org_id}/apiKeys/{key_id}"
    if api_key.count('/') == 3 and 'organizations/' in api_key and '/apiKeys/' in api_key:
        print("✅ API key format appears correct")
    else:
        print("❌ API key format may be incorrect")
        print(f"   Expected: {expected_format}")
        print(f"   Actual: {api_key}")
    
    # Check key ID format
    if len(key_id) == 36 and key_id.count('-') == 4:
        print("✅ Key ID format appears correct (UUID)")
    else:
        print("⚠️ Key ID format may be non-standard")
        print(f"   Length: {len(key_id)}, Dashes: {key_id.count('-')}")
    
    return False

def main():
    """Main function"""
    load_environment()
    success = debug_jwt_generation()
    
    if success:
        print("\n🎉 SUCCESS: Found working JWT configuration!")
    else:
        print("\n❌ FAILURE: No working JWT configuration found")
        print("\n💡 NEXT STEPS:")
        print("1. The API key configuration in the screenshot looks correct")
        print("2. All JWT formats are failing with 401 errors")
        print("3. This suggests either:")
        print("   - The API key needs to be regenerated")
        print("   - There's a delay in permission propagation")
        print("   - Coinbase has additional authentication requirements")
        print("   - The API endpoints have changed")
        print("\n🔧 RECOMMENDATION:")
        print("Try regenerating the API key in the Coinbase Developer Console")
        print("and ensure all permissions are enabled from the start.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
