"""
Intelligent Strategy Selector
Sophisticated strategy evaluation and selection mechanism for optimal trading performance
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from collections import deque, defaultdict
from enum import Enum
import json
import pickle
from pathlib import Path

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime classifications"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    BREAKOUT = "breakout"
    REVERSAL = "reversal"

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics for strategy evaluation"""
    win_rate: float = 0.0
    profit_loss: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    total_trades: int = 0
    avg_trade_duration: float = 0.0
    profit_factor: float = 0.0
    calmar_ratio: float = 0.0
    sortino_ratio: float = 0.0
    information_ratio: float = 0.0
    
    # Time-weighted metrics
    recent_24h_performance: float = 0.0
    recent_48h_performance: float = 0.0
    weekly_performance: float = 0.0
    
    # Risk metrics
    var_95: float = 0.0  # Value at Risk 95%
    expected_shortfall: float = 0.0
    volatility: float = 0.0
    
    # Consistency metrics
    consistency_score: float = 0.0
    stability_index: float = 0.0
    
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class MarketConditions:
    """Current market conditions for regime detection"""
    price_trend: float = 0.0
    volatility: float = 0.0
    volume_trend: float = 0.0
    momentum: float = 0.0
    rsi: float = 50.0
    bollinger_position: float = 0.0
    macd_signal: float = 0.0
    support_resistance_proximity: float = 0.0
    liquidity_score: float = 1.0
    news_sentiment: float = 0.0
    
    regime: MarketRegime = MarketRegime.SIDEWAYS
    confidence: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class StrategyScore:
    """Comprehensive strategy scoring"""
    strategy_name: str
    total_score: float
    confidence: float
    
    # Component scores
    performance_score: float = 0.0
    regime_fit_score: float = 0.0
    risk_adjusted_score: float = 0.0
    consistency_score: float = 0.0
    recent_performance_score: float = 0.0
    
    # Weights used
    weights: Dict[str, float] = field(default_factory=dict)
    
    # Supporting data
    expected_return: float = 0.0
    expected_risk: float = 0.0
    trade_probability: float = 0.0
    
    timestamp: datetime = field(default_factory=datetime.now)

class IntelligentStrategySelector:
    """
    Sophisticated strategy evaluation and selection mechanism
    """
    
    def __init__(self, strategies: Dict[str, Any], config: Dict[str, Any] = None):
        self.strategies = strategies
        self.config = config or {}
        
        # Performance tracking
        self.strategy_performance = {}
        self.trade_history = deque(maxlen=10000)  # Last 10k trades
        self.regime_history = deque(maxlen=1000)   # Last 1k regime observations
        
        # Strategy selection parameters
        self.min_confidence_threshold = self.config.get('min_confidence', 0.60)
        self.consensus_threshold = self.config.get('consensus_threshold', 0.70)
        self.learning_rate = self.config.get('learning_rate', 0.01)
        
        # Scoring weights (adaptive)
        self.scoring_weights = {
            'performance': 0.30,
            'regime_fit': 0.25,
            'risk_adjusted': 0.20,
            'consistency': 0.15,
            'recent_performance': 0.10
        }
        
        # Market regime detection
        self.regime_detector = MarketRegimeDetector()
        
        # Initialize performance tracking for each strategy
        for strategy_name in self.strategies.keys():
            self.strategy_performance[strategy_name] = {
                'metrics': PerformanceMetrics(),
                'regime_performance': defaultdict(lambda: PerformanceMetrics()),
                'trade_outcomes': deque(maxlen=1000),
                'last_evaluation': datetime.now()
            }
        
        # Persistence
        self.data_file = Path("data/strategy_selector_state.pkl")
        self.data_file.parent.mkdir(parents=True, exist_ok=True)
        self._load_state()
        
        logger.info("IntelligentStrategySelector initialized with adaptive learning")
    
    async def select_optimal_strategy(self, market_data: Dict[str, Any]) -> Optional[Tuple[str, StrategyScore]]:
        """
        Select the optimal strategy based on current market conditions and performance analysis
        """
        try:
            # 1. Detect current market regime
            market_conditions = await self.regime_detector.detect_regime(market_data)
            self.regime_history.append(market_conditions)
            
            # 2. Update performance metrics
            await self._update_performance_metrics()
            
            # 3. Score all strategies
            strategy_scores = await self._score_all_strategies(market_conditions)
            
            # 4. Select best strategy with confidence validation
            best_strategy = await self._select_best_strategy(strategy_scores)
            
            # 5. Validate with multi-strategy consensus if required
            if self.config.get('require_consensus', False):
                consensus_result = await self._validate_with_consensus(strategy_scores, market_conditions)
                if not consensus_result:
                    logger.info("No strategy consensus reached - holding position")
                    return None
            
            # 6. Log selection decision
            if best_strategy:
                strategy_name, score = best_strategy
                logger.info(f"🎯 Selected strategy: {strategy_name} (score: {score.total_score:.3f}, confidence: {score.confidence:.3f})")
                logger.info(f"🎯 Market regime: {market_conditions.regime.value} (confidence: {market_conditions.confidence:.3f})")
                
                # 7. Adaptive learning - adjust weights based on recent performance
                await self._adaptive_weight_adjustment()
            
            return best_strategy
            
        except Exception as e:
            logger.error(f"Error in strategy selection: {e}")
            return None
    
    async def _score_all_strategies(self, market_conditions: MarketConditions) -> Dict[str, StrategyScore]:
        """Score all available strategies"""
        strategy_scores = {}
        
        for strategy_name in self.strategies.keys():
            try:
                score = await self._calculate_strategy_score(strategy_name, market_conditions)
                strategy_scores[strategy_name] = score
            except Exception as e:
                logger.error(f"Error scoring strategy {strategy_name}: {e}")
                continue
        
        return strategy_scores
    
    async def _calculate_strategy_score(self, strategy_name: str, market_conditions: MarketConditions) -> StrategyScore:
        """Calculate comprehensive score for a strategy"""
        performance_data = self.strategy_performance[strategy_name]
        metrics = performance_data['metrics']
        regime_metrics = performance_data['regime_performance'][market_conditions.regime]
        
        # 1. Performance Score (30%)
        performance_score = self._calculate_performance_score(metrics)
        
        # 2. Regime Fit Score (25%)
        regime_fit_score = self._calculate_regime_fit_score(strategy_name, market_conditions, regime_metrics)
        
        # 3. Risk-Adjusted Score (20%)
        risk_adjusted_score = self._calculate_risk_adjusted_score(metrics)
        
        # 4. Consistency Score (15%)
        consistency_score = self._calculate_consistency_score(performance_data['trade_outcomes'])
        
        # 5. Recent Performance Score (10%)
        recent_performance_score = self._calculate_recent_performance_score(metrics)
        
        # Weighted total score
        total_score = (
            performance_score * self.scoring_weights['performance'] +
            regime_fit_score * self.scoring_weights['regime_fit'] +
            risk_adjusted_score * self.scoring_weights['risk_adjusted'] +
            consistency_score * self.scoring_weights['consistency'] +
            recent_performance_score * self.scoring_weights['recent_performance']
        )
        
        # Calculate confidence based on data quality and consistency
        confidence = self._calculate_selection_confidence(metrics, regime_metrics, market_conditions)
        
        return StrategyScore(
            strategy_name=strategy_name,
            total_score=total_score,
            confidence=confidence,
            performance_score=performance_score,
            regime_fit_score=regime_fit_score,
            risk_adjusted_score=risk_adjusted_score,
            consistency_score=consistency_score,
            recent_performance_score=recent_performance_score,
            weights=self.scoring_weights.copy(),
            expected_return=self._estimate_expected_return(strategy_name, market_conditions),
            expected_risk=self._estimate_expected_risk(strategy_name, market_conditions),
            trade_probability=self._estimate_trade_probability(strategy_name, market_conditions)
        )
    
    def _calculate_performance_score(self, metrics: PerformanceMetrics) -> float:
        """Calculate performance-based score"""
        if metrics.total_trades < 5:
            return 0.5  # Neutral score for insufficient data
        
        # Combine multiple performance metrics
        win_rate_score = min(metrics.win_rate * 2, 1.0)  # Cap at 1.0
        profit_score = np.tanh(metrics.profit_loss / 100) * 0.5 + 0.5  # Normalize around 0.5
        sharpe_score = np.tanh(metrics.sharpe_ratio / 2) * 0.5 + 0.5
        
        # Weight the components
        performance_score = (
            win_rate_score * 0.4 +
            profit_score * 0.4 +
            sharpe_score * 0.2
        )
        
        return np.clip(performance_score, 0.0, 1.0)
    
    def _calculate_regime_fit_score(self, strategy_name: str, market_conditions: MarketConditions, 
                                  regime_metrics: PerformanceMetrics) -> float:
        """Calculate how well strategy fits current market regime"""
        
        # Historical performance in this regime
        if regime_metrics.total_trades >= 3:
            regime_performance = self._calculate_performance_score(regime_metrics)
        else:
            # Use strategy's general affinity for this regime type
            regime_affinity = self._get_strategy_regime_affinity(strategy_name, market_conditions.regime)
            regime_performance = regime_affinity
        
        # Adjust based on regime detection confidence
        regime_confidence_factor = market_conditions.confidence
        
        return regime_performance * regime_confidence_factor + 0.5 * (1 - regime_confidence_factor)
    
    def _get_strategy_regime_affinity(self, strategy_name: str, regime: MarketRegime) -> float:
        """Get strategy's theoretical affinity for market regime"""
        affinities = {
            'momentum': {
                MarketRegime.TRENDING_UP: 0.9,
                MarketRegime.TRENDING_DOWN: 0.8,
                MarketRegime.BREAKOUT: 0.9,
                MarketRegime.SIDEWAYS: 0.3,
                MarketRegime.HIGH_VOLATILITY: 0.6,
                MarketRegime.REVERSAL: 0.4
            },
            'mean_reversion': {
                MarketRegime.SIDEWAYS: 0.9,
                MarketRegime.HIGH_VOLATILITY: 0.7,
                MarketRegime.REVERSAL: 0.8,
                MarketRegime.TRENDING_UP: 0.3,
                MarketRegime.TRENDING_DOWN: 0.3,
                MarketRegime.BREAKOUT: 0.2
            },
            'adaptive_neural': {
                MarketRegime.HIGH_VOLATILITY: 0.8,
                MarketRegime.TRENDING_UP: 0.7,
                MarketRegime.TRENDING_DOWN: 0.7,
                MarketRegime.BREAKOUT: 0.7,
                MarketRegime.SIDEWAYS: 0.6,
                MarketRegime.REVERSAL: 0.6
            }
        }
        
        return affinities.get(strategy_name, {}).get(regime, 0.5)
    
    def _calculate_risk_adjusted_score(self, metrics: PerformanceMetrics) -> float:
        """Calculate risk-adjusted performance score"""
        if metrics.total_trades < 5:
            return 0.5
        
        # Sharpe ratio component
        sharpe_component = np.tanh(metrics.sharpe_ratio / 2) * 0.5 + 0.5
        
        # Maximum drawdown component (lower is better)
        drawdown_component = max(0, 1 - metrics.max_drawdown / 20)  # Penalize >20% drawdown
        
        # Volatility component (moderate volatility preferred)
        volatility_component = 1 - abs(metrics.volatility - 0.15) / 0.15  # Optimal around 15%
        volatility_component = max(0, volatility_component)
        
        risk_adjusted_score = (
            sharpe_component * 0.5 +
            drawdown_component * 0.3 +
            volatility_component * 0.2
        )
        
        return np.clip(risk_adjusted_score, 0.0, 1.0)
    
    def _calculate_consistency_score(self, trade_outcomes: deque) -> float:
        """Calculate consistency of strategy performance"""
        if len(trade_outcomes) < 10:
            return 0.5
        
        # Convert to returns
        returns = [outcome.get('return', 0) for outcome in trade_outcomes]
        
        # Calculate consistency metrics
        returns_array = np.array(returns)
        
        # Standard deviation of returns (lower is more consistent)
        std_returns = np.std(returns_array)
        consistency_from_std = max(0, 1 - std_returns / 0.1)  # Normalize
        
        # Streak analysis (avoid long losing streaks)
        losing_streaks = self._calculate_losing_streaks(returns)
        max_losing_streak = max(losing_streaks) if losing_streaks else 0
        streak_penalty = max(0, 1 - max_losing_streak / 5)  # Penalize streaks >5
        
        # Positive return frequency
        positive_frequency = sum(1 for r in returns if r > 0) / len(returns)
        
        consistency_score = (
            consistency_from_std * 0.4 +
            streak_penalty * 0.3 +
            positive_frequency * 0.3
        )
        
        return np.clip(consistency_score, 0.0, 1.0)
    
    def _calculate_recent_performance_score(self, metrics: PerformanceMetrics) -> float:
        """Calculate score based on recent performance"""
        # Weight recent performance more heavily
        recent_24h_weight = 0.5
        recent_48h_weight = 0.3
        weekly_weight = 0.2
        
        # Normalize performance values
        perf_24h = np.tanh(metrics.recent_24h_performance / 5) * 0.5 + 0.5
        perf_48h = np.tanh(metrics.recent_48h_performance / 10) * 0.5 + 0.5
        perf_weekly = np.tanh(metrics.weekly_performance / 20) * 0.5 + 0.5
        
        recent_score = (
            perf_24h * recent_24h_weight +
            perf_48h * recent_48h_weight +
            perf_weekly * weekly_weight
        )
        
        return np.clip(recent_score, 0.0, 1.0)
    
    def _calculate_selection_confidence(self, metrics: PerformanceMetrics, 
                                      regime_metrics: PerformanceMetrics,
                                      market_conditions: MarketConditions) -> float:
        """Calculate confidence in strategy selection"""
        
        # Data quality factors
        total_trades_factor = min(metrics.total_trades / 50, 1.0)  # More trades = higher confidence
        regime_trades_factor = min(regime_metrics.total_trades / 10, 1.0)  # Regime-specific experience
        
        # Market condition clarity
        market_clarity_factor = market_conditions.confidence
        
        # Performance consistency
        consistency_factor = metrics.consistency_score
        
        # Recent performance stability
        recent_stability = 1 - abs(metrics.recent_24h_performance - metrics.weekly_performance) / 10
        recent_stability = max(0, recent_stability)
        
        confidence = (
            total_trades_factor * 0.25 +
            regime_trades_factor * 0.20 +
            market_clarity_factor * 0.25 +
            consistency_factor * 0.15 +
            recent_stability * 0.15
        )
        
        return np.clip(confidence, 0.0, 1.0)
    
    async def _select_best_strategy(self, strategy_scores: Dict[str, StrategyScore]) -> Optional[Tuple[str, StrategyScore]]:
        """Select the best strategy with confidence validation"""
        if not strategy_scores:
            return None
        
        # Sort by total score
        sorted_strategies = sorted(strategy_scores.items(), 
                                 key=lambda x: x[1].total_score, 
                                 reverse=True)
        
        best_strategy_name, best_score = sorted_strategies[0]
        
        # Validate confidence threshold
        if best_score.confidence < self.min_confidence_threshold:
            logger.info(f"Best strategy {best_strategy_name} confidence {best_score.confidence:.3f} below threshold {self.min_confidence_threshold}")
            return None
        
        # Check if significantly better than second best
        if len(sorted_strategies) > 1:
            second_best_score = sorted_strategies[1][1].total_score
            score_difference = best_score.total_score - second_best_score
            
            # Require minimum score difference for clear winner
            min_difference = self.config.get('min_score_difference', 0.05)
            if score_difference < min_difference:
                logger.info(f"Strategy scores too close: {best_score.total_score:.3f} vs {second_best_score:.3f}")
                return None
        
        return best_strategy_name, best_score

    async def _validate_with_consensus(self, strategy_scores: Dict[str, StrategyScore],
                                     market_conditions: MarketConditions) -> bool:
        """Validate strategy selection with multi-strategy consensus"""
        if len(strategy_scores) < 2:
            return True  # No consensus needed with single strategy

        # Get top strategies
        sorted_strategies = sorted(strategy_scores.items(),
                                 key=lambda x: x[1].total_score,
                                 reverse=True)

        top_strategies = sorted_strategies[:3]  # Top 3 strategies

        # Check for consensus (multiple strategies with similar scores)
        consensus_count = 0
        best_score = top_strategies[0][1].total_score

        for strategy_name, score in top_strategies:
            if score.total_score >= best_score * self.consensus_threshold:
                consensus_count += 1

        # Require at least 2 strategies in consensus
        return consensus_count >= 2

    async def _adaptive_weight_adjustment(self):
        """Adjust scoring weights based on recent performance"""
        try:
            # Analyze which scoring components correlate with actual performance
            recent_trades = list(self.trade_history)[-100:]  # Last 100 trades

            if len(recent_trades) < 20:
                return  # Insufficient data

            # Calculate correlation between score components and actual returns
            # This is a simplified version - in practice, you'd use more sophisticated analysis

            # Adjust weights slightly based on what's working
            adjustment_factor = self.learning_rate

            # Example: If recent performance component is correlating well with actual results,
            # increase its weight slightly
            performance_correlation = self._calculate_component_correlation('recent_performance', recent_trades)

            if performance_correlation > 0.3:  # Strong positive correlation
                self.scoring_weights['recent_performance'] += adjustment_factor
                self.scoring_weights['performance'] -= adjustment_factor * 0.5
                self.scoring_weights['regime_fit'] -= adjustment_factor * 0.5

            # Normalize weights to sum to 1.0
            total_weight = sum(self.scoring_weights.values())
            for key in self.scoring_weights:
                self.scoring_weights[key] /= total_weight

            logger.debug(f"Adjusted scoring weights: {self.scoring_weights}")

        except Exception as e:
            logger.error(f"Error in adaptive weight adjustment: {e}")

    def _calculate_component_correlation(self, component: str, trades: List[Dict]) -> float:
        """Calculate correlation between score component and actual performance"""
        # Simplified correlation calculation
        # In practice, you'd implement proper statistical correlation
        return 0.0  # Placeholder

    async def record_trade_outcome(self, strategy_name: str, trade_data: Dict[str, Any]):
        """Record trade outcome for performance tracking and learning"""
        try:
            # Extract trade information
            trade_outcome = {
                'strategy': strategy_name,
                'timestamp': trade_data.get('timestamp', datetime.now()),
                'symbol': trade_data.get('symbol'),
                'action': trade_data.get('action'),
                'quantity': trade_data.get('quantity', 0),
                'entry_price': trade_data.get('entry_price', 0),
                'exit_price': trade_data.get('exit_price', 0),
                'return': trade_data.get('return', 0),
                'duration': trade_data.get('duration', 0),
                'success': trade_data.get('success', False),
                'market_regime': trade_data.get('market_regime', MarketRegime.SIDEWAYS)
            }

            # Add to global trade history
            self.trade_history.append(trade_outcome)

            # Add to strategy-specific tracking
            if strategy_name in self.strategy_performance:
                self.strategy_performance[strategy_name]['trade_outcomes'].append(trade_outcome)

            # Update performance metrics
            await self._update_strategy_metrics(strategy_name, trade_outcome)

            # Save state periodically
            if len(self.trade_history) % 10 == 0:
                self._save_state()

            logger.info(f"Recorded trade outcome for {strategy_name}: {trade_outcome['return']:.2%} return")

        except Exception as e:
            logger.error(f"Error recording trade outcome: {e}")

    async def _update_strategy_metrics(self, strategy_name: str, trade_outcome: Dict[str, Any]):
        """Update comprehensive performance metrics for a strategy"""
        try:
            performance_data = self.strategy_performance[strategy_name]
            metrics = performance_data['metrics']

            # Update basic metrics
            metrics.total_trades += 1
            metrics.profit_loss += trade_outcome['return']

            if trade_outcome['success']:
                # Update win rate
                current_wins = metrics.win_rate * (metrics.total_trades - 1)
                metrics.win_rate = (current_wins + 1) / metrics.total_trades
            else:
                # Update win rate
                current_wins = metrics.win_rate * (metrics.total_trades - 1)
                metrics.win_rate = current_wins / metrics.total_trades

            # Update regime-specific metrics
            regime = trade_outcome.get('market_regime', MarketRegime.SIDEWAYS)
            regime_metrics = performance_data['regime_performance'][regime]
            regime_metrics.total_trades += 1
            regime_metrics.profit_loss += trade_outcome['return']

            # Update time-based performance
            trade_time = trade_outcome['timestamp']
            now = datetime.now()

            if now - trade_time <= timedelta(hours=24):
                metrics.recent_24h_performance += trade_outcome['return']
            if now - trade_time <= timedelta(hours=48):
                metrics.recent_48h_performance += trade_outcome['return']
            if now - trade_time <= timedelta(days=7):
                metrics.weekly_performance += trade_outcome['return']

            # Calculate advanced metrics (Sharpe ratio, drawdown, etc.)
            await self._calculate_advanced_metrics(strategy_name)

            metrics.last_updated = datetime.now()

        except Exception as e:
            logger.error(f"Error updating strategy metrics: {e}")

    async def _calculate_advanced_metrics(self, strategy_name: str):
        """Calculate advanced performance metrics"""
        try:
            performance_data = self.strategy_performance[strategy_name]
            metrics = performance_data['metrics']
            trade_outcomes = list(performance_data['trade_outcomes'])

            if len(trade_outcomes) < 5:
                return

            returns = [trade['return'] for trade in trade_outcomes]
            returns_array = np.array(returns)

            # Sharpe ratio
            if np.std(returns_array) > 0:
                metrics.sharpe_ratio = np.mean(returns_array) / np.std(returns_array) * np.sqrt(252)  # Annualized

            # Maximum drawdown
            cumulative_returns = np.cumsum(returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            metrics.max_drawdown = abs(np.min(drawdowns)) if len(drawdowns) > 0 else 0

            # Volatility
            metrics.volatility = np.std(returns_array) * np.sqrt(252)  # Annualized

            # Profit factor
            winning_trades = [r for r in returns if r > 0]
            losing_trades = [r for r in returns if r < 0]

            if losing_trades:
                metrics.profit_factor = sum(winning_trades) / abs(sum(losing_trades))
            else:
                metrics.profit_factor = float('inf') if winning_trades else 0

            # Consistency score
            metrics.consistency_score = self._calculate_consistency_score(performance_data['trade_outcomes'])

        except Exception as e:
            logger.error(f"Error calculating advanced metrics: {e}")

    async def _update_performance_metrics(self):
        """Update performance metrics for all strategies"""
        try:
            for strategy_name in self.strategies.keys():
                await self._calculate_advanced_metrics(strategy_name)
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    def _calculate_losing_streaks(self, returns: List[float]) -> List[int]:
        """Calculate losing streak lengths"""
        streaks = []
        current_streak = 0

        for return_val in returns:
            if return_val < 0:
                current_streak += 1
            else:
                if current_streak > 0:
                    streaks.append(current_streak)
                current_streak = 0

        if current_streak > 0:
            streaks.append(current_streak)

        return streaks

    def _estimate_expected_return(self, strategy_name: str, market_conditions: MarketConditions) -> float:
        """Estimate expected return for strategy in current conditions"""
        performance_data = self.strategy_performance[strategy_name]
        regime_metrics = performance_data['regime_performance'][market_conditions.regime]

        if regime_metrics.total_trades >= 5:
            return regime_metrics.profit_loss / regime_metrics.total_trades
        else:
            # Use overall performance
            general_metrics = performance_data['metrics']
            if general_metrics.total_trades >= 5:
                return general_metrics.profit_loss / general_metrics.total_trades
            else:
                return 0.0

    def _estimate_expected_risk(self, strategy_name: str, market_conditions: MarketConditions) -> float:
        """Estimate expected risk for strategy in current conditions"""
        performance_data = self.strategy_performance[strategy_name]
        regime_metrics = performance_data['regime_performance'][market_conditions.regime]

        if regime_metrics.total_trades >= 5:
            return regime_metrics.volatility
        else:
            general_metrics = performance_data['metrics']
            return general_metrics.volatility

    def _estimate_trade_probability(self, strategy_name: str, market_conditions: MarketConditions) -> float:
        """Estimate probability of strategy generating a trade signal"""
        # This would be based on historical data of signal generation frequency
        # For now, return a default value
        return 0.3  # 30% chance of generating a signal

    def _save_state(self):
        """Save selector state to disk"""
        try:
            state = {
                'strategy_performance': self.strategy_performance,
                'trade_history': list(self.trade_history),
                'regime_history': list(self.regime_history),
                'scoring_weights': self.scoring_weights,
                'last_saved': datetime.now()
            }

            with open(self.data_file, 'wb') as f:
                pickle.dump(state, f)

        except Exception as e:
            logger.error(f"Error saving state: {e}")

    def _load_state(self):
        """Load selector state from disk"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'rb') as f:
                    state = pickle.load(f)

                self.strategy_performance.update(state.get('strategy_performance', {}))
                self.trade_history.extend(state.get('trade_history', []))
                self.regime_history.extend(state.get('regime_history', []))
                self.scoring_weights.update(state.get('scoring_weights', {}))

                logger.info("Loaded strategy selector state from disk")

        except Exception as e:
            logger.error(f"Error loading state: {e}")

class MarketRegimeDetector:
    """Detect current market regime based on multiple indicators"""

    def __init__(self):
        self.lookback_period = 50
        self.volatility_threshold = 0.02
        self.trend_threshold = 0.01

    async def detect_regime(self, market_data: Dict[str, Any]) -> MarketConditions:
        """Detect current market regime"""
        try:
            # Extract price data
            price_data = market_data.get('price_data', {})

            # Initialize market conditions
            conditions = MarketConditions()

            # Analyze each exchange/symbol
            regime_votes = []

            for exchange, symbols in price_data.items():
                for symbol, data in symbols.items():
                    regime_vote = await self._analyze_symbol_regime(symbol, data)
                    if regime_vote:
                        regime_votes.append(regime_vote)

            # Aggregate regime votes
            if regime_votes:
                conditions = self._aggregate_regime_votes(regime_votes)

            return conditions

        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return MarketConditions()  # Default conditions

    async def _analyze_symbol_regime(self, symbol: str, data: Dict[str, Any]) -> Optional[MarketConditions]:
        """Analyze regime for a specific symbol"""
        try:
            # This is a simplified implementation
            # In practice, you'd use more sophisticated technical analysis

            current_price = data.get('price', 0)
            volume = data.get('volume', 0)
            change_24h = data.get('change_24h', 0)

            conditions = MarketConditions()

            # Simple trend detection
            if change_24h > self.trend_threshold:
                conditions.price_trend = 1.0
                conditions.regime = MarketRegime.TRENDING_UP
            elif change_24h < -self.trend_threshold:
                conditions.price_trend = -1.0
                conditions.regime = MarketRegime.TRENDING_DOWN
            else:
                conditions.price_trend = 0.0
                conditions.regime = MarketRegime.SIDEWAYS

            # Simple volatility detection
            volatility = abs(change_24h)
            if volatility > self.volatility_threshold:
                conditions.volatility = volatility
                if conditions.regime == MarketRegime.SIDEWAYS:
                    conditions.regime = MarketRegime.HIGH_VOLATILITY

            # Confidence based on data quality
            conditions.confidence = min(1.0, volume / 1000000)  # Higher volume = higher confidence

            return conditions

        except Exception as e:
            logger.error(f"Error analyzing regime for {symbol}: {e}")
            return None

    def _aggregate_regime_votes(self, regime_votes: List[MarketConditions]) -> MarketConditions:
        """Aggregate multiple regime votes into final assessment"""
        if not regime_votes:
            return MarketConditions()

        # Count regime votes
        regime_counts = defaultdict(int)
        total_confidence = 0

        for vote in regime_votes:
            regime_counts[vote.regime] += 1
            total_confidence += vote.confidence

        # Select most voted regime
        winning_regime = max(regime_counts.items(), key=lambda x: x[1])[0]

        # Calculate aggregate conditions
        conditions = MarketConditions()
        conditions.regime = winning_regime
        conditions.confidence = total_confidence / len(regime_votes)

        # Aggregate other metrics
        conditions.price_trend = np.mean([v.price_trend for v in regime_votes])
        conditions.volatility = np.mean([v.volatility for v in regime_votes])

        return conditions

# Factory function for easy integration
def create_intelligent_strategy_selector(strategies: Dict[str, Any], config: Dict[str, Any] = None) -> IntelligentStrategySelector:
    """Create and return an intelligent strategy selector"""
    return IntelligentStrategySelector(strategies, config)
