#!/usr/bin/env python3
"""
Encrypt New Coinbase API Credentials
====================================

This script encrypts the new Coinbase API credentials using the same HybridCrypto
system as the existing credentials in the .env file.

New Credentials (from regenerated API key):
- API Key ID: 6548b65d-0ea5-40f0-b2ac-c0c89ca1bcd2
- Secret: sc0cVMnuTbTi4h299X35+Mc2oSkhVMv+009T3H0k3Yx8iP5SOQSgkhtcC8pDPZQFeYXzF9pf0VJim6GDOJJHBw==
- Signature Algorithm: Ed25519 (Recommended)
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Encrypt new Coinbase credentials and update .env file"""
    
    # Load existing .env file
    load_dotenv()
    
    print("🔐 Encrypting New Coinbase API Credentials")
    print("=" * 50)
    
    # New credentials from regenerated API key
    new_api_key_id = "6548b65d-0ea5-40f0-b2ac-c0c89ca1bcd2"
    organization_id = "7405b51f-cfea-4f54-a52d-02838b5cb217"  # From old credentials
    new_full_api_key = f"organizations/{organization_id}/apiKeys/{new_api_key_id}"
    new_secret = "sc0cVMnuTbTi4h299X35+Mc2oSkhVMv+009T3H0k3Yx8iP5SOQSgkhtcC8pDPZQFeYXzF9pf0VJim6GDOJJHBw=="

    print(f"📋 API Key ID: {new_api_key_id}")
    print(f"📋 Organization ID: {organization_id}")
    print(f"📋 Full API Key: {new_full_api_key}")
    print(f"📋 Secret: {new_secret[:20]}...{new_secret[-10:]}")
    print(f"📋 Algorithm: Ed25519 (Recommended)")
    print()
    
    try:
        # Import the HybridCrypto system
        from src.utils.cryptography.hybrid import HybridCrypto
        
        # Initialize crypto with private key
        private_key_path = "src/utils/cryptography/private.pem"
        if not Path(private_key_path).exists():
            print(f"❌ Private key not found at {private_key_path}")
            print("   Make sure private.pem exists in src/utils/cryptography/")
            return False
            
        crypto = HybridCrypto(private_key_path)
        print(f"✅ Initialized HybridCrypto with {private_key_path}")
        
        # Encrypt the new credentials
        print("\n🔒 Encrypting credentials...")

        encrypted_full_api_key = crypto.encrypt_value(new_full_api_key)
        encrypted_secret = crypto.encrypt_value(new_secret)
        encrypted_client_api_key = crypto.encrypt_value(new_api_key_id)  # Just the key ID for client API
        encrypted_key_id = crypto.encrypt_value(new_api_key_id)  # Just the key ID

        print(f"✅ Encrypted Full API Key: {encrypted_full_api_key[:50]}...")
        print(f"✅ Encrypted Secret: {encrypted_secret[:50]}...")
        print(f"✅ Encrypted Client API Key: {encrypted_client_api_key[:50]}...")
        print(f"✅ Encrypted Key ID: {encrypted_key_id[:50]}...")
        
        # Update .env file
        print("\n📝 Updating .env file...")
        
        env_path = Path(".env")
        if not env_path.exists():
            print("❌ .env file not found")
            return False
            
        # Read current .env content
        with open(env_path, 'r') as f:
            content = f.read()
        
        # Replace the current encrypted values with new ones
        # Update the main API key and private key (lines 11-12)
        content = content.replace(
            "ENCRYPTED_COINBASE_API_KEY_NAME=gAAAAABoUGu7zlEwvRYiJVcLO7JinmLu25Rbe0Yb1WvKmLPhA3pPdMT7ZKlJXmlU0MLDX_I3bN0TVa0XVPZYAYF-3gOZMM-92tR0Htu4Iuz-knDsvXRVSzb58E25BfitsszhlqD-G4iv",
            f"ENCRYPTED_COINBASE_API_KEY_NAME={encrypted_full_api_key}"
        )
        content = content.replace(
            "ENCRYPTED_COINBASE_PRIVATE_KEY=gAAAAABoUGu7zaTM5SxDEvKHnP7Y61efi558MYOaYmS7ObkpE9FcVZc19qlOvtJYGa_ZVgOdEz4clh5VecyQU1TDVOma8OcJlrjaiZ3szvN-Ln6S9GdOwibmSl2PbbFTntXRc6qEA_NMrsqnwca5Zo_8vqqWRmUiiZvGF0KjgMOrb2G6PZaOpTUWSEaqYYDU1kimR0ZvtYJH",
            f"ENCRYPTED_COINBASE_PRIVATE_KEY={encrypted_secret}"
        )

        # Update the client API key and key ID (lines 36-37)
        content = content.replace(
            "ENCRYPTED_COINBASE_CLIENT_API_KEY=gAAAAABoTzrDvoAQa0jsROp86gWnDznUGKPNtfk8e9ZDn2Ud5EcS0vl84chH5RbDfGxFDHdYwTuyJdK9o81q0NKmgLsJECgvu7Dz200c4XeSSF1dm9KgNd2ejwNadwgvp86-EX45As-p",
            f"ENCRYPTED_COINBASE_CLIENT_API_KEY={encrypted_client_api_key}"
        )
        content = content.replace(
            "ENCRYPTED_COINBASE_KEY_ID=gAAAAABoTzrDOVES6IelbkLuvJmkIn8LA9czhPZJqY4jwGS3M0IdUT7LO2n5-DYT9Rq95Q5Ymf1jMQ87KKcOwp3j3a-5EhrkhJZyj_ZgZGTHLVpywqQGJJNw1IOwAFc5fDGmIhUnIY-m",
            f"ENCRYPTED_COINBASE_KEY_ID={encrypted_key_id}"
        )
        
        # Write updated content
        with open(env_path, 'w') as f:
            f.write(content)
        
        print("✅ Successfully updated .env file with encrypted credentials")
        
        # Verify the encryption works by testing decryption
        print("\n🔍 Verifying encryption/decryption...")

        decrypted_full_api_key = crypto.decrypt_value(encrypted_full_api_key)
        decrypted_secret = crypto.decrypt_value(encrypted_secret)
        decrypted_client_api_key = crypto.decrypt_value(encrypted_client_api_key)
        decrypted_key_id = crypto.decrypt_value(encrypted_key_id)

        if (decrypted_full_api_key == new_full_api_key and
            decrypted_secret == new_secret and
            decrypted_client_api_key == new_api_key_id and
            decrypted_key_id == new_api_key_id):
            print("✅ Encryption/decryption verification successful!")
            print(f"   Decrypted Full API Key: {decrypted_full_api_key}")
            print(f"   Decrypted Secret: {decrypted_secret[:20]}...{decrypted_secret[-10:]}")
            print(f"   Decrypted Client API Key: {decrypted_client_api_key}")
            print(f"   Decrypted Key ID: {decrypted_key_id}")
        else:
            print("❌ Encryption/decryption verification failed!")
            print(f"   Expected Full API Key: {new_full_api_key}")
            print(f"   Decrypted Full API Key: {decrypted_full_api_key}")
            print(f"   Expected Client API Key: {new_api_key_id}")
            print(f"   Decrypted Client API Key: {decrypted_client_api_key}")
            return False
        
        print("\n🎉 SUCCESS! New Coinbase credentials encrypted and stored")
        print("=" * 50)
        print("✅ Full API Key encrypted and stored in ENCRYPTED_COINBASE_API_KEY_NAME")
        print("✅ Secret encrypted and stored in ENCRYPTED_COINBASE_PRIVATE_KEY")
        print("✅ Client API Key encrypted and stored in ENCRYPTED_COINBASE_CLIENT_API_KEY")
        print("✅ Key ID encrypted and stored in ENCRYPTED_COINBASE_KEY_ID")
        print("✅ Ready to test with your trading system")
        print("\nNext steps:")
        print("1. Run debug_jwt_token.py to test the new credentials")
        print("2. Start your trading system - it should automatically detect working auth")
        print("3. Monitor for successful API calls instead of 401 errors")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure you're running from the project root directory")
        return False
    except Exception as e:
        print(f"❌ Encryption error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
