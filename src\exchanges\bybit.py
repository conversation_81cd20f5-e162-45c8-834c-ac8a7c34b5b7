# src/exchanges/bybit.py
from pybit.unified_trading import HTTP
from src.vault_client import VaultClient

class BybitTrader:
    def __init__(self):
        vault = VaultClient()
        credentials = vault.get_exchange_secret('bybit')
        
        self.session = HTTP(
            api_key=credentials['api_key'],
            api_secret=credentials['api_secret'],
            testnet=False  # Set True for demo account
        )

    def get_balance(self, coin: str = "USDT") -> dict:
        """Retrieve wallet balance"""
        try:
            response = self.session.get_wallet_balance(accountType="UNIFIED", coin=coin)
            return response['result']['list'][0]['coin'][0]['walletBalance']
        except Exception as e:
            raise TradingError(f"Bybit balance check failed: {str(e)}")