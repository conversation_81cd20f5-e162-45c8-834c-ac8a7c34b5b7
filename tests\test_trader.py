import pytest
from decimal import Decimal
from unittest.mock import AsyncMock, patch
from src.core.trader import LiveCryptoTrader, TradeResult
import aiohttp

@pytest.fixture
async def mock_trader():
    """Async fixture providing a trader instance with mocked exchange"""
    trader = LiveCryptoTrader("binance")
    
    # Mock the client session
    async with aiohttp.ClientSession() as session:
        trader.client.session = session
        trader.client.execute_order = AsyncMock()
        yield trader

@pytest.fixture
async def event_loop():
    """Create and provide an event loop for async tests"""
    import asyncio
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

class TestLiveCryptoTrader:
    @pytest.mark.asyncio
    async def test_initialization(self):
        """Verify trader initializes with correct exchange"""
        async with aiohttp.ClientSession() as session:
            trader = LiveCryptoTrader("coinbase")
            trader.client.session = session
            assert trader.exchange == "coinbase"
            assert trader.client is not None

    @pytest.mark.asyncio
    async def test_successful_trade(self, mock_trader):
        """Test successful order execution"""
        # Configure mock response
        mock_trader.client.execute_order.return_value = {
            "id": "12345",
            "price": "50000.00",
            "filled": "0.01",
            "timestamp": 1234567890
        }

        result = await mock_trader.place_order(
            symbol="BTC-USDT",
            side="buy",
            quantity=Decimal("0.01")
        )

        assert result.success is True
        assert result.order_id == "12345"

    @pytest.mark.asyncio
    async def test_failed_trade(self, mock_trader):
        """Test failed order execution"""
        mock_trader.client.execute_order.side_effect = Exception("API Error")

        result = await mock_trader.place_order(
            symbol="BTC-USDT",
            side="buy",
            quantity=Decimal("0.01")
        )

        assert result.success is False
        assert "API Error" in result.error