import os
import asyncio
from decimal import Decimal
from binance import AsyncClient, BinanceRequestException
from dotenv import load_dotenv
import logging

logger = logging.getLogger('trading.core')

class FirstTrade:
    def __init__(self):
        self.client = None
        self.logger = logger.getChild('first_trade')
        
    async def __aenter__(self):
        try:
            self.client = await AsyncClient.create(
                os.getenv('BINANCE_API_KEY'),
                os.getenv('BINANCE_API_SECRET'),
                testnet=config['exchanges']['binance']['testnet']
            )
            return self
        except Exception as e:
            self.logger.error("Connection failed: %s", str(e))
            raise

    async def __aexit__(self, exc_type, exc, tb):
        try:
            await self.client.close_connection()
        except Exception as e:
            self.logger.error("Disconnection error: %s", str(e))

    async def execute_test_trade(self) -> bool:
        """Execute minimal test trade with comprehensive safety checks"""
        try:
            # Validate balance
            balance = await self.client.get_asset_balance(asset='USDT')
            if float(balance['free']) < 10:
                raise ValueError("Insufficient test balance (<$10)")

            # Get current price with slippage buffer
            ticker = await self.client.get_symbol_ticker(symbol='BTCUSDT')
            price = Decimal(ticker['price']) * Decimal('0.99')  # 1% below market
            
            # Place test order
            order = await self.client.create_order(
                symbol='BTCUSDT',
                side='BUY',
                type='LIMIT',
                timeInForce='GTC',
                quantity=Decimal('0.0001'),
                price=str(price.quantize(Decimal('0.00'))),
                newOrderRespType='FULL'
            )
            
            self.logger.info("Test order placed: %s", order)
            return True
            
        except BinanceRequestException as e:
            self.logger.error("API error: %s", e.message)
            return False
        except Exception as e:
            self.logger.error("Trade failed: %s", str(e), exc_info=True)
            return False

async def main():
    async with FirstTrade() as trader:
        if await trader.execute_test_trade():
            logger.info("First trade executed successfully")

# Updated LiveTradingSession with FIX and Distributed Locking
class LiveTradingSession:
    def __init__(self, config: dict):
        self.fix_engine = InstitutionalFIXEngine(config['fix'])
        self.lock_manager = DistributedLockManager(config['redis_uri'])
        # ... existing init
        
    async def execute_block_order(self, order: dict):
        """Institutional order handling"""
        async with self.lock_manager.acquire_lock(f"order_{order['symbol']}"):
            if order['type'] == 'INSTITUTIONAL':
                clord_id = await self.fix_engine.send_order(
                    order['venue'], order)
                return await self._track_institutional_order(clord_id)
            else:
                return await self.execute_retail_order(order)

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())