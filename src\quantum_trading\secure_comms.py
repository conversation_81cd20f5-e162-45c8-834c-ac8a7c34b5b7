# secure_comms.py - Post-Quantum secure communication module for federated learning updates.
import os
import base64
import logging
import pickle  # for optional serialization of complex objects

# Try importing post-quantum cryptography KEM and symmetric encryption libraries.
PQ_ENABLED = True
CRYPTO_ENABLED = True
try:
    # Use a lattice-based KEM (Kyber-1024 equivalent for strong security)
    from pqcrypto.kem.ml_kem_1024 import generate_keypair, encrypt, decrypt
except ImportError:
    PQ_ENABLED = False
    logging.warning("secure_comms: pqcrypto library not found. Post-quantum encryption disabled.")
try:
    from cryptography.fernet import Fernet
except ImportError:
    CRYPTO_ENABLED = False
    logging.warning("secure_comms: cryptography library not found. Symmetric encryption disabled.")

# Load aggregator server URL and optional client identifier from environment variables.
AGGREGATOR_URL = os.getenv("FEDERATED_SERVER_URL") or os.getenv("AGGREGATOR_URL") or os.getenv("FL_SERVER_URL")
CLIENT_ID = os.getenv("CLIENT_ID")

# Load aggregator's public key (for encrypting data to send to server).
aggregator_public_key = None
if PQ_ENABLED:
    agg_key_b64 = os.getenv("AGGREGATOR_PUBLIC_KEY")
    agg_key_file = os.getenv("AGGREGATOR_PUBLIC_KEY_FILE")
    try:
        if agg_key_b64:
            # Provided as base64 string in environment.
            aggregator_public_key = base64.b64decode(agg_key_b64.encode())
        elif agg_key_file and os.path.exists(agg_key_file):
            # Provided as file path, read bytes from file.
            with open(agg_key_file, "rb") as f:
                key_data = f.read()
                try:
                    aggregator_public_key = base64.b64decode(key_data)
                except Exception:
                    aggregator_public_key = key_data
        else:
            aggregator_public_key = None
    except Exception as e:
        aggregator_public_key = None
        logging.error(f"secure_comms: Failed to load aggregator public key - {e}")
else:
    aggregator_public_key = None

# Generate a static client key pair (to decrypt responses if the server sends encrypted data back).
CLIENT_PUBLIC_KEY = None
CLIENT_SECRET_KEY = None
if PQ_ENABLED:
    try:
        CLIENT_PUBLIC_KEY, CLIENT_SECRET_KEY = generate_keypair()
        logging.debug("secure_comms: Client KEM key pair generated.")
        # Note: The client public key should be shared with the server (out-of-band) if the server needs to send encrypted responses.
    except Exception as e:
        logging.error(f"secure_comms: Error generating client KEM key pair - {e}")
        CLIENT_PUBLIC_KEY = None
        CLIENT_SECRET_KEY = None

def encrypt_message(message_bytes):
    # Encrypt a message with the aggregator's public key using post-quantum KEM + symmetric encryption.
    if not (PQ_ENABLED and CRYPTO_ENABLED and aggregator_public_key):
        logging.error("secure_comms: Encryption not available. Data will not be encrypted.")
        return None, None
    try:
        # Derive a shared secret and ciphertext via KEM
        ciphertext, shared_secret = encrypt(aggregator_public_key)
        # Ensure shared_secret is 32 bytes for symmetric key (Fernet requires 32 bytes key)
        if len(shared_secret) != 32:
            import hashlib
            shared_key_32 = hashlib.sha256(shared_secret).digest()
        else:
            shared_key_32 = shared_secret
        # Initialize Fernet symmetric cipher with the shared key
        fernet_key = base64.urlsafe_b64encode(shared_key_32)
        f = Fernet(fernet_key)
        # Encrypt the message bytes
        encrypted_message = f.encrypt(message_bytes)
        # Encode outputs in base64 for safe transport
        ciphertext_b64 = base64.b64encode(ciphertext).decode()
        encrypted_message_b64 = encrypted_message.decode()
        return ciphertext_b64, encrypted_message_b64
    except Exception as e:
        logging.error(f"secure_comms: Encryption error - {e}", exc_info=True)
        return None, None

def decrypt_message(ciphertext_b64, encrypted_message_b64):
    # Decrypt a message from the aggregator using the client's secret key.
    if not (PQ_ENABLED and CRYPTO_ENABLED and CLIENT_SECRET_KEY):
        logging.error("secure_comms: Decryption not available.")
        return None
    try:
        # Decode the base64 inputs
        ciphertext = base64.b64decode(ciphertext_b64.encode())
        encrypted_message = encrypted_message_b64.encode()
        # Recover shared secret using our secret key and the received ciphertext
        shared_secret = decrypt(CLIENT_SECRET_KEY, ciphertext)
        # Ensure 32-byte key
        if len(shared_secret) != 32:
            import hashlib
            shared_key_32 = hashlib.sha256(shared_secret).digest()
        else:
            shared_key_32 = shared_secret
        # Decrypt with Fernet
        fernet_key = base64.urlsafe_b64encode(shared_key_32)
        f = Fernet(fernet_key)
        original_message = f.decrypt(encrypted_message)
        return original_message
    except Exception as e:
        logging.error(f"secure_comms: Decryption error - {e}", exc_info=True)
        return None

def send_update(update_data):
    # Securely send a federated learning update to the aggregator.
    if AGGREGATOR_URL is None or aggregator_public_key is None:
        # Server or key not configured, skip sending.
        return False
    try:
        # Convert update_data to bytes
        if isinstance(update_data, bytes):
            data_bytes = update_data
        elif isinstance(update_data, str):
            data_bytes = update_data.encode()
        else:
            data_bytes = pickle.dumps(update_data)
        # Encrypt the update payload
        ct_b64, enc_msg_b64 = encrypt_message(data_bytes)
        if ct_b64 is None or enc_msg_b64 is None:
            logging.error("secure_comms: Encryption failed, update not sent.")
            return False
        # Prepare request payload (JSON format)
        payload = {"ct": ct_b64, "data": enc_msg_b64}
        if CLIENT_ID:
            payload["client_id"] = CLIENT_ID
        import requests
        response = requests.post(AGGREGATOR_URL, json=payload, timeout=5)
        if response.status_code != 200:
            logging.warning(f"secure_comms: Update send returned status {response.status_code}")
            return False
        logging.info("secure_comms: Federated learning update sent to server.")
        return True
    except Exception as e:
        logging.error(f"secure_comms: Failed to send update - {e}", exc_info=True)
        return False

def fetch_global_update():
    # Fetch the latest global model update from the aggregator (if available) and decrypt it.
    if AGGREGATOR_URL is None:
        return None
    try:
        import requests
        # We assume the aggregator provides an endpoint for global model (e.g., base_url/global)
        url = AGGREGATOR_URL.rstrip('/') + '/global'
        params = {}
        if CLIENT_ID:
            params["client_id"] = CLIENT_ID
        response = requests.get(url, params=params, timeout=5)
        if response.status_code != 200:
            return None
        global_data = None
        try:
            data_json = response.json()
            if isinstance(data_json, dict) and "ct" in data_json and "data" in data_json:
                # Decrypt the encrypted global model payload
                global_data = decrypt_message(data_json["ct"], data_json["data"])
            else:
                # If response is not an encrypted JSON, use raw content
                global_data = response.content if response.content else None
        except ValueError:
            # Non-JSON response, treat as raw content
            global_data = response.content if response.content else None
        if global_data:
            logging.info("secure_comms: Global model update received from server.")
        return global_data
    except Exception as e:
        logging.error(f"secure_comms: Failed to fetch global update - {e}", exc_info=True)
        return None
