#!/usr/bin/env python3
"""
Debug script to investigate Bybit symbol validation failures.
This script will directly test the Bybit API and symbol validation logic.
"""

import asyncio
import json
import sys
import os
from decimal import Decimal

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(__file__))

try:
    from src.exchanges.bybit_client_fixed import BybitClientFixed
    from src.utils.config_loader import load_config
except ImportError as e:
    print(f"❌ [IMPORT-ERROR] {e}")
    print("Trying alternative import method...")
    import importlib.util

    # Try to load config manually
    config_path = os.path.join(os.path.dirname(__file__), 'src', 'utils', 'config_loader.py')
    if os.path.exists(config_path):
        spec = importlib.util.spec_from_file_location("config_loader", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        load_config = config_module.load_config
    else:
        print("❌ [FATAL] Cannot find config_loader.py")
        sys.exit(1)

async def debug_bybit_symbols():
    """Debug Bybit symbol validation issues"""
    print("🔍 [DEBUG] Starting Bybit symbol validation debug...")
    
    try:
        # Load configuration
        config = load_config()
        bybit_config = config.get('exchanges', {}).get('bybit', {})
        
        if not bybit_config.get('api_key') or not bybit_config.get('api_secret'):
            print("❌ [ERROR] Bybit credentials not found in config")
            return
            
        # Initialize Bybit client
        print("🔧 [DEBUG] Initializing Bybit client...")
        client = BybitClientFixed(
            api_key=bybit_config['api_key'],
            api_secret=bybit_config['api_secret'],
            testnet=False
        )
        
        print("✅ [DEBUG] Bybit client initialized")
        
        # Test 1: Check if get_all_instruments method exists
        print("\n📋 [TEST-1] Checking available methods...")
        methods = [method for method in dir(client) if not method.startswith('_')]
        instrument_methods = [method for method in methods if 'instrument' in method.lower()]
        print(f"Available instrument-related methods: {instrument_methods}")
        
        # Test 2: Try to get instruments using different methods
        print("\n📋 [TEST-2] Testing instrument fetching methods...")
        
        # Method 1: Try get_all_instruments if it exists
        if hasattr(client, 'get_all_instruments'):
            print("✅ [METHOD-1] get_all_instruments exists")
            try:
                instruments = client.get_all_instruments("spot")
                print(f"✅ [METHOD-1] Found {len(instruments)} instruments")
                if instruments:
                    print(f"✅ [METHOD-1] Sample instrument: {instruments[0].get('symbol', 'N/A')}")
            except Exception as e:
                print(f"❌ [METHOD-1] Error: {e}")
        else:
            print("❌ [METHOD-1] get_all_instruments does NOT exist")
        
        # Method 2: Try get_instruments_info
        if hasattr(client, 'get_instruments_info'):
            print("✅ [METHOD-2] get_instruments_info exists")
            try:
                result = client.get_instruments_info()
                print(f"✅ [METHOD-2] Result type: {type(result)}")
                if isinstance(result, dict) and 'list' in result:
                    instruments = result['list']
                    print(f"✅ [METHOD-2] Found {len(instruments)} instruments")
                    if instruments:
                        print(f"✅ [METHOD-2] Sample instrument: {instruments[0].get('symbol', 'N/A')}")
                elif isinstance(result, dict) and 'error' in result:
                    print(f"❌ [METHOD-2] Error: {result['error']}")
                else:
                    print(f"❌ [METHOD-2] Unexpected result format: {result}")
            except Exception as e:
                print(f"❌ [METHOD-2] Error: {e}")
        else:
            print("❌ [METHOD-2] get_instruments_info does NOT exist")
        
        # Method 3: Try direct session call
        if hasattr(client, 'session'):
            print("✅ [METHOD-3] session exists")
            try:
                response = client.session.get_instruments_info(category="spot")
                print(f"✅ [METHOD-3] Response type: {type(response)}")
                if response and response.get("retCode") == 0:
                    instruments = response.get("result", {}).get("list", [])
                    print(f"✅ [METHOD-3] Found {len(instruments)} instruments via session")
                    if instruments:
                        print(f"✅ [METHOD-3] Sample instrument: {instruments[0].get('symbol', 'N/A')}")
                        print(f"✅ [METHOD-3] Sample status: {instruments[0].get('status', 'N/A')}")
                else:
                    print(f"❌ [METHOD-3] API error: {response}")
            except Exception as e:
                print(f"❌ [METHOD-3] Error: {e}")
        else:
            print("❌ [METHOD-3] session does NOT exist")
        
        # Test 3: Test specific symbol validation
        print("\n🎯 [TEST-3] Testing specific symbol validation...")
        test_symbols = ['BTCUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT', 'ETHUSDT']
        
        for symbol in test_symbols:
            print(f"\n🔍 [SYMBOL-TEST] Testing {symbol}...")
            
            # Test normalization
            try:
                normalized = client._normalize_symbol(symbol)
                print(f"  ✅ Normalized: {symbol} -> {normalized}")
            except Exception as e:
                print(f"  ❌ Normalization error: {e}")
                continue
            
            # Test validation
            try:
                is_valid = client._is_valid_bybit_symbol(normalized)
                print(f"  {'✅' if is_valid else '❌'} Validation: {normalized} -> {is_valid}")
            except Exception as e:
                print(f"  ❌ Validation error: {e}")
            
            # Test price fetching
            try:
                price = client.get_price(symbol)
                print(f"  ✅ Price: {symbol} -> ${price}")
            except Exception as e:
                print(f"  ❌ Price error: {e}")
        
        # Test 4: Check cache state
        print("\n💾 [TEST-4] Checking cache state...")
        if hasattr(client, '_exchange_symbols_cache'):
            cache = client._exchange_symbols_cache
            if cache:
                print(f"✅ [CACHE] Exchange symbols cache has {len(cache)} symbols")
                sample_symbols = list(cache)[:10]
                print(f"✅ [CACHE] Sample cached symbols: {sample_symbols}")
                
                # Check if our test symbols are in cache
                for symbol in test_symbols:
                    in_cache = symbol in cache
                    print(f"  {'✅' if in_cache else '❌'} {symbol} in cache: {in_cache}")
            else:
                print("❌ [CACHE] Exchange symbols cache is empty")
        else:
            print("❌ [CACHE] No exchange symbols cache found")
        
        print("\n🏁 [DEBUG] Symbol validation debug completed!")
        
    except Exception as e:
        print(f"❌ [FATAL] Debug script error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_bybit_symbols())
