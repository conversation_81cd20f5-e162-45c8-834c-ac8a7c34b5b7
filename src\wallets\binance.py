import os
from decimal import Decimal
from binance import AsyncClient

class BinanceTrader:
    def __init__(self):
        self.client = AsyncClient(
            os.getenv('BINANCE_API_KEY'),
            os.getenv('BINANCE_API_SECRET')
        )
    
    
    async def execute_order(self, symbol: str, side: str, amount: Decimal) -> dict:
        """Implementation for Binance"""
        return await self.client.create_order(
            symbol=symbol,
            side=side,
            type='MARKET',
            quantity=float(amount))
    
    async def get_balance(self, currency: str) -> Decimal:
        balance = await self.client.get_asset_balance(asset=currency)
        return Decimal(balance['free'])
