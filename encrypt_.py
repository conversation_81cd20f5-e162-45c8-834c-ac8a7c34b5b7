# encrypt_secrets.py
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.fernet import <PERSON>rnet
import os

# Load public key
with open("public.pem", "rb") as f:
    public_key = serialization.load_pem_public_key(f.read())

# Load Fernet key
fernet_key = os.getenv('FERNET_KEY').encode()  # From .fernet_key file
cipher = Fernet(fernet_key)

# Encrypt secrets asymmetrically
def encrypt_secret(value: str) -> str:
    encrypted_fernet_key = public_key.encrypt(
        fernet_key,
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
    )
    encrypted_value = cipher.encrypt(value.encode())
    return f"{encrypted_fernet_key.hex()}:{encrypted_value.decode()}"

# Usage (store output in .env)
print(f"ENCRYPTED_COINBASE_KEY={encrypt_secret('your_api_key_here')}")
print(f"ENCRYPTED_COINBASE_SECRET={encrypt_secret('your_api_secret_here')}")