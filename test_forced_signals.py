#!/usr/bin/env python3
"""
Test Forced Signal Generation for Micro-Trading
Direct test of the enhanced signal generator with forced signals
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_forced_signal_generation():
    """Test the forced signal generation directly"""
    print("🔧 [TEST] Testing forced signal generation for micro-trading...")
    
    # Setup environment
    os.environ["BYBIT_ONLY_MODE"] = "true"
    os.environ["COINBASE_ENABLED"] = "false"
    os.environ["LIVE_TRADING"] = "true"
    os.environ["MICRO_TRADING_MODE"] = "true"
    os.environ["FORCE_SIGNAL_GENERATION"] = "true"
    
    try:
        from trading.enhanced_signal_generator import EnhancedSignalGenerator
        from dotenv import load_dotenv
        
        load_dotenv()
        
        # Create a mock exchange manager
        class MockExchangeManager:
            def __init__(self):
                pass

            async def get_all_trading_pairs(self):
                return {}

            async def get_all_trading_exchanges(self):
                return ['bybit']
        
        # Initialize signal generator
        exchange_manager = MockExchangeManager()
        signal_generator = EnhancedSignalGenerator(exchange_manager)
        
        print(f"✅ [TEST] Signal generator initialized")
        print(f"   Min confidence: {signal_generator.min_confidence}")
        print(f"   Min position value: {signal_generator.min_position_value_usd}")
        
        # Test forced signal generation directly
        print("\n🚨 [TEST] Testing forced micro signal generation...")
        
        trading_exchanges = ['bybit']
        market_data = {
            'price_data': {
                'bybit': {
                    'BTCUSDT': {'price': 95000, 'change_24h': 2.5},
                    'ETHUSDT': {'price': 3500, 'change_24h': 1.8},
                    'SOLUSDT': {'price': 180, 'change_24h': -0.5}
                }
            }
        }
        
        # Call the forced signal generation method directly
        forced_signals = await signal_generator._generate_forced_micro_signals(trading_exchanges, market_data)
        
        print(f"🚨 [RESULT] Generated {len(forced_signals)} forced signals")
        
        for signal_id, signal in forced_signals.items():
            print(f"   Signal: {signal_id}")
            print(f"      Symbol: {signal.get('symbol', 'N/A')}")
            print(f"      Action: {signal.get('action', 'N/A')}")
            print(f"      Amount: {signal.get('amount', 0):.6f}")
            print(f"      Price: ${signal.get('price', 0):.2f}")
            print(f"      Exchange: {signal.get('exchange', 'N/A')}")
            print(f"      Confidence: {signal.get('confidence', 0):.2f}")
            print(f"      Strategy: {signal.get('strategy', 'N/A')}")
            print(f"      Forced: {signal.get('forced', False)}")
        
        # Test the full signal generation with forced fallback
        print("\n🔍 [TEST] Testing full signal generation with forced fallback...")
        
        all_signals = await signal_generator.generate_reliable_signals(market_data, {})
        
        print(f"📊 [RESULT] Total signals generated: {len(all_signals)}")
        
        for signal_id, signal in all_signals.items():
            print(f"   Signal: {signal_id}")
            print(f"      Symbol: {signal.get('symbol', 'N/A')}")
            print(f"      Action: {signal.get('action', 'N/A')}")
            print(f"      Amount: {signal.get('amount', 0):.6f}")
            print(f"      Confidence: {signal.get('confidence', 0):.2f}")
            print(f"      Forced: {signal.get('forced', False)}")
        
        if len(all_signals) > 0:
            print("✅ [SUCCESS] Signal generation working - signals produced!")
            return True
        else:
            print("❌ [FAILED] No signals generated - forced generation not working")
            return False
            
    except Exception as e:
        print(f"❌ [ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_micro_trade_execution():
    """Test executing a micro trade with the generated signals"""
    print("\n🚨 [TRADE] Testing micro trade execution...")
    
    try:
        from pybit.unified_trading import HTTP
        from dotenv import load_dotenv
        
        load_dotenv()
        
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        session = HTTP(api_key=api_key, api_secret=api_secret, testnet=False, recv_window=10000)
        
        # Get balance
        balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
        if balance_response.get('retCode') != 0:
            print(f"❌ [ERROR] Balance check failed: {balance_response}")
            return False
        
        balance = float(balance_response['result']['list'][0]['coin'][0]['walletBalance'])
        print(f"💰 [BALANCE] Current: ${balance:.2f} USDT")
        
        if balance < 0.80:
            print(f"❌ [ERROR] Insufficient balance: ${balance:.2f}")
            return False
        
        # Execute micro trade (85% of balance)
        trade_amount = balance * 0.85
        symbol = "BTCUSDT"
        action = "Buy"
        
        print(f"🚨 [EXECUTING] {action} ${trade_amount:.2f} USDT of {symbol}...")
        
        order = session.place_order(
            category="spot",
            symbol=symbol,
            side=action,
            orderType="Market",
            qty=f"{trade_amount:.2f}",
            isLeverage=0,
            orderFilter="Order"
        )
        
        if order.get('retCode') == 0:
            order_id = order['result']['orderId']
            print(f"✅ [SUCCESS] Micro trade executed: {order_id}")
            print(f"💰 [SUCCESS] {action} ${trade_amount:.2f} USDT of {symbol}")
            return True
        else:
            print(f"❌ [FAILED] Micro trade failed: {order}")
            return False
            
    except Exception as e:
        print(f"❌ [ERROR] Micro trade execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test execution"""
    print("=" * 60)
    print("🎯 FORCED SIGNAL GENERATION TEST")
    print("💰 MICRO-TRADING WITH $0.91 BALANCE")
    print("🚨 TESTING RELAXED PARAMETERS")
    print("=" * 60)
    
    # Test 1: Forced signal generation
    signal_test_success = asyncio.run(test_forced_signal_generation())
    
    if signal_test_success:
        print("\n✅ [SUCCESS] Forced signal generation working!")
        
        # Test 2: Micro trade execution
        print("\n🚨 [NEXT] Testing micro trade execution...")
        trade_test_success = asyncio.run(test_micro_trade_execution())
        
        if trade_test_success:
            print("\n🎉 [SUCCESS] ALL TESTS PASSED")
            print("✅ Forced signal generation working")
            print("✅ Micro trade execution working")
            print("✅ Ready for continuous micro-trading")
            return True
        else:
            print("\n⚠️ [PARTIAL] Signal generation working, trade execution failed")
            return False
    else:
        print("\n❌ [FAILED] Forced signal generation not working")
        return False

if __name__ == "__main__":
    print("🚨 WARNING: This will test forced signal generation and execute ONE micro trade")
    print("🚨 WARNING: Real money trading will be tested")
    
    import time
    print("\n⏳ Starting in 3 seconds...")
    for i in range(3, 0, -1):
        print(f"⏳ {i}...")
        time.sleep(1)
    
    print("\n🚀 STARTING FORCED SIGNAL TEST...")
    
    success = main()
    if success:
        print("\n🎉 FORCED SIGNAL GENERATION READY FOR MAIN SYSTEM")
    else:
        print("\n❌ FORCED SIGNAL GENERATION NEEDS FURTHER FIXES")
    
    sys.exit(0 if success else 1)
