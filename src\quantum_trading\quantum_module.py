import numpy as np

class AnomalyDetector:
    def __init__(self, threshold=3.0):
        """
        threshold: number of standard deviations for considering an anomaly.
        In a quantum-enhanced version, this could use a quantum model instead.
        """
        self.threshold = threshold
        self.recent_window = []

    def update(self, value):
        """Update with a new market metric (e.g., price change or volume surge)."""
        self.recent_window.append(value)
        if len(self.recent_window) > 100:
            self.recent_window.pop(0)

    def anomaly_score(self):
        """Compute anomaly score (e.g., how many std devs away the latest value is)."""
        if len(self.recent_window) < 2:
            return 0.0
        data = np.array(self.recent_window)
        mean = data.mean()
        std = data.std() + 1e-8
        latest = data[-1]
        score = abs(latest - mean) / std
        return score

    def is_anomaly(self):
        """Check if latest data point is an anomaly."""
        return self.anomaly_score() > self.threshold

class MetaStrategyController:
    def __init__(self, agent_normal: HFTAgent, agent_high_vol: HFTAgent):
        """
        agent_normal: primary agent for normal market conditions.
        agent_high_vol: secondary agent for high volatility or anomalies.
        Both agents could be initialized with same structure but trained on different data regimes.
        """
        self.normal_agent = agent_normal
        self.high_vol_agent = agent_high_vol
        self.current_mode = 'normal'

    def choose_action(self, state, anomaly_detected=False):
        """
        Choose action from appropriate agent based on anomaly flag.
        If anomaly detected, switch to high_vol mode, otherwise normal.
        """
        if anomaly_detected:
            self.current_mode = 'high_vol'
            action, logp, val = self.high_vol_agent.select_action(state)
        else:
            self.current_mode = 'normal'
            action, logp, val = self.normal_agent.select_action(state)
        # We return the selected action and also logp, val for storage if needed
        return action, logp, val
