#!/usr/bin/env python3
"""
Test Cross-Currency Arbitrage Engine

This script tests the cross-currency arbitrage engine to ensure it can
properly detect and execute triangular arbitrage, cross-exchange arbitrage,
and cross-currency arbitrage opportunities.
"""

import asyncio
import logging
import sys
import os
from decimal import Decimal

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_cross_currency_arbitrage():
    """Test the cross-currency arbitrage engine functionality"""
    try:
        logger.info("🧪 [TEST] Starting cross-currency arbitrage engine test...")
        
        # Import required modules
        from src.trading.cross_currency_arbitrage_engine import CrossCurrencyArbitrageEngine, ArbitrageType
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            logger.error("❌ [TEST] Missing Bybit API credentials")
            return False
        
        # Initialize exchange clients
        logger.info("🔧 [TEST] Initializing exchange clients...")
        
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        
        if not bybit_client.session:
            logger.error("❌ [TEST] Failed to initialize Bybit client")
            return False
        
        exchange_clients = {
            'bybit': bybit_client
        }
        
        # Initialize arbitrage engine
        logger.info("🔄 [TEST] Initializing cross-currency arbitrage engine...")
        
        arbitrage_engine = CrossCurrencyArbitrageEngine(
            exchange_clients=exchange_clients,
            config={
                'min_profit_threshold': 0.001,  # 0.1% for testing
                'max_execution_time': 10.0,     # 10 seconds for testing
                'min_capital': 5.0               # $5 minimum for testing
            }
        )
        
        # Test 1: Initialize arbitrage engine
        logger.info("🧪 [TEST-1] Testing arbitrage engine initialization...")
        await arbitrage_engine.initialize()
        
        if not arbitrage_engine.currency_graphs:
            logger.error("❌ [TEST-1] No currency graphs built")
            return False
        
        logger.info(f"✅ [TEST-1] Built currency graphs for {len(arbitrage_engine.currency_graphs)} exchanges")
        
        # Show currency graph details
        for exchange_name, graph in arbitrage_engine.currency_graphs.items():
            logger.info(f"✅ [TEST-1] {exchange_name}: {len(graph.nodes)} currencies, {len(graph.edges)} pairs")
            
            # Show some example currencies and pairs
            example_currencies = list(graph.nodes)[:5]
            example_pairs = list(graph.edges.keys())[:5]
            
            logger.info(f"✅ [TEST-1] Example currencies: {example_currencies}")
            logger.info(f"✅ [TEST-1] Example pairs: {example_pairs}")
        
        # Test 2: Triangular arbitrage detection
        logger.info("🧪 [TEST-2] Testing triangular arbitrage detection...")
        
        triangular_opportunities = await arbitrage_engine.find_triangular_arbitrage_opportunities()
        
        logger.info(f"✅ [TEST-2] Found {len(triangular_opportunities)} triangular arbitrage opportunities")
        
        # Show details of top triangular opportunities
        for i, opp in enumerate(triangular_opportunities[:3]):
            logger.info(f"✅ [TEST-2] Triangular Opportunity {i+1}:")
            logger.info(f"  - Currency Path: {' -> '.join(opp.currency_path)}")
            logger.info(f"  - Symbol Path: {opp.symbol_path}")
            logger.info(f"  - Profit: {opp.profit_percentage:.4f}%")
            logger.info(f"  - Exchange: {opp.exchanges[0]}")
            logger.info(f"  - Confidence: {opp.confidence:.2f}")
        
        # Test 3: Cross-exchange arbitrage detection
        logger.info("🧪 [TEST-3] Testing cross-exchange arbitrage detection...")
        
        cross_exchange_opportunities = await arbitrage_engine.find_cross_exchange_arbitrage_opportunities()
        
        logger.info(f"✅ [TEST-3] Found {len(cross_exchange_opportunities)} cross-exchange arbitrage opportunities")
        
        # Show details of top cross-exchange opportunities
        for i, opp in enumerate(cross_exchange_opportunities[:3]):
            logger.info(f"✅ [TEST-3] Cross-Exchange Opportunity {i+1}:")
            logger.info(f"  - Symbol: {opp.symbol_path[0]}")
            logger.info(f"  - Exchanges: {' -> '.join(opp.exchanges)}")
            logger.info(f"  - Prices: {[float(p) for p in opp.price_path]}")
            logger.info(f"  - Profit: {opp.profit_percentage:.4f}%")
            logger.info(f"  - Confidence: {opp.confidence:.2f}")
        
        # Test 4: Cross-currency arbitrage detection
        logger.info("🧪 [TEST-4] Testing cross-currency arbitrage detection...")
        
        cross_currency_opportunities = await arbitrage_engine.find_cross_currency_arbitrage_opportunities()
        
        logger.info(f"✅ [TEST-4] Found {len(cross_currency_opportunities)} cross-currency arbitrage opportunities")
        
        # Show details of top cross-currency opportunities
        for i, opp in enumerate(cross_currency_opportunities[:3]):
            logger.info(f"✅ [TEST-4] Cross-Currency Opportunity {i+1}:")
            logger.info(f"  - Currency Path: {' -> '.join(opp.currency_path)}")
            logger.info(f"  - Symbol Path: {opp.symbol_path}")
            logger.info(f"  - Profit: {opp.profit_percentage:.4f}%")
            logger.info(f"  - Exchange: {opp.exchanges[0]}")
            logger.info(f"  - Confidence: {opp.confidence:.2f}")
        
        # Test 5: All arbitrage opportunities
        logger.info("🧪 [TEST-5] Testing comprehensive arbitrage detection...")
        
        all_opportunities = await arbitrage_engine.find_all_arbitrage_opportunities()
        
        logger.info(f"✅ [TEST-5] Found {len(all_opportunities)} total arbitrage opportunities")
        
        # Categorize opportunities by type
        opportunity_counts = {}
        for opp in all_opportunities:
            arb_type = opp.arbitrage_type.value
            opportunity_counts[arb_type] = opportunity_counts.get(arb_type, 0) + 1
        
        for arb_type, count in opportunity_counts.items():
            logger.info(f"✅ [TEST-5] {arb_type}: {count} opportunities")
        
        # Test 6: Execution validation (without actual execution)
        logger.info("🧪 [TEST-6] Testing arbitrage execution validation...")
        
        if all_opportunities:
            test_opportunity = all_opportunities[0]
            validation_result = await arbitrage_engine._validate_arbitrage_execution(test_opportunity)
            
            if validation_result['valid']:
                logger.info("✅ [TEST-6] Arbitrage execution validation passed")
            else:
                logger.warning(f"⚠️ [TEST-6] Validation failed: {validation_result['reason']}")
            
            # Test simulated execution
            logger.info("🧪 [TEST-6] Testing simulated arbitrage execution...")
            execution_result = await arbitrage_engine.execute_arbitrage_opportunity(test_opportunity)
            
            if execution_result.get('success', False):
                logger.info("✅ [TEST-6] Simulated arbitrage execution successful")
                logger.info(f"✅ [TEST-6] Arbitrage type: {execution_result.get('arbitrage_type')}")
                logger.info(f"✅ [TEST-6] Executed trades: {len(execution_result.get('executed_trades', []))}")
            else:
                logger.warning(f"⚠️ [TEST-6] Execution failed: {execution_result.get('error')}")
        else:
            logger.info("ℹ️ [TEST-6] No opportunities to test execution")
        
        # Test 7: Profit potential analysis
        logger.info("🧪 [TEST-7] Testing profit potential analysis...")
        
        if all_opportunities:
            total_potential_profit = sum(opp.expected_profit for opp in all_opportunities)
            avg_profit_percentage = sum(opp.profit_percentage for opp in all_opportunities) / len(all_opportunities)
            best_opportunity = max(all_opportunities, key=lambda x: x.profit_percentage)
            
            logger.info(f"✅ [TEST-7] Total potential profit: ${total_potential_profit:.2f}")
            logger.info(f"✅ [TEST-7] Average profit percentage: {avg_profit_percentage:.4f}%")
            logger.info(f"✅ [TEST-7] Best opportunity profit: {best_opportunity.profit_percentage:.4f}%")
            logger.info(f"✅ [TEST-7] Best opportunity type: {best_opportunity.arbitrage_type.value}")
        else:
            logger.info("ℹ️ [TEST-7] No opportunities for profit analysis")
        
        # Test 8: Risk assessment
        logger.info("🧪 [TEST-8] Testing risk assessment...")
        
        if all_opportunities:
            avg_risk_score = sum(opp.risk_score for opp in all_opportunities) / len(all_opportunities)
            low_risk_opportunities = [opp for opp in all_opportunities if opp.risk_score < 0.3]
            high_profit_low_risk = [
                opp for opp in all_opportunities 
                if opp.risk_score < 0.3 and opp.profit_percentage > 0.5
            ]
            
            logger.info(f"✅ [TEST-8] Average risk score: {avg_risk_score:.2f}")
            logger.info(f"✅ [TEST-8] Low risk opportunities: {len(low_risk_opportunities)}")
            logger.info(f"✅ [TEST-8] High profit, low risk opportunities: {len(high_profit_low_risk)}")
        else:
            logger.info("ℹ️ [TEST-8] No opportunities for risk assessment")
        
        # Summary
        logger.info("📊 [TEST-SUMMARY] Cross-currency arbitrage engine test results:")
        logger.info(f"  - Currency graphs built: {len(arbitrage_engine.currency_graphs)}")
        logger.info(f"  - Triangular opportunities: {len(triangular_opportunities)}")
        logger.info(f"  - Cross-exchange opportunities: {len(cross_exchange_opportunities)}")
        logger.info(f"  - Cross-currency opportunities: {len(cross_currency_opportunities)}")
        logger.info(f"  - Total opportunities: {len(all_opportunities)}")
        
        if all_opportunities:
            logger.info(f"  - Best profit potential: {max(opp.profit_percentage for opp in all_opportunities):.4f}%")
        
        logger.info("✅ [TEST] Cross-currency arbitrage engine test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Cross-currency arbitrage test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    try:
        logger.info("🚀 Starting cross-currency arbitrage engine tests...")
        
        success = await test_cross_currency_arbitrage()
        
        if success:
            logger.info("✅ All tests passed!")
            return 0
        else:
            logger.error("❌ Some tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
