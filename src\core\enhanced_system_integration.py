#!/usr/bin/env python3
"""
Enhanced System Integration Module
Integrates ComponentRegistry, HealthMonitor, DataAggregator, and ExecutionValidator
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import traceback

# Import our enhanced components
from .component_registry import ComponentRegistry, ComponentType, ComponentStatus
from .execution_results_validator import ExecutionResultsValidator, ValidationResult
from ..data_feeds.enhanced_data_aggregator import EnhancedDataAggregator, AggregatedData
from ..monitoring.health_monitor import HealthMonitor, HealthStatus, AlertSeverity

logger = logging.getLogger(__name__)

@dataclass
class SystemStatus:
    """Overall system status"""
    is_operational: bool
    component_registry_status: str
    health_monitor_status: str
    data_aggregator_status: str
    execution_validator_status: str
    active_components: int
    healthy_components: int
    failed_components: int
    data_sources_available: int
    last_update: datetime

class EnhancedSystemIntegration:
    """
    Enhanced System Integration Manager
    
    Coordinates all enhanced components:
    - ComponentRegistry for component discovery and management
    - HealthMonitor for system health monitoring
    - EnhancedDataAggregator for multi-source data
    - ExecutionResultsValidator for trade validation
    """
    
    def __init__(self):
        # Initialize core components
        self.component_registry = ComponentRegistry()
        self.health_monitor = HealthMonitor(check_interval=30)
        self.data_aggregator = EnhancedDataAggregator()
        self.execution_validator = ExecutionResultsValidator()
        
        # Integration state
        self.initialized = False
        self.system_status = SystemStatus(
            is_operational=False,
            component_registry_status="not_initialized",
            health_monitor_status="not_initialized",
            data_aggregator_status="not_initialized",
            execution_validator_status="ready",
            active_components=0,
            healthy_components=0,
            failed_components=0,
            data_sources_available=0,
            last_update=datetime.now()
        )
        
        # External components integration
        self.external_components: Dict[str, Any] = {}
        
        logger.info("🔗 [INTEGRATION] Enhanced system integration initialized")
    
    async def initialize(self, external_components: Optional[Dict[str, Any]] = None) -> bool:
        """
        Initialize the integrated system
        
        Args:
            external_components: Dictionary of external components to integrate
            
        Returns:
            True if initialization successful
        """
        try:
            logger.info("🚀 [INTEGRATION] Starting enhanced system initialization...")
            
            # Store external components for integration
            if external_components:
                self.external_components = external_components
                self.component_registry.external_components = external_components
            
            # Initialize component registry
            registry_success = await self.component_registry.initialize()
            self.system_status.component_registry_status = "operational" if registry_success else "failed"
            
            # Initialize health monitor
            health_success = await self.health_monitor.start_monitoring()
            self.system_status.health_monitor_status = "operational" if health_success else "failed"
            
            # Initialize data aggregator
            data_success = await self.data_aggregator.initialize()
            self.system_status.data_aggregator_status = "operational" if data_success else "failed"
            
            # Execution validator is always ready (no async initialization needed)
            self.system_status.execution_validator_status = "operational"
            
            # Discover and register components
            if registry_success:
                discovery_results = await self.component_registry.discover_and_register_components()
                logger.info(f"🔍 [INTEGRATION] Component discovery results: {discovery_results}")
            
            # Register components with health monitor
            await self._register_components_with_health_monitor()
            
            # Setup alert handlers
            self._setup_alert_handlers()
            
            # Update system status
            await self._update_system_status()
            
            self.initialized = True
            self.system_status.is_operational = (
                registry_success and health_success and data_success
            )
            
            if self.system_status.is_operational:
                logger.info("✅ [INTEGRATION] Enhanced system initialization completed successfully")
            else:
                logger.warning("⚠️ [INTEGRATION] System initialization completed with some failures")
            
            return self.system_status.is_operational
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATION] System initialization failed: {e}")
            logger.error(traceback.format_exc())
            return False
    
    async def _register_components_with_health_monitor(self):
        """Register discovered components with health monitor"""
        try:
            for component_name, metadata in self.component_registry.components.items():
                # Register component with health monitor
                component_health = self.health_monitor.register_component(
                    component_name=component_name
                )
                
                # Set up recovery handler if component supports it
                if hasattr(metadata.instance, 'restart') or hasattr(metadata.instance, 'reconnect'):
                    async def recovery_handler():
                        try:
                            if hasattr(metadata.instance, 'restart'):
                                return await metadata.instance.restart()
                            elif hasattr(metadata.instance, 'reconnect'):
                                return await metadata.instance.reconnect()
                            return False
                        except Exception as e:
                            logger.error(f"❌ [INTEGRATION] Recovery failed for {component_name}: {e}")
                            return False
                    
                    self.health_monitor.recovery_handlers[component_name] = recovery_handler
                
                logger.debug(f"📋 [INTEGRATION] Registered {component_name} with health monitor")
                
        except Exception as e:
            logger.error(f"❌ [INTEGRATION] Error registering components with health monitor: {e}")
    
    def _setup_alert_handlers(self):
        """Setup alert handlers for system notifications"""
        try:
            async def log_alert_handler(alert: Dict[str, Any]):
                """Log alerts to system logger"""
                severity = alert.get('severity', 'info')
                component = alert.get('component', 'unknown')
                message = alert.get('message', 'No message')
                
                log_message = f"🚨 [ALERT-{severity.upper()}] {component}: {message}"
                
                if severity == 'emergency':
                    logger.critical(log_message)
                elif severity == 'critical':
                    logger.error(log_message)
                elif severity == 'warning':
                    logger.warning(log_message)
                else:
                    logger.info(log_message)
            
            self.health_monitor.add_alert_handler(log_alert_handler)
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATION] Error setting up alert handlers: {e}")
    
    async def _update_system_status(self):
        """Update overall system status"""
        try:
            # Get component counts
            all_components = self.component_registry.components
            healthy_components = self.component_registry.get_healthy_components()
            
            self.system_status.active_components = len(all_components)
            self.system_status.healthy_components = len(healthy_components)
            self.system_status.failed_components = self.system_status.active_components - self.system_status.healthy_components
            
            # Get data source count
            self.system_status.data_sources_available = len([
                source for source in self.data_aggregator.data_sources.values()
                if source.enabled
            ])
            
            self.system_status.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATION] Error updating system status: {e}")
    
    async def get_aggregated_price_with_validation(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get aggregated price data with comprehensive validation
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Validated and enhanced price data
        """
        try:
            # Get aggregated price data
            price_data = await self.data_aggregator.get_aggregated_price(symbol)
            
            if not price_data:
                return None
            
            # Create standardized result
            result = {
                'symbol': symbol,
                'price': price_data.weighted_price,
                'volume': sum(source.volume for source in price_data.individual_sources),
                'confidence': price_data.confidence_score,
                'quality': price_data.data_quality.value,
                'source_count': price_data.source_count,
                'price_range': price_data.price_range,
                'timestamp': price_data.timestamp.isoformat(),
                'sources': [
                    {
                        'name': source.source,
                        'price': source.price,
                        'volume': source.volume,
                        'quality': source.quality.value,
                        'latency_ms': source.latency_ms
                    }
                    for source in price_data.individual_sources
                ]
            }
            
            return result
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATION] Error getting validated price for {symbol}: {e}")
            return None
    
    def validate_execution_result(self, execution_result: Dict[str, Any]) -> ValidationResult:
        """
        Validate execution result with enhanced error reporting
        
        Args:
            execution_result: Execution result to validate
            
        Returns:
            Comprehensive validation result
        """
        try:
            # Perform validation
            validation_result = self.execution_validator.validate(execution_result)
            
            # Update health metrics based on validation
            if validation_result.is_valid:
                self.health_monitor.update_component_health(
                    component_name="execution_validator",
                    status=HealthStatus.HEALTHY
                )
            else:
                critical_issues = validation_result.get_critical_issues()
                if critical_issues:
                    self.health_monitor.update_component_health(
                        component_name="execution_validator",
                        status=HealthStatus.CRITICAL,
                        error_message=f"Critical validation issues: {len(critical_issues)}"
                    )
            
            return validation_result
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATION] Error validating execution result: {e}")
            # Return error validation result
            error_result = ValidationResult(is_valid=False)
            error_result.add_issue(
                field="validation",
                severity="critical",
                message=f"Validation exception: {str(e)}"
            )
            return error_result
    
    def get_component(self, name: str) -> Optional[Any]:
        """Get a component instance by name"""
        return self.component_registry.get_component(name)
    
    def get_healthy_components(self, component_type: Optional[ComponentType] = None) -> List[str]:
        """Get list of healthy components"""
        return self.component_registry.get_healthy_components(component_type)
    
    async def get_system_health_report(self) -> Dict[str, Any]:
        """Get comprehensive system health report"""
        try:
            # Update system status
            await self._update_system_status()
            
            # Get health report from monitor
            health_report = self.health_monitor.get_health_report()
            
            # Get component status from registry
            component_status = self.component_registry.get_all_component_status()
            
            # Get data aggregator performance
            aggregator_performance = self.data_aggregator.get_performance_report()
            
            # Combine all reports
            comprehensive_report = {
                'timestamp': datetime.now().isoformat(),
                'system_status': {
                    'operational': self.system_status.is_operational,
                    'component_registry': self.system_status.component_registry_status,
                    'health_monitor': self.system_status.health_monitor_status,
                    'data_aggregator': self.system_status.data_aggregator_status,
                    'execution_validator': self.system_status.execution_validator_status
                },
                'component_summary': {
                    'total': self.system_status.active_components,
                    'healthy': self.system_status.healthy_components,
                    'failed': self.system_status.failed_components,
                    'data_sources': self.system_status.data_sources_available
                },
                'health_monitor': health_report,
                'component_registry': component_status,
                'data_aggregator': aggregator_performance
            }
            
            return comprehensive_report
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATION] Error generating health report: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def shutdown(self):
        """Gracefully shutdown the integrated system"""
        try:
            logger.info("🛑 [INTEGRATION] Starting system shutdown...")
            
            # Stop health monitoring
            await self.health_monitor.stop_monitoring()
            
            # Stop component registry monitoring
            await self.component_registry.stop_monitoring()
            
            # Cleanup data aggregator
            await self.data_aggregator.cleanup()
            
            self.initialized = False
            self.system_status.is_operational = False
            
            logger.info("✅ [INTEGRATION] System shutdown completed")
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATION] Error during shutdown: {e}")
    
    def is_operational(self) -> bool:
        """Check if the system is operational"""
        return self.initialized and self.system_status.is_operational
