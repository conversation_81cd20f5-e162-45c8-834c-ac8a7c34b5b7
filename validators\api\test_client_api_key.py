#!/usr/bin/env python3
"""
TEST COINBASE CLIENT API KEY
============================

Test the Client API Key approach for Coinbase authentication.
This uses the Client API Key instead of Secret API Keys.

Author: AutoGPT Trader
Date: June 2025
"""

import os
import requests
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("client_api_test")

def test_client_api_key():
    """Test Coinbase with Client API Key"""
    try:
        # Load credentials
        from credential_decryptor_fixed import setup_credentials
        setup_credentials()
        
        client_api_key = os.getenv('COINBASE_CLIENT_API_KEY')
        logger.info(f"🔑 [CLIENT] Client API Key: {client_api_key}")
        
        if not client_api_key:
            logger.error("❌ [ERROR] No Client API Key found")
            return False
        
        # Test different approaches with Client API Key
        
        # Method 1: Simple API key in header
        logger.info("🧪 [TEST-1] Testing with API key in header...")
        headers = {
            'Authorization': f'Bearer {client_api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'AutoGPT-Trader/1.0'
        }
        
        try:
            response = requests.get('https://api.coinbase.com/v2/exchange-rates', headers=headers, timeout=30)
            logger.info(f"📊 [RESPONSE] Status: {response.status_code}")
            
            if response.status_code == 200:
                logger.info("✅ [SUCCESS] Client API Key working!")
                data = response.json()
                logger.info(f"📊 [DATA] Response keys: {list(data.keys())}")
                return True
            else:
                logger.error(f"❌ [ERROR] {response.status_code}: {response.text[:200]}")
                
        except Exception as e:
            logger.error(f"❌ [REQUEST] Failed: {e}")
        
        # Method 2: API key as query parameter
        logger.info("🧪 [TEST-2] Testing with API key as query parameter...")
        try:
            url = f'https://api.coinbase.com/v2/exchange-rates?api_key={client_api_key}'
            response = requests.get(url, timeout=30)
            logger.info(f"📊 [RESPONSE] Status: {response.status_code}")
            
            if response.status_code == 200:
                logger.info("✅ [SUCCESS] Client API Key working with query param!")
                data = response.json()
                logger.info(f"📊 [DATA] Response keys: {list(data.keys())}")
                return True
            else:
                logger.error(f"❌ [ERROR] {response.status_code}: {response.text[:200]}")
                
        except Exception as e:
            logger.error(f"❌ [REQUEST] Failed: {e}")
        
        # Method 3: Try public endpoints first
        logger.info("🧪 [TEST-3] Testing public endpoints...")
        try:
            response = requests.get('https://api.coinbase.com/v2/exchange-rates', timeout=30)
            logger.info(f"📊 [PUBLIC] Status: {response.status_code}")
            
            if response.status_code == 200:
                logger.info("✅ [PUBLIC] Public API working!")
                data = response.json()
                logger.info(f"📊 [DATA] Response keys: {list(data.keys())}")
                
                # Now try authenticated endpoint
                logger.info("🧪 [AUTH] Testing authenticated endpoint...")
                headers = {'Authorization': f'Bearer {client_api_key}'}
                response = requests.get('https://api.coinbase.com/v2/accounts', headers=headers, timeout=30)
                logger.info(f"📊 [AUTH] Status: {response.status_code}")
                
                if response.status_code == 200:
                    logger.info("✅ [SUCCESS] Authenticated API working!")
                    return True
                else:
                    logger.error(f"❌ [AUTH] {response.status_code}: {response.text[:200]}")
                    
            else:
                logger.error(f"❌ [PUBLIC] {response.status_code}: {response.text[:200]}")
                
        except Exception as e:
            logger.error(f"❌ [PUBLIC] Failed: {e}")
        
        return False
        
    except Exception as e:
        logger.error(f"❌ [TEST] Client API test failed: {e}")
        return False

def test_different_endpoints():
    """Test different Coinbase API endpoints"""
    client_api_key = os.getenv('COINBASE_CLIENT_API_KEY')
    
    endpoints = [
        # Public endpoints (no auth needed)
        ('https://api.coinbase.com/v2/exchange-rates', 'Public Exchange Rates'),
        ('https://api.coinbase.com/v2/currencies', 'Public Currencies'),
        
        # Authenticated endpoints
        ('https://api.coinbase.com/v2/accounts', 'User Accounts'),
        ('https://api.coinbase.com/v2/user', 'User Info'),
        
        # Advanced Trade API
        ('https://api.coinbase.com/api/v3/brokerage/products', 'Advanced Trade Products'),
    ]
    
    for url, name in endpoints:
        logger.info(f"🧪 [TEST] {name}: {url}")
        
        try:
            # Try without auth first
            response = requests.get(url, timeout=30)
            logger.info(f"📊 [NO-AUTH] {name} Status: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"✅ [SUCCESS] {name} working without auth!")
                continue
            
            # Try with Client API Key
            headers = {'Authorization': f'Bearer {client_api_key}'}
            response = requests.get(url, headers=headers, timeout=30)
            logger.info(f"📊 [CLIENT-KEY] {name} Status: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"✅ [SUCCESS] {name} working with Client API Key!")
            else:
                logger.error(f"❌ [ERROR] {name}: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ [REQUEST] {name} failed: {e}")

def main():
    """Main function"""
    logger.info("🔍 [TEST] Coinbase Client API Key Test")
    
    # Test Client API Key approach
    logger.info("\n" + "="*50)
    logger.info("STEP 1: Client API Key Test")
    logger.info("="*50)
    success = test_client_api_key()
    
    if success:
        print("✅ Coinbase Client API Key working!")
        return True
    
    # Test different endpoints
    logger.info("\n" + "="*50)
    logger.info("STEP 2: Endpoint Testing")
    logger.info("="*50)
    test_different_endpoints()
    
    print("❌ Client API Key test failed")
    print("🔧 Recommendations:")
    print("   1. Verify the Client API Key is correct")
    print("   2. Check if Client API Key has proper permissions")
    print("   3. Try regenerating the Client API Key")
    print("   4. Consider using OAuth flow instead")
    
    return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
