from abc import ABC, abstractmethod
from decimal import Decimal

class ExchangeInterface(ABC):
    """Abstract base class for all exchange interfaces"""
    
    @abstractmethod
    async def execute_order(self, symbol: str, side: str, amount: Decimal) -> dict:
        """Execute a trade order"""
        pass

    @abstractmethod
    async def get_balance(self, currency: str) -> Decimal:
        """Get account balance"""
        pass
