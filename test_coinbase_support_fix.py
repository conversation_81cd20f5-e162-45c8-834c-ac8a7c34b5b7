#!/usr/bin/env python3
"""
Test Coinbase API with the exact implementation provided by Coinbase support
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.exchanges.coinbase_enhanced_client import CoinbaseEnhancedClient
from src.utils.cryptography.hybrid import HybridCrypto
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

def test_coinbase_support_fix():
    """Test Coinbase API with support's exact JWT implementation"""

    print("🔧 [COINBASE-SUPPORT-FIX] Testing Coinbase API with support's exact implementation...")

    try:
        # Load credentials using the same method as other scripts
        from src.utils.cryptography.secure_credentials import decrypt_value

        # Get encrypted credentials from .env
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')

        if not encrypted_api_key or not encrypted_private_key:
            print("❌ Missing encrypted credentials in .env file")
            return False

        # Decrypt credentials
        api_key_name = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)

        # Extract key ID from API key
        if "/apiKeys/" in api_key_name:
            key_id = api_key_name.split("/apiKeys/")[1]
        else:
            print("❌ Cannot extract key ID from API key format")
            return False

        print(f"✅ [CREDENTIALS] Loaded API key: {api_key_name[:50]}...")
        print(f"✅ [CREDENTIALS] Key ID: {key_id}")

        # Test Coinbase connection
        coinbase_client = CoinbaseEnhancedClient(api_key_name, private_key_pem, key_id)
        
        print("✅ [STEP 1] Coinbase client initialized")
        
        # Test connection (skip async test for now)
        print("🔍 [STEP 2] Testing connection...")
        print("✅ [STEP 2] Connection test SKIPPED (async method)")

        # Test account access
        print("🔍 [STEP 3] Testing account access...")
        balances = coinbase_client.get_all_balances()

        if balances and not isinstance(balances, dict) or not balances.get('error'):
            print(f"✅ [STEP 3] Account access SUCCESSFUL")
            print(f"📊 [BALANCES] Found {len(balances)} currencies")

            # Show balance details
            for currency, balance in balances.items():
                print(f"   • {currency}: {balance}")

            return True
        else:
            print(f"❌ [STEP 3] Account access FAILED: {balances}")
            return False
            
    except Exception as e:
        print(f"❌ [ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 COINBASE SUPPORT FIX TEST")
    print("=" * 60)
    
    success = test_coinbase_support_fix()
    
    print("=" * 60)
    if success:
        print("✅ COINBASE SUPPORT FIX SUCCESSFUL!")
        print("✅ Coinbase API authentication WORKING")
        print("✅ Ready for live trading integration")
    else:
        print("❌ COINBASE SUPPORT FIX FAILED")
        print("❌ Need further investigation")
    print("=" * 60)
