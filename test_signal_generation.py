#!/usr/bin/env python3
"""
Test script to verify signal generation fixes
"""

import asyncio
import sys
import os
from decimal import Decimal

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from trading.enhanced_signal_generator import EnhancedSignalGenerator
from trading.enhanced_exchange_manager import EnhancedExchangeManager

class MockExchangeManager:
    """Mock exchange manager for testing"""
    
    def __init__(self):
        self.exchanges = {
            'bybit_client_fixed': MockBybitClient()
        }

class MockBybitClient:
    """Mock Bybit client for testing"""
    
    async def get_balance(self, currency):
        """Return mock balance"""
        if currency == 'USDT':
            return Decimal('63.34')
        return Decimal('0')
    
    def get_all_trading_pairs(self):
        """Return mock trading pairs"""
        return {
            'trading_pairs': {
                'BTCUSDT': {
                    'base_currency': 'BTC',
                    'quote_currency': 'USDT',
                    'status': 'trading',
                    'min_order_qty': 0.000048,
                    'min_order_amt': 5.0,
                    'symbol_bybit': 'BTCUSDT',
                    'symbol_user': 'BTC-USDT'
                },
                'ETHUSDT': {
                    'base_currency': 'ETH',
                    'quote_currency': 'USDT',
                    'status': 'trading',
                    'min_order_qty': 0.002,
                    'min_order_amt': 5.0,
                    'symbol_bybit': 'ETHUSDT',
                    'symbol_user': 'ETH-USDT'
                },
                'SOLUSDT': {
                    'base_currency': 'SOL',
                    'quote_currency': 'USDT',
                    'status': 'trading',
                    'min_order_qty': 0.04,
                    'min_order_amt': 5.0,
                    'symbol_bybit': 'SOLUSDT',
                    'symbol_user': 'SOL-USDT'
                }
            }
        }

async def test_signal_generation():
    """Test signal generation with fixes"""
    print("🧪 Testing Signal Generation Fixes...")
    
    # Create mock exchange manager
    mock_exchange_manager = MockExchangeManager()
    
    # Create enhanced signal generator
    signal_generator = EnhancedSignalGenerator(mock_exchange_manager)
    
    # Test 1: Symbol discovery
    print("\n1. Testing Symbol Discovery...")
    symbols = await signal_generator._get_symbols_for_exchange('bybit')
    print(f"   Discovered symbols: {symbols}")
    
    # Test 2: Position sizing
    print("\n2. Testing Position Sizing...")
    test_price = 50000.0  # BTC price
    position_size = await signal_generator._calculate_minimum_position_size('BTCUSDT', test_price, 'bybit')
    usd_value = float(position_size) * test_price
    print(f"   Position size: {position_size:.6f} BTC")
    print(f"   USD value: ${usd_value:.2f}")
    print(f"   Target range: $12.67-$15.84 (20-25% of $63.34)")
    
    # Test 3: Signal generation
    print("\n3. Testing Signal Generation...")
    market_data = {}
    active_exchanges = ['bybit']
    
    signals = await signal_generator._generate_time_based_signals_for_exchange('bybit')
    print(f"   Generated signals: {len(signals)}")
    
    for signal_id, signal in signals.items():
        print(f"   Signal: {signal['action']} {signal['symbol']} - ${signal['usd_value']:.2f}")
    
    # Test 4: Balance checking
    print("\n4. Testing Balance Checking...")
    balances = await signal_generator._get_exchange_balances('bybit')
    print(f"   Available balances: {balances}")
    
    print("\n✅ Test completed!")
    
    return len(signals) > 0

if __name__ == "__main__":
    success = asyncio.run(test_signal_generation())
    if success:
        print("\n🎉 SUCCESS: Signal generation is working!")
        sys.exit(0)
    else:
        print("\n❌ FAILURE: No signals generated!")
        sys.exit(1)
