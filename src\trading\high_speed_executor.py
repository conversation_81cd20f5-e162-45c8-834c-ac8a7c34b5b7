"""
High-Speed Trading Execution Engine
Ultra-fast order execution with minimal latency to prevent money loss from timing
"""

import asyncio
import aiohttp
import time
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import threading
from collections import deque
import json

logger = logging.getLogger(__name__)

@dataclass
class FastOrder:
    """High-speed order structure"""
    order_id: str
    symbol: str
    side: str  # BUY/SELL
    amount: Decimal
    price: Optional[Decimal] = None
    order_type: str = "market"  # market, limit, stop
    exchange: str = "coinbase"
    priority: int = 1  # 1=highest, 5=lowest
    timestamp: datetime = None
    timeout_ms: int = 5000  # 5 second timeout
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class ExecutionResult:
    """Execution result with timing metrics"""
    order_id: str
    success: bool
    execution_time_ms: float
    fill_price: Optional[Decimal] = None
    fill_amount: Optional[Decimal] = None
    exchange: str = ""
    error_message: str = ""
    latency_breakdown: Dict[str, float] = None
    
    def __post_init__(self):
        if self.latency_breakdown is None:
            self.latency_breakdown = {}

class ConnectionPool:
    """High-performance connection pool for exchanges"""
    
    def __init__(self, max_connections: int = 50):
        self.max_connections = max_connections
        self.pools = {}
        self.session_cache = {}
        self.lock = asyncio.Lock()
    
    async def get_session(self, exchange: str) -> aiohttp.ClientSession:
        """Get optimized session for exchange"""
        if exchange not in self.session_cache:
            async with self.lock:
                if exchange not in self.session_cache:
                    # Ultra-fast session configuration
                    timeout = aiohttp.ClientTimeout(
                        total=5,
                        connect=1,
                        sock_read=2,
                        sock_connect=1
                    )
                    
                    connector = aiohttp.TCPConnector(
                        limit=self.max_connections,
                        limit_per_host=20,
                        ttl_dns_cache=300,
                        use_dns_cache=True,
                        keepalive_timeout=30,
                        enable_cleanup_closed=True
                    )
                    
                    self.session_cache[exchange] = aiohttp.ClientSession(
                        timeout=timeout,
                        connector=connector,
                        headers={
                            'User-Agent': 'HighSpeedTrader/1.0',
                            'Connection': 'keep-alive',
                            'Keep-Alive': 'timeout=30, max=100'
                        }
                    )
        
        return self.session_cache[exchange]
    
    async def close_all(self):
        """Close all sessions"""
        for session in self.session_cache.values():
            await session.close()
        self.session_cache.clear()

class HighSpeedExecutor:
    """
    Ultra-fast trading execution engine
    Designed for minimal latency and maximum speed
    """
    
    def __init__(self, exchanges: Dict[str, Any]):
        self.exchanges = exchanges
        self.connection_pool = ConnectionPool(max_connections=100)
        
        # High-speed execution queues
        self.priority_queue = asyncio.PriorityQueue()
        self.execution_workers = []
        self.worker_count = 10  # Parallel execution workers
        
        # Performance monitoring
        self.execution_stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'avg_execution_time': 0.0,
            'fastest_execution': float('inf'),
            'slowest_execution': 0.0
        }
        
        # Circuit breaker for failed executions
        self.circuit_breaker = {
            'failure_count': 0,
            'failure_threshold': 5,
            'reset_time': 60,
            'last_failure': None,
            'is_open': False
        }
        
        # Pre-warmed connections
        self.connection_warmup_done = False
        
        logger.info("HighSpeedExecutor initialized - ready for ultra-fast trading")
    
    async def start_execution_engine(self):
        """Start the high-speed execution engine"""
        logger.info("Starting high-speed execution engine...")
        
        # Warm up connections
        await self._warmup_connections()
        
        # Start execution workers
        for i in range(self.worker_count):
            worker = asyncio.create_task(self._execution_worker(f"worker_{i}"))
            self.execution_workers.append(worker)
        
        logger.info(f"High-speed execution engine started with {self.worker_count} workers")
    
    async def _warmup_connections(self):
        """Pre-warm connections to all exchanges"""
        logger.info("Warming up exchange connections...")
        start_time = time.time()
        
        warmup_tasks = []
        for exchange_name, exchange_client in self.exchanges.items():
            if hasattr(exchange_client, 'test_connection'):
                warmup_tasks.append(self._warmup_exchange(exchange_name, exchange_client))
        
        await asyncio.gather(*warmup_tasks, return_exceptions=True)
        
        warmup_time = (time.time() - start_time) * 1000
        self.connection_warmup_done = True
        logger.info(f"Connection warmup completed in {warmup_time:.2f}ms")
    
    async def _warmup_exchange(self, exchange_name: str, exchange_client):
        """Warm up connection to specific exchange"""
        try:
            start_time = time.time()

            # Test connection - handle both async and sync methods
            if hasattr(exchange_client, 'test_connection'):
                import inspect
                test_method = exchange_client.test_connection

                if inspect.iscoroutinefunction(test_method):
                    # Async method - await it
                    await test_method()
                else:
                    # Sync method - call it directly
                    test_method()

            # Get session for HTTP requests
            await self.connection_pool.get_session(exchange_name)

            warmup_time = (time.time() - start_time) * 1000
            logger.info(f"Warmed up {exchange_name} connection in {warmup_time:.2f}ms")

        except Exception as e:
            logger.warning(f"Failed to warm up {exchange_name}: {e}")
    
    async def execute_order_fast(self, order: FastOrder) -> ExecutionResult:
        """Execute order with maximum speed"""
        if self.circuit_breaker['is_open']:
            if self._should_reset_circuit_breaker():
                self._reset_circuit_breaker()
            else:
                return ExecutionResult(
                    order_id=order.order_id,
                    success=False,
                    execution_time_ms=0,
                    error_message="Circuit breaker is open - too many failures"
                )
        
        # Add to priority queue for immediate execution
        await self.priority_queue.put((order.priority, time.time(), order))
        
        # Wait for execution result (with timeout)
        try:
            result = await asyncio.wait_for(
                self._wait_for_execution_result(order.order_id),
                timeout=order.timeout_ms / 1000
            )
            return result
        except asyncio.TimeoutError:
            return ExecutionResult(
                order_id=order.order_id,
                success=False,
                execution_time_ms=order.timeout_ms,
                error_message="Execution timeout"
            )
    
    async def _execution_worker(self, worker_name: str):
        """High-speed execution worker"""
        logger.info(f"Execution worker {worker_name} started")
        
        while True:
            try:
                # Get next order from priority queue
                priority, queue_time, order = await self.priority_queue.get()
                
                # Execute immediately
                result = await self._execute_order_immediate(order, queue_time)
                
                # Store result for retrieval
                if not hasattr(self, '_execution_results'):
                    self._execution_results = {}
                self._execution_results[order.order_id] = result
                
                # Update statistics
                self._update_execution_stats(result)
                
                # Mark task as done
                self.priority_queue.task_done()
                
            except Exception as e:
                logger.error(f"Execution worker {worker_name} error: {e}")
                await asyncio.sleep(0.1)  # Brief pause on error
    
    async def _execute_order_immediate(self, order: FastOrder, queue_time: float) -> ExecutionResult:
        """Execute order immediately with timing breakdown"""
        start_time = time.time()
        latency_breakdown = {
            'queue_time': (start_time - queue_time) * 1000,
            'preparation_time': 0,
            'network_time': 0,
            'processing_time': 0
        }
        
        try:
            # Preparation phase
            prep_start = time.time()
            exchange_client = self.exchanges.get(order.exchange)
            if not exchange_client:
                raise ValueError(f"Exchange {order.exchange} not available")
            
            latency_breakdown['preparation_time'] = (time.time() - prep_start) * 1000
            
            # Network execution phase
            network_start = time.time()
            
            if order.order_type == "market":
                result = await self._execute_market_order(exchange_client, order)
            elif order.order_type == "limit":
                result = await self._execute_limit_order(exchange_client, order)
            else:
                raise ValueError(f"Unsupported order type: {order.order_type}")
            
            latency_breakdown['network_time'] = (time.time() - network_start) * 1000
            
            # Processing phase
            processing_start = time.time()
            total_time = (time.time() - start_time) * 1000
            latency_breakdown['processing_time'] = (time.time() - processing_start) * 1000
            
            execution_result = ExecutionResult(
                order_id=order.order_id,
                success=True,
                execution_time_ms=total_time,
                fill_price=result.get('fill_price'),
                fill_amount=result.get('fill_amount'),
                exchange=order.exchange,
                latency_breakdown=latency_breakdown
            )
            
            logger.info(f"Order {order.order_id} executed in {total_time:.2f}ms")
            return execution_result
            
        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            self._handle_execution_failure()
            
            return ExecutionResult(
                order_id=order.order_id,
                success=False,
                execution_time_ms=total_time,
                exchange=order.exchange,
                error_message=str(e),
                latency_breakdown=latency_breakdown
            )
    
    async def _execute_market_order(self, exchange_client, order: FastOrder) -> Dict:
        """Execute market order with maximum speed"""
        try:
            if hasattr(exchange_client, 'place_market_order'):
                result = await exchange_client.place_market_order(
                    symbol=order.symbol,
                    side=order.side.lower(),
                    amount=float(order.amount)
                )
                return {
                    'fill_price': Decimal(str(result.get('price', 0))),
                    'fill_amount': Decimal(str(result.get('amount', 0)))
                }
            else:
                # Fallback to generic order placement
                # Handle different exchange parameter names
                if hasattr(exchange_client, '__class__') and 'Bybit' in exchange_client.__class__.__name__:
                    # CRITICAL FIX: Use quote currency amount for Bybit minimum compliance
                    # Calculate quote amount (USDT) from base amount
                    quote_amount = float(order.amount) * float(order.price) if order.price else float(order.amount) * 150.0  # Fallback price

                    # Ensure minimum quote amount ($5 USDT for Bybit)
                    if quote_amount < 5.0:
                        quote_amount = 5.1  # Slightly above minimum

                    logger.warning(f"🔧 [HIGH-SPEED-BYBIT] Using quote amount ${quote_amount:.2f} for {order.symbol}")

                    # Bybit uses quote currency amount for market buy orders
                    result = exchange_client.place_order(
                        symbol=order.symbol,
                        side=order.side.lower(),
                        amount=quote_amount,  # Use quote currency amount (USDT)
                        order_type='market',
                        is_quote_amount=True  # CRITICAL: Specify this is quote currency amount
                    )
                else:
                    # Other exchanges use 'amount' parameter and may be async
                    result = await exchange_client.place_order(
                        symbol=order.symbol,
                        side=order.side.lower(),
                        amount=float(order.amount),
                        order_type='market'
                    )
                return {
                    'fill_price': Decimal(str(result.get('price', 0))),
                    'fill_amount': Decimal(str(result.get('amount', 0)))
                }
        except Exception as e:
            logger.error(f"Market order execution failed: {e}")
            raise
    
    async def _execute_limit_order(self, exchange_client, order: FastOrder) -> Dict:
        """Execute limit order with maximum speed"""
        try:
            if hasattr(exchange_client, 'place_limit_order'):
                result = await exchange_client.place_limit_order(
                    symbol=order.symbol,
                    side=order.side.lower(),
                    amount=float(order.amount),
                    price=float(order.price)
                )
                return {
                    'fill_price': order.price,
                    'fill_amount': order.amount
                }
            else:
                # Fallback to generic order placement
                # Handle different exchange parameter names
                if hasattr(exchange_client, '__class__') and 'Bybit' in exchange_client.__class__.__name__:
                    # CRITICAL FIX: For Bybit limit orders, use base currency amount (not quote)
                    # Limit orders specify exact price, so we use base currency quantity
                    result = exchange_client.place_order(
                        symbol=order.symbol,
                        side=order.side.lower(),
                        amount=float(order.amount),  # Use base currency amount for limit orders
                        price=float(order.price) if order.price else None,
                        order_type='limit',
                        is_quote_amount=False  # Limit orders use base currency amount
                    )
                else:
                    # Other exchanges use 'amount' parameter and may be async
                    result = await exchange_client.place_order(
                        symbol=order.symbol,
                        side=order.side.lower(),
                        amount=float(order.amount),
                        price=float(order.price) if order.price else None,
                        order_type='limit'
                    )
                return {
                    'fill_price': order.price,
                    'fill_amount': order.amount
                }
        except Exception as e:
            logger.error(f"Limit order execution failed: {e}")
            raise
    
    async def _wait_for_execution_result(self, order_id: str) -> ExecutionResult:
        """Wait for execution result"""
        if not hasattr(self, '_execution_results'):
            self._execution_results = {}
        
        # Poll for result (optimized for speed)
        for _ in range(1000):  # Max 10 seconds at 10ms intervals
            if order_id in self._execution_results:
                result = self._execution_results.pop(order_id)
                return result
            await asyncio.sleep(0.01)  # 10ms polling interval
        
        raise asyncio.TimeoutError("Execution result not available")
    
    def _update_execution_stats(self, result: ExecutionResult):
        """Update execution statistics"""
        self.execution_stats['total_orders'] += 1
        
        if result.success:
            self.execution_stats['successful_orders'] += 1
            
            # Update timing statistics
            exec_time = result.execution_time_ms
            self.execution_stats['fastest_execution'] = min(
                self.execution_stats['fastest_execution'], exec_time
            )
            self.execution_stats['slowest_execution'] = max(
                self.execution_stats['slowest_execution'], exec_time
            )
            
            # Update average
            total_successful = self.execution_stats['successful_orders']
            current_avg = self.execution_stats['avg_execution_time']
            self.execution_stats['avg_execution_time'] = (
                (current_avg * (total_successful - 1) + exec_time) / total_successful
            )
        else:
            self.execution_stats['failed_orders'] += 1
    
    def _handle_execution_failure(self):
        """Handle execution failure for circuit breaker"""
        self.circuit_breaker['failure_count'] += 1
        self.circuit_breaker['last_failure'] = time.time()
        
        if self.circuit_breaker['failure_count'] >= self.circuit_breaker['failure_threshold']:
            self.circuit_breaker['is_open'] = True
            logger.warning("Circuit breaker opened due to execution failures")
    
    def _should_reset_circuit_breaker(self) -> bool:
        """Check if circuit breaker should be reset"""
        if self.circuit_breaker['last_failure']:
            time_since_failure = time.time() - self.circuit_breaker['last_failure']
            return time_since_failure >= self.circuit_breaker['reset_time']
        return False
    
    def _reset_circuit_breaker(self):
        """Reset circuit breaker"""
        self.circuit_breaker['is_open'] = False
        self.circuit_breaker['failure_count'] = 0
        logger.info("Circuit breaker reset")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get execution performance statistics"""
        stats = self.execution_stats.copy()
        stats['success_rate'] = (
            stats['successful_orders'] / max(stats['total_orders'], 1) * 100
        )
        stats['circuit_breaker_status'] = 'OPEN' if self.circuit_breaker['is_open'] else 'CLOSED'
        return stats
    
    async def shutdown(self):
        """Shutdown execution engine"""
        logger.info("Shutting down high-speed execution engine...")
        
        # Cancel all workers
        for worker in self.execution_workers:
            worker.cancel()
        
        # Close connection pool
        await self.connection_pool.close_all()
        
        logger.info("High-speed execution engine shutdown complete")

class SmartOrderRouter:
    """
    Intelligent order routing for best execution
    Routes orders to optimal exchanges based on liquidity, latency, and fees
    """

    def __init__(self, exchanges: Dict[str, Any], executor: HighSpeedExecutor):
        self.exchanges = exchanges
        self.executor = executor

        # Exchange performance tracking
        self.exchange_metrics = {}
        for exchange in exchanges.keys():
            self.exchange_metrics[exchange] = {
                'avg_latency': 0.0,
                'success_rate': 100.0,
                'last_update': time.time(),
                'order_count': 0,
                'total_latency': 0.0,
                'failures': 0
            }

        # Real-time liquidity tracking
        self.liquidity_cache = {}
        self.liquidity_update_interval = 5  # seconds

        logger.info("SmartOrderRouter initialized")

    async def route_order_optimal(self, symbol: str, side: str, amount: Decimal,
                                 order_type: str = "market") -> FastOrder:
        """Route order to optimal exchange"""
        try:
            # Get best exchange for this order
            best_exchange = await self._select_best_exchange(symbol, side, amount)

            # Create optimized order
            order = FastOrder(
                order_id=f"fast_{int(time.time() * 1000000)}",
                symbol=symbol,
                side=side,
                amount=amount,
                order_type=order_type,
                exchange=best_exchange,
                priority=1,  # Highest priority
                timeout_ms=3000  # 3 second timeout for speed
            )

            logger.info(f"Routing {side} order for {symbol} to {best_exchange}")
            return order

        except Exception as e:
            logger.error(f"Order routing failed: {e}")
            # Fallback to first available exchange
            fallback_exchange = list(self.exchanges.keys())[0]
            return FastOrder(
                order_id=f"fallback_{int(time.time() * 1000000)}",
                symbol=symbol,
                side=side,
                amount=amount,
                order_type=order_type,
                exchange=fallback_exchange,
                priority=2
            )

    async def _select_best_exchange(self, symbol: str, side: str, amount: Decimal) -> str:
        """Select best exchange based on multiple factors"""
        scores = {}

        for exchange_name in self.exchanges.keys():
            try:
                score = await self._calculate_exchange_score(
                    exchange_name, symbol, side, amount
                )
                scores[exchange_name] = score
            except Exception as e:
                logger.warning(f"Failed to score {exchange_name}: {e}")
                scores[exchange_name] = 0

        # Return exchange with highest score
        best_exchange = max(scores.items(), key=lambda x: x[1])[0]
        logger.debug(f"Exchange scores: {scores}, selected: {best_exchange}")

        return best_exchange

    async def _calculate_exchange_score(self, exchange_name: str, symbol: str,
                                      side: str, amount: Decimal) -> float:
        """Calculate exchange score for order routing"""
        metrics = self.exchange_metrics[exchange_name]

        # Base score factors
        latency_score = max(0, 100 - metrics['avg_latency'])  # Lower latency = higher score
        reliability_score = metrics['success_rate']

        # Liquidity score
        liquidity_score = await self._get_liquidity_score(exchange_name, symbol, side, amount)

        # Fee score (simplified - could be enhanced with real fee data)
        fee_score = self._get_fee_score(exchange_name)

        # Weighted total score
        total_score = (
            latency_score * 0.3 +
            reliability_score * 0.3 +
            liquidity_score * 0.3 +
            fee_score * 0.1
        )

        return total_score

    async def _get_liquidity_score(self, exchange_name: str, symbol: str,
                                 side: str, amount: Decimal) -> float:
        """Get liquidity score for exchange/symbol combination"""
        try:
            # Check cache first
            cache_key = f"{exchange_name}_{symbol}"
            current_time = time.time()

            if (cache_key in self.liquidity_cache and
                current_time - self.liquidity_cache[cache_key]['timestamp'] < self.liquidity_update_interval):
                return self.liquidity_cache[cache_key]['score']

            # Get real-time order book data if available
            exchange_client = self.exchanges[exchange_name]
            if hasattr(exchange_client, 'get_order_book'):
                order_book = await exchange_client.get_order_book(symbol)

                # Calculate liquidity score based on order book depth
                if side.upper() == 'BUY':
                    relevant_orders = order_book.get('asks', [])
                else:
                    relevant_orders = order_book.get('bids', [])

                # Calculate available liquidity
                total_liquidity = sum(float(order[1]) for order in relevant_orders[:10])
                liquidity_ratio = min(total_liquidity / float(amount), 10.0)
                score = liquidity_ratio * 10  # Scale to 0-100

                # Cache the result
                self.liquidity_cache[cache_key] = {
                    'score': score,
                    'timestamp': current_time
                }

                return score
            else:
                # Default score if no order book available
                return 50.0

        except Exception as e:
            logger.warning(f"Failed to get liquidity score for {exchange_name}: {e}")
            return 50.0  # Default score

    def _get_fee_score(self, exchange_name: str) -> float:
        """Get fee score for exchange (higher score = lower fees)"""
        # Simplified fee scoring - could be enhanced with real fee data
        fee_scores = {
            'coinbase': 85.0,  # Higher fees but good execution
            'bybit': 90.0,     # Lower fees
            'binance': 88.0    # Competitive fees
        }
        return fee_scores.get(exchange_name, 75.0)

    def update_exchange_metrics(self, exchange_name: str, execution_result: ExecutionResult):
        """Update exchange performance metrics"""
        if exchange_name not in self.exchange_metrics:
            return

        metrics = self.exchange_metrics[exchange_name]
        metrics['order_count'] += 1

        if execution_result.success:
            # Update latency
            metrics['total_latency'] += execution_result.execution_time_ms
            metrics['avg_latency'] = metrics['total_latency'] / metrics['order_count']

            # Update success rate
            success_count = metrics['order_count'] - metrics['failures']
            metrics['success_rate'] = (success_count / metrics['order_count']) * 100
        else:
            metrics['failures'] += 1
            metrics['success_rate'] = ((metrics['order_count'] - metrics['failures']) /
                                     metrics['order_count']) * 100

        metrics['last_update'] = time.time()

        logger.debug(f"Updated metrics for {exchange_name}: "
                    f"latency={metrics['avg_latency']:.2f}ms, "
                    f"success_rate={metrics['success_rate']:.1f}%")

# Global instances for fast access
_high_speed_executor = None
_smart_order_router = None

async def get_high_speed_executor(exchanges: Dict[str, Any]) -> HighSpeedExecutor:
    """Get or create global high-speed executor"""
    global _high_speed_executor
    if _high_speed_executor is None:
        _high_speed_executor = HighSpeedExecutor(exchanges)
        await _high_speed_executor.start_execution_engine()
    return _high_speed_executor

async def get_smart_order_router(exchanges: Dict[str, Any]) -> SmartOrderRouter:
    """Get or create global smart order router"""
    global _smart_order_router, _high_speed_executor
    if _smart_order_router is None:
        if _high_speed_executor is None:
            _high_speed_executor = await get_high_speed_executor(exchanges)
        _smart_order_router = SmartOrderRouter(exchanges, _high_speed_executor)
    return _smart_order_router
