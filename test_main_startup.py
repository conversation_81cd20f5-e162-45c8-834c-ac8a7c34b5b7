#!/usr/bin/env python3
"""
Test script to identify what's causing main.py to hang during startup
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Setup paths
PROJECT_ROOT = Path("X:/autogpt_trade_project/The_real_deal/autogpt-trader")
SRC_DIR = PROJECT_ROOT / "src"
sys.path.insert(0, str(PROJECT_ROOT))
sys.path.insert(0, str(SRC_DIR))

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment_setup():
    """Test environment setup"""
    try:
        logger.info("🔧 [TEST] Testing environment setup...")
        
        # Test 1: Environment variables
        os.environ["LIVE_TRADING"] = "true"
        os.environ["REAL_MONEY_TRADING"] = "true" 
        os.environ["DEMO_MODE"] = "false"
        os.environ["DRY_RUN"] = "false"
        os.environ["TRADING_MODE"] = "live"
        os.environ["BYBIT_ONLY_MODE"] = "true"
        os.environ["COINBASE_ENABLED"] = "false"
        
        logger.info("✅ [TEST] Environment variables set")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Environment setup failed: {e}")
        return False

def test_basic_imports():
    """Test basic imports that might be causing hangs"""
    try:
        logger.info("🔧 [TEST] Testing basic imports...")
        
        # Test imports one by one to identify which one hangs
        logger.info("🔧 [TEST] Importing datetime...")
        from datetime import datetime
        
        logger.info("🔧 [TEST] Importing decimal...")
        from decimal import Decimal
        
        logger.info("🔧 [TEST] Importing dotenv...")
        from dotenv import load_dotenv
        
        logger.info("🔧 [TEST] Importing typing...")
        from typing import Dict, Optional, Any
        
        logger.info("✅ [TEST] Basic imports successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Basic imports failed: {e}")
        return False

def test_neural_imports():
    """Test neural network imports that might be hanging"""
    try:
        logger.info("🔧 [TEST] Testing neural imports...")
        
        # Test TensorFlow deterministic setup
        logger.info("🔧 [TEST] Setting TensorFlow environment...")
        os.environ.update({
            'TF_ENABLE_ONEDNN_OPTS': '0',
            'TF_DETERMINISTIC_OPS': '1',
            'PYTHONHASHSEED': '0',
            'TF_CUDNN_DETERMINISTIC': '1',
            'TF_CPP_MIN_LOG_LEVEL': '2'
        })
        
        # Test numpy import
        logger.info("🔧 [TEST] Importing numpy...")
        import numpy as np
        np.random.seed(42)
        
        # Test TensorFlow import (this might be hanging)
        logger.info("🔧 [TEST] Importing TensorFlow...")
        try:
            import tensorflow as tf
            tf.random.set_seed(42)
            logger.info("✅ [TEST] TensorFlow imported successfully")
        except ImportError:
            logger.warning("⚠️ [TEST] TensorFlow not available")
        
        logger.info("✅ [TEST] Neural imports successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Neural imports failed: {e}")
        return False

def test_credential_loading():
    """Test credential loading that might be hanging"""
    try:
        logger.info("🔧 [TEST] Testing credential loading...")
        
        # Test .env loading
        env_file = PROJECT_ROOT / ".env"
        if env_file.exists():
            from dotenv import load_dotenv
            load_dotenv(env_file)
            logger.info("✅ [TEST] .env file loaded")
        else:
            logger.warning("⚠️ [TEST] .env file not found")
        
        # Test credential decryption import
        logger.info("🔧 [TEST] Testing credential decryption import...")
        try:
            from src.utils.cryptography.secure_credentials import decrypt_value
            logger.info("✅ [TEST] Credential decryption imported")
        except ImportError as e:
            logger.warning(f"⚠️ [TEST] Credential decryption not available: {e}")
        
        logger.info("✅ [TEST] Credential loading successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Credential loading failed: {e}")
        return False

def test_trading_components():
    """Test trading component imports"""
    try:
        logger.info("🔧 [TEST] Testing trading component imports...")
        
        # Test IntelligentCurrencySwitcher (our fix)
        logger.info("🔧 [TEST] Importing IntelligentCurrencySwitcher...")
        from src.trading.intelligent_currency_switcher import IntelligentCurrencySwitcher
        logger.info("✅ [TEST] IntelligentCurrencySwitcher imported")
        
        # Test MultiCurrencyTradingEngine
        logger.info("🔧 [TEST] Importing MultiCurrencyTradingEngine...")
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        logger.info("✅ [TEST] MultiCurrencyTradingEngine imported")
        
        # Test EnhancedSignalGenerator
        logger.info("🔧 [TEST] Importing EnhancedSignalGenerator...")
        from src.trading.enhanced_signal_generator import EnhancedSignalGenerator
        logger.info("✅ [TEST] EnhancedSignalGenerator imported")
        
        logger.info("✅ [TEST] Trading component imports successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Trading component imports failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 [TEST] Starting main.py startup diagnostic...")
    
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Basic Imports", test_basic_imports),
        ("Neural Imports", test_neural_imports),
        ("Credential Loading", test_credential_loading),
        ("Trading Components", test_trading_components),
    ]
    
    for test_name, test_func in tests:
        logger.info(f"🧪 [TEST] Running {test_name}...")
        try:
            success = test_func()
            if success:
                logger.info(f"✅ [TEST] {test_name} PASSED")
            else:
                logger.error(f"❌ [TEST] {test_name} FAILED")
                break
        except Exception as e:
            logger.error(f"❌ [TEST] {test_name} CRASHED: {e}")
            break
    
    logger.info("🎯 [TEST] Diagnostic complete - check results above")

if __name__ == "__main__":
    main()
