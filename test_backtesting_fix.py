#!/usr/bin/env python3
"""
BACKTESTING SYSTEM FIX VALIDATION
=================================

Test script to validate that the backtesting system fixes are working correctly.
This will test the missing methods and signal forwarding mechanism.

Author: AutoGPT Trader System
Date: June 2025
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from datetime import datetime
from decimal import Decimal

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/backtesting_fix_test.log')
    ]
)

logger = logging.getLogger(__name__)

async def test_backtesting_engine_methods():
    """Test that the RealTimeBacktestingEngine has all required methods"""
    logger.info("🔍 [TEST] Testing RealTimeBacktestingEngine methods...")
    
    try:
        from src.trading.backtesting import RealTimeBacktestingEngine
        
        # Create engine instance
        config = {
            'initial_balance': 10000.0,
            'risk_per_trade': 0.02,
            'enable_learning': True,
            'sync_with_live': True,
            'update_interval': 60
        }
        
        engine = RealTimeBacktestingEngine(config)
        
        # Check that all required methods exist
        required_methods = [
            '_process_live_signals',
            '_simulate_trades', 
            '_update_performance_metrics',
            '_sync_with_live_data',
            '_process_signal_for_backtest',
            '_simulate_trade_execution',
            '_calculate_backtest_position_size'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(engine, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            logger.error(f"❌ [FAIL] Missing methods: {missing_methods}")
            return False
        else:
            logger.info("✅ [PASS] All required methods exist")
            return True
            
    except Exception as e:
        logger.error(f"❌ [ERROR] Failed to test engine methods: {e}")
        return False

async def test_signal_processing():
    """Test signal processing in backtesting engine"""
    logger.info("🔍 [TEST] Testing signal processing...")
    
    try:
        from src.trading.backtesting import RealTimeBacktestingEngine
        
        config = {
            'initial_balance': 10000.0,
            'risk_per_trade': 0.02
        }
        
        engine = RealTimeBacktestingEngine(config)
        
        # Create test signal
        test_signal = {
            'symbol': 'BTCUSDT',
            'action': 'BUY',
            'confidence': 0.8,
            'price': 103000.0,
            'amount': 0.001,
            'strategy': 'test',
            'timestamp': datetime.now()
        }
        
        # Test signal processing
        initial_trades = len(engine.simulated_trades)
        await engine._process_signal_for_backtest(test_signal)
        final_trades = len(engine.simulated_trades)
        
        if final_trades > initial_trades:
            logger.info(f"✅ [PASS] Signal processed successfully (trades: {initial_trades} → {final_trades})")
            return True
        else:
            logger.warning(f"⚠️ [WARN] Signal not processed (trades: {initial_trades} → {final_trades})")
            return False
            
    except Exception as e:
        logger.error(f"❌ [ERROR] Failed to test signal processing: {e}")
        return False

async def test_parallel_backtesting_startup():
    """Test that parallel backtesting can start without errors"""
    logger.info("🔍 [TEST] Testing parallel backtesting startup...")
    
    try:
        from src.trading.backtesting import RealTimeBacktestingEngine
        
        config = {
            'initial_balance': 10000.0,
            'risk_per_trade': 0.02
        }
        
        engine = RealTimeBacktestingEngine(config)
        
        # Mock live trading system
        class MockLiveTradingSystem:
            def __init__(self):
                self.latest_signals = []
                self.components = {}
        
        mock_system = MockLiveTradingSystem()
        market_data_feed = {'source': 'test', 'real_time': True}
        
        # Test that startup doesn't crash immediately
        startup_task = asyncio.create_task(
            engine.start_parallel_backtesting(market_data_feed, mock_system)
        )
        
        # Let it run for a short time
        await asyncio.sleep(2)
        
        # Check if engine is running
        if engine.is_running:
            logger.info("✅ [PASS] Parallel backtesting started successfully")
            
            # Stop the engine
            engine.is_running = False
            startup_task.cancel()
            
            try:
                await startup_task
            except asyncio.CancelledError:
                pass
            
            return True
        else:
            logger.error("❌ [FAIL] Engine not running after startup")
            return False
            
    except Exception as e:
        logger.error(f"❌ [ERROR] Failed to test parallel startup: {e}")
        return False

async def test_price_forwarding_fix():
    """Test that the price forwarding fix works"""
    logger.info("🔍 [TEST] Testing price forwarding fix...")
    
    try:
        # Test the _get_current_price_for_symbol method fix
        from main import ComprehensiveLiveTradingSystem
        
        # Create a minimal system instance
        system = ComprehensiveLiveTradingSystem()
        system.components = {}
        
        # Test price retrieval with fallback
        price = await system._get_current_price_for_symbol('BTCUSDT', None)
        
        if price > 0:
            logger.info(f"✅ [PASS] Price retrieval working: BTCUSDT = ${price}")
            return True
        else:
            logger.error("❌ [FAIL] Price retrieval returned 0")
            return False
            
    except Exception as e:
        logger.error(f"❌ [ERROR] Failed to test price forwarding: {e}")
        return False

async def main():
    """Run all backtesting fix validation tests"""
    logger.info("🚀 [START] Backtesting System Fix Validation")
    logger.info("=" * 60)
    
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)
    
    tests = [
        ("Engine Methods", test_backtesting_engine_methods),
        ("Signal Processing", test_signal_processing),
        ("Parallel Startup", test_parallel_backtesting_startup),
        ("Price Forwarding", test_price_forwarding_fix)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 [TEST] Running {test_name}...")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"📊 [RESULT] {test_name}: {status}")
        except Exception as e:
            logger.error(f"❌ [ERROR] {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 [SUMMARY] Backtesting Fix Validation Results")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"   {test_name}: {status}")
    
    logger.info(f"\n🎯 [FINAL] {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 [SUCCESS] All backtesting fixes validated successfully!")
        logger.info("✅ System ready for live trading with working backtesting")
    else:
        logger.error("❌ [FAILURE] Some backtesting fixes need attention")
        logger.error("⚠️ Review failed tests before proceeding with live trading")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
