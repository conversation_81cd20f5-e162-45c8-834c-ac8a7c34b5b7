#!/usr/bin/env python3
"""
Production Readiness System Validator
Provides comprehensive production-readiness validation with trading system validation,
required file verification, dependency checking, system health assessment, and error diagnosis.
"""

import sys
import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple
import importlib.util

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemValidator:
    """Production readiness system validator"""
    
    def __init__(self):
        self.results = {}
        self.errors = []
        self.warnings = []
        self.critical_issues = []
        
    def validate_system(self) -> Dict[str, Any]:
        """Run comprehensive system validation"""
        logger.info("🔍 Starting production readiness validation...")
        
        validation_categories = [
            ("Trading System", self._validate_trading_system),
            ("Required Files", self._validate_required_files),
            ("Dependencies", self._validate_dependencies),
            ("Configuration", self._validate_configuration),
            ("Security", self._validate_security),
            ("Exchange Integration", self._validate_exchange_integration),
            ("Neural Networks", self._validate_neural_networks),
            ("Monitoring", self._validate_monitoring),
            ("System Health", self._validate_system_health)
        ]
        
        for category, validator in validation_categories:
            logger.info(f"📋 Validating: {category}")
            try:
                result = validator()
                self.results[category] = result
                
                if result.get('status') == 'PASS':
                    logger.info(f"✅ {category}: PASSED")
                elif result.get('status') == 'CRITICAL':
                    logger.error(f"🚨 {category}: CRITICAL FAILURE")
                    self.critical_issues.append(category)
                else:
                    logger.warning(f"⚠️ {category}: {result.get('status', 'UNKNOWN')}")
                    
            except Exception as e:
                self.results[category] = {'status': 'ERROR', 'error': str(e)}
                self.errors.append(f"{category}: {str(e)}")
                logger.error(f"❌ {category}: ERROR - {str(e)}")
        
        return self._generate_production_report()
    
    def _validate_trading_system(self) -> Dict[str, Any]:
        """Validate core trading system components"""
        try:
            # Check main entry points
            main_files = ['main.py', 'working_live_trader.py', 'bootstrap.py']
            available_entry_points = []
            
            for file in main_files:
                if Path(file).exists():
                    available_entry_points.append(file)
            
            # Test core trading imports
            try:
                from src.trading.core import LiveCryptoTrader
                trading_core_available = True
            except ImportError:
                trading_core_available = False
            
            # Check neural components
            try:
                from src.neural.hybrid_agent import HybridTradingAgent
                neural_available = True
            except ImportError:
                neural_available = False
            
            if not available_entry_points:
                return {'status': 'CRITICAL', 'error': 'No main entry points found'}
            
            return {
                'status': 'PASS',
                'entry_points': available_entry_points,
                'trading_core': trading_core_available,
                'neural_components': neural_available
            }
        except Exception as e:
            return {'status': 'FAIL', 'error': str(e)}
    
    def _validate_required_files(self) -> Dict[str, Any]:
        """Validate required file verification"""
        required_files = [
            '.env',
            'credential_decryptor_fixed.py',
            'src/utils/cryptography/private.pem',
            'src/utils/cryptography/hybrid.py',
            'requirements.txt'
        ]
        
        optional_files = [
            'config/config.json',
            'MASTER_LIVE_TRADING_LAUNCHER.bat',
            'UNIFIED_LIVE_TRADING_FLOW.py'
        ]
        
        missing_required = []
        missing_optional = []
        
        for file in required_files:
            if not Path(file).exists():
                missing_required.append(file)
        
        for file in optional_files:
            if not Path(file).exists():
                missing_optional.append(file)
        
        if missing_required:
            return {
                'status': 'CRITICAL',
                'missing_required': missing_required,
                'missing_optional': missing_optional
            }
        
        return {
            'status': 'PASS',
            'all_required_present': True,
            'missing_optional': missing_optional
        }
    
    def _validate_dependencies(self) -> Dict[str, Any]:
        """Validate dependency checking"""
        critical_dependencies = [
            'cryptography', 'ccxt', 'numpy', 'pandas', 'torch',
            'stable_baselines3', 'redis', 'aiohttp', 'websockets'
        ]
        
        missing_deps = []
        available_deps = []
        
        for dep in critical_dependencies:
            try:
                importlib.import_module(dep)
                available_deps.append(dep)
            except ImportError:
                missing_deps.append(dep)
        
        if missing_deps:
            return {
                'status': 'FAIL',
                'missing_dependencies': missing_deps,
                'available_dependencies': available_deps
            }
        
        return {
            'status': 'PASS',
            'all_dependencies_available': True,
            'dependency_count': len(available_deps)
        }
    
    def _validate_configuration(self) -> Dict[str, Any]:
        """Validate system configuration"""
        try:
            # Check environment configuration
            env_vars = {
                'LIVE_TRADING': os.getenv('LIVE_TRADING'),
                'DEMO_MODE': os.getenv('DEMO_MODE'),
                'REAL_MONEY_TRADING': os.getenv('REAL_MONEY_TRADING'),
                'USE_TESTNET': os.getenv('USE_TESTNET'),
                'DRY_RUN': os.getenv('DRY_RUN')
            }
            
            # Validate live trading configuration
            live_trading = str(env_vars['LIVE_TRADING']).lower() == 'true'
            demo_mode = str(env_vars['DEMO_MODE']).lower() == 'true'
            real_money = str(env_vars['REAL_MONEY_TRADING']).lower() == 'true'
            
            config_valid = live_trading and not demo_mode and real_money
            
            # Check config.json if exists
            config_file_valid = True
            if Path('config/config.json').exists():
                try:
                    with open('config/config.json', 'r') as f:
                        config_data = json.load(f)
                        config_file_valid = True
                except json.JSONDecodeError:
                    config_file_valid = False
            
            if not config_valid:
                return {
                    'status': 'CRITICAL',
                    'error': 'Invalid live trading configuration',
                    'environment_variables': env_vars
                }
            
            return {
                'status': 'PASS',
                'live_trading_configured': config_valid,
                'config_file_valid': config_file_valid,
                'environment_variables': env_vars
            }
        except Exception as e:
            return {'status': 'FAIL', 'error': str(e)}
    
    def _validate_security(self) -> Dict[str, Any]:
        """Validate security systems"""
        try:
            from credential_decryptor_fixed import CredentialDecryptor
            from src.utils.cryptography.hybrid import HybridCrypto
            
            # Test credential decryptor
            decryptor = CredentialDecryptor()
            decryption_available = decryptor.decryption_available
            
            # Test HybridCrypto
            private_key_path = 'src/utils/cryptography/private.pem'
            hybrid_crypto_available = Path(private_key_path).exists()
            
            # Check encrypted credentials
            encrypted_vars = [
                'ENCRYPTED_COINBASE_API_PRIVATE_KEY',
                'ENCRYPTED_BYBIT_API_KEY',
                'ENCRYPTED_BYBIT_API_SECRET'
            ]
            
            encrypted_creds_present = sum(1 for var in encrypted_vars if os.getenv(var))
            
            if not decryption_available:
                return {'status': 'CRITICAL', 'error': 'Credential decryption not available'}
            
            return {
                'status': 'PASS',
                'credential_decryption': decryption_available,
                'hybrid_crypto': hybrid_crypto_available,
                'encrypted_credentials': encrypted_creds_present
            }
        except Exception as e:
            return {'status': 'FAIL', 'error': str(e)}
    
    def _validate_exchange_integration(self) -> Dict[str, Any]:
        """Validate exchange integration"""
        try:
            from src.exchanges.coinbase_advanced_client import CoinbaseAdvancedClient
            from src.exchanges.bybit_client_fixed import BybitClientFixed
            
            # Test credential validation
            from credential_decryptor_fixed import CredentialDecryptor
            decryptor = CredentialDecryptor()
            
            coinbase_valid = decryptor.validate_credentials('coinbase')
            bybit_valid = decryptor.validate_credentials('bybit')
            
            exchange_status = {
                'coinbase': 'VALID' if coinbase_valid else 'INVALID',
                'bybit': 'VALID' if bybit_valid else 'INVALID'
            }
            
            valid_exchanges = sum(1 for status in exchange_status.values() if status == 'VALID')
            
            if valid_exchanges == 0:
                return {'status': 'CRITICAL', 'error': 'No valid exchange credentials'}
            
            return {
                'status': 'PASS',
                'exchange_credentials': exchange_status,
                'valid_exchanges': valid_exchanges
            }
        except Exception as e:
            return {'status': 'FAIL', 'error': str(e)}
    
    def _validate_neural_networks(self) -> Dict[str, Any]:
        """Validate neural network components"""
        try:
            from src.neural.hybrid_agent import HybridTradingAgent
            from src.neural.directory.ltsm_processor import LSTMTradingProcessor
            
            # Test neural component availability
            neural_components = ['HybridTradingAgent', 'LSTMTradingProcessor']
            
            return {
                'status': 'PASS',
                'neural_components': neural_components,
                'components_available': len(neural_components)
            }
        except ImportError as e:
            return {'status': 'FAIL', 'error': f'Neural components not available: {str(e)}'}
    
    def _validate_monitoring(self) -> Dict[str, Any]:
        """Validate monitoring systems"""
        try:
            from src.monitoring.service import MonitoringService
            from src.monitoring.data_collector import DataCollector
            
            monitoring_components = ['MonitoringService', 'DataCollector']
            
            return {
                'status': 'PASS',
                'monitoring_available': True,
                'components': monitoring_components
            }
        except ImportError:
            return {'status': 'PARTIAL', 'error': 'Some monitoring components not available'}
    
    def _validate_system_health(self) -> Dict[str, Any]:
        """Validate overall system health"""
        try:
            import psutil
            
            # Check system resources
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Health thresholds
            cpu_healthy = cpu_percent < 80
            memory_healthy = memory.percent < 85
            disk_healthy = disk.percent < 90
            
            overall_healthy = cpu_healthy and memory_healthy and disk_healthy
            
            return {
                'status': 'PASS' if overall_healthy else 'WARNING',
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': disk.percent,
                'system_healthy': overall_healthy
            }
        except Exception as e:
            return {'status': 'PARTIAL', 'error': str(e)}
    
    def _generate_production_report(self) -> Dict[str, Any]:
        """Generate comprehensive production readiness report"""
        passed_tests = sum(1 for result in self.results.values() 
                          if result.get('status') == 'PASS')
        total_tests = len(self.results)
        
        # Determine overall readiness
        if self.critical_issues:
            overall_status = 'NOT_READY'
            readiness = 'CRITICAL ISSUES FOUND'
        elif self.errors:
            overall_status = 'PARTIAL'
            readiness = 'ISSUES NEED ATTENTION'
        elif passed_tests == total_tests:
            overall_status = 'READY'
            readiness = 'PRODUCTION READY'
        else:
            overall_status = 'PARTIAL'
            readiness = 'MINOR ISSUES'
        
        return {
            'production_readiness': overall_status,
            'readiness_status': readiness,
            'tests_passed': passed_tests,
            'total_tests': total_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'critical_issues': self.critical_issues,
            'detailed_results': self.results,
            'errors': self.errors,
            'warnings': self.warnings,
            'recommendation': self._get_recommendation(overall_status)
        }
    
    def _get_recommendation(self, status: str) -> str:
        """Get recommendation based on validation status"""
        if status == 'READY':
            return "System is ready for production live trading"
        elif status == 'PARTIAL':
            return "Address warnings before production deployment"
        else:
            return "Critical issues must be resolved before live trading"


def main():
    """Main validation function"""
    print("🔍 AutoGPT Trader - Production Readiness Validation")
    print("=" * 70)
    
    validator = SystemValidator()
    report = validator.validate_system()
    
    print("\n📊 PRODUCTION READINESS REPORT")
    print("=" * 70)
    print(f"Overall Status: {report['production_readiness']}")
    print(f"Readiness: {report['readiness_status']}")
    print(f"Tests Passed: {report['tests_passed']}/{report['total_tests']}")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    
    if report['critical_issues']:
        print("\n🚨 CRITICAL ISSUES:")
        for issue in report['critical_issues']:
            print(f"  - {issue}")
    
    if report['errors']:
        print("\n❌ ERRORS:")
        for error in report['errors']:
            print(f"  - {error}")
    
    if report['warnings']:
        print("\n⚠️ WARNINGS:")
        for warning in report['warnings']:
            print(f"  - {warning}")
    
    print(f"\n💡 RECOMMENDATION: {report['recommendation']}")
    
    return report['production_readiness'] == 'READY'


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
