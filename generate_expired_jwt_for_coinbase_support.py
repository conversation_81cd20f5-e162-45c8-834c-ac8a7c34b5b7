#!/usr/bin/env python3
"""
Generate Expired JWT Token for Coinbase Support
This script creates a properly formatted but expired JWT token to help Coinbase support debug authentication issues.
"""

import os
import sys
import time
import jwt
import json
import base64
from pathlib import Path
from dotenv import load_dotenv
from cryptography.hazmat.primitives import serialization

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

def load_environment():
    """Load environment variables"""
    load_dotenv()
    print("✅ Environment loaded")

def get_credentials():
    """Get and decrypt Coinbase credentials"""
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        # Get encrypted credentials
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        if not encrypted_api_key or not encrypted_private_key:
            print("❌ Missing encrypted credentials in .env file")
            return None, None, None
        
        # Decrypt credentials
        api_key = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)
        
        # Extract key ID from API key
        if "/apiKeys/" in api_key:
            key_id = api_key.split("/apiKeys/")[1]
        else:
            print("❌ Cannot extract key ID from API key format")
            return None, None, None
        
        print("✅ Credentials decrypted successfully")
        print(f"📋 API Key: {api_key}")
        print(f"📋 Key ID: {key_id}")
        print(f"📋 Private Key Length: {len(private_key_pem)} characters")
        
        return api_key, private_key_pem, key_id
        
    except Exception as e:
        print(f"❌ Failed to get credentials: {e}")
        return None, None, None

def load_private_key(private_key_pem):
    """Load and validate the private key"""
    try:
        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )
        print("✅ Private key loaded successfully")
        return private_key
    except Exception as e:
        print(f"❌ Failed to load private key: {e}")
        return None

def generate_expired_jwt(api_key, private_key, key_id):
    """Generate an expired JWT token that matches Coinbase documentation exactly"""
    try:
        # Use a timestamp from 10 minutes ago to ensure it's expired
        past_time = int(time.time()) - 600  # 10 minutes ago
        
        # Create JWT payload exactly as per Coinbase documentation
        payload = {
            'iss': 'cdp',                    # Issuer: Coinbase Developer Platform
            'nbf': past_time,                # Not before (10 minutes ago)
            'exp': past_time + 120,          # Expires 2 minutes after nbf (8 minutes ago - definitely expired)
            'sub': api_key,                  # Subject: Full API key name
            'uri': 'GET /api/v3/brokerage/accounts'  # The endpoint we're trying to access
        }
        
        # Create JWT headers exactly as per Coinbase documentation
        headers = {
            'alg': 'ES256',                  # Algorithm: ECDSA with SHA-256
            'kid': key_id,                   # Key ID
            'typ': 'JWT'                     # Token type
        }
        
        # Generate the JWT token
        token = jwt.encode(
            payload,
            private_key,
            algorithm='ES256',
            headers=headers
        )
        
        print("✅ Expired JWT token generated successfully")
        return token, payload, headers
        
    except Exception as e:
        print(f"❌ Failed to generate JWT: {e}")
        return None, None, None

def decode_and_analyze_jwt(token):
    """Decode and analyze the JWT token structure"""
    try:
        # Split the JWT into its parts
        header_b64, payload_b64, signature_b64 = token.split('.')
        
        # Decode header and payload (add padding if needed)
        def decode_base64_url(data):
            # Add padding if needed
            missing_padding = len(data) % 4
            if missing_padding:
                data += '=' * (4 - missing_padding)
            return base64.urlsafe_b64decode(data)
        
        header = json.loads(decode_base64_url(header_b64))
        payload = json.loads(decode_base64_url(payload_b64))
        
        print("\n📋 JWT Token Analysis:")
        print(f"   Token Length: {len(token)} characters")
        print(f"   Header: {json.dumps(header, indent=2)}")
        print(f"   Payload: {json.dumps(payload, indent=2)}")
        print(f"   Signature Length: {len(signature_b64)} characters")
        
        # Check if token is expired
        current_time = int(time.time())
        if payload['exp'] < current_time:
            expired_minutes = (current_time - payload['exp']) // 60
            print(f"   ✅ Token is EXPIRED (expired {expired_minutes} minutes ago)")
        else:
            print(f"   ❌ Token is still VALID")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to analyze JWT: {e}")
        return False

def main():
    """Main function to generate expired JWT for Coinbase support"""
    print("🔧 Generating Expired JWT Token for Coinbase Support")
    print("=" * 60)
    
    # Load environment
    load_environment()
    
    # Get credentials
    api_key, private_key_pem, key_id = get_credentials()
    if not all([api_key, private_key_pem, key_id]):
        print("❌ Failed to get required credentials")
        return False
    
    # Load private key
    private_key = load_private_key(private_key_pem)
    if not private_key:
        return False
    
    # Generate expired JWT
    token, payload, headers = generate_expired_jwt(api_key, private_key, key_id)
    if not token:
        return False
    
    # Analyze the token
    decode_and_analyze_jwt(token)
    
    # Output the expired JWT for Coinbase support
    print("\n" + "=" * 60)
    print("📤 EXPIRED JWT TOKEN FOR COINBASE SUPPORT:")
    print("=" * 60)
    print(f"{token}")
    print("=" * 60)
    
    print("\n📋 Token Details for Support:")
    print(f"   Organization ID: 7405b51f-cfea-4f54-a52d-02838b5cb217")
    print(f"   API Key ID: {key_id}")
    print(f"   Algorithm: ES256")
    print(f"   Endpoint: GET /api/v3/brokerage/accounts")
    print(f"   Token Status: EXPIRED")
    
    print("\n✅ Copy the token above and send it to Coinbase support")
    print("   This expired token will help them debug the authentication issue")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
