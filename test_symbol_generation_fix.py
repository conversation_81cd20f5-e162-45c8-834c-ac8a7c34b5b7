#!/usr/bin/env python3
"""
Test script to verify the symbol generation fixes.
"""

import os
import sys
import json
import asyncio
from decimal import Decimal

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

async def test_symbol_generation():
    """Test the fixed symbol generation logic"""
    print("🔧 [TEST] Testing symbol generation fixes...")
    
    try:
        # Import after path setup
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        
        # Load config manually
        config_file = os.path.join(os.path.dirname(__file__), 'config', 'config.json')
        if not os.path.exists(config_file):
            print("❌ [ERROR] Config file not found")
            return False
            
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        bybit_config = config.get('exchanges', {}).get('bybit', {})
        if not bybit_config.get('api_key') or not bybit_config.get('api_secret'):
            print("❌ [ERROR] Bybit credentials not found")
            return False
        
        # Initialize client
        print("🔧 [TEST] Initializing Bybit client...")
        client = BybitClientFixed(
            api_key=bybit_config['api_key'],
            api_secret=bybit_config['api_secret'],
            testnet=False
        )
        
        print("✅ [TEST] Client initialized successfully")
        
        # Initialize trading engine
        print("🔧 [TEST] Initializing trading engine...")
        engine = MultiCurrencyTradingEngine(
            exchange_clients={'bybit': client},
            config=config
        )
        
        # Test the fixed symbol generation
        print("\n🎯 [TEST] Testing symbol generation...")
        
        # Test the intelligent comprehensive generation
        valid_symbols = await engine._generate_comprehensive_trading_pairs(client, 'bybit')
        
        print(f"✅ [TEST] Generated {len(valid_symbols)} symbols")
        print(f"📋 [TEST] Sample symbols: {valid_symbols[:10]}")
        
        # Check for invalid patterns
        invalid_patterns = []
        for symbol in valid_symbols:
            # Check for invalid patterns like USDTBTC, USDTETH, etc.
            if symbol.startswith('USDT') and symbol != 'USDTUSDT':
                if symbol[4:] in ['BTC', 'ETH', 'SOL', 'ADA', 'DOT']:
                    invalid_patterns.append(symbol)
            elif symbol.startswith('USD') and symbol != 'USDUSDT':
                if symbol[3:] in ['BTC', 'ETH', 'SOL', 'ADA', 'DOT']:
                    invalid_patterns.append(symbol)
        
        if invalid_patterns:
            print(f"❌ [TEST] Found invalid patterns: {invalid_patterns}")
            return False
        else:
            print("✅ [TEST] No invalid patterns found!")
        
        # Test specific valid symbols
        expected_valid = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']
        found_valid = [s for s in expected_valid if s in valid_symbols]
        
        print(f"✅ [TEST] Found expected symbols: {found_valid}")
        
        # Test symbol validation performance
        print("\n⚡ [TEST] Testing symbol validation performance...")
        import time
        
        test_symbols = ['BTCUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']
        total_time = 0
        
        for symbol in test_symbols:
            start_time = time.time()
            is_valid = client._is_valid_bybit_symbol(symbol)
            end_time = time.time()
            
            duration = (end_time - start_time) * 1000  # Convert to ms
            total_time += duration
            
            print(f"  {'✅' if is_valid else '❌'} {symbol}: {duration:.1f}ms")
        
        avg_time = total_time / len(test_symbols)
        print(f"⚡ [TEST] Average validation time: {avg_time:.1f}ms")
        
        if avg_time < 200:  # Should be much faster than 1500ms
            print("✅ [TEST] Performance is good!")
        else:
            print("⚠️ [TEST] Performance could be better")
        
        print("\n🏁 [TEST] Symbol generation test completed!")
        return True
        
    except Exception as e:
        print(f"❌ [FATAL] Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_symbol_generation())
    sys.exit(0 if success else 1)
