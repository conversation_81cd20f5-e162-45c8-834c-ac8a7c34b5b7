#!/usr/bin/env python3
"""
Comprehensive test to debug order execution pipeline
"""

import sys
import os
import asyncio
import logging
from decimal import Decimal

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_coinbase_client_setup():
    """Test 1: Verify CoinbaseEnhancedClient setup and authentication"""
    try:
        from exchanges.coinbase_enhanced_client import CoinbaseEnhancedClient
        from utils.hybrid_crypto import HybridCrypto
        
        print("🔧 [TEST 1] Testing CoinbaseEnhancedClient setup...")
        
        # Initialize crypto for credential decryption
        crypto = HybridCrypto()
        
        # Get decrypted credentials
        try:
            api_key_name = crypto.decrypt_value('COINBASE_API_KEY_NAME')
            private_key_pem = crypto.decrypt_value('COINBASE_PRIVATE_KEY')
            key_id = api_key_name.split('-')[0] if api_key_name else None
            
            print(f"✅ [TEST 1] Credentials loaded - Key ID: {key_id}")
            
            # Initialize client
            client = CoinbaseEnhancedClient(api_key_name, private_key_pem, key_id)
            
            # Test connection
            connection_result = await client.test_connection()
            print(f"🔍 [TEST 1] Connection result: {connection_result}")
            print(f"🔍 [TEST 1] Access level: {client.access_level}")
            print(f"🔍 [TEST 1] Authenticated: {client.authenticated}")
            
            # Get authentication status
            auth_status = client.get_authentication_status()
            print(f"🔍 [TEST 1] Auth status: {auth_status}")
            
            return client, connection_result
            
        except Exception as cred_error:
            print(f"❌ [TEST 1] Credential error: {cred_error}")
            return None, False
            
    except Exception as e:
        print(f"❌ [TEST 1] Setup failed: {e}")
        return None, False

async def test_order_execution_methods(client):
    """Test 2: Verify order execution methods are available and working"""
    try:
        print("🔧 [TEST 2] Testing order execution methods...")
        
        if not client:
            print("❌ [TEST 2] No client available")
            return False
        
        # Check method availability
        methods = ['execute_order', 'place_order', 'create_market_buy_order', 'create_market_sell_order']
        available_methods = []
        
        for method in methods:
            if hasattr(client, method):
                available_methods.append(method)
                print(f"✅ [TEST 2] Method available: {method}")
            else:
                print(f"❌ [TEST 2] Method missing: {method}")
        
        if not available_methods:
            print("❌ [TEST 2] No trading methods available")
            return False
        
        # Test a small order execution (dry run style)
        print("🔍 [TEST 2] Testing execute_order method...")
        
        # Use a very small amount for testing
        test_result = await client.execute_order(
            symbol='BTC-USD',
            side='buy',
            amount=0.01,  # $0.01 worth
            order_type='market'
        )
        
        print(f"🔍 [TEST 2] Test order result: {test_result}")
        
        # Analyze the result
        if test_result:
            success = test_result.get('success', False)
            error = test_result.get('error', 'No error')
            message = test_result.get('message', 'No message')
            
            print(f"🔍 [TEST 2] Success: {success}")
            print(f"🔍 [TEST 2] Error: {error}")
            print(f"🔍 [TEST 2] Message: {message}")
            
            if success:
                print("✅ [TEST 2] Order execution method working")
                return True
            else:
                print(f"⚠️ [TEST 2] Order execution failed: {error} - {message}")
                return False
        else:
            print("❌ [TEST 2] No result from order execution")
            return False
            
    except Exception as e:
        print(f"❌ [TEST 2] Order execution test failed: {e}")
        return False

async def test_main_execution_pipeline():
    """Test 3: Test the main.py execution pipeline with mock signal"""
    try:
        print("🔧 [TEST 3] Testing main.py execution pipeline...")
        
        # This would require importing the main trading system
        # For now, let's just verify the logic fixes
        
        # Simulate the order result formats we expect
        test_cases = [
            # CoinbaseEnhancedClient format
            {'success': True, 'order_id': 'test123', 'symbol': 'BTC-USD'},
            # Standard exchange format
            {'id': 'test456', 'filled': 0.001, 'average': 50000},
            # Error case
            {'success': False, 'error': 'INSUFFICIENT_ACCESS', 'message': 'Test error'},
            # No response case
            None
        ]
        
        for i, order_result in enumerate(test_cases):
            print(f"🔍 [TEST 3.{i+1}] Testing order result: {order_result}")
            
            # Simulate the fixed logic from main.py
            order_id = order_result.get('id') or order_result.get('order_id') if order_result else None
            success = order_result.get('success', True) if order_result else False
            
            if order_result and order_id and success:
                print(f"✅ [TEST 3.{i+1}] Would succeed with order_id: {order_id}")
            else:
                error_msg = order_result.get('error', order_result.get('message', 'Unknown error')) if order_result else 'No response'
                print(f"❌ [TEST 3.{i+1}] Would fail with error: {error_msg}")
        
        print("✅ [TEST 3] Main execution pipeline logic verified")
        return True
        
    except Exception as e:
        print(f"❌ [TEST 3] Pipeline test failed: {e}")
        return False

async def test_symbol_format_fixes():
    """Test 4: Verify symbol format fixes"""
    try:
        print("🔧 [TEST 4] Testing symbol format fixes...")
        
        # Check that invalid symbols are not in the codebase
        invalid_symbols = ['ADABTC', 'SOLETH', 'ADAETH', 'DOTETH', 'SOLBTC', 'DOTBTC']
        
        # Read main.py to check for invalid symbols
        try:
            with open('main.py', 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            with open('main.py', 'r', encoding='latin-1') as f:
                content = f.read()
        
        found_invalid = []
        for symbol in invalid_symbols:
            if f"'{symbol}'" in content or f'"{symbol}"' in content:
                found_invalid.append(symbol)
        
        if not found_invalid:
            print("✅ [TEST 4] No invalid symbols found")
            return True
        else:
            print(f"❌ [TEST 4] Found invalid symbols: {found_invalid}")
            return False
            
    except Exception as e:
        print(f"❌ [TEST 4] Symbol format test failed: {e}")
        return False

async def main():
    """Run comprehensive order execution debugging"""
    print("🚀 Starting Comprehensive Order Execution Debug Test")
    print("=" * 60)
    
    # Test 1: Client setup
    client, connection_ok = await test_coinbase_client_setup()
    
    # Test 2: Order execution methods
    methods_ok = await test_order_execution_methods(client) if client else False
    
    # Test 3: Main pipeline logic
    pipeline_ok = await test_main_execution_pipeline()
    
    # Test 4: Symbol format fixes
    symbols_ok = await test_symbol_format_fixes()
    
    # Summary
    print("=" * 60)
    print("📊 TEST RESULTS SUMMARY:")
    print(f"   Client Setup: {'✅ PASS' if connection_ok else '❌ FAIL'}")
    print(f"   Order Methods: {'✅ PASS' if methods_ok else '❌ FAIL'}")
    print(f"   Pipeline Logic: {'✅ PASS' if pipeline_ok else '❌ FAIL'}")
    print(f"   Symbol Formats: {'✅ PASS' if symbols_ok else '❌ FAIL'}")
    
    total_passed = sum([connection_ok, methods_ok, pipeline_ok, symbols_ok])
    print(f"\n🎯 OVERALL: {total_passed}/4 tests passed")
    
    if total_passed == 4:
        print("🎉 ALL TESTS PASSED - System should be ready for live trading!")
    else:
        print("⚠️  Some tests failed - review the issues above")
    
    return total_passed == 4

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
