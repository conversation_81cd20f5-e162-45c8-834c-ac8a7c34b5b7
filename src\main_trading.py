# backend/src/core/main_trading.py
import asyncio
import aiohttp
import json
import hmac
import hashlib
import time
import os
from decimal import Decimal
from typing import Dict, Optional
import logging
from aiolimiter import AsyncLimiter

class LiveTradingSession:
    def __init__(self, exchange_config: dict):
        self.logger = logging.getLogger('LiveTrading')
        self.api_key = os.getenv('EXCHANGE_API_KEY')
        self.api_secret = os.getenv('EXCHANGE_API_SECRET')
        self.exchange_config = exchange_config
        self.nonce = int(time.time() * 1000)
        self.rate_limiter = AsyncLimiter(max_rate=10, time_period=1)  # Exchange API rate limits
        self.order_states = asyncio.Queue()
        self.circuit_breaker = False
        self.session = aiohttp.ClientSession()
        self.active_orders = set()
        self.lock = asyncio.Lock()

    async def execute_live_order(self, order: dict) -> Optional[Dict]:
        """Enhanced atomic order execution with full safety checks"""
        if self.circuit_breaker:
            self.logger.warning("Circuit breaker active - blocking trades")
            return None

        try:
            async with self.rate_limiter, self.lock:
                # Order freshness check (5-second window)
                if time.time() * 1000 - self.nonce > 5000:
                    raise ValueError("Order nonce outside valid time window")

                # Generate secure nonce
                self.nonce += 1
                order['nonce'] = self.nonce

                # Price validation check
                current_price = await self._get_market_price(order['symbol'])
                if not self._validate_order_price(order, current_price):
                    raise ValueError(f"Price deviation too high: {order['price']} vs {current_price}")

                # Sign and execute order
                headers = await self._generate_auth_headers(order)
                
                async with self.session.post(
                    self.exchange_config['order_endpoint'],
                    json=order,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=3)
                ) as response:
                    return await self._handle_order_response(response, order)

        except aiohttp.ClientError as e:
            self.logger.error(f"Network error: {str(e)}")
            await self._handle_retry(order)
            return None
        except Exception as e:
            self.logger.critical(f"Order failed: {str(e)}", exc_info=True)
            await self.emergency_shutdown()
            return None

    async def _handle_order_response(self, response, order) -> Dict:
        """Process exchange response with detailed validation"""
        if response.status != 200:
            text = await response.text()
            self.logger.error(f"Order rejected: {text}")
            raise ExchangeError(f"API Error: {text}")

        result = await response.json()
        
        # Validate critical response fields
        required_fields = ['orderId', 'status', 'filledQty', 'avgPrice']
        if not all(field in result for field in required_fields):
            raise InvalidResponseError("Missing required order fields")
        
        # Update order state
        async with self.lock:
            self.active_orders.add(result['orderId'])
            await self.order_states.put({
                'order_id': result['orderId'],
                'status': result['status'],
                'filled': Decimal(result['filledQty']),
                'price': Decimal(result['avgPrice']),
                'timestamp': time.time()
            })
            
        # Implement post-trade checks
        if result['status'] == 'rejected':
            await self._analyze_rejection(result)
            
        return result

    async def emergency_shutdown(self):
        """Professional-grade emergency shutdown procedure"""
        self.logger.critical("INITIATING EMERGENCY SHUTDOWN SEQUENCE")
        self.circuit_breaker = True

        try:
            # 1. Cancel all active orders
            await self._cancel_all_orders()
            
            # 2. Liquidate positions according to exchange rules
            if self.exchange_config.get('supports_position_liquidation'):
                await self._liquidate_positions()
                
            # 3. Flush order states to persistent storage
            await self._persist_order_states()
            
            # 4. Notify monitoring systems
            await self._send_emergency_alert()
            
            # 5. Close connections gracefully
            await self.session.close()

        except Exception as e:
            self.logger.error(f"Shutdown error: {str(e)}")
        finally:
            exit(1)

    async def _generate_auth_headers(self, payload: dict) -> Dict:
        """HMAC-SHA256 signing with recvWindow protection"""
        recv_window = '5000'  # 5-second validity window
        timestamp = str(int(time.time() * 1000))
        
        params = {
            'timestamp': timestamp,
            'recvWindow': recv_window,
            **payload
        }
        
        query_string = '&'.join([f"{k}={v}" for k, v in sorted(params.items())])
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json',
            'X-SIGNATURE': signature,
            'X-TIMESTAMP': timestamp,
            'X-RECV-WINDOW': recv_window
        }

    async def _cancel_all_orders(self):
        """Exchange-specific order cancellation"""
        # Implementation varies by exchange
        pass

    async def _liquidate_positions(self):
        """Exchange-specific position liquidation"""
        # Implementation varies by exchange
        pass

class ExchangeError(Exception):
    pass

class InvalidResponseError(Exception):
    pass