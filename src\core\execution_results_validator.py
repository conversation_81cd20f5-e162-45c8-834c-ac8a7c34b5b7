#!/usr/bin/env python3
"""
Execution Results Validation System
Provides comprehensive validation for execution_results dictionary structure
and ensures type safety throughout the trading pipeline
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from decimal import Decimal
import json

logger = logging.getLogger(__name__)

class ExecutionStatus(Enum):
    """Standardized execution status values"""
    SUCCESS = "success"
    FAILED = "failed"
    ERROR = "error"
    PENDING = "pending"
    CANCELLED = "cancelled"
    PARTIAL = "partial"

class ValidationSeverity(Enum):
    """Validation issue severity levels"""
    CRITICAL = "critical"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

@dataclass
class ValidationIssue:
    """Represents a validation issue"""
    field: str
    severity: ValidationSeverity
    message: str
    expected_type: Optional[str] = None
    actual_type: Optional[str] = None
    expected_value: Optional[Any] = None
    actual_value: Optional[Any] = None

@dataclass
class ValidationResult:
    """Result of execution_results validation"""
    is_valid: bool
    issues: List[ValidationIssue] = field(default_factory=list)
    normalized_result: Optional[Dict[str, Any]] = None
    validation_timestamp: datetime = field(default_factory=datetime.now)
    
    def add_issue(self, field: str, severity: ValidationSeverity, message: str, **kwargs):
        """Add a validation issue"""
        issue = ValidationIssue(
            field=field,
            severity=severity,
            message=message,
            **kwargs
        )
        self.issues.append(issue)
        
        # Mark as invalid if critical or error
        if severity in [ValidationSeverity.CRITICAL, ValidationSeverity.ERROR]:
            self.is_valid = False
    
    def get_critical_issues(self) -> List[ValidationIssue]:
        """Get only critical issues"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.CRITICAL]
    
    def get_error_issues(self) -> List[ValidationIssue]:
        """Get error-level issues"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.ERROR]

class ExecutionResultsValidator:
    """
    Comprehensive validator for execution_results dictionaries
    
    Features:
    - Type safety validation
    - Required field validation
    - Value range validation
    - Format standardization
    - Automatic normalization
    - Detailed error reporting
    """
    
    def __init__(self):
        # Define the standard execution_results schema
        self.required_fields = {
            'status': str,
            'action': str,
            'symbol': str,
            'amount': (float, int, Decimal),
            'exchange': str
        }
        
        self.optional_fields = {
            'price': (float, int, Decimal),
            'order_id': str,
            'timestamp': (str, datetime),
            'error': str,
            'signal_id': str,
            'execution_time': (float, int),
            'fees': (float, int, Decimal),
            'filled_amount': (float, int, Decimal),
            'remaining_amount': (float, int, Decimal),
            'average_price': (float, int, Decimal),
            'slippage': (float, int),
            'market_impact': (float, int),
            'venue': str,
            'order_type': str,
            'side': str,
            'cost': (float, int, Decimal),
            'commission': (float, int, Decimal),
            'info': dict
        }
        
        # Valid values for enum-like fields
        self.valid_statuses = {status.value for status in ExecutionStatus}
        self.valid_actions = {'buy', 'sell', 'long', 'short', 'close'}
        self.valid_exchanges = {'bybit', 'coinbase', 'binance', 'bybit_client', 'coinbase_client', 'binance_client'}
        self.valid_order_types = {'market', 'limit', 'stop', 'stop_limit', 'iceberg', 'twap', 'vwap'}
        self.valid_sides = {'buy', 'sell', 'long', 'short'}
        
        logger.info("🔍 [VALIDATOR] Execution results validator initialized")
    
    def validate(self, execution_result: Dict[str, Any]) -> ValidationResult:
        """
        Validate an execution_results dictionary
        
        Args:
            execution_result: The execution result to validate
            
        Returns:
            ValidationResult with detailed validation information
        """
        result = ValidationResult(is_valid=True)
        
        try:
            # Basic structure validation
            if not isinstance(execution_result, dict):
                result.add_issue(
                    field="root",
                    severity=ValidationSeverity.CRITICAL,
                    message="Execution result must be a dictionary",
                    expected_type="dict",
                    actual_type=type(execution_result).__name__
                )
                return result
            
            # Validate required fields
            self._validate_required_fields(execution_result, result)
            
            # Validate field types
            self._validate_field_types(execution_result, result)
            
            # Validate field values
            self._validate_field_values(execution_result, result)
            
            # Validate business logic
            self._validate_business_logic(execution_result, result)
            
            # Create normalized result if validation passed
            if result.is_valid:
                result.normalized_result = self._normalize_result(execution_result)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ [VALIDATOR] Validation failed with exception: {e}")
            result.add_issue(
                field="validation",
                severity=ValidationSeverity.CRITICAL,
                message=f"Validation exception: {str(e)}"
            )
            return result
    
    def _validate_required_fields(self, execution_result: Dict[str, Any], result: ValidationResult):
        """Validate that all required fields are present"""
        for field, expected_type in self.required_fields.items():
            if field not in execution_result:
                result.add_issue(
                    field=field,
                    severity=ValidationSeverity.CRITICAL,
                    message=f"Required field '{field}' is missing"
                )
            elif execution_result[field] is None:
                result.add_issue(
                    field=field,
                    severity=ValidationSeverity.ERROR,
                    message=f"Required field '{field}' cannot be None"
                )
    
    def _validate_field_types(self, execution_result: Dict[str, Any], result: ValidationResult):
        """Validate field types"""
        all_fields = {**self.required_fields, **self.optional_fields}
        
        for field, value in execution_result.items():
            if field in all_fields and value is not None:
                expected_types = all_fields[field]
                if not isinstance(expected_types, tuple):
                    expected_types = (expected_types,)
                
                if not isinstance(value, expected_types):
                    result.add_issue(
                        field=field,
                        severity=ValidationSeverity.ERROR,
                        message=f"Field '{field}' has incorrect type",
                        expected_type=str(expected_types),
                        actual_type=type(value).__name__,
                        actual_value=value
                    )
    
    def _validate_field_values(self, execution_result: Dict[str, Any], result: ValidationResult):
        """Validate field values against allowed values"""
        # Validate status
        if 'status' in execution_result:
            status = execution_result['status']
            if status not in self.valid_statuses:
                result.add_issue(
                    field='status',
                    severity=ValidationSeverity.ERROR,
                    message=f"Invalid status value: {status}",
                    expected_value=list(self.valid_statuses),
                    actual_value=status
                )
        
        # Validate action
        if 'action' in execution_result:
            action = execution_result['action']
            if action.lower() not in self.valid_actions:
                result.add_issue(
                    field='action',
                    severity=ValidationSeverity.WARNING,
                    message=f"Unusual action value: {action}",
                    expected_value=list(self.valid_actions),
                    actual_value=action
                )
        
        # Validate exchange
        if 'exchange' in execution_result:
            exchange = execution_result['exchange']
            if exchange not in self.valid_exchanges:
                result.add_issue(
                    field='exchange',
                    severity=ValidationSeverity.WARNING,
                    message=f"Unknown exchange: {exchange}",
                    expected_value=list(self.valid_exchanges),
                    actual_value=exchange
                )
        
        # Validate numeric ranges
        self._validate_numeric_ranges(execution_result, result)
    
    def _validate_numeric_ranges(self, execution_result: Dict[str, Any], result: ValidationResult):
        """Validate numeric field ranges"""
        # Amount should be positive
        if 'amount' in execution_result:
            amount = execution_result['amount']
            try:
                amount_val = float(amount)
                if amount_val <= 0:
                    result.add_issue(
                        field='amount',
                        severity=ValidationSeverity.ERROR,
                        message=f"Amount must be positive: {amount_val}"
                    )
            except (ValueError, TypeError):
                result.add_issue(
                    field='amount',
                    severity=ValidationSeverity.ERROR,
                    message=f"Amount must be numeric: {amount}"
                )
        
        # Price should be positive (if present)
        if 'price' in execution_result and execution_result['price'] is not None:
            price = execution_result['price']
            try:
                price_val = float(price)
                if price_val <= 0:
                    result.add_issue(
                        field='price',
                        severity=ValidationSeverity.ERROR,
                        message=f"Price must be positive: {price_val}"
                    )
            except (ValueError, TypeError):
                result.add_issue(
                    field='price',
                    severity=ValidationSeverity.ERROR,
                    message=f"Price must be numeric: {price}"
                )
    
    def _validate_business_logic(self, execution_result: Dict[str, Any], result: ValidationResult):
        """Validate business logic constraints"""
        # If status is success, certain fields should be present
        if execution_result.get('status') == 'success':
            required_success_fields = ['price', 'order_id']
            for field in required_success_fields:
                if field not in execution_result or execution_result[field] is None:
                    result.add_issue(
                        field=field,
                        severity=ValidationSeverity.WARNING,
                        message=f"Field '{field}' should be present for successful executions"
                    )
        
        # If status is error or failed, error field should be present
        if execution_result.get('status') in ['error', 'failed']:
            if 'error' not in execution_result or not execution_result['error']:
                result.add_issue(
                    field='error',
                    severity=ValidationSeverity.WARNING,
                    message="Error field should be present for failed executions"
                )
    
    def _normalize_result(self, execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize and standardize the execution result"""
        normalized = execution_result.copy()
        
        # Standardize status
        if 'status' in normalized:
            normalized['status'] = normalized['status'].lower()
        
        # Standardize action
        if 'action' in normalized:
            normalized['action'] = normalized['action'].lower()
        
        # Standardize exchange name
        if 'exchange' in normalized:
            exchange = normalized['exchange']
            if exchange.endswith('_client'):
                normalized['exchange'] = exchange.replace('_client', '')
        
        # Convert numeric fields to appropriate types
        numeric_fields = ['amount', 'price', 'fees', 'filled_amount', 'remaining_amount', 
                         'average_price', 'slippage', 'market_impact', 'cost', 'commission']
        
        for field in numeric_fields:
            if field in normalized and normalized[field] is not None:
                try:
                    normalized[field] = float(normalized[field])
                except (ValueError, TypeError):
                    pass  # Keep original value if conversion fails
        
        # Add timestamp if missing
        if 'timestamp' not in normalized:
            normalized['timestamp'] = datetime.now().isoformat()
        
        return normalized

    def validate_batch(self, execution_results: List[Dict[str, Any]]) -> List[ValidationResult]:
        """
        Validate a batch of execution results

        Args:
            execution_results: List of execution results to validate

        Returns:
            List of ValidationResult objects
        """
        results = []

        try:
            for i, execution_result in enumerate(execution_results):
                try:
                    validation_result = self.validate(execution_result)
                    results.append(validation_result)
                except Exception as e:
                    # Create error result for failed validation
                    error_result = ValidationResult(is_valid=False)
                    error_result.add_issue(
                        field=f"batch_item_{i}",
                        severity=ValidationSeverity.CRITICAL,
                        message=f"Validation failed: {str(e)}"
                    )
                    results.append(error_result)

            return results

        except Exception as e:
            logger.error(f"❌ [VALIDATOR] Batch validation failed: {e}")
            return results

    def get_validation_summary(self, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """
        Get summary statistics for a batch of validation results

        Args:
            validation_results: List of validation results

        Returns:
            Summary statistics dictionary
        """
        try:
            total_results = len(validation_results)
            valid_results = sum(1 for r in validation_results if r.is_valid)
            invalid_results = total_results - valid_results

            # Count issues by severity
            critical_issues = 0
            error_issues = 0
            warning_issues = 0
            info_issues = 0

            for result in validation_results:
                for issue in result.issues:
                    if issue.severity == ValidationSeverity.CRITICAL:
                        critical_issues += 1
                    elif issue.severity == ValidationSeverity.ERROR:
                        error_issues += 1
                    elif issue.severity == ValidationSeverity.WARNING:
                        warning_issues += 1
                    elif issue.severity == ValidationSeverity.INFO:
                        info_issues += 1

            # Calculate success rate
            success_rate = (valid_results / total_results * 100) if total_results > 0 else 0

            return {
                'total_results': total_results,
                'valid_results': valid_results,
                'invalid_results': invalid_results,
                'success_rate': success_rate,
                'issues': {
                    'critical': critical_issues,
                    'error': error_issues,
                    'warning': warning_issues,
                    'info': info_issues,
                    'total': critical_issues + error_issues + warning_issues + info_issues
                },
                'validation_timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ [VALIDATOR] Error generating validation summary: {e}")
            return {
                'error': str(e),
                'validation_timestamp': datetime.now().isoformat()
            }

    def create_standard_result(self, status: str, action: str, symbol: str,
                             amount: Union[float, int, Decimal], exchange: str,
                             **kwargs) -> Dict[str, Any]:
        """
        Create a standardized execution result dictionary

        Args:
            status: Execution status
            action: Trading action (buy/sell)
            symbol: Trading symbol
            amount: Trade amount
            exchange: Exchange name
            **kwargs: Additional optional fields

        Returns:
            Standardized execution result dictionary
        """
        try:
            # Create base result with required fields
            result = {
                'status': status.lower() if isinstance(status, str) else str(status).lower(),
                'action': action.lower() if isinstance(action, str) else str(action).lower(),
                'symbol': str(symbol).upper(),
                'amount': float(amount),
                'exchange': str(exchange).lower(),
                'timestamp': datetime.now().isoformat()
            }

            # Add optional fields
            for key, value in kwargs.items():
                if key in self.optional_fields and value is not None:
                    result[key] = value

            # Validate the created result
            validation_result = self.validate(result)

            if validation_result.is_valid:
                return validation_result.normalized_result
            else:
                logger.warning(f"⚠️ [VALIDATOR] Created result has validation issues: {validation_result.issues}")
                return result

        except Exception as e:
            logger.error(f"❌ [VALIDATOR] Error creating standard result: {e}")
            return {
                'status': 'error',
                'error': f"Failed to create result: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }

    def create_error_result(self, error_message: str, **context) -> Dict[str, Any]:
        """
        Create a standardized error result

        Args:
            error_message: Error description
            **context: Additional context fields

        Returns:
            Standardized error result dictionary
        """
        try:
            result = {
                'status': 'error',
                'error': str(error_message),
                'timestamp': datetime.now().isoformat()
            }

            # Add context fields
            for key, value in context.items():
                if value is not None:
                    result[key] = value

            return result

        except Exception as e:
            logger.error(f"❌ [VALIDATOR] Error creating error result: {e}")
            return {
                'status': 'error',
                'error': f"Failed to create error result: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }

    def is_successful_result(self, execution_result: Dict[str, Any]) -> bool:
        """
        Check if an execution result represents a successful trade

        Args:
            execution_result: Execution result to check

        Returns:
            True if the result represents a successful trade
        """
        try:
            return (
                isinstance(execution_result, dict) and
                execution_result.get('status', '').lower() == 'success' and
                'amount' in execution_result and
                float(execution_result['amount']) > 0
            )
        except (ValueError, TypeError):
            return False

    def extract_trade_metrics(self, execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract key trading metrics from an execution result

        Args:
            execution_result: Execution result to analyze

        Returns:
            Dictionary of extracted metrics
        """
        try:
            metrics = {
                'is_successful': self.is_successful_result(execution_result),
                'status': execution_result.get('status', 'unknown'),
                'action': execution_result.get('action', 'unknown'),
                'symbol': execution_result.get('symbol', 'unknown'),
                'exchange': execution_result.get('exchange', 'unknown')
            }

            # Extract numeric metrics
            numeric_fields = ['amount', 'price', 'fees', 'cost', 'slippage']
            for field in numeric_fields:
                if field in execution_result:
                    try:
                        metrics[field] = float(execution_result[field])
                    except (ValueError, TypeError):
                        metrics[field] = None
                else:
                    metrics[field] = None

            # Calculate derived metrics
            if metrics['amount'] and metrics['price']:
                metrics['trade_value'] = metrics['amount'] * metrics['price']
            else:
                metrics['trade_value'] = None

            if metrics['fees'] and metrics['trade_value']:
                metrics['fee_percentage'] = (metrics['fees'] / metrics['trade_value']) * 100
            else:
                metrics['fee_percentage'] = None

            return metrics

        except Exception as e:
            logger.error(f"❌ [VALIDATOR] Error extracting trade metrics: {e}")
            return {
                'error': str(e),
                'is_successful': False
            }
