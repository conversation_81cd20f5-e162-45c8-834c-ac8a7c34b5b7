#!/usr/bin/env python3
"""
Test Live Trading Execution - Verify that trades are actually executed
"""

import os
import sys
import asyncio
import json
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT / "src"))

# Load environment variables
load_dotenv()

async def test_live_trading_execution():
    """Test that the trading system actually executes trades"""
    try:
        print("🔍 TESTING LIVE TRADING EXECUTION")
        print("=" * 50)
        
        # Import the main trading system
        from main import ComprehensiveLiveTradingSystem
        
        # Create system instance
        system = ComprehensiveLiveTradingSystem()
        
        print("\n📊 Initializing trading system...")

        # Initialize the comprehensive system with correct method
        await system.initialize_comprehensive_system()
        
        print(f"Available components: {list(system.components.keys())}")
        
        # Run a single trading cycle to test execution
        print(f"\n🚀 Running single trading cycle...")
        
        try:
            await system.run_trading_cycle()
            print(f"✅ Trading cycle completed successfully")
        except Exception as e:
            print(f"❌ Trading cycle failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Test the comprehensive trading loop for a few cycles
        print(f"\n🔄 Testing comprehensive trading loop (3 cycles)...")
        
        # Set up for limited testing
        system.running = True
        cycle_count = 0
        max_cycles = 3
        
        while system.running and cycle_count < max_cycles:
            try:
                cycle_count += 1
                print(f"\n--- CYCLE {cycle_count} ---")
                
                # Get market data
                market_data = await system._collect_live_market_data()
                print(f"Market data collected: {len(market_data.get('price_data', {}))} exchanges")
                
                # Perform AI analysis
                analysis_results = await system._perform_ai_analysis(market_data)
                print(f"AI analysis completed")
                
                # Generate signals
                trading_signals = await system._generate_ai_trading_signals(market_data, analysis_results)
                print(f"Generated {len(trading_signals)} trading signals")
                
                if trading_signals:
                    # Show signal details
                    for signal_id, signal in list(trading_signals.items())[:3]:  # Show first 3
                        print(f"  Signal: {signal['action']} {signal['amount']} {signal['symbol']} - {signal['reasoning']}")
                    
                    # Validate signals
                    validated_signals = await system._validate_signals_with_risk_management(trading_signals)
                    print(f"Validated {len(validated_signals)} signals")
                    
                    if validated_signals:
                        # Execute trades
                        print(f"🚀 Executing {len(validated_signals)} validated signals...")
                        execution_results = await system._execute_autonomous_trades(validated_signals)
                        
                        successful_trades = sum(1 for result in execution_results if result.get('status') == 'success')
                        print(f"💰 Executed {successful_trades}/{len(validated_signals)} trades successfully")
                        
                        # Show execution results
                        for result in execution_results:
                            if result.get('status') == 'success':
                                print(f"  ✅ {result['action']} {result['amount']} {result['symbol']} at ${result['price']:.2f} on {result['exchange']}")
                            else:
                                print(f"  ❌ {result['action']} {result['symbol']}: {result.get('error', 'Unknown error')}")
                    else:
                        print(f"⚠️ No signals passed risk validation")
                else:
                    print(f"📊 No trading signals generated")
                
                print(f"Cycle {cycle_count} completed")
                
                # Short sleep between cycles
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"❌ Error in cycle {cycle_count}: {e}")
                import traceback
                traceback.print_exc()
                break
        
        system.running = False
        print(f"\n✅ Test completed - {cycle_count} cycles executed")
        
        print("\n" + "=" * 50)
        print("🎯 LIVE TRADING EXECUTION TEST COMPLETED")
        
    except Exception as e:
        print(f"❌ Critical error in live trading execution test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_live_trading_execution())
