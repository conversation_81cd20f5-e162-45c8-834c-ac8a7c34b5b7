#!/usr/bin/env python3
"""
Test script to verify the symbol validation fix.
"""

import os
import sys
import json
from decimal import Decimal

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

def test_symbol_validation():
    """Test the symbol validation fix"""
    print("🔧 [TEST] Testing symbol validation fix...")
    
    try:
        # Import after path setup
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Load config manually
        config_file = os.path.join(os.path.dirname(__file__), 'config', 'config.json')
        if not os.path.exists(config_file):
            print("❌ [ERROR] Config file not found")
            return False
            
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        bybit_config = config.get('exchanges', {}).get('bybit', {})
        if not bybit_config.get('api_key') or not bybit_config.get('api_secret'):
            print("❌ [ERROR] Bybit credentials not found")
            return False
        
        # Initialize client
        print("🔧 [TEST] Initializing Bybit client...")
        client = BybitClientFixed(
            api_key=bybit_config['api_key'],
            api_secret=bybit_config['api_secret'],
            testnet=False
        )
        
        print("✅ [TEST] Client initialized successfully")
        
        # Test symbol validation for common pairs
        test_symbols = ['BTCUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT', 'ETHUSDT']
        
        print("\n🎯 [TEST] Testing symbol validation...")
        for symbol in test_symbols:
            try:
                # Test the fixed method
                is_valid = client._is_valid_bybit_symbol(symbol)
                print(f"  {'✅' if is_valid else '❌'} {symbol}: {is_valid}")
                
                if is_valid:
                    # Try to get price
                    try:
                        price = client.get_price(symbol)
                        print(f"    💰 Price: ${price}")
                    except Exception as e:
                        print(f"    ❌ Price error: {e}")
                        
            except Exception as e:
                print(f"  ❌ {symbol}: Error - {e}")
        
        # Test cache
        print("\n💾 [TEST] Checking symbol cache...")
        if hasattr(client, '_exchange_symbols_cache') and client._exchange_symbols_cache:
            cache_size = len(client._exchange_symbols_cache)
            print(f"✅ [CACHE] Found {cache_size} cached symbols")
            
            # Check if our test symbols are in cache
            for symbol in test_symbols:
                in_cache = symbol in client._exchange_symbols_cache
                print(f"  {'✅' if in_cache else '❌'} {symbol} in cache: {in_cache}")
        else:
            print("❌ [CACHE] No symbols cached")
        
        print("\n🏁 [TEST] Symbol validation test completed!")
        return True
        
    except Exception as e:
        print(f"❌ [FATAL] Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_symbol_validation()
    sys.exit(0 if success else 1)
