#!/usr/bin/env python3
"""
Test script to check signal generation
"""
import asyncio
import sys
import os
sys.path.append('src')

# Set up environment
os.environ['LIVE_TRADING'] = 'true'
os.environ['REAL_MONEY_TRADING'] = 'true'

async def test_signals():
    try:
        from main import ComprehensiveLiveTradingSystem
        
        system = ComprehensiveLiveTradingSystem()
        
        # Mock market data with strong signals
        market_data = {
            'price_data': {
                'bybit_client': {
                    'BTC-USD': {'price': 45000, 'volume': 1000, 'change': 0, 'percentage': 5.5},
                    'ETH-USD': {'price': 3000, 'volume': 800, 'change': 0, 'percentage': 3.2},
                    'SOL-USD': {'price': 100, 'volume': 600, 'change': 0, 'percentage': -4.1}
                }
            }
        }
        
        print("Testing signal generation...")
        
        # Test simple price signals
        signals = await system._generate_simple_price_signals(market_data)
        print(f'Generated {len(signals)} simple price signals:')
        for signal_id, signal in signals.items():
            print(f'  {signal_id}: {signal["action"]} {signal["symbol"]} - {signal["reasoning"]}')
        
        # Test technical signals
        tech_signals = await system._generate_technical_signals(market_data)
        print(f'Generated {len(tech_signals)} technical signals:')
        for signal_id, signal in tech_signals.items():
            print(f'  {signal_id}: {signal["action"]} {signal["symbol"]} - {signal["reasoning"]}')
        
        # Test full AI signal generation
        analysis_results = {}
        ai_signals = await system._generate_ai_trading_signals(market_data, analysis_results)
        print(f'Generated {len(ai_signals)} AI signals:')
        for signal_id, signal in ai_signals.items():
            print(f'  {signal_id}: {signal["action"]} {signal["symbol"]} - {signal["reasoning"]}')
            
    except Exception as e:
        print(f"Error testing signals: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_signals())
