#!/usr/bin/env python3
"""
Enhanced Capital Manager for Adaptive Exchange Operation
Handles capital management for single vs dual exchange operation
"""

import logging
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class CapitalAllocation:
    """Capital allocation tracking"""
    exchange: str
    total_capital: Decimal
    available_capital: Decimal
    allocated_capital: Decimal
    profit_accumulated: Decimal
    last_updated: datetime

class EnhancedCapitalManager:
    """
    Enhanced capital manager with Coinbase-Primary Capital Management
    Coinbase remains primary for strategy and profit distribution regardless of API status
    All available exchanges trade independently with unified profit management
    """

    def __init__(self, exchange_manager):
        self.exchange_manager = exchange_manager
        self.allocations = {}
        self.profit_split_ratio = 0.5  # 50% reinvested, 50% to Coinbase wallet
        self.coinbase_wallet_address = "******************************************"
        self.position_size_percentage = Decimal('0.25')  # 25% of available balance per trade

        # Coinbase-Primary Capital Management settings
        self.coinbase_primary_mode = True  # Always treat Coinbase as primary
        self.queued_transfers = []  # Queue transfers when Coinbase unavailable
        self.unified_profit_pool = Decimal('0')  # Combined profits from all exchanges
        
    async def initialize(self, components: Dict) -> bool:
        """Initialize capital manager with current balances"""
        try:
            logger.info("💰 [CAPITAL-MGR] Initializing enhanced capital manager...")
            
            # Get current balances from all exchanges
            await self._update_capital_allocations(components)
            
            # Determine initial strategy based on available exchanges
            mode = self.exchange_manager.capital_management_mode
            logger.info(f"💰 [CAPITAL-MGR] Operating in {mode} mode")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Initialization failed: {e}")
            return False
    
    async def _update_capital_allocations(self, components: Dict):
        """Update capital allocations from exchange balances"""
        try:
            # Update Bybit allocation
            if 'bybit_client' in components:
                bybit_client = components['bybit_client']
                try:
                    usdt_balance = await bybit_client.get_balance('USDT')
                    self.allocations['bybit'] = CapitalAllocation(
                        exchange='bybit',
                        total_capital=usdt_balance,
                        available_capital=usdt_balance,
                        allocated_capital=Decimal('0'),
                        profit_accumulated=Decimal('0'),
                        last_updated=datetime.now()
                    )
                    logger.info(f"💰 [BYBIT] Capital: ${usdt_balance:.2f} USDT")
                except Exception as e:
                    logger.warning(f"⚠️ [BYBIT] Could not get balance: {e}")
            
            # Update Coinbase allocation
            if 'coinbase_client' in components:
                coinbase_client = components['coinbase_client']
                try:
                    # Get Coinbase portfolio value (simplified)
                    portfolio_value = Decimal('233.0')  # Known portfolio value in USD
                    self.allocations['coinbase'] = CapitalAllocation(
                        exchange='coinbase',
                        total_capital=portfolio_value,
                        available_capital=portfolio_value,
                        allocated_capital=Decimal('0'),
                        profit_accumulated=Decimal('0'),
                        last_updated=datetime.now()
                    )
                    logger.info(f"💰 [COINBASE] Capital: ${portfolio_value:.2f} USD")
                except Exception as e:
                    logger.warning(f"⚠️ [COINBASE] Could not get balance: {e}")
                    
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error updating allocations: {e}")
    
    async def calculate_position_size(self, symbol: str, price: float, action: str) -> Tuple[str, Decimal]:
        """
        Calculate optimal position size and select exchange
        Uses Coinbase-Primary Capital Management regardless of API status
        """
        try:
            # Get all available exchanges for trading
            available_exchanges = self.exchange_manager.get_active_exchanges()

            if not available_exchanges:
                logger.warning("⚠️ [CAPITAL-MGR] No active exchanges available")
                return 'none', Decimal('0')

            # Select best exchange for this symbol regardless of Coinbase status
            selected_exchange = await self._select_optimal_exchange(symbol, available_exchanges)

            if not selected_exchange:
                logger.warning(f"⚠️ [CAPITAL-MGR] No suitable exchange for {symbol}")
                return 'none', Decimal('0')

            # Calculate position size for the selected exchange
            position_size = await self._calculate_exchange_position(selected_exchange, symbol, price, action)

            logger.info(f"💰 [COINBASE-PRIMARY] Selected {selected_exchange} for {symbol}: {position_size:.6f}")
            return selected_exchange, position_size

        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error calculating position size: {e}")
            return 'none', Decimal('0')

    async def _select_optimal_exchange(self, symbol: str, available_exchanges: List[str]) -> Optional[str]:
        """Select the optimal exchange for trading this symbol"""
        try:
            base_currency = symbol.split('-')[0]

            # Priority order for exchange selection (independent of Coinbase API status)
            exchange_priority = {
                'bybit': 1,      # High liquidity, good for most pairs
                'binance': 2,    # Alternative high liquidity
                'coinbase': 3,   # Only if API is working
                'phantom': 4     # Solana-specific
            }

            # Filter available exchanges and sort by priority
            suitable_exchanges = []
            for exchange in available_exchanges:
                if exchange in exchange_priority:
                    # Check if exchange has sufficient balance
                    if exchange in self.allocations:
                        allocation = self.allocations[exchange]
                        if allocation.available_capital >= Decimal('5.0'):  # Minimum trading amount
                            suitable_exchanges.append((exchange, exchange_priority[exchange]))

            if not suitable_exchanges:
                logger.warning(f"⚠️ [EXCHANGE-SELECT] No exchanges with sufficient balance for {symbol}")
                return None

            # Sort by priority and return the best option
            suitable_exchanges.sort(key=lambda x: x[1])
            selected_exchange = suitable_exchanges[0][0]

            logger.debug(f"🎯 [EXCHANGE-SELECT] Selected {selected_exchange} for {symbol} from {available_exchanges}")
            return selected_exchange

        except Exception as e:
            logger.error(f"❌ [EXCHANGE-SELECT] Error selecting exchange: {e}")
            return None

    async def _calculate_exchange_position(self, exchange: str, symbol: str, price: float, action: str) -> Decimal:
        """Calculate position size for a specific exchange"""
        try:
            if exchange not in self.allocations:
                logger.warning(f"⚠️ [POSITION-CALC] No allocation data for {exchange}")
                return Decimal('0')

            allocation = self.allocations[exchange]
            price_decimal = Decimal(str(price))

            # Exchange-specific position sizing
            if exchange == 'bybit':
                return await self._calculate_bybit_position(allocation, symbol, price_decimal)
            elif exchange == 'binance':
                return await self._calculate_binance_position(allocation, symbol, price_decimal)
            elif exchange == 'coinbase':
                return await self._calculate_coinbase_position(allocation, symbol, price_decimal)
            elif exchange == 'phantom':
                return await self._calculate_phantom_position(allocation, symbol, price_decimal)
            else:
                # Generic calculation for unknown exchanges
                return await self._calculate_generic_position(allocation, symbol, price_decimal)

        except Exception as e:
            logger.error(f"❌ [POSITION-CALC] Error calculating position for {exchange}: {e}")
            return Decimal('0')

    async def _calculate_bybit_position(self, allocation: CapitalAllocation, symbol: str, price: Decimal) -> Decimal:
        """Calculate position size for Bybit"""
        try:
            # Use 20-25% of available balance with minimum $5.50 safety margin
            min_value_with_margin = Decimal('5.5')

            if allocation.available_capital < min_value_with_margin:
                logger.warning(f"⚠️ [BYBIT] Insufficient balance: ${allocation.available_capital:.2f}")
                return Decimal('0')

            # Use 25% of available balance
            position_value = allocation.available_capital * self.position_size_percentage
            position_value = max(position_value, min_value_with_margin)

            # Don't exceed 90% of available balance
            max_position_value = allocation.available_capital * Decimal('0.9')
            position_value = min(position_value, max_position_value)

            position_size = position_value / price

            logger.debug(f"💰 [BYBIT] {symbol}: ${position_value:.2f} = {position_size:.6f}")
            return position_size

        except Exception as e:
            logger.error(f"❌ [BYBIT] Position calculation error: {e}")
            return Decimal('0')

    async def _calculate_binance_position(self, allocation: CapitalAllocation, symbol: str, price: Decimal) -> Decimal:
        """Calculate position size for Binance"""
        try:
            # Similar to Bybit but with Binance-specific minimums
            min_value = Decimal('10.0')  # Binance typically has $10 minimum

            if allocation.available_capital < min_value:
                logger.warning(f"⚠️ [BINANCE] Insufficient balance: ${allocation.available_capital:.2f}")
                return Decimal('0')

            # Use 20% of available balance for Binance
            position_value = allocation.available_capital * Decimal('0.20')
            position_value = max(position_value, min_value)

            position_size = position_value / price

            logger.debug(f"💰 [BINANCE] {symbol}: ${position_value:.2f} = {position_size:.6f}")
            return position_size

        except Exception as e:
            logger.error(f"❌ [BINANCE] Position calculation error: {e}")
            return Decimal('0')

    async def _calculate_coinbase_position(self, allocation: CapitalAllocation, symbol: str, price: Decimal) -> Decimal:
        """Calculate position size for Coinbase (when API is working)"""
        try:
            # Conservative position sizing for Coinbase portfolio
            min_value = Decimal('1.0')  # Coinbase minimum

            if allocation.available_capital < min_value:
                logger.warning(f"⚠️ [COINBASE] Insufficient balance: ${allocation.available_capital:.2f}")
                return Decimal('0')

            # Use 1-2% of portfolio per trade (conservative)
            position_value = allocation.available_capital * Decimal('0.02')
            position_value = max(position_value, min_value)
            position_value = min(position_value, Decimal('20.0'))  # Maximum $20

            position_size = position_value / price

            logger.debug(f"💰 [COINBASE] {symbol}: ${position_value:.2f} = {position_size:.6f}")
            return position_size

        except Exception as e:
            logger.error(f"❌ [COINBASE] Position calculation error: {e}")
            return Decimal('0')

    async def _calculate_phantom_position(self, allocation: CapitalAllocation, symbol: str, price: Decimal) -> Decimal:
        """Calculate position size for Phantom (Solana)"""
        try:
            # Solana-specific position sizing
            min_value = Decimal('0.1')  # Lower minimum for Solana

            if allocation.available_capital < min_value:
                logger.warning(f"⚠️ [PHANTOM] Insufficient balance: ${allocation.available_capital:.2f}")
                return Decimal('0')

            # Use 15% of available balance for Phantom
            position_value = allocation.available_capital * Decimal('0.15')
            position_value = max(position_value, min_value)

            position_size = position_value / price

            logger.debug(f"💰 [PHANTOM] {symbol}: ${position_value:.2f} = {position_size:.6f}")
            return position_size

        except Exception as e:
            logger.error(f"❌ [PHANTOM] Position calculation error: {e}")
            return Decimal('0')

    async def _calculate_generic_position(self, allocation: CapitalAllocation, symbol: str, price: Decimal) -> Decimal:
        """Calculate position size for generic/unknown exchanges"""
        try:
            # Conservative generic calculation
            min_value = Decimal('5.0')

            if allocation.available_capital < min_value:
                logger.warning(f"⚠️ [GENERIC] Insufficient balance: ${allocation.available_capital:.2f}")
                return Decimal('0')

            # Use 10% of available balance for unknown exchanges
            position_value = allocation.available_capital * Decimal('0.10')
            position_value = max(position_value, min_value)

            position_size = position_value / price

            logger.debug(f"💰 [GENERIC] {symbol}: ${position_value:.2f} = {position_size:.6f}")
            return position_size

        except Exception as e:
            logger.error(f"❌ [GENERIC] Position calculation error: {e}")
            return Decimal('0')
    
    async def _calculate_dual_exchange_position(self, symbol: str, price: float, action: str) -> Tuple[str, Decimal]:
        """Calculate position for dual exchange operation"""
        try:
            # Route based on symbol and available capital
            base_currency = symbol.split('-')[0]
            
            # Prefer Coinbase for major cryptos if we have holdings
            if base_currency in ['BTC', 'ETH', 'SOL'] and 'coinbase' in self.allocations:
                coinbase_allocation = self.allocations['coinbase']
                if coinbase_allocation.available_capital > Decimal('10.0'):
                    # Use small position from Coinbase portfolio
                    position_value = min(Decimal('20.0'), coinbase_allocation.available_capital * Decimal('0.05'))
                    position_size = position_value / Decimal(str(price))
                    return 'coinbase', position_size
            
            # Use Bybit for other trades
            if 'bybit' in self.allocations:
                bybit_allocation = self.allocations['bybit']
                if bybit_allocation.available_capital >= Decimal('5.5'):
                    # Use 25% of available Bybit balance
                    position_value = bybit_allocation.available_capital * self.position_size_percentage
                    position_value = max(position_value, Decimal('5.5'))  # Ensure minimum
                    position_size = position_value / Decimal(str(price))
                    return 'bybit', position_size
            
            return 'none', Decimal('0')
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error in dual exchange calculation: {e}")
            return 'none', Decimal('0')
    
    async def _calculate_bybit_only_position(self, symbol: str, price: float, action: str) -> Tuple[str, Decimal]:
        """Calculate position for Bybit-only operation"""
        try:
            if 'bybit' not in self.allocations:
                return 'none', Decimal('0')
            
            bybit_allocation = self.allocations['bybit']
            
            # Use larger position sizes since we're accumulating profits locally
            if bybit_allocation.available_capital >= Decimal('5.5'):
                # Use 20-25% of available balance per trade
                position_value = bybit_allocation.available_capital * self.position_size_percentage
                
                # Ensure minimum order value with safety margin
                position_value = max(position_value, Decimal('5.5'))
                
                # Don't exceed 90% of available balance
                max_position_value = bybit_allocation.available_capital * Decimal('0.9')
                position_value = min(position_value, max_position_value)
                
                position_size = position_value / Decimal(str(price))
                
                logger.info(f"💰 [BYBIT-ONLY] {symbol}: ${position_value:.2f} = {position_size:.6f}")
                return 'bybit', position_size
            
            return 'none', Decimal('0')
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error in Bybit-only calculation: {e}")
            return 'none', Decimal('0')
    
    async def _calculate_coinbase_only_position(self, symbol: str, price: float, action: str) -> Tuple[str, Decimal]:
        """Calculate position for Coinbase-only operation"""
        try:
            if 'coinbase' not in self.allocations:
                return 'none', Decimal('0')
            
            coinbase_allocation = self.allocations['coinbase']
            
            # Conservative position sizing for Coinbase portfolio
            if coinbase_allocation.available_capital >= Decimal('5.0'):
                # Use 1-2% of portfolio per trade
                position_value = coinbase_allocation.available_capital * Decimal('0.02')
                position_value = max(position_value, Decimal('2.0'))  # Minimum $2
                position_value = min(position_value, Decimal('20.0'))  # Maximum $20
                
                position_size = position_value / Decimal(str(price))
                
                logger.info(f"💰 [COINBASE-ONLY] {symbol}: ${position_value:.2f} = {position_size:.6f}")
                return 'coinbase', position_size
            
            return 'none', Decimal('0')
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error in Coinbase-only calculation: {e}")
            return 'none', Decimal('0')
    
    async def record_trade(self, exchange: str, symbol: str, action: str, amount: Decimal, price: float, success: bool):
        """Record trade execution and update capital allocations"""
        try:
            if exchange not in self.allocations:
                logger.warning(f"⚠️ [CAPITAL-MGR] Unknown exchange: {exchange}")
                return
            
            allocation = self.allocations[exchange]
            trade_value = amount * Decimal(str(price))
            
            if success:
                if action.upper() == 'BUY':
                    # Reduce available capital
                    allocation.available_capital -= trade_value
                    allocation.allocated_capital += trade_value
                elif action.upper() == 'SELL':
                    # Increase available capital (simplified - doesn't account for actual P&L)
                    allocation.available_capital += trade_value
                    allocation.allocated_capital -= min(allocation.allocated_capital, trade_value)
                
                allocation.last_updated = datetime.now()
                
                logger.info(f"💰 [TRADE-RECORD] {exchange}: {action} {amount:.6f} {symbol} "
                          f"(Available: ${allocation.available_capital:.2f})")
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error recording trade: {e}")
    
    async def handle_profit_distribution(self, profit_amount: Decimal, exchange: str):
        """
        Handle profit distribution using Coinbase-Primary Capital Management
        Always implements 50/50 split regardless of which exchange generated the profit
        """
        try:
            # Add profit to unified pool from any exchange
            self.unified_profit_pool += profit_amount

            logger.info(f"💰 [COINBASE-PRIMARY] Profit ${profit_amount:.2f} from {exchange} added to unified pool")
            logger.info(f"💰 [UNIFIED-POOL] Total accumulated: ${self.unified_profit_pool:.2f}")

            # Always implement 50/50 split regardless of exchange availability
            await self._handle_coinbase_primary_profits(profit_amount, exchange)

        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error handling profit distribution: {e}")

    async def _handle_coinbase_primary_profits(self, profit_amount: Decimal, source_exchange: str):
        """Handle profits using Coinbase-Primary model"""
        try:
            # Calculate 50/50 split
            reinvest_amount = profit_amount * Decimal(str(self.profit_split_ratio))
            transfer_amount = profit_amount - reinvest_amount

            # Reinvest 50% in the source exchange
            if source_exchange in self.allocations:
                self.allocations[source_exchange].available_capital += reinvest_amount
                self.allocations[source_exchange].profit_accumulated += profit_amount

                logger.info(f"💰 [REINVEST] ${reinvest_amount:.2f} reinvested in {source_exchange}")

            # Handle 50% transfer to Coinbase wallet
            await self._handle_coinbase_wallet_transfer(transfer_amount)

        except Exception as e:
            logger.error(f"❌ [COINBASE-PRIMARY] Error handling profits: {e}")

    async def _handle_coinbase_wallet_transfer(self, transfer_amount: Decimal):
        """Handle transfer to Coinbase wallet (queue if API unavailable)"""
        try:
            # Check if Coinbase is available for transfers
            coinbase_available = 'coinbase' in self.exchange_manager.get_active_exchanges()

            if coinbase_available:
                # Attempt immediate transfer
                success = await self._execute_coinbase_transfer(transfer_amount)
                if success:
                    logger.info(f"✅ [TRANSFER] ${transfer_amount:.2f} transferred to Coinbase wallet")
                    # Process any queued transfers
                    await self._process_queued_transfers()
                else:
                    # Transfer failed, queue it
                    await self._queue_transfer(transfer_amount)
            else:
                # Coinbase unavailable, queue the transfer
                await self._queue_transfer(transfer_amount)

        except Exception as e:
            logger.error(f"❌ [TRANSFER] Error handling Coinbase wallet transfer: {e}")

    async def _execute_coinbase_transfer(self, amount: Decimal) -> bool:
        """Execute actual transfer to Coinbase wallet"""
        try:
            # This would implement the actual transfer logic
            # For now, simulate the transfer
            logger.info(f"🔄 [TRANSFER] Executing transfer of ${amount:.2f} to {self.coinbase_wallet_address}")

            # TODO: Implement actual Coinbase API transfer
            # - Convert to ETH or BTC as specified
            # - Execute transfer to wallet address
            # - Verify transaction completion

            # Simulate success for now
            return True

        except Exception as e:
            logger.error(f"❌ [TRANSFER-EXEC] Transfer execution failed: {e}")
            return False

    async def _queue_transfer(self, amount: Decimal):
        """Queue transfer for when Coinbase becomes available"""
        try:
            transfer_entry = {
                'amount': amount,
                'timestamp': datetime.now(),
                'destination': self.coinbase_wallet_address,
                'status': 'queued'
            }

            self.queued_transfers.append(transfer_entry)

            total_queued = sum(t['amount'] for t in self.queued_transfers)
            logger.warning(f"📋 [QUEUE] ${amount:.2f} queued for Coinbase transfer")
            logger.warning(f"📋 [QUEUE] Total queued: ${total_queued:.2f} ({len(self.queued_transfers)} transfers)")

        except Exception as e:
            logger.error(f"❌ [QUEUE] Error queuing transfer: {e}")

    async def _process_queued_transfers(self):
        """Process all queued transfers when Coinbase becomes available"""
        try:
            if not self.queued_transfers:
                return

            logger.info(f"🔄 [QUEUE-PROCESS] Processing {len(self.queued_transfers)} queued transfers")

            successful_transfers = []
            failed_transfers = []

            for transfer in self.queued_transfers:
                success = await self._execute_coinbase_transfer(transfer['amount'])
                if success:
                    successful_transfers.append(transfer)
                    logger.info(f"✅ [QUEUE-SUCCESS] ${transfer['amount']:.2f} transfer completed")
                else:
                    failed_transfers.append(transfer)
                    logger.error(f"❌ [QUEUE-FAILED] ${transfer['amount']:.2f} transfer failed")

            # Update queue with only failed transfers
            self.queued_transfers = failed_transfers

            total_processed = sum(t['amount'] for t in successful_transfers)
            total_remaining = sum(t['amount'] for t in failed_transfers)

            logger.info(f"📊 [QUEUE-SUMMARY] Processed: ${total_processed:.2f}, Remaining: ${total_remaining:.2f}")

        except Exception as e:
            logger.error(f"❌ [QUEUE-PROCESS] Error processing queued transfers: {e}")
    
    async def _handle_dual_mode_profits(self, profit_amount: Decimal, exchange: str):
        """Handle profits in dual exchange mode"""
        try:
            # Implement 50/50 profit split
            reinvest_amount = profit_amount * Decimal(str(self.profit_split_ratio))
            transfer_amount = profit_amount - reinvest_amount
            
            # Reinvest in trading capital
            if exchange in self.allocations:
                self.allocations[exchange].available_capital += reinvest_amount
                self.allocations[exchange].profit_accumulated += profit_amount
            
            # Transfer to Coinbase wallet (simulated)
            logger.info(f"💰 [PROFIT-SPLIT] Reinvest: ${reinvest_amount:.2f}, "
                       f"Transfer: ${transfer_amount:.2f} to {self.coinbase_wallet_address}")
            
            # TODO: Implement actual transfer to Coinbase wallet
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error in dual mode profit handling: {e}")
    
    async def _handle_bybit_only_profits(self, profit_amount: Decimal):
        """Handle profits in Bybit-only mode"""
        try:
            # Accumulate all profits in Bybit for larger position sizes
            if 'bybit' in self.allocations:
                self.allocations['bybit'].available_capital += profit_amount
                self.allocations['bybit'].profit_accumulated += profit_amount
                
                logger.info(f"💰 [BYBIT-ACCUMULATE] Profit ${profit_amount:.2f} accumulated "
                          f"(Total: ${self.allocations['bybit'].available_capital:.2f})")
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error in Bybit-only profit handling: {e}")
    
    async def _handle_coinbase_only_profits(self, profit_amount: Decimal):
        """Handle profits in Coinbase-only mode"""
        try:
            # Add profits to Coinbase allocation
            if 'coinbase' in self.allocations:
                self.allocations['coinbase'].available_capital += profit_amount
                self.allocations['coinbase'].profit_accumulated += profit_amount
                
                logger.info(f"💰 [COINBASE-ACCUMULATE] Profit ${profit_amount:.2f} accumulated")
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error in Coinbase-only profit handling: {e}")
    
    def get_capital_status(self) -> Dict[str, Any]:
        """Get comprehensive capital status report"""
        try:
            status = {
                'mode': self.exchange_manager.capital_management_mode,
                'total_capital': Decimal('0'),
                'total_available': Decimal('0'),
                'total_allocated': Decimal('0'),
                'total_profit': Decimal('0'),
                'exchanges': {}
            }
            
            for exchange, allocation in self.allocations.items():
                status['total_capital'] += allocation.total_capital
                status['total_available'] += allocation.available_capital
                status['total_allocated'] += allocation.allocated_capital
                status['total_profit'] += allocation.profit_accumulated
                
                status['exchanges'][exchange] = {
                    'total_capital': float(allocation.total_capital),
                    'available_capital': float(allocation.available_capital),
                    'allocated_capital': float(allocation.allocated_capital),
                    'profit_accumulated': float(allocation.profit_accumulated),
                    'last_updated': allocation.last_updated
                }
            
            # Convert Decimals to floats for JSON serialization
            status['total_capital'] = float(status['total_capital'])
            status['total_available'] = float(status['total_available'])
            status['total_allocated'] = float(status['total_allocated'])
            status['total_profit'] = float(status['total_profit'])
            
            return status
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MGR] Error getting capital status: {e}")
            return {'mode': 'unknown', 'error': str(e)}
