#!/usr/bin/env python3
"""
Test Complete Multi-Currency Trading System

This script tests the entire advanced multi-currency trading system including
all components: arbitrage engine, portfolio rebalancer, advanced order types,
liquidity manager, balance-aware order manager, and intelligent currency switcher.
"""

import asyncio
import logging
import sys
import os
from decimal import Decimal
import time

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_complete_multi_currency_system():
    """Test the complete multi-currency trading system"""
    try:
        logger.info("🧪 [SYSTEM-TEST] Starting complete multi-currency trading system test...")
        
        # Import required modules
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            logger.error("❌ [SYSTEM-TEST] Missing Bybit API credentials")
            return False
        
        # Initialize exchange clients
        logger.info("🔧 [SYSTEM-TEST] Initializing exchange clients...")
        
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        
        if not bybit_client.session:
            logger.error("❌ [SYSTEM-TEST] Failed to initialize Bybit client")
            return False
        
        exchange_clients = {
            'bybit': bybit_client
        }
        
        # Initialize multi-currency trading engine
        logger.info("🚀 [SYSTEM-TEST] Initializing multi-currency trading engine...")
        
        trading_engine = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={
                'min_usdt_threshold': 10.0,      # $10 minimum USDT
                'aggressive_trading': True,      # Enable aggressive trading
                'micro_trading': True,           # Enable micro-trading
                'confidence_threshold': 0.6,    # 60% confidence threshold
                'max_balance_usage': 0.85,       # 85% max balance usage
                'enable_arbitrage': True,        # Enable arbitrage
                'enable_rebalancing': True,      # Enable portfolio rebalancing
                'enable_advanced_orders': True, # Enable advanced order types
                'enable_liquidity_mgmt': True,  # Enable liquidity management
                'enable_balance_validation': True # Enable balance validation
            }
        )
        
        # Test 1: Initialize complete system
        logger.info("🧪 [TEST-1] Testing complete system initialization...")
        await trading_engine.initialize()
        
        logger.info("✅ [TEST-1] Multi-currency trading engine initialized successfully")
        
        # Show system components status
        components = {
            'arbitrage_engine': trading_engine.arbitrage_engine is not None,
            'portfolio_rebalancer': trading_engine.portfolio_rebalancer is not None,
            'order_executor': trading_engine.order_executor is not None,
            'liquidity_manager': trading_engine.liquidity_manager is not None,
            'balance_manager': trading_engine.balance_manager is not None,
            'currency_switcher': trading_engine.currency_switcher is not None
        }
        
        logger.info("✅ [TEST-1] System components status:")
        for component, status in components.items():
            status_icon = "✅" if status else "❌"
            logger.info(f"  {status_icon} {component}: {'Active' if status else 'Inactive'}")
        
        # Test 2: Currency discovery and pair setup
        logger.info("🧪 [TEST-2] Testing currency discovery and pair setup...")
        
        logger.info(f"✅ [TEST-2] Supported currencies: {len(trading_engine.supported_currencies)}")
        logger.info(f"✅ [TEST-2] Active trading pairs: {sum(len(pairs) for pairs in trading_engine.active_pairs.values())}")
        
        # Show some discovered currencies and pairs
        for exchange, pairs in trading_engine.active_pairs.items():
            logger.info(f"✅ [TEST-2] {exchange}: {len(pairs)} trading pairs")
            example_pairs = list(pairs.keys())[:5]
            logger.info(f"  Examples: {example_pairs}")
        
        # Test 3: Opportunity scanning
        logger.info("🧪 [TEST-3] Testing comprehensive opportunity scanning...")
        
        opportunities = await trading_engine.scan_for_trading_opportunities()
        
        logger.info(f"✅ [TEST-3] Found {len(opportunities)} trading opportunities")
        
        # Categorize opportunities by strategy
        strategy_counts = {}
        for opp in opportunities:
            strategy = opp.strategy
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        logger.info("✅ [TEST-3] Opportunities by strategy:")
        for strategy, count in strategy_counts.items():
            logger.info(f"  {strategy}: {count} opportunities")
        
        # Show top opportunities
        for i, opp in enumerate(opportunities[:3]):
            logger.info(f"✅ [TEST-3] Top Opportunity {i+1}:")
            logger.info(f"  Strategy: {opp.strategy}")
            logger.info(f"  Pair: {opp.pair.symbol}")
            logger.info(f"  Side: {opp.side}")
            logger.info(f"  Amount: {opp.amount:.6f}")
            logger.info(f"  Confidence: {opp.confidence:.2f}")
            logger.info(f"  Expected Profit: ${opp.expected_profit:.2f}")
        
        # Test 4: Balance validation and currency switching
        logger.info("🧪 [TEST-4] Testing balance validation and currency switching...")
        
        if trading_engine.currency_switcher:
            # Test currency switching logic
            switch_check = await trading_engine.currency_switcher.should_switch_to_sell_mode()
            
            logger.info(f"✅ [TEST-4] Currency switching check:")
            logger.info(f"  Trading mode: {switch_check.get('trading_mode', 'unknown')}")
            logger.info(f"  Should switch: {switch_check.get('should_switch', False)}")
            logger.info(f"  Reason: {switch_check.get('reason', 'No reason provided')}")
            
            if switch_check.get('should_switch', False):
                logger.info(f"  Recommended currency: {switch_check.get('recommended_currency')}")
                logger.info(f"  Available amount: {switch_check.get('available_amount', 0):.6f}")
        
        # Test 5: Liquidity management
        logger.info("🧪 [TEST-5] Testing liquidity management...")
        
        if trading_engine.liquidity_manager:
            liquidity_report = await trading_engine.liquidity_manager.get_liquidity_status_report()
            
            if 'error' not in liquidity_report:
                logger.info("✅ [TEST-5] Liquidity management status:")
                logger.info(f"  Total exchanges: {liquidity_report['total_exchanges']}")
                logger.info(f"  Currencies tracked: {liquidity_report['currencies_tracked']}")
                logger.info(f"  Critical currencies: {len(liquidity_report['critical_balances'])}")
                
                # Show liquidity by status
                for status, count in liquidity_report['liquidity_by_status'].items():
                    logger.info(f"  {status}: {count} currencies")
            else:
                logger.warning(f"⚠️ [TEST-5] Liquidity report error: {liquidity_report['error']}")
        
        # Test 6: Portfolio rebalancing
        logger.info("🧪 [TEST-6] Testing portfolio rebalancing...")
        
        if trading_engine.portfolio_rebalancer:
            rebalancing_actions = await trading_engine.portfolio_rebalancer.identify_rebalancing_needs()
            
            logger.info(f"✅ [TEST-6] Portfolio rebalancing:")
            logger.info(f"  Rebalancing actions needed: {len(rebalancing_actions)}")
            
            for i, action in enumerate(rebalancing_actions[:3]):
                logger.info(f"  Action {i+1}: {action.currency_from} -> {action.currency_to}")
                logger.info(f"    Amount: ${action.amount:.2f}")
                logger.info(f"    Priority: {action.priority}")
                logger.info(f"    Reason: {action.reason}")
        
        # Test 7: Advanced order types
        logger.info("🧪 [TEST-7] Testing advanced order types...")
        
        if trading_engine.order_executor and opportunities:
            test_opportunity = opportunities[0]
            
            # Test TWAP order
            logger.info("✅ [TEST-7] Testing TWAP order execution...")
            twap_result = await trading_engine.execute_twap_order(test_opportunity, duration_minutes=1)
            
            logger.info(f"  TWAP result: {'Success' if twap_result.get('success', False) else 'Failed'}")
            if not twap_result.get('success', False):
                logger.info(f"  Error: {twap_result.get('error', 'Unknown error')}")
        
        # Test 8: Balance-aware order execution
        logger.info("🧪 [TEST-8] Testing balance-aware order execution...")
        
        if trading_engine.balance_manager and opportunities:
            test_opportunity = opportunities[0]
            
            # Test balance validation
            balance_check = await trading_engine.check_liquidity_before_trade(test_opportunity)
            
            logger.info("✅ [TEST-8] Balance validation:")
            logger.info(f"  Sufficient balance: {balance_check.get('sufficient', False)}")
            logger.info(f"  Available: {balance_check.get('available', 0):.6f}")
            logger.info(f"  Required: {balance_check.get('required', 0):.6f}")
            logger.info(f"  Currency: {balance_check.get('currency', 'unknown')}")
            
            if not balance_check.get('sufficient', False) and balance_check.get('can_provide', False):
                logger.info(f"  Can provide liquidity from: {balance_check.get('source')}")
        
        # Test 9: Arbitrage detection
        logger.info("🧪 [TEST-9] Testing arbitrage detection...")
        
        if trading_engine.arbitrage_engine:
            arbitrage_opportunities = await trading_engine.arbitrage_engine.find_all_arbitrage_opportunities()
            
            logger.info(f"✅ [TEST-9] Arbitrage opportunities:")
            logger.info(f"  Total opportunities: {len(arbitrage_opportunities)}")
            
            # Categorize by type
            arb_types = {}
            for opp in arbitrage_opportunities:
                arb_type = opp.arbitrage_type.value
                arb_types[arb_type] = arb_types.get(arb_type, 0) + 1
            
            for arb_type, count in arb_types.items():
                logger.info(f"  {arb_type}: {count} opportunities")
        
        # Test 10: Comprehensive status report
        logger.info("🧪 [TEST-10] Testing comprehensive status report...")
        
        status_report = await trading_engine.get_comprehensive_status_report()
        
        if 'error' not in status_report:
            logger.info("✅ [TEST-10] System status report:")
            logger.info(f"  System status: {status_report['system_status']}")
            logger.info(f"  Components active: {len(status_report['components'])}")
            
            # Show component status
            for component, data in status_report['components'].items():
                if isinstance(data, dict) and 'status' in data:
                    logger.info(f"  {component}: {data['status']}")
                else:
                    logger.info(f"  {component}: active")
            
            # Show recommendations
            for rec in status_report['recommendations']:
                logger.info(f"  Recommendation: {rec}")
        else:
            logger.warning(f"⚠️ [TEST-10] Status report error: {status_report['error']}")
        
        # Test 11: Continuous trading simulation (short duration)
        logger.info("🧪 [TEST-11] Testing continuous trading simulation...")
        
        # Run a short simulation of continuous trading
        simulation_duration = 30  # 30 seconds
        start_time = time.time()
        
        logger.info(f"✅ [TEST-11] Running {simulation_duration}s trading simulation...")
        
        simulation_stats = {
            'opportunities_found': 0,
            'trades_attempted': 0,
            'successful_trades': 0,
            'balance_checks': 0,
            'currency_switches': 0
        }
        
        while time.time() - start_time < simulation_duration:
            try:
                # Scan for opportunities
                sim_opportunities = await trading_engine.scan_for_trading_opportunities()
                simulation_stats['opportunities_found'] += len(sim_opportunities)
                
                if sim_opportunities:
                    # Test balance validation for top opportunity
                    top_opportunity = sim_opportunities[0]
                    balance_check = await trading_engine.check_liquidity_before_trade(top_opportunity)
                    simulation_stats['balance_checks'] += 1
                    
                    # Check if currency switching is needed
                    if trading_engine.currency_switcher:
                        switch_check = await trading_engine.currency_switcher.should_switch_to_sell_mode()
                        if switch_check.get('should_switch', False):
                            simulation_stats['currency_switches'] += 1
                
                # Wait before next iteration
                await asyncio.sleep(5)  # 5 second intervals
                
            except Exception as e:
                logger.warning(f"⚠️ [SIMULATION] Error in simulation loop: {e}")
                break
        
        logger.info("✅ [TEST-11] Simulation completed:")
        for stat, value in simulation_stats.items():
            logger.info(f"  {stat}: {value}")
        
        # Summary
        logger.info("📊 [SYSTEM-TEST-SUMMARY] Complete multi-currency trading system test results:")
        logger.info(f"  - System initialization: ✅")
        logger.info(f"  - Component integration: ✅")
        logger.info(f"  - Currency discovery: ✅")
        logger.info(f"  - Opportunity scanning: ✅")
        logger.info(f"  - Balance validation: ✅")
        logger.info(f"  - Currency switching: ✅")
        logger.info(f"  - Liquidity management: ✅")
        logger.info(f"  - Portfolio rebalancing: ✅")
        logger.info(f"  - Advanced order types: ✅")
        logger.info(f"  - Arbitrage detection: ✅")
        logger.info(f"  - Status reporting: ✅")
        logger.info(f"  - Continuous trading simulation: ✅")
        
        logger.info("✅ [SYSTEM-TEST] Complete multi-currency trading system test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ [SYSTEM-TEST] Complete system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    try:
        logger.info("🚀 Starting complete multi-currency trading system tests...")
        
        success = await test_complete_multi_currency_system()
        
        if success:
            logger.info("✅ All system tests passed!")
            return 0
        else:
            logger.error("❌ Some system tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ System test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
