# MetaStrategyController: Controls switching between normal and high volatility trading agents based on anomaly detection flags.
import logging

class MetaStrategyController:
    def __init__(self, agent_normal, agent_high_vol, anomaly_detector):
        # Initialize with two agent instances and an anomaly detector.
        self.agent_normal = agent_normal
        self.agent_high_vol = agent_high_vol
        self.anomaly_detector = anomaly_detector
        # Start with normal agent by default
        self.current_agent = agent_normal

    def select_agent(self):
        # Select the appropriate agent based on anomaly detection status, and log any switch.
        # If either agent is missing, handle gracefully.
        if self.agent_normal is None and self.agent_high_vol is None:
            logging.error("MetaStrategyController: No agents available to select.")
            return None
        if self.agent_high_vol is None:
            # High-volatility agent not available, always use normal agent.
            return self.agent_normal
        if self.agent_normal is None:
            # Normal agent not available, use high-volatility agent exclusively.
            return self.agent_high_vol
        try:
            anomaly_active = False
            # Determine if anomaly condition is active using available method or attribute.
            if hasattr(self.anomaly_detector, "is_anomaly_detected"):
                anomaly_active = self.anomaly_detector.is_anomaly_detected()
            elif hasattr(self.anomaly_detector, "is_anomaly"):
                anomaly_active = self.anomaly_detector.is_anomaly()
            elif hasattr(self.anomaly_detector, "flagged"):
                anomaly_active = bool(self.anomaly_detector.flagged)
            else:
                # Default to False if no known indicator.
                anomaly_active = False

            # Switch agents if needed.
            if anomaly_active:
                if self.current_agent is not self.agent_high_vol:
                    self.current_agent = self.agent_high_vol
                    logging.info("MetaStrategyController: Switched to high volatility strategy.")
            else:
                if self.current_agent is not self.agent_normal:
                    self.current_agent = self.agent_normal
                    logging.info("MetaStrategyController: Switched to normal strategy.")
        except Exception as e:
            logging.error(f"MetaStrategyController: Error during agent selection - {e}", exc_info=True)
        # Return the currently selected agent (after any switch).
        return self.current_agent

    def decide_action(self, state):
        # Use the selected agent to decide an action based on the current state.
        agent = self.select_agent()
        if agent is None:
            return None
        try:
            if hasattr(agent, "decide_action"):
                action = agent.decide_action(state)
            elif hasattr(agent, "act"):
                action = agent.act(state)
            elif hasattr(agent, "execute"):
                action = agent.execute(state)
            else:
                logging.error("MetaStrategyController: Agent has no valid action method.")
                action = None
        except Exception as e:
            logging.error(f"MetaStrategyController: Error during action decision - {e}", exc_info=True)
            action = None
        return action
