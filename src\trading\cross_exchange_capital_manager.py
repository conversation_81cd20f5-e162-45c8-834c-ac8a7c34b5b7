"""
Cross-Exchange Capital Management Integration

Integrates with existing Coinbase-Primary Capital Management system to trigger
automatic transfers when both USDT and crypto balances are insufficient on
trading exchanges. Supports multi-currency transfers based on availability
and gas costs with intelligent routing.
"""

import asyncio
import logging
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import time

logger = logging.getLogger(__name__)

class TransferType(Enum):
    """Types of cross-exchange transfers"""
    DEPOSIT = "deposit"           # Transfer from Coinbase to trading exchange
    WITHDRAWAL = "withdrawal"     # Transfer from trading exchange to Coinbase
    REBALANCE = "rebalance"      # Rebalance between exchanges
    EMERGENCY = "emergency"       # Emergency liquidity provision

class TransferStatus(Enum):
    """Transfer execution status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TransferRequest:
    """Cross-exchange transfer request"""
    transfer_id: str
    transfer_type: TransferType
    currency: str
    amount: Decimal
    source_exchange: str
    target_exchange: str
    priority: int
    reason: str
    estimated_cost: Decimal
    estimated_time: float
    gas_cost: Decimal
    network: str

@dataclass
class ExchangeBalance:
    """Balance information for an exchange"""
    exchange: str
    currency: str
    available: Decimal
    locked: Decimal
    total: Decimal
    usd_value: Decimal
    last_update: float

class CrossExchangeCapitalManager:
    """
    Cross-exchange capital management system that integrates with Coinbase
    as the primary capital source and manages transfers to trading exchanges
    """
    
    def __init__(self, exchange_clients: Dict[str, Any], config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}
        
        # Configuration
        self.coinbase_wallet_address = self.config.get('coinbase_wallet_address', '******************************************')
        self.min_transfer_amount = self.config.get('min_transfer_amount', Decimal('10'))  # $10 minimum
        self.max_transfer_amount = self.config.get('max_transfer_amount', Decimal('1000'))  # $1000 maximum
        self.emergency_threshold = self.config.get('emergency_threshold', Decimal('5'))  # $5 emergency threshold
        
        # Gas cost thresholds
        self.max_gas_cost_percentage = self.config.get('max_gas_cost_percentage', 0.05)  # 5% max gas cost
        self.preferred_networks = self.config.get('preferred_networks', ['polygon', 'bsc', 'arbitrum', 'ethereum'])
        
        # Transfer tracking
        self.pending_transfers = {}
        self.transfer_history = []
        self.exchange_balances = {}
        
        # Performance metrics
        self.transfer_stats = {
            'total_transfers': 0,
            'successful_transfers': 0,
            'failed_transfers': 0,
            'total_volume': Decimal('0'),
            'total_gas_costs': Decimal('0')
        }
        
        logger.info("🏦 [CAPITAL-MANAGER] Initialized cross-exchange capital manager")
    
    async def initialize(self):
        """Initialize the cross-exchange capital manager"""
        try:
            logger.info("🔧 [CAPITAL-MANAGER] Initializing capital manager...")
            
            # Update exchange balances
            await self.update_all_exchange_balances()
            
            # Validate Coinbase connection
            await self.validate_coinbase_connection()
            
            # Check for pending transfers
            await self.check_pending_transfers()
            
            logger.info("✅ [CAPITAL-MANAGER] Capital manager initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-MANAGER] Error initializing capital manager: {e}")
            raise
    
    async def update_all_exchange_balances(self):
        """Update balance information for all exchanges"""
        try:
            logger.info("💰 [BALANCES] Updating all exchange balances...")
            
            for exchange_name, client in self.exchange_clients.items():
                try:
                    if hasattr(client, 'get_all_available_balances'):
                        balances = await client.get_all_available_balances()
                        
                        exchange_balance_data = {}
                        for currency, balance in balances.items():
                            if balance > 0:
                                usd_value = await self._get_usd_value(currency, balance, exchange_name)
                                
                                exchange_balance_data[currency] = ExchangeBalance(
                                    exchange=exchange_name,
                                    currency=currency,
                                    available=Decimal(str(balance)),
                                    locked=Decimal('0'),  # Simplified - would need specific API calls
                                    total=Decimal(str(balance)),
                                    usd_value=usd_value,
                                    last_update=time.time()
                                )
                        
                        self.exchange_balances[exchange_name] = exchange_balance_data
                        logger.info(f"💰 [BALANCES] {exchange_name}: {len(exchange_balance_data)} currencies")
                
                except Exception as e:
                    logger.warning(f"⚠️ [BALANCES] Error updating {exchange_name} balances: {e}")
                    continue
            
            logger.info(f"💰 [BALANCES] Updated balances for {len(self.exchange_balances)} exchanges")
            
        except Exception as e:
            logger.error(f"❌ [BALANCES] Error updating exchange balances: {e}")
    
    async def assess_capital_needs(self, target_exchange: str) -> Dict[str, Any]:
        """Assess capital transfer needs for a target exchange"""
        try:
            logger.info(f"🔍 [ASSESSMENT] Assessing capital needs for {target_exchange}...")
            
            # Get current balances on target exchange
            target_balances = self.exchange_balances.get(target_exchange, {})
            
            # Calculate total USD value on target exchange
            total_usd_value = sum(
                balance.usd_value for balance in target_balances.values()
            )
            
            # Check if emergency transfer is needed
            needs_emergency = total_usd_value < self.emergency_threshold
            
            # Identify specific currency needs
            currency_needs = []
            
            # Check USDT/USD specifically
            usdt_balance = target_balances.get('USDT', ExchangeBalance('', 'USDT', Decimal('0'), Decimal('0'), Decimal('0'), Decimal('0'), 0))
            usd_balance = target_balances.get('USD', ExchangeBalance('', 'USD', Decimal('0'), Decimal('0'), Decimal('0'), Decimal('0'), 0))
            
            stable_value = usdt_balance.usd_value + usd_balance.usd_value
            
            if stable_value < Decimal('20'):  # Need at least $20 in stables
                currency_needs.append({
                    'currency': 'USDT',
                    'current_value': stable_value,
                    'needed_value': Decimal('50'),  # Target $50
                    'priority': 'high'
                })
            
            # Check major crypto holdings
            major_cryptos = ['BTC', 'ETH', 'SOL', 'ADA']
            for crypto in major_cryptos:
                crypto_balance = target_balances.get(crypto, ExchangeBalance('', crypto, Decimal('0'), Decimal('0'), Decimal('0'), Decimal('0'), 0))
                if crypto_balance.usd_value < Decimal('10'):  # Need at least $10 in each major crypto
                    currency_needs.append({
                        'currency': crypto,
                        'current_value': crypto_balance.usd_value,
                        'needed_value': Decimal('25'),  # Target $25
                        'priority': 'medium'
                    })
            
            assessment = {
                'exchange': target_exchange,
                'total_usd_value': total_usd_value,
                'needs_emergency': needs_emergency,
                'currency_needs': currency_needs,
                'recommended_transfers': [],
                'assessment_time': time.time()
            }
            
            # Generate transfer recommendations
            if currency_needs:
                for need in currency_needs:
                    transfer_amount = need['needed_value'] - need['current_value']
                    if transfer_amount > self.min_transfer_amount:
                        
                        # Find best source for this currency
                        source_exchange = await self._find_best_source_exchange(need['currency'], transfer_amount)
                        
                        if source_exchange:
                            assessment['recommended_transfers'].append({
                                'currency': need['currency'],
                                'amount': transfer_amount,
                                'source': source_exchange,
                                'target': target_exchange,
                                'priority': need['priority'],
                                'estimated_cost': await self._estimate_transfer_cost(need['currency'], transfer_amount)
                            })
            
            logger.info(f"🔍 [ASSESSMENT] {target_exchange} needs {len(currency_needs)} currency transfers")
            return assessment
            
        except Exception as e:
            logger.error(f"❌ [ASSESSMENT] Error assessing capital needs: {e}")
            return {'error': str(e)}
    
    async def execute_capital_transfer(self, transfer_request: TransferRequest) -> Dict[str, Any]:
        """Execute a cross-exchange capital transfer"""
        try:
            logger.info(f"🚀 [TRANSFER] Executing capital transfer")
            logger.info(f"🚀 [TRANSFER] {transfer_request.amount:.6f} {transfer_request.currency}")
            logger.info(f"🚀 [TRANSFER] {transfer_request.source_exchange} -> {transfer_request.target_exchange}")
            
            # Validate transfer request
            validation = await self._validate_transfer_request(transfer_request)
            if not validation['valid']:
                return {"success": False, "error": validation['reason']}
            
            # Check gas costs
            gas_check = await self._check_gas_costs(transfer_request)
            if not gas_check['acceptable']:
                return {"success": False, "error": f"Gas costs too high: {gas_check['reason']}"}
            
            # Execute transfer based on source/target
            if transfer_request.source_exchange == 'coinbase':
                result = await self._execute_coinbase_to_exchange_transfer(transfer_request)
            elif transfer_request.target_exchange == 'coinbase':
                result = await self._execute_exchange_to_coinbase_transfer(transfer_request)
            else:
                result = await self._execute_exchange_to_exchange_transfer(transfer_request)
            
            # Update statistics
            self.transfer_stats['total_transfers'] += 1
            if result.get('success', False):
                self.transfer_stats['successful_transfers'] += 1
                self.transfer_stats['total_volume'] += transfer_request.amount
                self.transfer_stats['total_gas_costs'] += transfer_request.gas_cost
            else:
                self.transfer_stats['failed_transfers'] += 1
            
            # Record transfer
            self.transfer_history.append({
                'timestamp': time.time(),
                'transfer_request': transfer_request,
                'result': result
            })
            
            return result
            
        except Exception as e:
            logger.error(f"❌ [TRANSFER] Error executing transfer: {e}")
            return {"success": False, "error": str(e)}
    
    async def _execute_coinbase_to_exchange_transfer(self, transfer_request: TransferRequest) -> Dict[str, Any]:
        """Execute transfer from Coinbase to trading exchange"""
        try:
            logger.info(f"🏦 [COINBASE-TRANSFER] Transferring from Coinbase to {transfer_request.target_exchange}")
            
            # For now, simulate the transfer
            # In production, this would use Coinbase Advanced API
            logger.info(f"🏦 [COINBASE-TRANSFER] Simulating Coinbase withdrawal")
            logger.info(f"  Currency: {transfer_request.currency}")
            logger.info(f"  Amount: {transfer_request.amount:.6f}")
            logger.info(f"  Target: {transfer_request.target_exchange}")
            logger.info(f"  Network: {transfer_request.network}")
            logger.info(f"  Gas Cost: ${transfer_request.gas_cost:.2f}")
            
            # Simulate transfer time
            await asyncio.sleep(2)  # 2 seconds for simulation
            
            return {
                "success": True,
                "transfer_type": "coinbase_to_exchange",
                "transaction_id": f"cb_tx_{int(time.time())}",
                "currency": transfer_request.currency,
                "amount": float(transfer_request.amount),
                "source": transfer_request.source_exchange,
                "target": transfer_request.target_exchange,
                "network": transfer_request.network,
                "gas_cost": float(transfer_request.gas_cost),
                "estimated_confirmation_time": transfer_request.estimated_time
            }
            
        except Exception as e:
            logger.error(f"❌ [COINBASE-TRANSFER] Error in Coinbase transfer: {e}")
            return {"success": False, "error": str(e)}
    
    async def _execute_exchange_to_coinbase_transfer(self, transfer_request: TransferRequest) -> Dict[str, Any]:
        """Execute transfer from trading exchange to Coinbase"""
        try:
            logger.info(f"🏦 [EXCHANGE-TRANSFER] Transferring from {transfer_request.source_exchange} to Coinbase")
            
            # Get source exchange client
            source_client = self.exchange_clients.get(transfer_request.source_exchange)
            if not source_client:
                return {"success": False, "error": f"Source exchange {transfer_request.source_exchange} not available"}
            
            # For now, simulate the transfer
            # In production, this would use exchange withdrawal APIs
            logger.info(f"🏦 [EXCHANGE-TRANSFER] Simulating exchange withdrawal")
            logger.info(f"  Currency: {transfer_request.currency}")
            logger.info(f"  Amount: {transfer_request.amount:.6f}")
            logger.info(f"  Target: Coinbase wallet {self.coinbase_wallet_address}")
            logger.info(f"  Network: {transfer_request.network}")
            
            # Simulate transfer time
            await asyncio.sleep(3)  # 3 seconds for simulation
            
            return {
                "success": True,
                "transfer_type": "exchange_to_coinbase",
                "transaction_id": f"ex_tx_{int(time.time())}",
                "currency": transfer_request.currency,
                "amount": float(transfer_request.amount),
                "source": transfer_request.source_exchange,
                "target": "coinbase",
                "wallet_address": self.coinbase_wallet_address,
                "network": transfer_request.network,
                "gas_cost": float(transfer_request.gas_cost)
            }
            
        except Exception as e:
            logger.error(f"❌ [EXCHANGE-TRANSFER] Error in exchange transfer: {e}")
            return {"success": False, "error": str(e)}
    
    async def _execute_exchange_to_exchange_transfer(self, transfer_request: TransferRequest) -> Dict[str, Any]:
        """Execute transfer between trading exchanges"""
        try:
            logger.info(f"🔄 [EXCHANGE-TO-EXCHANGE] Direct exchange transfer")
            
            # This would typically go through Coinbase as intermediary
            # For now, simulate direct transfer
            logger.info(f"🔄 [EXCHANGE-TO-EXCHANGE] Simulating direct transfer")
            logger.info(f"  {transfer_request.source_exchange} -> {transfer_request.target_exchange}")
            logger.info(f"  {transfer_request.amount:.6f} {transfer_request.currency}")
            
            # Simulate transfer time
            await asyncio.sleep(4)  # 4 seconds for simulation
            
            return {
                "success": True,
                "transfer_type": "exchange_to_exchange",
                "transaction_id": f"e2e_tx_{int(time.time())}",
                "currency": transfer_request.currency,
                "amount": float(transfer_request.amount),
                "source": transfer_request.source_exchange,
                "target": transfer_request.target_exchange,
                "network": transfer_request.network
            }
            
        except Exception as e:
            logger.error(f"❌ [EXCHANGE-TO-EXCHANGE] Error in direct transfer: {e}")
            return {"success": False, "error": str(e)}
    
    async def validate_coinbase_connection(self):
        """Validate connection to Coinbase"""
        try:
            logger.info("🔗 [COINBASE] Validating Coinbase connection...")
            
            # Check if Coinbase client is available
            coinbase_client = self.exchange_clients.get('coinbase')
            if coinbase_client:
                logger.info("✅ [COINBASE] Coinbase client available")
                
                # Test connection (simplified)
                if hasattr(coinbase_client, 'get_all_available_balances'):
                    balances = await coinbase_client.get_all_available_balances()
                    logger.info(f"✅ [COINBASE] Connected - {len(balances)} currencies available")
                else:
                    logger.warning("⚠️ [COINBASE] Coinbase client missing balance method")
            else:
                logger.warning("⚠️ [COINBASE] Coinbase client not available - transfers will be simulated")
            
        except Exception as e:
            logger.error(f"❌ [COINBASE] Error validating Coinbase connection: {e}")
    
    async def check_pending_transfers(self):
        """Check status of pending transfers"""
        logger.info("🔍 [PENDING] Checking pending transfers...")
        # Implementation for checking pending transfer status
        pass
    
    async def _find_best_source_exchange(self, currency: str, amount: Decimal) -> Optional[str]:
        """Find the best source exchange for a currency transfer"""
        try:
            best_source = None
            best_available = Decimal('0')
            
            # Check Coinbase first (primary capital source)
            coinbase_balances = self.exchange_balances.get('coinbase', {})
            if currency in coinbase_balances:
                available = coinbase_balances[currency].available
                if available >= amount:
                    return 'coinbase'
                elif available > best_available:
                    best_source = 'coinbase'
                    best_available = available
            
            # Check other exchanges
            for exchange_name, balances in self.exchange_balances.items():
                if exchange_name == 'coinbase':
                    continue
                
                if currency in balances:
                    available = balances[currency].available
                    if available >= amount and available > best_available:
                        best_source = exchange_name
                        best_available = available
            
            return best_source
            
        except Exception as e:
            logger.error(f"❌ [SOURCE] Error finding source exchange: {e}")
            return None
    
    async def _validate_transfer_request(self, transfer_request: TransferRequest) -> Dict[str, Any]:
        """Validate a transfer request"""
        try:
            # Check minimum amount
            if transfer_request.amount < self.min_transfer_amount:
                return {"valid": False, "reason": f"Amount below minimum {self.min_transfer_amount}"}
            
            # Check maximum amount
            if transfer_request.amount > self.max_transfer_amount:
                return {"valid": False, "reason": f"Amount above maximum {self.max_transfer_amount}"}
            
            # Check source has sufficient balance
            source_balances = self.exchange_balances.get(transfer_request.source_exchange, {})
            if transfer_request.currency in source_balances:
                available = source_balances[transfer_request.currency].available
                if available < transfer_request.amount:
                    return {"valid": False, "reason": f"Insufficient balance: {available} < {transfer_request.amount}"}
            else:
                return {"valid": False, "reason": f"Currency {transfer_request.currency} not available on {transfer_request.source_exchange}"}
            
            return {"valid": True, "reason": "Validation passed"}
            
        except Exception as e:
            return {"valid": False, "reason": f"Validation error: {str(e)}"}
    
    async def _check_gas_costs(self, transfer_request: TransferRequest) -> Dict[str, Any]:
        """Check if gas costs are acceptable"""
        try:
            gas_percentage = float(transfer_request.gas_cost / transfer_request.amount)
            
            if gas_percentage > self.max_gas_cost_percentage:
                return {
                    "acceptable": False,
                    "reason": f"Gas cost {gas_percentage*100:.1f}% > {self.max_gas_cost_percentage*100:.1f}% threshold"
                }
            
            return {"acceptable": True, "reason": "Gas costs acceptable"}
            
        except Exception as e:
            return {"acceptable": False, "reason": f"Gas check error: {str(e)}"}
    
    async def _estimate_transfer_cost(self, currency: str, amount: Decimal) -> Decimal:
        """Estimate transfer cost including gas fees"""
        try:
            # Simplified gas cost estimation
            base_costs = {
                'USDT': Decimal('2.0'),   # $2 USDT transfer
                'BTC': Decimal('5.0'),    # $5 BTC transfer
                'ETH': Decimal('3.0'),    # $3 ETH transfer
                'SOL': Decimal('0.1'),    # $0.10 SOL transfer
            }
            
            return base_costs.get(currency, Decimal('1.0'))
            
        except Exception as e:
            logger.error(f"❌ [COST] Error estimating cost: {e}")
            return Decimal('1.0')
    
    async def _get_usd_value(self, currency: str, amount: float, exchange_name: str) -> Decimal:
        """Get USD value of a currency amount"""
        try:
            if currency in ['USD', 'USDT', 'USDC']:
                return Decimal(str(amount))
            
            # Try to get price from exchange
            client = self.exchange_clients.get(exchange_name)
            if client and hasattr(client, 'get_price'):
                symbol = f"{currency}USDT"
                price = client.get_price(symbol)
                if price and float(price) > 0:
                    return Decimal(str(amount)) * Decimal(str(price))
            
            return Decimal('0')
            
        except Exception as e:
            logger.debug(f"Error getting USD value for {currency}: {e}")
            return Decimal('0')
