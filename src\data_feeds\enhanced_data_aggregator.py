#!/usr/bin/env python3
"""
Enhanced Multi-Source Data Aggregation System
Combines APIs, web crawlers, and alternative sources with intelligent weighting
"""

import asyncio
import aiohttp
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import statistics
from decimal import Decimal

logger = logging.getLogger(__name__)

class DataSourceType(Enum):
    """Data source type classification"""
    EXCHANGE_API = "exchange_api"
    PRICE_API = "price_api"
    NEWS_CRAWLER = "news_crawler"
    SOCIAL_CRAWLER = "social_crawler"
    ALTERNATIVE_API = "alternative_api"
    WEBSOCKET_FEED = "websocket_feed"

class DataQuality(Enum):
    """Data quality levels"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    UNRELIABLE = "unreliable"

@dataclass
class DataSourceConfig:
    """Configuration for a data source"""
    name: str
    source_type: DataSourceType
    url: str
    weight: float = 1.0
    timeout: int = 10
    retry_count: int = 3
    rate_limit: int = 60  # requests per minute
    api_key: Optional[str] = None
    headers: Dict[str, str] = field(default_factory=dict)
    enabled: bool = True
    priority: int = 50  # 0-100, higher is better

@dataclass
class PriceData:
    """Standardized price data structure"""
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    source: str
    bid: Optional[float] = None
    ask: Optional[float] = None
    high_24h: Optional[float] = None
    low_24h: Optional[float] = None
    change_24h: Optional[float] = None
    quality: DataQuality = DataQuality.GOOD
    latency_ms: float = 0.0
    confidence: float = 1.0

@dataclass
class AggregatedData:
    """Aggregated data from multiple sources"""
    symbol: str
    weighted_price: float
    price_range: Tuple[float, float]
    volume_weighted_price: float
    confidence_score: float
    data_quality: DataQuality
    source_count: int
    timestamp: datetime
    individual_sources: List[PriceData] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class EnhancedDataAggregator:
    """
    Enhanced Multi-Source Data Aggregation System
    
    Features:
    - Multiple data source integration (APIs, crawlers, WebSockets)
    - Intelligent weighting and quality assessment
    - Real-time price discovery for any cryptocurrency pair
    - Automatic outlier detection and filtering
    - Performance monitoring and optimization
    - Fallback mechanisms and error recovery
    """
    
    def __init__(self):
        self.data_sources: Dict[str, DataSourceConfig] = {}
        self.session: Optional[aiohttp.ClientSession] = None
        self.price_cache: Dict[str, AggregatedData] = {}
        self.cache_ttl = 30  # seconds
        
        # Performance tracking
        self.source_performance: Dict[str, Dict[str, float]] = defaultdict(lambda: {
            'response_time': 0.0,
            'success_rate': 100.0,
            'error_count': 0,
            'total_requests': 0,
            'last_success': None
        })
        
        # Quality control
        self.outlier_threshold = 0.05  # 5% price deviation
        self.min_sources_required = 2
        self.max_price_age_seconds = 300  # 5 minutes
        
        # Rate limiting
        self.rate_limiters: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        logger.info("🔗 [AGGREGATOR] Enhanced data aggregator initialized")
    
    async def initialize(self) -> bool:
        """Initialize the data aggregator"""
        try:
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                connector=aiohttp.TCPConnector(limit=100, limit_per_host=20)
            )
            
            # Initialize default data sources
            await self._setup_default_sources()
            
            logger.info("✅ [AGGREGATOR] Data aggregator initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Failed to initialize: {e}")
            return False
    
    async def _setup_default_sources(self):
        """Setup default data sources"""
        try:
            # Exchange APIs
            exchange_sources = [
                DataSourceConfig(
                    name="binance_api",
                    source_type=DataSourceType.EXCHANGE_API,
                    url="https://api.binance.com/api/v3/ticker/24hr",
                    weight=1.5,
                    priority=90
                ),
                DataSourceConfig(
                    name="coinbase_api",
                    source_type=DataSourceType.EXCHANGE_API,
                    url="https://api.exchange.coinbase.com/products/{symbol}/ticker",
                    weight=1.3,
                    priority=85
                ),
                DataSourceConfig(
                    name="bybit_api",
                    source_type=DataSourceType.EXCHANGE_API,
                    url="https://api.bybit.com/v5/market/tickers",
                    weight=1.2,
                    priority=80
                )
            ]
            
            # Alternative price APIs
            alternative_sources = [
                DataSourceConfig(
                    name="coingecko_api",
                    source_type=DataSourceType.ALTERNATIVE_API,
                    url="https://api.coingecko.com/api/v3/simple/price",
                    weight=0.8,
                    priority=70,
                    rate_limit=30  # Lower rate limit for free API
                ),
                DataSourceConfig(
                    name="dexscreener_api",
                    source_type=DataSourceType.ALTERNATIVE_API,
                    url="https://api.dexscreener.com/latest/dex/tokens/{token}",
                    weight=0.6,
                    priority=60
                ),
                DataSourceConfig(
                    name="coinmarketcap_api",
                    source_type=DataSourceType.ALTERNATIVE_API,
                    url="https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest",
                    weight=0.9,
                    priority=75,
                    headers={"X-CMC_PRO_API_KEY": ""}  # API key would be set separately
                )
            ]
            
            # Register all sources
            all_sources = exchange_sources + alternative_sources
            for source in all_sources:
                self.data_sources[source.name] = source
            
            logger.info(f"📊 [AGGREGATOR] Registered {len(all_sources)} default data sources")
            
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error setting up default sources: {e}")
    
    async def get_aggregated_price(self, symbol: str, force_refresh: bool = False) -> Optional[AggregatedData]:
        """
        Get aggregated price data from multiple sources
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT', 'ETH-USD')
            force_refresh: Force refresh from sources, ignore cache
            
        Returns:
            AggregatedData object with weighted price information
        """
        try:
            # Check cache first
            if not force_refresh and symbol in self.price_cache:
                cached_data = self.price_cache[symbol]
                age = (datetime.now() - cached_data.timestamp).total_seconds()
                if age < self.cache_ttl:
                    return cached_data
            
            # Fetch from all available sources
            source_data = await self._fetch_from_all_sources(symbol)
            
            if not source_data:
                logger.warning(f"⚠️ [AGGREGATOR] No data available for {symbol}")
                return None
            
            # Filter outliers
            filtered_data = self._filter_outliers(source_data)
            
            if len(filtered_data) < self.min_sources_required:
                logger.warning(f"⚠️ [AGGREGATOR] Insufficient reliable sources for {symbol}: {len(filtered_data)}")
                # Use unfiltered data if we don't have enough filtered sources
                filtered_data = source_data
            
            # Aggregate the data
            aggregated = self._aggregate_price_data(symbol, filtered_data)
            
            # Cache the result
            self.price_cache[symbol] = aggregated
            
            return aggregated
            
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error getting aggregated price for {symbol}: {e}")
            return None
    
    async def _fetch_from_all_sources(self, symbol: str) -> List[PriceData]:
        """Fetch price data from all available sources"""
        tasks = []
        
        for source_name, source_config in self.data_sources.items():
            if source_config.enabled and self._check_rate_limit(source_name):
                task = asyncio.create_task(
                    self._fetch_from_source(source_name, source_config, symbol)
                )
                tasks.append(task)
        
        # Execute all requests in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful results
        price_data = []
        for result in results:
            if isinstance(result, PriceData):
                price_data.append(result)
            elif isinstance(result, Exception):
                logger.debug(f"Source fetch failed: {result}")
        
        return price_data
    
    async def _fetch_from_source(self, source_name: str, config: DataSourceConfig, symbol: str) -> Optional[PriceData]:
        """Fetch price data from a specific source"""
        start_time = time.time()
        
        try:
            # Update rate limiter
            self.rate_limiters[source_name].append(time.time())
            
            # Prepare URL and headers
            url = self._prepare_url(config.url, symbol)
            headers = config.headers.copy()
            
            # Make request
            async with self.session.get(url, headers=headers, timeout=config.timeout) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Parse response based on source type
                    price_data = self._parse_response(source_name, data, symbol)
                    
                    if price_data:
                        # Calculate latency
                        latency_ms = (time.time() - start_time) * 1000
                        price_data.latency_ms = latency_ms
                        
                        # Update performance metrics
                        self._update_source_performance(source_name, True, latency_ms)
                        
                        return price_data
                else:
                    logger.warning(f"⚠️ [AGGREGATOR] HTTP {response.status} from {source_name}")
                    self._update_source_performance(source_name, False)
                    
        except Exception as e:
            logger.debug(f"Error fetching from {source_name}: {e}")
            self._update_source_performance(source_name, False)
        
        return None
    
    def _prepare_url(self, url_template: str, symbol: str) -> str:
        """Prepare URL with symbol substitution"""
        try:
            # Handle different symbol formats
            symbol_formats = {
                'symbol': symbol,
                'token': symbol.replace('USDT', '').replace('USD', ''),
                'base': symbol.split('USDT')[0] if 'USDT' in symbol else symbol.split('USD')[0],
                'quote': 'USDT' if 'USDT' in symbol else 'USD'
            }
            
            # Replace placeholders
            url = url_template
            for key, value in symbol_formats.items():
                url = url.replace(f'{{{key}}}', value)
            
            return url
            
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error preparing URL: {e}")
            return url_template

    def _parse_response(self, source_name: str, data: Dict[str, Any], symbol: str) -> Optional[PriceData]:
        """Parse API response into standardized PriceData"""
        try:
            if source_name == "binance_api":
                return self._parse_binance_response(data, symbol)
            elif source_name == "coinbase_api":
                return self._parse_coinbase_response(data, symbol)
            elif source_name == "bybit_api":
                return self._parse_bybit_response(data, symbol)
            elif source_name == "coingecko_api":
                return self._parse_coingecko_response(data, symbol)
            elif source_name == "dexscreener_api":
                return self._parse_dexscreener_response(data, symbol)
            elif source_name == "coinmarketcap_api":
                return self._parse_coinmarketcap_response(data, symbol)
            else:
                logger.warning(f"⚠️ [AGGREGATOR] Unknown source: {source_name}")
                return None

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error parsing {source_name} response: {e}")
            return None

    def _parse_binance_response(self, data: Dict[str, Any], symbol: str) -> Optional[PriceData]:
        """Parse Binance API response"""
        try:
            if isinstance(data, list):
                # Find matching symbol in list
                for item in data:
                    if item.get('symbol') == symbol:
                        return PriceData(
                            symbol=symbol,
                            price=float(item['lastPrice']),
                            volume=float(item['volume']),
                            timestamp=datetime.now(),
                            source="binance",
                            bid=float(item.get('bidPrice', 0)),
                            ask=float(item.get('askPrice', 0)),
                            high_24h=float(item.get('highPrice', 0)),
                            low_24h=float(item.get('lowPrice', 0)),
                            change_24h=float(item.get('priceChangePercent', 0))
                        )
            elif isinstance(data, dict):
                return PriceData(
                    symbol=symbol,
                    price=float(data['lastPrice']),
                    volume=float(data['volume']),
                    timestamp=datetime.now(),
                    source="binance",
                    bid=float(data.get('bidPrice', 0)),
                    ask=float(data.get('askPrice', 0)),
                    high_24h=float(data.get('highPrice', 0)),
                    low_24h=float(data.get('lowPrice', 0)),
                    change_24h=float(data.get('priceChangePercent', 0))
                )
            return None

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error parsing Binance data: {e}")
            return None

    def _parse_coinbase_response(self, data: Dict[str, Any], symbol: str) -> Optional[PriceData]:
        """Parse Coinbase API response"""
        try:
            return PriceData(
                symbol=symbol,
                price=float(data['price']),
                volume=float(data.get('volume', 0)),
                timestamp=datetime.now(),
                source="coinbase",
                bid=float(data.get('bid', 0)),
                ask=float(data.get('ask', 0))
            )
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error parsing Coinbase data: {e}")
            return None

    def _parse_bybit_response(self, data: Dict[str, Any], symbol: str) -> Optional[PriceData]:
        """Parse Bybit API response"""
        try:
            result = data.get('result', {})
            if isinstance(result, dict) and 'list' in result:
                for item in result['list']:
                    if item.get('symbol') == symbol:
                        return PriceData(
                            symbol=symbol,
                            price=float(item['lastPrice']),
                            volume=float(item.get('volume24h', 0)),
                            timestamp=datetime.now(),
                            source="bybit",
                            bid=float(item.get('bid1Price', 0)),
                            ask=float(item.get('ask1Price', 0)),
                            high_24h=float(item.get('highPrice24h', 0)),
                            low_24h=float(item.get('lowPrice24h', 0)),
                            change_24h=float(item.get('price24hPcnt', 0))
                        )
            return None
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error parsing Bybit data: {e}")
            return None

    def _parse_coingecko_response(self, data: Dict[str, Any], symbol: str) -> Optional[PriceData]:
        """Parse CoinGecko API response"""
        try:
            # CoinGecko returns different format
            token = symbol.replace('USDT', '').replace('USD', '').lower()
            if token in data:
                price_data = data[token]
                return PriceData(
                    symbol=symbol,
                    price=float(price_data.get('usd', 0)),
                    volume=0,  # Volume not available in simple price endpoint
                    timestamp=datetime.now(),
                    source="coingecko",
                    quality=DataQuality.GOOD
                )
            return None
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error parsing CoinGecko data: {e}")
            return None

    def _parse_dexscreener_response(self, data: Dict[str, Any], symbol: str) -> Optional[PriceData]:
        """Parse DEXScreener API response"""
        try:
            if 'pairs' in data and data['pairs']:
                pair = data['pairs'][0]  # Take first pair
                return PriceData(
                    symbol=symbol,
                    price=float(pair.get('priceUsd', 0)),
                    volume=float(pair.get('volume', {}).get('h24', 0)),
                    timestamp=datetime.now(),
                    source="dexscreener",
                    quality=DataQuality.FAIR  # DEX data might be less reliable
                )
            return None
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error parsing DEXScreener data: {e}")
            return None

    def _parse_coinmarketcap_response(self, data: Dict[str, Any], symbol: str) -> Optional[PriceData]:
        """Parse CoinMarketCap API response"""
        try:
            if 'data' in data:
                for coin_data in data['data'].values():
                    quote = coin_data.get('quote', {}).get('USD', {})
                    return PriceData(
                        symbol=symbol,
                        price=float(quote.get('price', 0)),
                        volume=float(quote.get('volume_24h', 0)),
                        timestamp=datetime.now(),
                        source="coinmarketcap",
                        change_24h=float(quote.get('percent_change_24h', 0))
                    )
            return None
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error parsing CoinMarketCap data: {e}")
            return None

    def _filter_outliers(self, price_data: List[PriceData]) -> List[PriceData]:
        """Filter out price outliers using statistical methods"""
        try:
            if len(price_data) < 3:
                return price_data  # Not enough data to filter outliers

            prices = [data.price for data in price_data]
            median_price = statistics.median(prices)

            # Filter based on percentage deviation from median
            filtered_data = []
            for data in price_data:
                deviation = abs(data.price - median_price) / median_price
                if deviation <= self.outlier_threshold:
                    filtered_data.append(data)
                else:
                    logger.debug(f"🚫 [AGGREGATOR] Filtered outlier: {data.source} price {data.price} "
                               f"(deviation: {deviation:.2%})")

            return filtered_data

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error filtering outliers: {e}")
            return price_data

    def _aggregate_price_data(self, symbol: str, price_data: List[PriceData]) -> AggregatedData:
        """Aggregate price data from multiple sources"""
        try:
            if not price_data:
                raise ValueError("No price data to aggregate")

            # Calculate weighted average price
            total_weight = 0
            weighted_sum = 0
            volume_weighted_sum = 0
            total_volume = 0

            for data in price_data:
                source_config = self.data_sources.get(data.source)
                weight = source_config.weight if source_config else 1.0

                # Adjust weight based on data quality and performance
                quality_multiplier = self._get_quality_multiplier(data.quality)
                performance_multiplier = self._get_performance_multiplier(data.source)

                adjusted_weight = weight * quality_multiplier * performance_multiplier

                weighted_sum += data.price * adjusted_weight
                total_weight += adjusted_weight

                # Volume weighted calculation
                if data.volume > 0:
                    volume_weighted_sum += data.price * data.volume
                    total_volume += data.volume

            # Calculate final prices
            weighted_price = weighted_sum / total_weight if total_weight > 0 else 0
            volume_weighted_price = volume_weighted_sum / total_volume if total_volume > 0 else weighted_price

            # Calculate price range
            prices = [data.price for data in price_data]
            price_range = (min(prices), max(prices))

            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(price_data)

            # Determine overall data quality
            data_quality = self._determine_overall_quality(price_data)

            return AggregatedData(
                symbol=symbol,
                weighted_price=weighted_price,
                price_range=price_range,
                volume_weighted_price=volume_weighted_price,
                confidence_score=confidence_score,
                data_quality=data_quality,
                source_count=len(price_data),
                timestamp=datetime.now(),
                individual_sources=price_data,
                metadata={
                    'total_volume': total_volume,
                    'price_spread': price_range[1] - price_range[0],
                    'price_spread_percentage': ((price_range[1] - price_range[0]) / weighted_price * 100) if weighted_price > 0 else 0
                }
            )

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error aggregating price data: {e}")
            raise

    def _get_quality_multiplier(self, quality: DataQuality) -> float:
        """Get quality multiplier for weighting"""
        multipliers = {
            DataQuality.EXCELLENT: 1.2,
            DataQuality.GOOD: 1.0,
            DataQuality.FAIR: 0.8,
            DataQuality.POOR: 0.6,
            DataQuality.UNRELIABLE: 0.3
        }
        return multipliers.get(quality, 1.0)

    def _get_performance_multiplier(self, source_name: str) -> float:
        """Get performance multiplier based on source reliability"""
        try:
            performance = self.source_performance[source_name]
            success_rate = performance['success_rate']

            if success_rate >= 95:
                return 1.1
            elif success_rate >= 90:
                return 1.0
            elif success_rate >= 80:
                return 0.9
            elif success_rate >= 70:
                return 0.7
            else:
                return 0.5

        except Exception:
            return 1.0

    def _calculate_confidence_score(self, price_data: List[PriceData]) -> float:
        """Calculate confidence score based on data consistency"""
        try:
            if len(price_data) < 2:
                return 0.5  # Low confidence with single source

            prices = [data.price for data in price_data]
            mean_price = statistics.mean(prices)
            std_dev = statistics.stdev(prices) if len(prices) > 1 else 0

            # Calculate coefficient of variation
            cv = std_dev / mean_price if mean_price > 0 else 1

            # Convert to confidence score (lower CV = higher confidence)
            confidence = max(0.1, 1.0 - (cv * 10))  # Scale CV to confidence

            # Boost confidence for more sources
            source_bonus = min(0.2, len(price_data) * 0.05)
            confidence += source_bonus

            return min(1.0, confidence)

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error calculating confidence: {e}")
            return 0.5

    def _determine_overall_quality(self, price_data: List[PriceData]) -> DataQuality:
        """Determine overall data quality from individual sources"""
        try:
            if not price_data:
                return DataQuality.UNRELIABLE

            # Count quality levels
            quality_counts = defaultdict(int)
            for data in price_data:
                quality_counts[data.quality] += 1

            total_sources = len(price_data)

            # Determine overall quality based on distribution
            if quality_counts[DataQuality.EXCELLENT] >= total_sources * 0.5:
                return DataQuality.EXCELLENT
            elif quality_counts[DataQuality.GOOD] >= total_sources * 0.5:
                return DataQuality.GOOD
            elif quality_counts[DataQuality.FAIR] >= total_sources * 0.5:
                return DataQuality.FAIR
            elif quality_counts[DataQuality.POOR] >= total_sources * 0.5:
                return DataQuality.POOR
            else:
                return DataQuality.FAIR  # Mixed quality

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error determining quality: {e}")
            return DataQuality.FAIR

    def _check_rate_limit(self, source_name: str) -> bool:
        """Check if source is within rate limits"""
        try:
            source_config = self.data_sources.get(source_name)
            if not source_config:
                return False

            now = time.time()
            rate_limiter = self.rate_limiters[source_name]

            # Remove old requests (older than 1 minute)
            while rate_limiter and now - rate_limiter[0] > 60:
                rate_limiter.popleft()

            # Check if we're within rate limit
            return len(rate_limiter) < source_config.rate_limit

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error checking rate limit: {e}")
            return False

    def _update_source_performance(self, source_name: str, success: bool, latency_ms: float = 0):
        """Update performance metrics for a source"""
        try:
            performance = self.source_performance[source_name]
            performance['total_requests'] += 1

            if success:
                performance['last_success'] = datetime.now()
                if latency_ms > 0:
                    # Update average response time
                    if performance['response_time'] == 0:
                        performance['response_time'] = latency_ms
                    else:
                        performance['response_time'] = (performance['response_time'] + latency_ms) / 2
            else:
                performance['error_count'] += 1

            # Update success rate
            successful_requests = performance['total_requests'] - performance['error_count']
            performance['success_rate'] = (successful_requests / performance['total_requests']) * 100

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error updating performance: {e}")

    async def get_multiple_symbols(self, symbols: List[str]) -> Dict[str, Optional[AggregatedData]]:
        """Get aggregated data for multiple symbols"""
        try:
            tasks = []
            for symbol in symbols:
                task = asyncio.create_task(self.get_aggregated_price(symbol))
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            symbol_data = {}
            for symbol, result in zip(symbols, results):
                if isinstance(result, AggregatedData):
                    symbol_data[symbol] = result
                else:
                    symbol_data[symbol] = None
                    if isinstance(result, Exception):
                        logger.error(f"❌ [AGGREGATOR] Error for {symbol}: {result}")

            return symbol_data

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error getting multiple symbols: {e}")
            return {}

    def get_performance_report(self) -> Dict[str, Any]:
        """Get performance report for all sources"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'total_sources': len(self.data_sources),
                'enabled_sources': sum(1 for s in self.data_sources.values() if s.enabled),
                'cache_size': len(self.price_cache),
                'sources': {}
            }

            for source_name, performance in self.source_performance.items():
                source_config = self.data_sources.get(source_name)
                report['sources'][source_name] = {
                    'enabled': source_config.enabled if source_config else False,
                    'priority': source_config.priority if source_config else 0,
                    'weight': source_config.weight if source_config else 0,
                    'success_rate': performance['success_rate'],
                    'avg_response_time': performance['response_time'],
                    'error_count': performance['error_count'],
                    'total_requests': performance['total_requests'],
                    'last_success': performance['last_success'].isoformat() if performance['last_success'] else None
                }

            return report

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error generating performance report: {e}")
            return {'error': str(e)}

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()
                self.session = None

            logger.info("🧹 [AGGREGATOR] Cleanup completed")

        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error during cleanup: {e}")

    def add_custom_source(self, config: DataSourceConfig):
        """Add a custom data source"""
        try:
            self.data_sources[config.name] = config
            logger.info(f"➕ [AGGREGATOR] Added custom source: {config.name}")
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error adding custom source: {e}")

    def disable_source(self, source_name: str):
        """Disable a data source"""
        try:
            if source_name in self.data_sources:
                self.data_sources[source_name].enabled = False
                logger.info(f"🚫 [AGGREGATOR] Disabled source: {source_name}")
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error disabling source: {e}")

    def enable_source(self, source_name: str):
        """Enable a data source"""
        try:
            if source_name in self.data_sources:
                self.data_sources[source_name].enabled = True
                logger.info(f"✅ [AGGREGATOR] Enabled source: {source_name}")
        except Exception as e:
            logger.error(f"❌ [AGGREGATOR] Error enabling source: {e}")
