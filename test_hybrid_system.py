#!/usr/bin/env python3
"""
Hybrid Trading System Validation Test
Tests the complete hybrid trading system with Bybit PRIMARY and Coinbase SECONDARY
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_hybrid_trading_system():
    """Test the complete hybrid trading system"""
    print("🚀 HYBRID TRADING SYSTEM VALIDATION TEST")
    print("=" * 60)
    print(f"🕒 Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Testing: Bybit PRIMARY → Coinbase SECONDARY")
    print()
    
    try:
        # Load environment
        from dotenv import load_dotenv
        load_dotenv()
        
        # Test 1: Initialize Hybrid Trading System
        print("🔍 TEST 1: Hybrid Trading System Initialization")
        print("-" * 50)
        
        from src.trading.hybrid_trading_system import HybridTradingSystem
        
        hybrid_system = HybridTradingSystem()
        success = await hybrid_system.initialize()
        
        if success:
            print("✅ Hybrid trading system initialized successfully")
            
            # Get system status
            status = hybrid_system.get_exchange_status()
            print(f"   Primary Exchange: {status['primary_exchange']}")
            print(f"   Secondary Exchange: {status['secondary_exchange'] or 'NONE'}")
            print(f"   Coinbase Enabled: {status['coinbase_enabled']}")
            print(f"   Monitoring Active: {status['monitoring_active']}")
            
            # Test exchange status
            for exchange, data in status['exchanges'].items():
                status_icon = "✅" if data['active'] else "❌"
                print(f"   {exchange.upper()}: {status_icon} {data['role']} - {'ACTIVE' if data['active'] else 'INACTIVE'}")
                if data['last_test']:
                    print(f"      Last test: {data.get('last_test_human', 'Unknown')}")
        else:
            print("❌ Hybrid trading system initialization failed")
            return False
        
        print()
        
        # Test 2: Primary Exchange (Bybit) Functionality
        print("🔍 TEST 2: Primary Exchange (Bybit) Functionality")
        print("-" * 50)
        
        if status['exchanges']['bybit']['active']:
            # Test balance retrieval
            balance = await hybrid_system.get_available_balance("USDT")
            print(f"💰 Available USDT Balance: {balance:.2f}")
            
            # Test position sizing
            if balance > 0:
                position_size = hybrid_system.calculate_position_size(balance)
                print(f"📊 Position Size (22.5%): {position_size:.2f} USDT")
                
                if position_size >= 10.0:
                    print("✅ Sufficient balance for trading")
                else:
                    print("⚠️ Low balance - trades may be limited")
            else:
                print("❌ No USDT balance available")
        else:
            print("❌ Primary exchange (Bybit) not active")
            return False
        
        print()
        
        # Test 3: Secondary Exchange (Coinbase) Status
        print("🔍 TEST 3: Secondary Exchange (Coinbase) Status")
        print("-" * 50)
        
        if status['exchanges']['coinbase']['active']:
            print("✅ Coinbase secondary exchange is active")
            
            # Test Coinbase functionality
            coinbase_client = hybrid_system.exchanges.get('coinbase')
            if coinbase_client:
                auth_status = coinbase_client.get_authentication_status()
                print(f"   Authentication: {'✅' if auth_status['authenticated'] else '❌'}")
                print(f"   Access Level: {auth_status['access_level']}")
                print(f"   Algorithm: {auth_status['algorithm']}")
                print(f"   Capabilities: {', '.join(auth_status['capabilities'])}")
                
                # Test balance retrieval
                balances = coinbase_client.get_all_balances()
                if balances:
                    print("   Balances:")
                    for currency, balance in balances.items():
                        print(f"      {currency}: {balance}")
        else:
            print("⚠️ Coinbase secondary exchange not active")
            print("   This is expected due to API access restrictions")
            print("   Manual portfolio tracking is available")
            print("   Monitoring service will detect when API access is restored")
        
        print()
        
        # Test 4: Trading Execution Simulation
        print("🔍 TEST 4: Trading Execution Simulation")
        print("-" * 50)
        
        # Simulate a trade execution (without actually placing orders)
        print("🎯 Simulating trade execution flow...")
        
        # This would be a real trade in production
        trade_result = {
            'success': True,
            'exchange': 'bybit',
            'symbol': 'BTCUSDT',
            'side': 'buy',
            'amount': position_size,
            'simulation': True
        }
        
        if trade_result['success']:
            print(f"✅ Trade simulation successful:")
            print(f"   Exchange: {trade_result['exchange'].upper()}")
            print(f"   Symbol: {trade_result['symbol']}")
            print(f"   Side: {trade_result['side'].upper()}")
            print(f"   Amount: {trade_result['amount']:.2f} USDT")
            print("   Note: This was a simulation - no real trade executed")
        else:
            print("❌ Trade simulation failed")
        
        print()
        
        # Test 5: Failover Mechanism
        print("🔍 TEST 5: Failover Mechanism")
        print("-" * 50)
        
        print("🔄 Testing automatic failover logic...")
        
        # Test primary exchange availability
        if status['exchanges']['bybit']['active']:
            print("✅ Primary exchange (Bybit) available - trades will execute here")
        
        # Test secondary exchange fallback
        if status['exchanges']['coinbase']['active']:
            print("✅ Secondary exchange (Coinbase) available - can serve as fallback")
        else:
            print("⚠️ Secondary exchange (Coinbase) not available")
            print("   System will operate in Bybit-only mode")
            print("   This is acceptable for live trading")
        
        print()
        
        # Test 6: Monitoring Service
        print("🔍 TEST 6: Monitoring Service")
        print("-" * 50)
        
        if status['monitoring_active']:
            print("✅ Coinbase API restoration monitoring is active")
            print("   Service checks every 30 minutes for API access restoration")
            print("   Will automatically notify when Coinbase API is restored")
        else:
            print("⚠️ Monitoring service not active")
            if status['exchanges']['coinbase']['active']:
                print("   This is normal when Coinbase is already working")
            else:
                print("   This may indicate a configuration issue")
        
        print()
        
        # Test 7: System Readiness
        print("🔍 TEST 7: System Readiness for Live Trading")
        print("-" * 50)
        
        ready_for_trading = True
        readiness_checks = []
        
        # Check primary exchange
        if status['exchanges']['bybit']['active']:
            readiness_checks.append("✅ Primary exchange (Bybit) active")
        else:
            readiness_checks.append("❌ Primary exchange (Bybit) inactive")
            ready_for_trading = False
        
        # Check balance
        if balance >= 10.0:
            readiness_checks.append("✅ Sufficient balance for trading")
        else:
            readiness_checks.append("⚠️ Low balance - limited trading capability")
        
        # Check position sizing
        if position_size > 0:
            readiness_checks.append("✅ Position sizing calculation working")
        else:
            readiness_checks.append("❌ Position sizing calculation failed")
            ready_for_trading = False
        
        # Check failover capability
        if status['primary_exchange']:
            readiness_checks.append("✅ Failover system configured")
        else:
            readiness_checks.append("❌ Failover system not configured")
            ready_for_trading = False
        
        # Display readiness checks
        for check in readiness_checks:
            print(f"   {check}")
        
        print()
        if ready_for_trading:
            print("🎉 SYSTEM READY FOR LIVE TRADING!")
            print("✅ All critical components operational")
            print("🚀 Can start live trading immediately")
        else:
            print("❌ SYSTEM NOT READY FOR LIVE TRADING")
            print("🔧 Please resolve the issues above before starting")
        
        print()
        
        # Test Summary
        print("📊 TEST SUMMARY")
        print("=" * 30)
        print(f"🕒 Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Primary Exchange: {'✅ ACTIVE' if status['exchanges']['bybit']['active'] else '❌ INACTIVE'}")
        print(f"🎯 Secondary Exchange: {'✅ ACTIVE' if status['exchanges']['coinbase']['active'] else '⚠️ INACTIVE (Expected)'}")
        print(f"🔍 Monitoring: {'✅ ACTIVE' if status['monitoring_active'] else '⚠️ INACTIVE'}")
        print(f"💰 Available Balance: {balance:.2f} USDT")
        print(f"📊 Position Size: {position_size:.2f} USDT")
        print(f"🚀 Ready for Trading: {'YES' if ready_for_trading else 'NO'}")
        
        # Cleanup
        await hybrid_system.shutdown()
        
        return ready_for_trading
        
    except Exception as e:
        print(f"❌ Hybrid system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    success = await test_hybrid_trading_system()
    
    if success:
        print("\n🎉 All tests passed! System ready for live trading.")
        return 0
    else:
        print("\n❌ Some tests failed. Please resolve issues before live trading.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
