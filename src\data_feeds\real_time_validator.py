"""
ENTERPRISE-GRADE REAL-TIME DATA VALIDATION SYSTEM
Eliminates hardcoded fallbacks and ensures data integrity for live trading
"""

import asyncio
import logging
import time
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
import numpy as np
from decimal import Decimal
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class DataSource:
    """Data source configuration"""
    name: str
    url: str
    weight: float
    timeout: float
    rate_limit: int
    last_success: Optional[datetime] = None
    error_count: int = 0
    total_requests: int = 0
    avg_response_time: float = 0.0

@dataclass
class PriceValidation:
    """Price validation result"""
    symbol: str
    price: float
    confidence: float
    sources_used: List[str]
    timestamp: datetime
    validation_passed: bool
    outliers_detected: List[str]
    data_quality_score: float

class RealTimeDataValidator:
    """Enterprise-grade real-time data validation system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.data_sources = self._initialize_data_sources()
        self.price_cache = {}
        self.validation_history = deque(maxlen=10000)
        self.outlier_threshold = config.get('outlier_threshold', 0.05)  # 5%
        self.min_sources_required = config.get('min_sources_required', 2)
        self.max_price_age_seconds = config.get('max_price_age_seconds', 30)
        
        # Performance tracking
        self.source_performance = defaultdict(lambda: {
            'response_times': deque(maxlen=100),
            'success_rate': 1.0,
            'error_count': 0,
            'total_requests': 0
        })
        
        # Session for HTTP requests
        self.session = None
        
        logger.info("🔍 [VALIDATOR] Real-time data validator initialized")
    
    def _initialize_data_sources(self) -> Dict[str, DataSource]:
        """Initialize data sources with enterprise-grade endpoints"""
        sources = {
            'binance': DataSource(
                name='binance',
                url='https://api.binance.com/api/v3/ticker/price',
                weight=0.3,
                timeout=2.0,
                rate_limit=1200  # requests per minute
            ),
            'coinbase': DataSource(
                name='coinbase',
                url='https://api.exchange.coinbase.com/products/{symbol}/ticker',
                weight=0.25,
                timeout=2.0,
                rate_limit=600
            ),
            'kraken': DataSource(
                name='kraken',
                url='https://api.kraken.com/0/public/Ticker',
                weight=0.2,
                timeout=3.0,
                rate_limit=300
            ),
            'bybit': DataSource(
                name='bybit',
                url='https://api.bybit.com/v5/market/tickers',
                weight=0.25,
                timeout=2.0,
                rate_limit=600
            )
        }
        
        return sources
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=5.0),
            connector=aiohttp.TCPConnector(limit=100, limit_per_host=20)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def validate_price_data(self, symbol: str, required_confidence: float = 0.8) -> PriceValidation:
        """Validate price data from multiple sources with confidence scoring"""
        try:
            start_time = time.time()
            
            # Fetch prices from all available sources
            price_data = await self._fetch_from_all_sources(symbol)
            
            if not price_data:
                logger.error(f"❌ [VALIDATOR] No price data available for {symbol}")
                return PriceValidation(
                    symbol=symbol,
                    price=0.0,
                    confidence=0.0,
                    sources_used=[],
                    timestamp=datetime.now(),
                    validation_passed=False,
                    outliers_detected=[],
                    data_quality_score=0.0
                )
            
            # Validate and clean data
            validated_prices = self._validate_price_consistency(price_data)
            
            if len(validated_prices) < self.min_sources_required:
                logger.warning(f"⚠️ [VALIDATOR] Insufficient valid sources for {symbol}: {len(validated_prices)}")
                return PriceValidation(
                    symbol=symbol,
                    price=0.0,
                    confidence=0.0,
                    sources_used=list(validated_prices.keys()),
                    timestamp=datetime.now(),
                    validation_passed=False,
                    outliers_detected=[],
                    data_quality_score=0.0
                )
            
            # Calculate weighted average price
            final_price, confidence = self._calculate_weighted_price(validated_prices)
            
            # Calculate data quality score
            data_quality = self._calculate_data_quality_score(validated_prices, price_data)
            
            # Check if validation passes
            validation_passed = (
                confidence >= required_confidence and
                len(validated_prices) >= self.min_sources_required and
                data_quality >= 0.7
            )
            
            # Detect outliers
            outliers = self._detect_outliers(price_data, final_price)
            
            validation_result = PriceValidation(
                symbol=symbol,
                price=final_price,
                confidence=confidence,
                sources_used=list(validated_prices.keys()),
                timestamp=datetime.now(),
                validation_passed=validation_passed,
                outliers_detected=outliers,
                data_quality_score=data_quality
            )
            
            # Cache result
            self.price_cache[symbol] = validation_result
            self.validation_history.append(validation_result)
            
            # Log performance
            processing_time = time.time() - start_time
            logger.info(f"✅ [VALIDATOR] {symbol}: ${final_price:.2f} (confidence: {confidence:.3f}, "
                       f"sources: {len(validated_prices)}, time: {processing_time:.3f}s)")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"❌ [VALIDATOR] Error validating price for {symbol}: {e}")
            return PriceValidation(
                symbol=symbol,
                price=0.0,
                confidence=0.0,
                sources_used=[],
                timestamp=datetime.now(),
                validation_passed=False,
                outliers_detected=[],
                data_quality_score=0.0
            )
    
    async def _fetch_from_all_sources(self, symbol: str) -> Dict[str, float]:
        """Fetch price data from all available sources concurrently"""
        tasks = []
        
        for source_name, source in self.data_sources.items():
            task = asyncio.create_task(
                self._fetch_from_source(source, symbol),
                name=f"fetch_{source_name}_{symbol}"
            )
            tasks.append((source_name, task))
        
        # Wait for all tasks with timeout
        results = {}
        
        for source_name, task in tasks:
            try:
                price = await asyncio.wait_for(task, timeout=5.0)
                if price and price > 0:
                    results[source_name] = price
                    self._update_source_performance(source_name, True, 0.0)
            except asyncio.TimeoutError:
                logger.warning(f"⏰ [VALIDATOR] Timeout fetching from {source_name}")
                self._update_source_performance(source_name, False, 5.0)
            except Exception as e:
                logger.debug(f"🔍 [VALIDATOR] Error fetching from {source_name}: {e}")
                self._update_source_performance(source_name, False, 0.0)
        
        return results
    
    async def _fetch_from_source(self, source: DataSource, symbol: str) -> Optional[float]:
        """Fetch price from a specific data source"""
        try:
            start_time = time.time()
            
            # Rate limiting check
            if not self._check_rate_limit(source.name):
                return None
            
            # Prepare URL and parameters
            url, params = self._prepare_request(source, symbol)
            
            if not url:
                return None
            
            # Make HTTP request
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    price = self._extract_price_from_response(source.name, data, symbol)
                    
                    response_time = time.time() - start_time
                    self._update_source_performance(source.name, True, response_time)
                    
                    return price
                else:
                    logger.warning(f"⚠️ [VALIDATOR] HTTP {response.status} from {source.name}")
                    
        except Exception as e:
            logger.debug(f"🔍 [VALIDATOR] Error fetching from {source.name}: {e}")
            
        return None
    
    def _prepare_request(self, source: DataSource, symbol: str) -> Tuple[str, Dict[str, Any]]:
        """Prepare HTTP request URL and parameters for each source"""
        try:
            if source.name == 'binance':
                # Convert symbol format (e.g., BTCUSDT)
                binance_symbol = symbol.replace('-', '').upper()
                return source.url, {'symbol': binance_symbol}
                
            elif source.name == 'coinbase':
                # Convert symbol format (e.g., BTC-USD)
                if 'USDT' in symbol:
                    coinbase_symbol = symbol.replace('USDT', '-USD')
                else:
                    coinbase_symbol = symbol
                url = source.url.format(symbol=coinbase_symbol)
                return url, {}
                
            elif source.name == 'kraken':
                # Kraken uses different symbol format
                kraken_symbol = symbol.replace('USDT', 'USD')
                return source.url, {'pair': kraken_symbol}
                
            elif source.name == 'bybit':
                # Bybit format
                return source.url, {'category': 'spot', 'symbol': symbol}
                
        except Exception as e:
            logger.debug(f"🔍 [VALIDATOR] Error preparing request for {source.name}: {e}")
            
        return "", {}
    
    def _extract_price_from_response(self, source_name: str, data: Dict[str, Any], symbol: str) -> Optional[float]:
        """Extract price from API response based on source format"""
        try:
            if source_name == 'binance':
                return float(data.get('price', 0))
                
            elif source_name == 'coinbase':
                return float(data.get('price', 0))
                
            elif source_name == 'kraken':
                # Kraken has nested structure
                result = data.get('result', {})
                for pair_data in result.values():
                    if isinstance(pair_data, dict) and 'c' in pair_data:
                        return float(pair_data['c'][0])  # Current price
                        
            elif source_name == 'bybit':
                result = data.get('result', {})
                ticker_list = result.get('list', [])
                for ticker in ticker_list:
                    if ticker.get('symbol') == symbol:
                        return float(ticker.get('lastPrice', 0))
                        
        except (ValueError, KeyError, TypeError) as e:
            logger.debug(f"🔍 [VALIDATOR] Error extracting price from {source_name}: {e}")
            
        return None
