"""
Adaptive Learning Framework for Continuous Model Improvement
Implements self-learning, hyperparameter optimization, and strategy evolution
"""

import asyncio
import logging
import numpy as np
import torch
import torch.nn as nn
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import deque, defaultdict
import json
import pickle
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class LearningMetrics:
    """Metrics for tracking learning performance"""
    accuracy: float = 0.0
    loss: float = float('inf')
    profit_factor: float = 0.0
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    convergence_rate: float = 0.0
    adaptation_speed: float = 0.0
    stability_score: float = 0.0
    
@dataclass
class HyperparameterConfig:
    """Hyperparameter configuration"""
    learning_rate: float = 0.001
    batch_size: int = 32
    dropout: float = 0.1
    weight_decay: float = 1e-4
    momentum: float = 0.9
    beta1: float = 0.9
    beta2: float = 0.999
    epsilon: float = 1e-8
    
@dataclass
class AdaptationState:
    """State of the adaptation process"""
    current_strategy: str = "default"
    performance_history: List[float] = field(default_factory=list)
    hyperparameter_history: List[HyperparameterConfig] = field(default_factory=list)
    adaptation_count: int = 0
    last_adaptation: Optional[datetime] = None
    stability_period: int = 0
    exploration_phase: bool = True

class BayesianOptimizer:
    """Bayesian optimization for hyperparameter tuning"""
    
    def __init__(self, parameter_bounds: Dict[str, Tuple[float, float]]):
        self.parameter_bounds = parameter_bounds
        self.observations = []
        self.parameter_history = []
        
    def suggest_parameters(self) -> Dict[str, float]:
        """Suggest next set of parameters to try"""
        if len(self.observations) < 3:
            # Random exploration for first few iterations
            return self._random_sample()
        
        # Use Gaussian Process for optimization
        return self._gaussian_process_optimization()
    
    def _random_sample(self) -> Dict[str, float]:
        """Random sampling from parameter bounds"""
        params = {}
        for param, (low, high) in self.parameter_bounds.items():
            if param in ['batch_size']:
                # Integer parameters
                params[param] = int(np.random.uniform(low, high))
            else:
                # Float parameters
                params[param] = np.random.uniform(low, high)
        return params
    
    def _gaussian_process_optimization(self) -> Dict[str, float]:
        """Simplified Gaussian Process optimization"""
        # For simplicity, use a basic acquisition function
        best_idx = np.argmax(self.observations)
        best_params = self.parameter_history[best_idx]
        
        # Add noise for exploration
        params = {}
        for param, value in best_params.items():
            low, high = self.parameter_bounds[param]
            noise_scale = (high - low) * 0.1
            
            if param in ['batch_size']:
                new_value = int(np.clip(value + np.random.normal(0, noise_scale), low, high))
            else:
                new_value = np.clip(value + np.random.normal(0, noise_scale), low, high)
            
            params[param] = new_value
        
        return params
    
    def update(self, parameters: Dict[str, float], performance: float):
        """Update optimizer with new observation"""
        self.parameter_history.append(parameters)
        self.observations.append(performance)

class OnlineLearner:
    """Online learning component for real-time adaptation"""
    
    def __init__(self, model: nn.Module, learning_rate: float = 0.001):
        self.model = model
        self.optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        self.criterion = nn.MSELoss()
        
        # Online learning state
        self.learning_buffer = deque(maxlen=1000)
        self.performance_window = deque(maxlen=100)
        self.adaptation_threshold = 0.05
        
    async def update_model(self, x: torch.Tensor, y: torch.Tensor) -> float:
        """Update model with new data point"""
        # Add to learning buffer
        self.learning_buffer.append((x, y))
        
        # Perform online update
        self.optimizer.zero_grad()
        prediction = self.model(x)
        loss = self.criterion(prediction, y)
        loss.backward()
        self.optimizer.step()
        
        # Track performance
        self.performance_window.append(loss.item())
        
        return loss.item()
    
    def should_adapt(self) -> bool:
        """Check if model should adapt based on performance degradation"""
        if len(self.performance_window) < 20:
            return False
        
        recent_performance = np.mean(list(self.performance_window)[-10:])
        historical_performance = np.mean(list(self.performance_window)[:-10])
        
        performance_degradation = (recent_performance - historical_performance) / historical_performance
        
        return performance_degradation > self.adaptation_threshold
    
    async def batch_update(self, batch_size: int = 32) -> float:
        """Perform batch update from learning buffer"""
        if len(self.learning_buffer) < batch_size:
            return 0.0
        
        # Sample batch from buffer
        batch_indices = np.random.choice(len(self.learning_buffer), batch_size, replace=False)
        batch_data = [self.learning_buffer[i] for i in batch_indices]
        
        # Prepare batch tensors
        x_batch = torch.stack([item[0] for item in batch_data])
        y_batch = torch.stack([item[1] for item in batch_data])
        
        # Update model
        self.optimizer.zero_grad()
        predictions = self.model(x_batch)
        loss = self.criterion(predictions, y_batch)
        loss.backward()
        self.optimizer.step()
        
        return loss.item()

class StrategyEvolution:
    """Evolution of trading strategies based on performance"""
    
    def __init__(self):
        self.strategies = {}
        self.strategy_performance = defaultdict(list)
        self.active_strategies = set()
        self.strategy_weights = {}
        
    def register_strategy(self, name: str, strategy_func: Callable, weight: float = 1.0):
        """Register a new trading strategy"""
        self.strategies[name] = strategy_func
        self.strategy_weights[name] = weight
        self.active_strategies.add(name)
        
        logger.info(f"Registered strategy: {name}")
    
    def evaluate_strategy(self, name: str, performance_metrics: LearningMetrics):
        """Evaluate strategy performance"""
        if name not in self.strategies:
            return
        
        # Calculate composite performance score
        performance_score = (
            performance_metrics.profit_factor * 0.3 +
            performance_metrics.sharpe_ratio * 0.2 +
            performance_metrics.win_rate * 0.2 +
            performance_metrics.accuracy * 0.15 +
            performance_metrics.stability_score * 0.15
        )
        
        self.strategy_performance[name].append(performance_score)
        
        # Update strategy weight based on recent performance
        if len(self.strategy_performance[name]) >= 5:
            recent_performance = np.mean(self.strategy_performance[name][-5:])
            self.strategy_weights[name] = max(0.1, recent_performance)
    
    def select_best_strategies(self, top_k: int = 3) -> List[str]:
        """Select top performing strategies"""
        strategy_scores = {}
        
        for name in self.active_strategies:
            if name in self.strategy_performance and self.strategy_performance[name]:
                recent_scores = self.strategy_performance[name][-10:]  # Last 10 evaluations
                strategy_scores[name] = np.mean(recent_scores)
        
        # Sort by performance and select top k
        sorted_strategies = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)
        return [name for name, _ in sorted_strategies[:top_k]]
    
    def evolve_strategies(self) -> Dict[str, float]:
        """Evolve strategy weights based on performance"""
        total_weight = sum(self.strategy_weights.values())
        
        if total_weight > 0:
            # Normalize weights
            for name in self.strategy_weights:
                self.strategy_weights[name] /= total_weight
        
        return self.strategy_weights.copy()

class AdaptiveLearningFramework:
    """
    Comprehensive adaptive learning framework
    Features:
    - Continuous learning from live trading results
    - Automated hyperparameter optimization
    - Dynamic model selection and ensemble
    - Strategy evolution and adaptation
    - Online learning capabilities
    """
    
    def __init__(self, models: Dict[str, nn.Module], config_path: str = "adaptive_config.json"):
        self.models = models
        self.config_path = config_path
        
        # Learning components
        self.bayesian_optimizer = None
        self.online_learners = {}
        self.strategy_evolution = StrategyEvolution()
        
        # Adaptation state
        self.adaptation_state = AdaptationState()
        self.learning_metrics_history = deque(maxlen=1000)
        
        # Configuration
        self.adaptation_frequency = timedelta(hours=1)
        self.min_samples_for_adaptation = 50
        self.performance_threshold = 0.1
        
        # Initialize components
        self._initialize_components()
        
        logger.info("Adaptive Learning Framework initialized")
    
    def _initialize_components(self):
        """Initialize learning components"""
        # Initialize Bayesian optimizer
        parameter_bounds = {
            'learning_rate': (1e-5, 1e-1),
            'batch_size': (8, 128),
            'dropout': (0.0, 0.5),
            'weight_decay': (1e-6, 1e-2)
        }
        self.bayesian_optimizer = BayesianOptimizer(parameter_bounds)
        
        # Initialize online learners for each model
        for name, model in self.models.items():
            self.online_learners[name] = OnlineLearner(model)
    
    async def continuous_learning_loop(self):
        """Main continuous learning loop"""
        while True:
            try:
                # Check if adaptation is needed
                if self._should_adapt():
                    await self._perform_adaptation()
                
                # Perform online learning updates
                await self._online_learning_update()
                
                # Evolve strategies
                self._evolve_strategies()
                
                # Wait before next iteration
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Error in continuous learning loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    def _should_adapt(self) -> bool:
        """Check if adaptation should be triggered"""
        # Time-based adaptation
        if self.adaptation_state.last_adaptation is None:
            return True
        
        time_since_adaptation = datetime.now() - self.adaptation_state.last_adaptation
        if time_since_adaptation >= self.adaptation_frequency:
            return True
        
        # Performance-based adaptation
        if len(self.learning_metrics_history) >= self.min_samples_for_adaptation:
            recent_performance = [m.profit_factor for m in list(self.learning_metrics_history)[-10:]]
            historical_performance = [m.profit_factor for m in list(self.learning_metrics_history)[:-10]]
            
            if recent_performance and historical_performance:
                recent_avg = np.mean(recent_performance)
                historical_avg = np.mean(historical_performance)
                
                performance_degradation = (historical_avg - recent_avg) / (historical_avg + 1e-8)
                
                if performance_degradation > self.performance_threshold:
                    return True
        
        return False
    
    async def _perform_adaptation(self):
        """Perform model adaptation"""
        logger.info("🔄 [ADAPTIVE] Starting adaptation process...")
        
        try:
            # Get current performance metrics
            current_metrics = self._calculate_current_metrics()
            
            # Optimize hyperparameters
            new_hyperparams = await self._optimize_hyperparameters(current_metrics)
            
            # Select best models
            best_models = await self._select_best_models()
            
            # Update adaptation state
            self.adaptation_state.adaptation_count += 1
            self.adaptation_state.last_adaptation = datetime.now()
            self.adaptation_state.hyperparameter_history.append(new_hyperparams)
            
            logger.info(f"✅ [ADAPTIVE] Adaptation complete - Round {self.adaptation_state.adaptation_count}")
            
        except Exception as e:
            logger.error(f"❌ [ADAPTIVE] Adaptation failed: {e}")
    
    async def _optimize_hyperparameters(self, current_metrics: LearningMetrics) -> HyperparameterConfig:
        """Optimize hyperparameters using Bayesian optimization"""
        # Suggest new parameters
        suggested_params = self.bayesian_optimizer.suggest_parameters()
        
        # Update Bayesian optimizer with current performance
        if self.adaptation_state.hyperparameter_history:
            last_params = self.adaptation_state.hyperparameter_history[-1]
            last_params_dict = {
                'learning_rate': last_params.learning_rate,
                'batch_size': last_params.batch_size,
                'dropout': last_params.dropout,
                'weight_decay': last_params.weight_decay
            }
            self.bayesian_optimizer.update(last_params_dict, current_metrics.profit_factor)
        
        # Create new hyperparameter configuration
        new_config = HyperparameterConfig(
            learning_rate=suggested_params.get('learning_rate', 0.001),
            batch_size=int(suggested_params.get('batch_size', 32)),
            dropout=suggested_params.get('dropout', 0.1),
            weight_decay=suggested_params.get('weight_decay', 1e-4)
        )
        
        # Apply new hyperparameters to online learners
        for learner in self.online_learners.values():
            for param_group in learner.optimizer.param_groups:
                param_group['lr'] = new_config.learning_rate
                param_group['weight_decay'] = new_config.weight_decay
        
        logger.info(f"📊 [ADAPTIVE] New hyperparameters: LR={new_config.learning_rate:.6f}, "
                   f"Batch={new_config.batch_size}, Dropout={new_config.dropout:.3f}")
        
        return new_config
    
    async def _select_best_models(self) -> List[str]:
        """Select best performing models"""
        model_performance = {}
        
        for name, learner in self.online_learners.items():
            if learner.performance_window:
                avg_performance = np.mean(list(learner.performance_window))
                model_performance[name] = 1.0 / (1.0 + avg_performance)  # Lower loss = higher score
        
        # Sort by performance
        sorted_models = sorted(model_performance.items(), key=lambda x: x[1], reverse=True)
        best_models = [name for name, _ in sorted_models[:3]]  # Top 3 models
        
        logger.info(f"🏆 [ADAPTIVE] Best models: {best_models}")
        return best_models
    
    async def _online_learning_update(self):
        """Perform online learning updates"""
        for name, learner in self.online_learners.items():
            if learner.should_adapt():
                loss = await learner.batch_update()
                logger.debug(f"📚 [ONLINE] {name} batch update - Loss: {loss:.6f}")
    
    def _evolve_strategies(self):
        """Evolve trading strategies"""
        if len(self.learning_metrics_history) >= 10:
            recent_metrics = list(self.learning_metrics_history)[-10:]
            
            for i, metrics in enumerate(recent_metrics):
                strategy_name = f"strategy_{i % 3}"  # Assume 3 strategies
                self.strategy_evolution.evaluate_strategy(strategy_name, metrics)
            
            # Get evolved weights
            evolved_weights = self.strategy_evolution.evolve_strategies()
            logger.debug(f"🧬 [EVOLUTION] Strategy weights: {evolved_weights}")
    
    def _calculate_current_metrics(self) -> LearningMetrics:
        """Calculate current performance metrics"""
        if not self.learning_metrics_history:
            return LearningMetrics()
        
        recent_metrics = list(self.learning_metrics_history)[-10:]
        
        return LearningMetrics(
            accuracy=np.mean([m.accuracy for m in recent_metrics]),
            loss=np.mean([m.loss for m in recent_metrics]),
            profit_factor=np.mean([m.profit_factor for m in recent_metrics]),
            sharpe_ratio=np.mean([m.sharpe_ratio for m in recent_metrics]),
            win_rate=np.mean([m.win_rate for m in recent_metrics]),
            convergence_rate=np.mean([m.convergence_rate for m in recent_metrics]),
            stability_score=np.std([m.profit_factor for m in recent_metrics])
        )
    
    def update_metrics(self, metrics: LearningMetrics):
        """Update learning metrics"""
        self.learning_metrics_history.append(metrics)
    
    def get_adaptation_status(self) -> Dict[str, Any]:
        """Get current adaptation status"""
        return {
            'adaptation_count': self.adaptation_state.adaptation_count,
            'last_adaptation': self.adaptation_state.last_adaptation.isoformat() if self.adaptation_state.last_adaptation else None,
            'current_strategy': self.adaptation_state.current_strategy,
            'exploration_phase': self.adaptation_state.exploration_phase,
            'stability_period': self.adaptation_state.stability_period,
            'metrics_history_length': len(self.learning_metrics_history),
            'active_models': list(self.online_learners.keys()),
            'strategy_weights': self.strategy_evolution.strategy_weights
        }
