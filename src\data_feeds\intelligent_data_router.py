#!/usr/bin/env python3
"""
Intelligent Data Source Routing
Routes data requests to optimal sources based on availability and quality
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque

from .real_time_data_aggregator import RealTimeDataAggregator, AggregatedMarketData, DataSourceStatus

logger = logging.getLogger(__name__)

class RoutingStrategy(Enum):
    """Data routing strategy options"""
    FASTEST = "fastest"
    MOST_RELIABLE = "most_reliable"
    HIGHEST_QUALITY = "highest_quality"
    BALANCED = "balanced"
    FALLBACK_CASCADE = "fallback_cascade"

@dataclass
class SourceMetrics:
    """Performance metrics for a data source"""
    name: str
    reliability_score: float
    avg_response_time: float
    success_rate: float
    data_quality: float
    last_success: Optional[datetime] = None
    consecutive_failures: int = 0
    total_requests: int = 0

class IntelligentDataRouter:
    """
    Route data requests to optimal sources based on availability and quality
    
    Features:
    - Multiple routing strategies
    - Real-time performance monitoring
    - Automatic failover and recovery
    - Quality-based source selection
    - Adaptive routing based on historical performance
    """
    
    def __init__(self, aggregator: RealTimeDataAggregator):
        self.aggregator = aggregator
        self.routing_strategy = RoutingStrategy.BALANCED
        
        # Source priority configurations
        self.source_priorities = {
            'bybit_api': {'base_priority': 1.0, 'reliability_weight': 0.4, 'speed_weight': 0.3, 'quality_weight': 0.3},
            'coinbase_api': {'base_priority': 0.9, 'reliability_weight': 0.4, 'speed_weight': 0.3, 'quality_weight': 0.3},
            'binance_api': {'base_priority': 0.8, 'reliability_weight': 0.4, 'speed_weight': 0.3, 'quality_weight': 0.3},
            'dexscreener_api': {'base_priority': 0.6, 'reliability_weight': 0.3, 'speed_weight': 0.2, 'quality_weight': 0.5},
            'coingecko_api': {'base_priority': 0.5, 'reliability_weight': 0.3, 'speed_weight': 0.2, 'quality_weight': 0.5}
        }
        
        # Performance tracking
        self.source_metrics: Dict[str, SourceMetrics] = {}
        self.routing_history = deque(maxlen=1000)
        
        # Adaptive thresholds
        self.min_reliability_threshold = 0.7
        self.max_response_time_threshold = 5000  # milliseconds
        self.quality_threshold = 0.6
        
        logger.info("🧭 [DATA-ROUTER] Intelligent data router initialized")
    
    async def get_best_price_data(self, symbol: str, strategy: Optional[RoutingStrategy] = None) -> Optional[AggregatedMarketData]:
        """Get price data from most optimal source based on strategy"""
        try:
            routing_strategy = strategy or self.routing_strategy
            start_time = time.time()
            
            # Update source metrics
            await self._update_source_metrics()
            
            # Get optimal source order based on strategy
            source_order = self._get_optimal_source_order(routing_strategy)
            
            logger.info(f"🧭 [ROUTING] Using {routing_strategy.value} strategy for {symbol}")
            logger.debug(f"🔄 [SOURCE-ORDER] {[s[0] for s in source_order[:3]]}")
            
            # Try sources in order
            for source_name, score in source_order:
                try:
                    # Check if source meets minimum requirements
                    if not self._meets_minimum_requirements(source_name):
                        logger.debug(f"⚠️ [SKIP-SOURCE] {source_name} doesn't meet minimum requirements")
                        continue
                    
                    # Attempt to get data from this source
                    data = await self._get_data_from_source(source_name, symbol)
                    
                    if data and self._validate_data_quality(data, source_name):
                        elapsed_ms = (time.time() - start_time) * 1000
                        
                        # Record successful routing
                        self._record_routing_success(source_name, symbol, elapsed_ms, score)
                        
                        logger.info(f"✅ [ROUTING-SUCCESS] {symbol} from {source_name} "
                                  f"(score: {score:.2f}, time: {elapsed_ms:.0f}ms)")
                        
                        return data
                    else:
                        logger.debug(f"⚠️ [DATA-QUALITY] {source_name} data failed validation for {symbol}")
                        
                except Exception as e:
                    logger.debug(f"🔍 [SOURCE-ERROR] {source_name} failed for {symbol}: {e}")
                    self._record_routing_failure(source_name, symbol, str(e))
            
            # If all sources failed, try fallback to aggregated data
            logger.warning(f"⚠️ [FALLBACK] All primary sources failed for {symbol}, trying aggregated data")
            return await self.aggregator.get_comprehensive_market_data(symbol)
            
        except Exception as e:
            logger.error(f"❌ [ROUTING-ERROR] Failed to get data for {symbol}: {e}")
            return None
    
    def _get_optimal_source_order(self, strategy: RoutingStrategy) -> List[Tuple[str, float]]:
        """Get optimal source order based on routing strategy"""
        try:
            source_scores = []
            
            for source_name, config in self.source_priorities.items():
                metrics = self.source_metrics.get(source_name)
                
                if strategy == RoutingStrategy.FASTEST:
                    score = self._calculate_speed_score(source_name, metrics, config)
                elif strategy == RoutingStrategy.MOST_RELIABLE:
                    score = self._calculate_reliability_score(source_name, metrics, config)
                elif strategy == RoutingStrategy.HIGHEST_QUALITY:
                    score = self._calculate_quality_score(source_name, metrics, config)
                elif strategy == RoutingStrategy.BALANCED:
                    score = self._calculate_balanced_score(source_name, metrics, config)
                elif strategy == RoutingStrategy.FALLBACK_CASCADE:
                    score = config['base_priority']
                else:
                    score = config['base_priority']
                
                source_scores.append((source_name, score))
            
            # Sort by score (highest first)
            return sorted(source_scores, key=lambda x: x[1], reverse=True)
            
        except Exception as e:
            logger.error(f"❌ [SCORING-ERROR] Error calculating source order: {e}")
            return [(name, config['base_priority']) for name, config in self.source_priorities.items()]
    
    def _calculate_speed_score(self, source_name: str, metrics: Optional[SourceMetrics], config: Dict[str, Any]) -> float:
        """Calculate speed-based score"""
        try:
            base_score = config['base_priority']
            
            if not metrics or metrics.avg_response_time == 0:
                return base_score * 0.5  # Penalty for unknown performance
            
            # Normalize response time (lower is better)
            normalized_speed = max(0.1, 1.0 - (metrics.avg_response_time / self.max_response_time_threshold))
            
            return base_score * normalized_speed
            
        except Exception as e:
            logger.error(f"❌ [SPEED-SCORE] Error calculating speed score: {e}")
            return config['base_priority'] * 0.5
    
    def _calculate_reliability_score(self, source_name: str, metrics: Optional[SourceMetrics], config: Dict[str, Any]) -> float:
        """Calculate reliability-based score"""
        try:
            base_score = config['base_priority']
            
            if not metrics:
                return base_score * 0.5
            
            # Factor in success rate and consecutive failures
            reliability_factor = (metrics.success_rate / 100.0) * max(0.1, 1.0 - (metrics.consecutive_failures * 0.2))
            
            return base_score * reliability_factor
            
        except Exception as e:
            logger.error(f"❌ [RELIABILITY-SCORE] Error calculating reliability score: {e}")
            return config['base_priority'] * 0.5
    
    def _calculate_quality_score(self, source_name: str, metrics: Optional[SourceMetrics], config: Dict[str, Any]) -> float:
        """Calculate quality-based score"""
        try:
            base_score = config['base_priority']
            
            if not metrics:
                return base_score * 0.5
            
            return base_score * metrics.data_quality
            
        except Exception as e:
            logger.error(f"❌ [QUALITY-SCORE] Error calculating quality score: {e}")
            return config['base_priority'] * 0.5
    
    def _calculate_balanced_score(self, source_name: str, metrics: Optional[SourceMetrics], config: Dict[str, Any]) -> float:
        """Calculate balanced score considering all factors"""
        try:
            base_score = config['base_priority']
            
            if not metrics:
                return base_score * 0.5
            
            # Weighted combination of all factors
            reliability_weight = config['reliability_weight']
            speed_weight = config['speed_weight']
            quality_weight = config['quality_weight']
            
            # Normalize metrics
            reliability_factor = metrics.success_rate / 100.0
            speed_factor = max(0.1, 1.0 - (metrics.avg_response_time / self.max_response_time_threshold))
            quality_factor = metrics.data_quality
            
            # Calculate weighted score
            weighted_score = (
                reliability_factor * reliability_weight +
                speed_factor * speed_weight +
                quality_factor * quality_weight
            )
            
            return base_score * weighted_score
            
        except Exception as e:
            logger.error(f"❌ [BALANCED-SCORE] Error calculating balanced score: {e}")
            return config['base_priority'] * 0.5
    
    def _meets_minimum_requirements(self, source_name: str) -> bool:
        """Check if source meets minimum performance requirements"""
        try:
            metrics = self.source_metrics.get(source_name)
            
            if not metrics:
                return True  # Allow unknown sources to be tried
            
            # Check minimum thresholds
            if metrics.reliability_score < self.min_reliability_threshold:
                return False
            
            if metrics.avg_response_time > self.max_response_time_threshold:
                return False
            
            if metrics.data_quality < self.quality_threshold:
                return False
            
            # Check if source has too many consecutive failures
            if metrics.consecutive_failures >= 5:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [REQUIREMENTS-CHECK] Error checking requirements: {e}")
            return True
    
    async def _get_data_from_source(self, source_name: str, symbol: str) -> Optional[AggregatedMarketData]:
        """Get data from a specific source"""
        try:
            # This would integrate with individual source clients
            # For now, use the aggregator but filter to specific source
            return await self.aggregator.get_comprehensive_market_data(symbol)
            
        except Exception as e:
            logger.error(f"❌ [SOURCE-DATA] Error getting data from {source_name}: {e}")
            return None
    
    def _validate_data_quality(self, data: AggregatedMarketData, source_name: str) -> bool:
        """Validate data quality from source"""
        try:
            # Check basic data validity
            if not data or data.consensus_price <= 0:
                return False
            
            # Check data freshness (within last 5 minutes)
            age = (datetime.now() - data.timestamp).total_seconds()
            if age > 300:
                return False
            
            # Check confidence score
            if data.confidence_score < self.quality_threshold:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [DATA-VALIDATION] Error validating data: {e}")
            return False
    
    async def _update_source_metrics(self):
        """Update source performance metrics"""
        try:
            # Get performance data from aggregator
            performance_report = self.aggregator.get_performance_report()
            
            for source_name, source_data in performance_report.get('sources', {}).items():
                if source_name not in self.source_metrics:
                    self.source_metrics[source_name] = SourceMetrics(
                        name=source_name,
                        reliability_score=0.5,
                        avg_response_time=1000.0,
                        success_rate=100.0,
                        data_quality=0.8
                    )
                
                metrics = self.source_metrics[source_name]
                
                # Update metrics from performance report
                metrics.success_rate = source_data.get('success_rate', 100.0)
                metrics.avg_response_time = source_data.get('avg_response_time', 1000.0)
                metrics.total_requests = source_data.get('total_requests', 0)
                
                # Calculate reliability score
                metrics.reliability_score = min(1.0, metrics.success_rate / 100.0)
                
                # Update last success
                last_success_str = source_data.get('last_success')
                if last_success_str:
                    metrics.last_success = datetime.fromisoformat(last_success_str)
                
        except Exception as e:
            logger.error(f"❌ [METRICS-UPDATE] Error updating metrics: {e}")
    
    def _record_routing_success(self, source_name: str, symbol: str, elapsed_ms: float, score: float):
        """Record successful routing"""
        try:
            self.routing_history.append({
                'timestamp': datetime.now(),
                'source': source_name,
                'symbol': symbol,
                'success': True,
                'elapsed_ms': elapsed_ms,
                'score': score
            })
            
            # Update source metrics
            if source_name in self.source_metrics:
                self.source_metrics[source_name].consecutive_failures = 0
                
        except Exception as e:
            logger.error(f"❌ [RECORD-SUCCESS] Error recording success: {e}")
    
    def _record_routing_failure(self, source_name: str, symbol: str, error: str):
        """Record routing failure"""
        try:
            self.routing_history.append({
                'timestamp': datetime.now(),
                'source': source_name,
                'symbol': symbol,
                'success': False,
                'error': error
            })
            
            # Update source metrics
            if source_name in self.source_metrics:
                self.source_metrics[source_name].consecutive_failures += 1
                
        except Exception as e:
            logger.error(f"❌ [RECORD-FAILURE] Error recording failure: {e}")
    
    def get_routing_report(self) -> Dict[str, Any]:
        """Get comprehensive routing performance report"""
        try:
            # Calculate success rates by source
            source_stats = defaultdict(lambda: {'total': 0, 'success': 0, 'avg_time': 0.0})
            
            for entry in self.routing_history:
                source = entry['source']
                source_stats[source]['total'] += 1
                
                if entry['success']:
                    source_stats[source]['success'] += 1
                    if 'elapsed_ms' in entry:
                        current_avg = source_stats[source]['avg_time']
                        new_time = entry['elapsed_ms']
                        source_stats[source]['avg_time'] = (current_avg + new_time) / 2
            
            # Generate report
            report = {
                'timestamp': datetime.now().isoformat(),
                'routing_strategy': self.routing_strategy.value,
                'total_routes': len(self.routing_history),
                'source_performance': {},
                'metrics_summary': {}
            }
            
            for source, stats in source_stats.items():
                success_rate = (stats['success'] / stats['total'] * 100) if stats['total'] > 0 else 0
                report['source_performance'][source] = {
                    'total_requests': stats['total'],
                    'success_rate': success_rate,
                    'avg_response_time': stats['avg_time']
                }
            
            # Add current metrics
            for source_name, metrics in self.source_metrics.items():
                report['metrics_summary'][source_name] = {
                    'reliability_score': metrics.reliability_score,
                    'data_quality': metrics.data_quality,
                    'consecutive_failures': metrics.consecutive_failures
                }
            
            return report
            
        except Exception as e:
            logger.error(f"❌ [ROUTING-REPORT] Error generating report: {e}")
            return {'error': str(e)}
    
    def set_routing_strategy(self, strategy: RoutingStrategy):
        """Set the routing strategy"""
        self.routing_strategy = strategy
        logger.info(f"🧭 [STRATEGY-CHANGE] Routing strategy set to: {strategy.value}")

# Global instance
intelligent_router = None

def get_intelligent_router(aggregator: RealTimeDataAggregator) -> IntelligentDataRouter:
    """Get or create intelligent router instance"""
    global intelligent_router
    if intelligent_router is None:
        intelligent_router = IntelligentDataRouter(aggregator)
    return intelligent_router
