#!/usr/bin/env python3
"""
Execute One Bybit Trade - IMMEDIATE TEST
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def execute_single_trade():
    """Execute a single trade to verify system is working"""
    print("🚨 [TRADE] Executing single Bybit trade...")
    
    try:
        from pybit.unified_trading import HTTP
        from dotenv import load_dotenv
        
        load_dotenv()
        
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        session = HTTP(api_key=api_key, api_secret=api_secret, testnet=False, recv_window=10000)
        
        # Get balance
        print("💰 [BALANCE] Checking balance...")
        balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
        if balance_response.get('retCode') != 0:
            print(f"❌ [ERROR] Balance check failed: {balance_response}")
            return False
        
        balance = float(balance_response['result']['list'][0]['coin'][0]['walletBalance'])
        print(f"💰 [BALANCE] Current: ${balance:.2f} USDT")
        
        if balance < 5.0:
            print(f"❌ [ERROR] Insufficient balance: ${balance:.2f}")
            return False

        # Execute trade with available balance (minimum $5)
        trade_amount = max(5.0, min(balance * 0.90, 15.0))
        symbol = "BTCUSDT"
        action = "Buy"
        
        print(f"🚨 [EXECUTING] {action} ${trade_amount:.2f} USDT of {symbol}...")
        
        order = session.place_order(
            category="spot",
            symbol=symbol,
            side=action,
            orderType="Market",
            qty=f"{trade_amount:.2f}",
            isLeverage=0,
            orderFilter="Order"
        )
        
        if order.get('retCode') == 0:
            order_id = order['result']['orderId']
            print(f"✅ [SUCCESS] Order executed: {order_id}")
            print(f"💰 [SUCCESS] {action} ${trade_amount:.2f} USDT of {symbol}")
            
            # Check new balance
            new_balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
            if new_balance_response.get('retCode') == 0:
                new_balance = float(new_balance_response['result']['list'][0]['coin'][0]['walletBalance'])
                balance_change = balance - new_balance
                print(f"💰 [BALANCE] New: ${new_balance:.2f} USDT (spent: ${balance_change:.2f})")
                
                if balance_change > 1.0:
                    print("✅ [VERIFIED] Real money trading confirmed - balance changed!")
                    return True
                else:
                    print("⚠️ [WARNING] Small balance change detected")
                    return True
            
            return True
        else:
            print(f"❌ [FAILED] Order failed: {order}")
            return False
            
    except Exception as e:
        print(f"❌ [ERROR] Trade execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution"""
    print("=" * 60)
    print("🎯 SINGLE BYBIT TRADE EXECUTION")
    print("🚨 REAL MONEY TRADING TEST")
    print("=" * 60)
    
    success = execute_single_trade()
    
    if success:
        print("\n✅ SINGLE TRADE SUCCESSFUL")
        print("✅ Bybit trading system working correctly")
        print("✅ Ready for continuous trading")
    else:
        print("\n❌ SINGLE TRADE FAILED")
        print("❌ System needs further debugging")
    
    return success

if __name__ == "__main__":
    print("🚨 WARNING: This will execute ONE real money trade on Bybit")
    
    import time
    print("\n⏳ Starting in 3 seconds...")
    for i in range(3, 0, -1):
        print(f"⏳ {i}...")
        time.sleep(1)
    
    print("\n🚀 EXECUTING TRADE...")
    
    success = main()
    
    if success:
        print("\n🎉 BYBIT TRADING VERIFIED - SYSTEM READY")
    else:
        print("\n❌ BYBIT TRADING FAILED - NEEDS FIXES")
    
    sys.exit(0 if success else 1)
