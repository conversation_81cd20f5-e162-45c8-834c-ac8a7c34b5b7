#!/usr/bin/env python3
"""
Comprehensive Trade Logging System
Enterprise-grade logging for all trading activities with detailed tracking
"""

import os
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class TradeLogEntry:
    """Comprehensive trade log entry structure"""
    timestamp: float
    trade_id: str
    exchange: str
    symbol: str
    side: str  # 'buy' or 'sell'
    order_type: str  # 'market', 'limit', etc.
    quantity: float
    price: float
    total_value: float
    currency_pair: Dict[str, str]  # {'base': 'BTC', 'quote': 'USDT'}
    
    # Balance information
    pre_trade_balances: Dict[str, float]
    post_trade_balances: Dict[str, float]
    balance_changes: Dict[str, float]
    
    # Execution details
    execution_time_ms: float
    order_id: Optional[str] = None
    fill_price: Optional[float] = None
    fees: Optional[Dict[str, float]] = None
    slippage: Optional[float] = None
    
    # Strategy information
    strategy_used: Optional[str] = None
    signal_confidence: Optional[float] = None
    entry_reason: Optional[str] = None
    
    # Status and results
    status: str = "pending"  # 'pending', 'filled', 'partial', 'failed', 'cancelled'
    success: bool = False
    error_message: Optional[str] = None
    
    # Profit/Loss tracking
    profit_loss_usd: Optional[float] = None
    profit_loss_percentage: Optional[float] = None
    
    # Additional metadata
    market_conditions: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None

class ComprehensiveTradeLogger:
    """Enterprise-grade comprehensive trade logging system"""
    
    def __init__(self, log_directory: str = "logs/trades"):
        self.log_directory = Path(log_directory)
        self.log_directory.mkdir(parents=True, exist_ok=True)
        
        # Log files
        self.daily_log_file = None
        self.summary_log_file = self.log_directory / "trade_summary.json"
        self.error_log_file = self.log_directory / "trade_errors.json"
        
        # In-memory tracking
        self.trade_entries = []
        self.daily_stats = {}
        self.total_stats = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_volume_usd': 0.0,
            'total_profit_loss_usd': 0.0,
            'currencies_traded': set(),
            'exchanges_used': set(),
            'strategies_used': set()
        }
        
        # Load existing data
        self._load_existing_data()
        
        logger.info(f"[TRADE-LOGGER] Comprehensive trade logging initialized")
        logger.info(f"[TRADE-LOGGER] Log directory: {self.log_directory}")
    
    def _get_daily_log_file(self) -> Path:
        """Get the daily log file path"""
        today = datetime.now().strftime("%Y-%m-%d")
        return self.log_directory / f"trades_{today}.json"
    
    def _load_existing_data(self):
        """Load existing trade data and statistics"""
        try:
            # Load summary statistics
            if self.summary_log_file.exists():
                with open(self.summary_log_file, 'r') as f:
                    data = json.load(f)
                    self.total_stats.update(data)
                    # Convert set back from list
                    for key in ['currencies_traded', 'exchanges_used', 'strategies_used']:
                        if key in self.total_stats:
                            self.total_stats[key] = set(self.total_stats[key])
            
            logger.info(f"[TRADE-LOGGER] Loaded existing statistics: {self.total_stats['total_trades']} total trades")
            
        except Exception as e:
            logger.warning(f"[TRADE-LOGGER] Could not load existing data: {e}")
    
    def log_trade_start(self, trade_data: Dict) -> str:
        """Log the start of a trade execution"""
        try:
            trade_id = f"trade_{int(time.time() * 1000)}_{trade_data.get('symbol', 'unknown')}"
            
            # Create initial trade entry
            trade_entry = TradeLogEntry(
                timestamp=time.time(),
                trade_id=trade_id,
                exchange=trade_data.get('exchange', 'unknown'),
                symbol=trade_data.get('symbol', 'unknown'),
                side=trade_data.get('side', 'unknown'),
                order_type=trade_data.get('order_type', 'market'),
                quantity=float(trade_data.get('quantity', 0)),
                price=float(trade_data.get('price', 0)),
                total_value=float(trade_data.get('total_value', 0)),
                currency_pair=trade_data.get('currency_pair', {}),
                pre_trade_balances=trade_data.get('pre_trade_balances', {}),
                post_trade_balances={},
                balance_changes={},
                execution_time_ms=0,
                strategy_used=trade_data.get('strategy', 'unknown'),
                signal_confidence=trade_data.get('confidence', None),
                entry_reason=trade_data.get('reason', None),
                status="pending"
            )
            
            # Add to in-memory tracking
            self.trade_entries.append(trade_entry)
            
            logger.info(f"[TRADE-LOGGER] Trade started: {trade_id}")
            logger.info(f"[TRADE-LOGGER] {trade_entry.side.upper()} {trade_entry.quantity} {trade_entry.symbol} "
                       f"at ${trade_entry.price:.4f} on {trade_entry.exchange}")
            
            return trade_id
            
        except Exception as e:
            logger.error(f"[TRADE-LOGGER] Error logging trade start: {e}")
            return f"error_trade_{int(time.time())}"
    
    def log_trade_completion(self, trade_id: str, completion_data: Dict):
        """Log the completion of a trade execution"""
        try:
            # Find the trade entry
            trade_entry = None
            for entry in self.trade_entries:
                if entry.trade_id == trade_id:
                    trade_entry = entry
                    break
            
            if not trade_entry:
                logger.warning(f"[TRADE-LOGGER] Trade entry not found for ID: {trade_id}")
                return
            
            # Update trade entry with completion data
            trade_entry.post_trade_balances = completion_data.get('post_trade_balances', {})
            trade_entry.balance_changes = completion_data.get('balance_changes', {})
            trade_entry.execution_time_ms = completion_data.get('execution_time_ms', 0)
            trade_entry.order_id = completion_data.get('order_id', None)
            trade_entry.fill_price = completion_data.get('fill_price', trade_entry.price)
            trade_entry.fees = completion_data.get('fees', {})
            trade_entry.slippage = completion_data.get('slippage', None)
            trade_entry.status = completion_data.get('status', 'completed')
            trade_entry.success = completion_data.get('success', False)
            trade_entry.error_message = completion_data.get('error_message', None)
            trade_entry.profit_loss_usd = completion_data.get('profit_loss_usd', None)
            trade_entry.profit_loss_percentage = completion_data.get('profit_loss_percentage', None)
            trade_entry.market_conditions = completion_data.get('market_conditions', {})
            trade_entry.notes = completion_data.get('notes', None)
            
            # Log completion
            if trade_entry.success:
                logger.info(f"[TRADE-LOGGER] ✅ Trade completed successfully: {trade_id}")
                logger.info(f"[TRADE-LOGGER] Filled at ${trade_entry.fill_price:.4f}, "
                           f"execution time: {trade_entry.execution_time_ms:.1f}ms")
                if trade_entry.profit_loss_usd:
                    logger.info(f"[TRADE-LOGGER] P&L: ${trade_entry.profit_loss_usd:.2f} "
                               f"({trade_entry.profit_loss_percentage:.2f}%)")
            else:
                logger.error(f"[TRADE-LOGGER] ❌ Trade failed: {trade_id}")
                if trade_entry.error_message:
                    logger.error(f"[TRADE-LOGGER] Error: {trade_entry.error_message}")
            
            # Save to daily log file
            self._save_trade_to_daily_log(trade_entry)
            
            # Update statistics
            self._update_statistics(trade_entry)
            
            # Save updated statistics
            self._save_statistics()
            
        except Exception as e:
            logger.error(f"[TRADE-LOGGER] Error logging trade completion: {e}")
    
    def _save_trade_to_daily_log(self, trade_entry: TradeLogEntry):
        """Save trade entry to daily log file"""
        try:
            daily_log_file = self._get_daily_log_file()
            
            # Load existing entries for today
            daily_entries = []
            if daily_log_file.exists():
                with open(daily_log_file, 'r') as f:
                    daily_entries = json.load(f)
            
            # Add new entry
            trade_dict = asdict(trade_entry)
            # Convert sets to lists for JSON serialization
            for key, value in trade_dict.items():
                if isinstance(value, set):
                    trade_dict[key] = list(value)
            
            daily_entries.append(trade_dict)
            
            # Save updated entries
            with open(daily_log_file, 'w') as f:
                json.dump(daily_entries, f, indent=2, default=str)
            
            logger.debug(f"[TRADE-LOGGER] Trade saved to daily log: {daily_log_file}")
            
        except Exception as e:
            logger.error(f"[TRADE-LOGGER] Error saving to daily log: {e}")
    
    def _update_statistics(self, trade_entry: TradeLogEntry):
        """Update trading statistics"""
        try:
            self.total_stats['total_trades'] += 1
            
            if trade_entry.success:
                self.total_stats['successful_trades'] += 1
            else:
                self.total_stats['failed_trades'] += 1
            
            self.total_stats['total_volume_usd'] += trade_entry.total_value
            
            if trade_entry.profit_loss_usd:
                self.total_stats['total_profit_loss_usd'] += trade_entry.profit_loss_usd
            
            # Track currencies, exchanges, and strategies
            if trade_entry.currency_pair:
                base = trade_entry.currency_pair.get('base')
                quote = trade_entry.currency_pair.get('quote')
                if base:
                    self.total_stats['currencies_traded'].add(base)
                if quote:
                    self.total_stats['currencies_traded'].add(quote)
            
            self.total_stats['exchanges_used'].add(trade_entry.exchange)
            
            if trade_entry.strategy_used:
                self.total_stats['strategies_used'].add(trade_entry.strategy_used)
            
        except Exception as e:
            logger.error(f"[TRADE-LOGGER] Error updating statistics: {e}")
    
    def _save_statistics(self):
        """Save updated statistics to summary file"""
        try:
            # Convert sets to lists for JSON serialization
            stats_to_save = self.total_stats.copy()
            for key in ['currencies_traded', 'exchanges_used', 'strategies_used']:
                if key in stats_to_save:
                    stats_to_save[key] = list(stats_to_save[key])
            
            with open(self.summary_log_file, 'w') as f:
                json.dump(stats_to_save, f, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"[TRADE-LOGGER] Error saving statistics: {e}")
    
    def get_trading_summary(self) -> Dict:
        """Get comprehensive trading summary"""
        try:
            success_rate = 0.0
            if self.total_stats['total_trades'] > 0:
                success_rate = (self.total_stats['successful_trades'] / self.total_stats['total_trades']) * 100
            
            return {
                'total_trades': self.total_stats['total_trades'],
                'successful_trades': self.total_stats['successful_trades'],
                'failed_trades': self.total_stats['failed_trades'],
                'success_rate_percentage': success_rate,
                'total_volume_usd': self.total_stats['total_volume_usd'],
                'total_profit_loss_usd': self.total_stats['total_profit_loss_usd'],
                'currencies_traded': list(self.total_stats['currencies_traded']),
                'exchanges_used': list(self.total_stats['exchanges_used']),
                'strategies_used': list(self.total_stats['strategies_used']),
                'log_directory': str(self.log_directory)
            }
            
        except Exception as e:
            logger.error(f"[TRADE-LOGGER] Error getting trading summary: {e}")
            return {}
    
    def log_error(self, error_data: Dict):
        """Log trading errors separately"""
        try:
            error_entry = {
                'timestamp': time.time(),
                'datetime': datetime.now().isoformat(),
                'error_type': error_data.get('type', 'unknown'),
                'error_message': error_data.get('message', ''),
                'exchange': error_data.get('exchange', 'unknown'),
                'symbol': error_data.get('symbol', ''),
                'context': error_data.get('context', {}),
                'stack_trace': error_data.get('stack_trace', '')
            }
            
            # Load existing errors
            errors = []
            if self.error_log_file.exists():
                with open(self.error_log_file, 'r') as f:
                    errors = json.load(f)
            
            # Add new error
            errors.append(error_entry)
            
            # Keep only last 1000 errors
            if len(errors) > 1000:
                errors = errors[-1000:]
            
            # Save updated errors
            with open(self.error_log_file, 'w') as f:
                json.dump(errors, f, indent=2, default=str)
            
            logger.error(f"[TRADE-LOGGER] Error logged: {error_data.get('type', 'unknown')}")
            
        except Exception as e:
            logger.error(f"[TRADE-LOGGER] Error logging error: {e}")

# Global trade logger instance
trade_logger = ComprehensiveTradeLogger()
