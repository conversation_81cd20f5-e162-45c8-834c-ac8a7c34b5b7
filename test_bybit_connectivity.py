#!/usr/bin/env python3
"""
Bybit Connectivity Diagnostic Tool
Tests Bybit API connectivity and identifies issues
"""

import os
import sys
import asyncio
import traceback
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Load environment
from dotenv import load_dotenv
load_dotenv()

async def test_bybit_connectivity():
    """Comprehensive Bybit connectivity test"""
    print("🔍 [BYBIT-DIAGNOSTIC] Starting Bybit connectivity diagnostic...")
    print(f"🕒 [BYBIT-DIAGNOSTIC] Test started at: {datetime.now()}")
    
    # Test 1: Check environment variables
    print("\n📋 [TEST-1] Checking Bybit credentials...")
    api_key = os.getenv('BYBIT_API_KEY')
    api_secret = os.getenv('BYBIT_API_SECRET')
    
    if not api_key:
        print("❌ [TEST-1] BYBIT_API_KEY not found in environment")
        return False
    if not api_secret:
        print("❌ [TEST-1] BYBIT_API_SECRET not found in environment")
        return False
    
    print(f"✅ [TEST-1] API Key found: {api_key[:8]}...")
    print(f"✅ [TEST-1] API Secret found: {api_secret[:8]}...")
    
    # Test 2: Test pybit import and basic connection
    print("\n📋 [TEST-2] Testing pybit library and basic connection...")
    try:
        from pybit.unified_trading import HTTP
        print("✅ [TEST-2] pybit library imported successfully")
        
        # Create session
        session = HTTP(
            api_key=api_key,
            api_secret=api_secret,
            testnet=False,
            recv_window=10000
        )
        print("✅ [TEST-2] Bybit session created successfully")
        
    except ImportError as e:
        print(f"❌ [TEST-2] Failed to import pybit: {e}")
        return False
    except Exception as e:
        print(f"❌ [TEST-2] Failed to create Bybit session: {e}")
        return False
    
    # Test 3: Test account info endpoint
    print("\n📋 [TEST-3] Testing account info endpoint...")
    try:
        account_info = session.get_account_info()
        if account_info and account_info.get("retCode") == 0:
            print("✅ [TEST-3] Account info retrieved successfully")
            print(f"✅ [TEST-3] Account type: {account_info.get('result', {}).get('unifiedMarginStatus', 'Unknown')}")
        else:
            print(f"❌ [TEST-3] Account info failed: {account_info}")
            return False
    except Exception as e:
        print(f"❌ [TEST-3] Account info error: {e}")
        print(f"❌ [TEST-3] Traceback: {traceback.format_exc()}")
        return False
    
    # Test 4: Test wallet balance endpoint
    print("\n📋 [TEST-4] Testing wallet balance endpoint...")
    try:
        balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
        if balance_response and balance_response.get("retCode") == 0:
            balance_data = balance_response.get('result', {}).get('list', [])
            if balance_data:
                usdt_balance = None
                for coin_data in balance_data[0].get('coin', []):
                    if coin_data.get('coin') == 'USDT':
                        usdt_balance = float(coin_data.get('walletBalance', 0))
                        break
                
                if usdt_balance is not None:
                    print(f"✅ [TEST-4] USDT balance retrieved: ${usdt_balance:.2f}")
                else:
                    print("⚠️ [TEST-4] USDT balance not found in response")
            else:
                print("⚠️ [TEST-4] No balance data in response")
        else:
            print(f"❌ [TEST-4] Balance request failed: {balance_response}")
            return False
    except Exception as e:
        print(f"❌ [TEST-4] Balance request error: {e}")
        print(f"❌ [TEST-4] Traceback: {traceback.format_exc()}")
        return False
    
    # Test 5: Test market data endpoint
    print("\n📋 [TEST-5] Testing market data endpoint...")
    try:
        ticker_response = session.get_tickers(category="spot", symbol="BTCUSDT")
        if ticker_response and ticker_response.get("retCode") == 0:
            ticker_data = ticker_response.get('result', {}).get('list', [])
            if ticker_data:
                btc_price = float(ticker_data[0].get('lastPrice', 0))
                print(f"✅ [TEST-5] BTC price retrieved: ${btc_price:.2f}")
            else:
                print("⚠️ [TEST-5] No ticker data in response")
        else:
            print(f"❌ [TEST-5] Ticker request failed: {ticker_response}")
            return False
    except Exception as e:
        print(f"❌ [TEST-5] Ticker request error: {e}")
        print(f"❌ [TEST-5] Traceback: {traceback.format_exc()}")
        return False
    
    # Test 6: Test our Bybit client implementation
    print("\n📋 [TEST-6] Testing our Bybit client implementation...")
    try:
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        bybit_client = BybitClientFixed(
            api_key=api_key,
            api_secret=api_secret,
            testnet=False
        )
        
        # Test connection
        connection_result = bybit_client.test_connection()
        if connection_result:
            print("✅ [TEST-6] Our Bybit client connection successful")
        else:
            print("❌ [TEST-6] Our Bybit client connection failed")
            return False

        # Test balance retrieval
        balance_result = await bybit_client.get_balance()
        if balance_result:
            print(f"✅ [TEST-6] Our Bybit client balance retrieval successful")
            print(f"✅ [TEST-6] Balance data: ${float(balance_result):.2f}")
        else:
            print("❌ [TEST-6] Our Bybit client balance retrieval failed")
            return False
            
    except Exception as e:
        print(f"❌ [TEST-6] Our Bybit client error: {e}")
        print(f"❌ [TEST-6] Traceback: {traceback.format_exc()}")
        return False
    
    print("\n🎉 [SUCCESS] All Bybit connectivity tests passed!")
    print("✅ [RESULT] Bybit API is working correctly")
    return True

async def main():
    """Main diagnostic execution"""
    print("=" * 60)
    print("🔧 BYBIT CONNECTIVITY DIAGNOSTIC")
    print("🚨 TESTING BYBIT API FUNCTIONALITY")
    print("=" * 60)
    
    try:
        success = await test_bybit_connectivity()
        
        if success:
            print("\n✅ [FINAL-RESULT] Bybit connectivity is working correctly")
            print("✅ [RECOMMENDATION] No fixes needed for Bybit connectivity")
            return 0
        else:
            print("\n❌ [FINAL-RESULT] Bybit connectivity issues detected")
            print("❌ [RECOMMENDATION] Review the failed tests above")
            return 1
            
    except Exception as e:
        print(f"\n❌ [CRITICAL-ERROR] Diagnostic failed: {e}")
        print(f"❌ [TRACEBACK] {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Diagnostic stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Diagnostic system error: {e}")
        sys.exit(1)
