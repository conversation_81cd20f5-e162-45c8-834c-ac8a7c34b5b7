import os
import logging
from pathlib import Path
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from src.exchanges.coinbase import CoinbaseTrader

logger = logging.getLogger('SecurityMonitor')
handler = logging.FileHandler('security_audit.log')
logger.addHandler(handler)
logger.setLevel(logging.CRITICAL)

def audit_system():
    # 1. Check .env permissions
    env_file = Path(__file__).parent.parent.parent / '.env'
    if oct(env_file.stat().st_mode)[-3:] != '600':
        logger.critical(f"Insecure .env permissions: {oct(env_file.stat().st_mode)}")
    
    # 2. Verify key decryption
    try:
        client = CoinbaseOrgClient()
        client._decrypt_key()  # Test decryption
    except InvalidToken as e:
        logger.critical(f"Decryption failed: {str(e)}")
    
    # 3. Monitor key usage
    with open('security_audit.log', 'a') as f:
        f.write(f"Security check at {datetime.utcnow().isoformat()}\n")

def alert_on_anomaly():
    # Implement your alerting logic (Email/SMS/Webhook)
    pass

def check_vault_status():
    vault = connect_vault()
    try:
        status = vault.secrets.kv.v2.read_secret_version(
            path='system/status',
            raise_on_deleted=True  # Explicitly handle deprecation warning
        )
        print(f"Vault Status: {status['data']['metadata']['version']}")
    except hvac.exceptions.InvalidPath:
        print("Vault not initialized! Run key migration first.")

def verify_exchange_connectivity():
    try:
        balances = CoinbaseTrader().get_balance()
        if not balances:
            raise ValueError("Empty balance response - possible API failure")
        return balances
    except Exception as e:
        logging.critical(f"Exchange connectivity failure: {str(e)}")
        sys.exit(1)