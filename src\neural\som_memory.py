from minisom import MiniSom
import numpy as np

class SOMemory:
    def __init__(self, grid_size=10, input_len=64):
        self.som = MiniSom(grid_size, grid_size, input_len, sigma=0.5, learning_rate=0.5)
        self.grid_size = grid_size
        
    def update(self, lstm_embeddings: np.ndarray):
        """Update SOM with latest LSTM states"""
        self.som.train_random(lstm_embeddings, 100)  # 100 iterations
        
    def get_cluster(self, state: np.ndarray) -> tuple:
        """Get best matching cluster for current state"""
        return self.som.winner(state)