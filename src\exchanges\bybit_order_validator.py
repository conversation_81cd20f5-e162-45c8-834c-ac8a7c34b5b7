#!/usr/bin/env python3
"""
Bybit Order Validator - Fixes ErrCode 170140: Order value exceeded lower limit
Implements dynamic minimum order validation and proper order sizing
"""
import logging
from decimal import Decimal, ROUND_DOWN, ROUND_HALF_UP
from typing import Dict, Tuple, Optional
import asyncio

logger = logging.getLogger(__name__)

class BybitOrderValidator:
    """
    Validates and adjusts Bybit orders to meet minimum requirements
    Prevents ErrCode 170140 by ensuring all orders meet Bybit's $5 minimum
    """
    
    def __init__(self, session):
        self.session = session
        self.minimum_requirements = {}
        self.current_prices = {}
        self.last_update = 0
        self.update_interval = 300  # 5 minutes
        
    async def get_minimum_requirements(self, symbol: str) -> Dict:
        """Get minimum order requirements for a symbol"""
        try:
            # Check if we need to update requirements
            import time
            if time.time() - self.last_update > self.update_interval:
                await self._update_requirements()
            
            return self.minimum_requirements.get(symbol, {
                'min_order_amt': 5.0,  # CRITICAL FIX: Bybit actual minimum is $5 USD (REAL API data)
                'min_order_qty': 0.000001,
                'qty_step': 0.000001,
                'tick_size': 0.01,
                'status': 'Trading'
            })
        except Exception as e:
            logger.error(f"Error getting minimum requirements for {symbol}: {e}")
            return {
                'min_order_amt': 5.0,  # CRITICAL FIX: Bybit actual minimum is $5 USD (REAL API data)
                'min_order_qty': 0.000001,
                'qty_step': 0.000001,
                'tick_size': 0.01,
                'status': 'Trading'
            }
    
    async def _update_requirements(self):
        """Update minimum requirements from Bybit API"""
        try:
            symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT', 'BNBUSDT', 'XRPUSDT']
            
            for symbol in symbols:
                try:
                    instrument_info = self.session.get_instruments_info(category="spot", symbol=symbol)
                    
                    if instrument_info.get('retCode') == 0:
                        instruments = instrument_info.get('result', {}).get('list', [])
                        if instruments:
                            instrument = instruments[0]
                            lot_size_filter = instrument.get('lotSizeFilter', {})
                            price_filter = instrument.get('priceFilter', {})
                            
                            self.minimum_requirements[symbol] = {
                                'min_order_amt': float(lot_size_filter.get('minOrderAmt', '5.0')),  # CRITICAL FIX: Default to $5 USD (REAL API data)
                                'min_order_qty': float(lot_size_filter.get('minOrderQty', '0.000001')),
                                'qty_step': float(lot_size_filter.get('qtyStep', '0.000001')),
                                'tick_size': float(price_filter.get('tickSize', '0.01')),
                                'status': instrument.get('status', 'Trading'),
                                'base_precision': float(lot_size_filter.get('basePrecision', '0.000001')),
                                'quote_precision': float(lot_size_filter.get('quotePrecision', '0.01'))
                            }
                            
                            logger.info(f"Updated requirements for {symbol}: min_amt=${self.minimum_requirements[symbol]['min_order_amt']}")
                except Exception as e:
                    logger.warning(f"Failed to update requirements for {symbol}: {e}")
            
            import time
            self.last_update = time.time()
            logger.info(f"Updated minimum requirements for {len(self.minimum_requirements)} symbols")
            
        except Exception as e:
            logger.error(f"Failed to update minimum requirements: {e}")
    
    async def get_current_price(self, symbol: str) -> float:
        """Get current price for a symbol"""
        try:
            ticker = self.session.get_tickers(category="spot", symbol=symbol)
            if ticker.get('retCode') == 0:
                price = float(ticker['result']['list'][0]['lastPrice'])
                self.current_prices[symbol] = price
                return price
            else:
                logger.error(f"Failed to get price for {symbol}: {ticker}")
                return 0.0
        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {e}")
            return 0.0
    
    async def validate_and_adjust_order(self, symbol: str, side: str, amount: float, order_type: str = "Market") -> Tuple[bool, Dict]:
        """
        Validate and adjust order to meet Bybit requirements
        Returns: (is_valid, adjusted_order_params)
        """
        try:
            logger.info(f"🔍 [VALIDATOR] Validating {side} order for {symbol}: ${amount:.2f}")
            
            # Get requirements and current price
            requirements = await self.get_minimum_requirements(symbol)
            current_price = await self.get_current_price(symbol)
            
            if current_price <= 0:
                return False, {"error": f"Could not get current price for {symbol}"}
            
            if requirements['status'] != 'Trading':
                return False, {"error": f"{symbol} is not available for trading (status: {requirements['status']})"}
            
            min_order_amt = requirements['min_order_amt']
            min_order_qty = requirements['min_order_qty']
            qty_step = requirements['qty_step']
            
            logger.info(f"📊 [VALIDATOR] {symbol} - Price: ${current_price:.2f}, Min Amt: ${min_order_amt:.2f}")
            
            # For market orders, we work with quote amount (USDT)
            if order_type.lower() == "market":
                # Check if order amount meets minimum
                if amount < min_order_amt:
                    # Adjust to minimum with safety margin
                    adjusted_amount = min_order_amt * 1.2  # 20% safety margin
                    logger.warning(f"🔧 [VALIDATOR] Adjusting order amount from ${amount:.2f} to ${adjusted_amount:.2f}")
                    amount = adjusted_amount
                elif amount == min_order_amt:
                    # CRITICAL FIX: Add small buffer for exactly minimum orders to prevent precision issues
                    adjusted_amount = min_order_amt + 0.25  # $0.25 safety buffer for exactly $5.00 orders
                    logger.warning(f"🔧 [VALIDATOR] Adding precision buffer: ${amount:.2f} -> ${adjusted_amount:.2f}")
                    amount = adjusted_amount
                
                # Calculate quantity for validation
                quantity = amount / current_price
                
                # Round quantity to proper step
                rounded_qty = self._round_to_step(quantity, qty_step)
                
                # Validate minimum quantity
                if rounded_qty < min_order_qty:
                    # Adjust amount to meet minimum quantity
                    required_amount = min_order_qty * current_price * 1.1  # 10% safety
                    logger.warning(f"🔧 [VALIDATOR] Adjusting for min quantity: ${amount:.2f} -> ${required_amount:.2f}")
                    amount = required_amount
                    rounded_qty = self._round_to_step(amount / current_price, qty_step)
                
                # Final validation with enhanced logging
                final_order_value = rounded_qty * current_price

                # CRITICAL FIX: Add comprehensive debug logging before validation checks
                logger.info(f"🔍 [ORDER-VALIDATION] Order value: ${final_order_value:.3f}")
                logger.info(f"🔍 [ORDER-VALIDATION] Minimum required: ${min_order_amt:.3f}")
                logger.info(f"🔍 [ORDER-VALIDATION] Validation result: {'PASS' if final_order_value >= min_order_amt else 'FAIL'}")

                if final_order_value < min_order_amt:
                    return False, {"error": f"Final order value ${final_order_value:.2f} below minimum ${min_order_amt:.2f}"}
                
                # Return validated order parameters
                return True, {
                    "symbol": symbol,
                    "side": side,
                    "orderType": order_type,
                    "qty": f"{amount:.2f}",  # Use quote amount for market orders
                    "category": "spot",
                    "isLeverage": 0,
                    "orderFilter": "Order",  # Important for quote-based orders
                    "calculated_qty": rounded_qty,
                    "calculated_value": final_order_value,
                    "price": current_price
                }
            
            else:
                # For limit orders, work with base quantity
                quantity = amount
                rounded_qty = self._round_to_step(quantity, qty_step)
                
                if rounded_qty < min_order_qty:
                    return False, {"error": f"Quantity {rounded_qty:.8f} below minimum {min_order_qty:.8f}"}
                
                order_value = rounded_qty * current_price
                if order_value < min_order_amt:
                    return False, {"error": f"Order value ${order_value:.2f} below minimum ${min_order_amt:.2f}"}
                
                return True, {
                    "symbol": symbol,
                    "side": side,
                    "orderType": order_type,
                    "qty": f"{rounded_qty:.8f}",
                    "category": "spot",
                    "isLeverage": 0,
                    "calculated_value": order_value,
                    "price": current_price
                }
                
        except Exception as e:
            logger.error(f"❌ [VALIDATOR] Error validating order: {e}")
            return False, {"error": str(e)}
    
    def _round_to_step(self, value: float, step: float) -> float:
        """Round value to the nearest step - CRITICAL FIX: Use ROUND_HALF_UP to prevent boundary condition bugs"""
        try:
            if step <= 0:
                return value

            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(step))

            # CRITICAL FIX: Use ROUND_HALF_UP instead of ROUND_DOWN to prevent $10.00 -> $9.999999 precision bugs
            # This ensures orders with exactly minimum value ($10.00) don't get rounded down below minimum
            rounded = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_HALF_UP) * decimal_step
            return float(rounded)
        except Exception:
            return value
    
    async def get_safe_order_amount(self, symbol: str, balance: float, percentage: float = 0.25) -> float:
        """
        Calculate a safe order amount based on balance and minimum requirements
        """
        try:
            requirements = await self.get_minimum_requirements(symbol)
            min_order_amt = requirements['min_order_amt']
            
            # Calculate percentage of balance
            percentage_amount = balance * percentage
            
            # Ensure we meet minimum with safety margin
            safe_minimum = min_order_amt * 1.2  # 20% safety margin
            
            # Use the larger of percentage or safe minimum
            recommended_amount = max(percentage_amount, safe_minimum)
            
            # Ensure we don't exceed available balance
            if recommended_amount > balance * 0.9:  # Leave 10% buffer
                recommended_amount = balance * 0.9
            
            # Final check against absolute minimum
            if recommended_amount < min_order_amt:
                return 0.0  # Insufficient balance
            
            logger.info(f"💰 [SAFE-ORDER] {symbol}: Balance=${balance:.2f}, Recommended=${recommended_amount:.2f}")
            return recommended_amount
            
        except Exception as e:
            logger.error(f"Error calculating safe order amount for {symbol}: {e}")
            return 0.0
