"""
Reinforcement Learning Agent for Neural Strategy Components
Provides ReinforcementLearningAgent class for strategy optimization
"""

import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Optional, Any, Tuple
from collections import deque
import random

logger = logging.getLogger(__name__)

class ReinforcementLearningAgent:
    """
    Reinforcement Learning Agent for strategy optimization and selection
    Compatible with adaptive_neural_strategy and neural_strategy_manager
    """
    
    def __init__(self, state_size: int, action_size: int, learning_rate: float = 0.001, **kwargs):
        """
        Initialize RL Agent
        
        Args:
            state_size: Size of the state space (market features)
            action_size: Size of the action space (strategies or parameters)
            learning_rate: Learning rate for neural network
        """
        self.state_size = state_size
        self.action_size = action_size
        self.learning_rate = learning_rate
        
        # Hyperparameters
        self.epsilon = kwargs.get('epsilon', 0.1)  # Exploration rate
        self.epsilon_decay = kwargs.get('epsilon_decay', 0.995)
        self.epsilon_min = kwargs.get('epsilon_min', 0.01)
        self.gamma = kwargs.get('gamma', 0.95)  # Discount factor
        self.batch_size = kwargs.get('batch_size', 32)
        self.memory_size = kwargs.get('memory_size', 10000)
        
        # Experience replay memory
        self.memory = deque(maxlen=self.memory_size)
        
        # Neural network setup
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self._build_model()
        
        # Training metrics
        self.training_step = 0
        self.last_loss = 0.0
        
        logger.info(f"ReinforcementLearningAgent initialized: state_size={state_size}, action_size={action_size}")
    
    def _build_model(self):
        """Build the neural network model"""
        class DQN(nn.Module):
            def __init__(self, state_size, action_size):
                super(DQN, self).__init__()
                self.fc1 = nn.Linear(state_size, 128)
                self.fc2 = nn.Linear(128, 128)
                self.fc3 = nn.Linear(128, 64)
                self.fc4 = nn.Linear(64, action_size)
                self.dropout = nn.Dropout(0.2)
                
            def forward(self, x):
                x = torch.relu(self.fc1(x))
                x = self.dropout(x)
                x = torch.relu(self.fc2(x))
                x = self.dropout(x)
                x = torch.relu(self.fc3(x))
                x = self.fc4(x)
                return x
        
        self.q_network = DQN(self.state_size, self.action_size).to(self.device)
        self.target_network = DQN(self.state_size, self.action_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=self.learning_rate)
        
        # Initialize target network with same weights
        self.update_target_network()
    
    def update_target_network(self):
        """Update target network with current network weights"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay memory"""
        self.memory.append((state, action, reward, next_state, done))
    
    def predict(self, state):
        """Predict action probabilities for given state"""
        try:
            if isinstance(state, (list, np.ndarray)):
                state = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            elif isinstance(state, torch.Tensor):
                if state.dim() == 1:
                    state = state.unsqueeze(0)
                state = state.to(self.device)
            
            self.q_network.eval()
            with torch.no_grad():
                q_values = self.q_network(state)
                # Convert Q-values to probabilities using softmax
                probabilities = torch.softmax(q_values, dim=1)
                return probabilities.cpu().numpy().flatten()
                
        except Exception as e:
            logger.error(f"Error in predict: {e}")
            # Return uniform probabilities as fallback
            return np.ones(self.action_size) / self.action_size
    
    def act(self, state):
        """Choose action based on epsilon-greedy policy"""
        if np.random.random() <= self.epsilon:
            return random.randrange(self.action_size)
        
        action_probs = self.predict(state)
        return np.argmax(action_probs)
    
    def replay(self, batch_size=None):
        """Train the model on a batch of experiences"""
        if batch_size is None:
            batch_size = self.batch_size
            
        if len(self.memory) < batch_size:
            return
        
        try:
            # Sample batch from memory
            batch = random.sample(self.memory, batch_size)
            states, actions, rewards, next_states, dones = zip(*batch)
            
            # Convert to tensors
            states = torch.FloatTensor(states).to(self.device)
            actions = torch.LongTensor(actions).to(self.device)
            rewards = torch.FloatTensor(rewards).to(self.device)
            next_states = torch.FloatTensor(next_states).to(self.device)
            dones = torch.BoolTensor(dones).to(self.device)
            
            # Current Q values
            current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
            
            # Next Q values from target network
            next_q_values = self.target_network(next_states).max(1)[0].detach()
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)
            
            # Compute loss
            loss = nn.MSELoss()(current_q_values.squeeze(), target_q_values)
            
            # Optimize
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            # Update epsilon
            if self.epsilon > self.epsilon_min:
                self.epsilon *= self.epsilon_decay
            
            # Update training metrics
            self.training_step += 1
            self.last_loss = loss.item()
            
            # Update target network periodically
            if self.training_step % 100 == 0:
                self.update_target_network()
            
            logger.debug(f"RL training step {self.training_step}: loss={self.last_loss:.4f}, epsilon={self.epsilon:.4f}")
            
        except Exception as e:
            logger.error(f"Error in replay training: {e}")
    
    def save_model(self, filepath: str):
        """Save the trained model"""
        try:
            torch.save({
                'q_network_state_dict': self.q_network.state_dict(),
                'target_network_state_dict': self.target_network.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'epsilon': self.epsilon,
                'training_step': self.training_step
            }, filepath)
            logger.info(f"RL model saved to {filepath}")
        except Exception as e:
            logger.error(f"Error saving RL model: {e}")
    
    def load_model(self, filepath: str):
        """Load a trained model"""
        try:
            checkpoint = torch.load(filepath, map_location=self.device)
            self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
            self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.epsilon = checkpoint.get('epsilon', self.epsilon)
            self.training_step = checkpoint.get('training_step', 0)
            logger.info(f"RL model loaded from {filepath}")
        except Exception as e:
            logger.error(f"Error loading RL model: {e}")
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get current training statistics"""
        return {
            'training_step': self.training_step,
            'epsilon': self.epsilon,
            'last_loss': self.last_loss,
            'memory_size': len(self.memory),
            'device': str(self.device)
        }
