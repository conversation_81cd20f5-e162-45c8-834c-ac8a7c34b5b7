import numpy as np
import random
import math
from collections import deque

# (Optional) PyTorch or TensorFlow for neural network implementation
# Using a simple numpy-based policy for pseudocode; can integrate PyTorch for real model.
# import torch
# import torch.nn as nn
# import torch.optim as optim

class HFTAgent:
    def __init__(self, state_dim, action_space, initial_balance=1000.0, risk_fraction=0.01):
        """
        Initialize the PPO agent.
        state_dim: dimension of state vector
        action_space: list or dict of possible actions (e.g., ['BUY','SELL','HOLD'] or continuous range)
        initial_balance: starting capital
        risk_fraction: base fraction of equity to risk per trade (e.g., 1%)
        """
        self.state_dim = state_dim
        self.action_space = action_space
        self.num_actions = len(action_space) if isinstance(action_space, (list, tuple)) else action_space
        self.risk_fraction = risk_fraction
        self.balance = initial_balance
        self.position_size = 0  # current position (positive = long, negative = short)
        self.position_price = None  # entry price of current position for PnL calc

        # PPO hyperparameters
        self.gamma = 0.99               # discount factor for rewards
        self.lambda_gae = 0.95          # GAE lambda for advantage estimation
        self.epsilon_clip = 0.2         # PPO clip parameter
        self.lr = 0.0003                # learning rate
        self.batch_size = 32           # batch of experiences for update
        self.train_freq = 64           # how many steps to collect before PPO update
        self.entropy_coef = 0.01        # encourage exploration
        self.value_coef = 0.5           # value function loss weight

        # Experience storage
        self.history = []  # list of (state, action, log_prob, reward, done, value) for PPO updates
        self.episode_reward = 0.0
        self.step_count = 0

        # Initialize policy and value networks (dummy placeholder, replace with actual neural net)
        # For example, you can integrate a PyTorch model here.
        # self.policy_net = PolicyNetwork(state_dim, num_actions)
        # self.value_net = ValueNetwork(state_dim)
        # self.optimizer = optim.Adam(list(self.policy_net.parameters()) + list(self.value_net.parameters()), lr=self.lr)
        pass  # Note: actual network initialization would go here

    def select_action(self, state):
        """Select an action based on current policy. Returns action and its log probability and value estimate."""
        # Placeholder: random action selection (to be replaced with policy network forward pass)
        if isinstance(self.action_space, (list, tuple)):
            action_idx = random.randrange(self.num_actions)
            action = self.action_space[action_idx]
        else:
            # If continuous action (like target position), sample from a distribution (e.g., Normal) - placeholder:
            action = random.random()  # a random float between 0 and 1 for demonstration
            action_idx = None

        # Compute log probability and value (dummy for now)
        log_prob = 0.0  # would come from policy distribution
        value = 0.0     # would come from value network

        # If using neural nets, you'd do something like:
        # state_tensor = torch.tensor(state, dtype=torch.float32)
        # pi_dist = self.policy_net(state_tensor)
        # action = pi_dist.sample()
        # log_prob = pi_dist.log_prob(action).item()
        # value = self.value_net(state_tensor).item()
        # action = action.item()  # convert from tensor to raw value if needed

        return action, log_prob, value

    def store_experience(self, state, action, log_prob, reward, done, value):
        """Store a single timestep of experience for PPO update."""
        self.history.append((state, action, log_prob, reward, done, value))
        self.episode_reward += reward
        self.step_count += 1

    def update_policy(self):
        """Perform a PPO update if enough experience is collected."""
        if len(self.history) < self.train_freq:
            return  # not enough data yet for an update

        # Convert history to numpy arrays for vectorized operations
        states = np.array([h[0] for h in self.history], dtype=np.float32)
        actions = np.array([h[1] for h in self.history])
        old_log_probs = np.array([h[2] for h in self.history], dtype=np.float32)
        rewards = np.array([h[3] for h in self.history], dtype=np.float32)
        dones = np.array([h[4] for h in self.history], dtype=np.float32)
        values = np.array([h[5] for h in self.history], dtype=np.float32)

        # Compute discounted rewards-to-go (targets for value function)
        returns = np.zeros_like(rewards)
        advs = np.zeros_like(rewards)
        running_return = 0.0
        running_adv = 0.0
        prev_value = 0.0
        prev_done = 0.0
        # Compute GAE (Generalized Advantage Estimation) for advantage
        for t in reversed(range(len(rewards))):
            running_return = rewards[t] + self.gamma * running_return * (1 - dones[t])
            returns[t] = running_return
            # TD Error + GAE
            delta = rewards[t] + self.gamma * prev_value * (1 - dones[t]) - values[t]
            running_adv = delta + self.gamma * self.lambda_gae * running_adv * (1 - prev_done)
            advs[t] = running_adv
            prev_value = values[t]
            prev_done = dones[t]
        advs = (advs - advs.mean()) / (advs.std() + 1e-8)  # normalize advantages

        # Here we would normally perform several epochs of PPO update on the policy and value networks
        # using the collected (states, actions, returns, advantages, old_log_probs).
        # Pseudocode for update (assuming neural nets and torch):
        #
        # for epoch in range(ppo_epochs):
        #     indices = np.arange(len(states))
        #     np.random.shuffle(indices)
        #     for start in range(0, len(states), batch_size):
        #         end = start + batch_size
        #         batch_idx = indices[start:end]
        #         batch_states = torch.tensor(states[batch_idx], dtype=torch.float32)
        #         batch_actions = torch.tensor(actions[batch_idx], dtype=torch.long or torch.float32)
        #         batch_old_log_probs = torch.tensor(old_log_probs[batch_idx], dtype=torch.float32)
        #         batch_returns = torch.tensor(returns[batch_idx], dtype=torch.float32)
        #         batch_advs = torch.tensor(advs[batch_idx], dtype=torch.float32)
        #
        #         # Get current policy dist and value
        #         dist = self.policy_net(batch_states)
        #         value = self.value_net(batch_states).squeeze()
        #         # Compute new log probs and entropy
        #         new_log_probs = dist.log_prob(batch_actions)
        #         entropy = dist.entropy().mean()
        #         # PPO ratio
        #         ratio = (new_log_probs - batch_old_log_probs).exp()
        #         # Policy loss
        #         surr1 = ratio * batch_advs
        #         surr2 = torch.clamp(ratio, 1-self.epsilon_clip, 1+self.epsilon_clip) * batch_advs
        #         policy_loss = -torch.min(surr1, surr2).mean()
        #         # Value loss (MSE)
        #         value_loss = self.value_coef * (batch_returns - value).pow(2).mean()
        #         # Combined loss
        #         loss = policy_loss + value_loss - self.entropy_coef * entropy
        #
        #         self.optimizer.zero_grad()
        #         loss.backward()
        #         self.optimizer.step()
        #
        # After updating, clear the experience buffer:
        self.history = []
        self.episode_reward = 0.0

    def scale_position(self, base_size, volatility_factor=1.0):
        """
        Determine position size based on current balance and risk settings.
        base_size: suggested base position size (e.g., from action or default).
        volatility_factor: multiplier (<1 if high volatility to reduce size).
        Returns adjusted position size.
        """
        # Risk management: only use a fraction of balance
        desired_amount = self.balance * self.risk_fraction
        size = min(base_size, desired_amount) * volatility_factor
        return max(size, 0.0)

    def update_balance(self, pnl):
        """Update balance after a trade (profit or loss)."""
        self.balance += pnl

    def reset(self):
        """Reset agent state (for new episode or reinitialization)."""
        self.history = []
        self.episode_reward = 0.0
        self.step_count = 0
        # Note: we do not reset balance so it carries over, reflecting real account growth/decline.
