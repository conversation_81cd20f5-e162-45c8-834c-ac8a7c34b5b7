{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Trading Bot Configuration Schema", "description": "Schema for validating the trading bot's config.json", "type": "object", "required": ["environment", "exchanges", "trading"], "properties": {"environment": {"type": "string", "enum": ["production", "development", "staging"], "default": "production"}, "default_exchange": {"type": "string", "enum": ["binance", "bybit", "coinbase"], "default": "binance"}, "exchanges": {"type": "object", "additionalProperties": {"type": "object", "required": ["api_key", "api_secret", "symbols"], "properties": {"testnet": {"type": "boolean", "default": true}, "api_key": {"type": "string"}, "api_secret": {"type": "string"}, "symbols": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "rate_limit_ms": {"type": "integer", "minimum": 100}, "timeout_ms": {"type": "integer", "minimum": 1000}}}}, "trading": {"type": "object", "required": ["risk"], "properties": {"risk": {"type": "object", "properties": {"max_position_size": {"type": "number", "minimum": 0.01, "maximum": 1}, "daily_loss_limit": {"type": "number", "maximum": 0}, "take_profit": {"type": "number", "minimum": 0}, "stop_loss": {"type": "number", "maximum": 0}, "max_trades_per_day": {"type": "integer", "minimum": 1}, "cooldown_minutes": {"type": "integer", "minimum": 1}, "max_leverage": {"type": "integer", "minimum": 1}}}, "execution": {"type": "object", "properties": {"slippage": {"type": "number", "minimum": 0}, "fee_multiplier": {"type": "number", "minimum": 1}, "order_timeout_seconds": {"type": "integer", "minimum": 1}, "requote_delay_ms": {"type": "integer", "minimum": 0}, "price_precision": {"type": "integer", "minimum": 0}}}}}, "strategies": {"type": "object", "properties": {"momentum": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "rsi_period": {"type": "integer", "minimum": 1}, "rsi_overbought": {"type": "number", "minimum": 50, "maximum": 90}, "rsi_oversold": {"type": "number", "minimum": 10, "maximum": 50}, "volume_threshold": {"type": "number", "minimum": 0}, "min_trade_size": {"type": "number", "minimum": 0}, "cooloff_period": {"type": "integer", "minimum": 0}}}, "mean_reversion": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "z_score_window": {"type": "integer", "minimum": 1}, "entry_z": {"type": "number", "minimum": 0}, "exit_z": {"type": "number", "minimum": 0}, "lookback_period": {"type": "integer", "minimum": 1}, "confirmation_bars": {"type": "integer", "minimum": 1}}}}}, "monitoring": {"type": "object", "properties": {"telegram": {"type": "object", "properties": {"bot_token": {"type": "string"}, "chat_id": {"type": "string"}, "alerts_enabled": {"type": "boolean"}, "trade_notifications": {"type": "boolean"}}}, "healthchecks": {"type": "object", "properties": {"interval_minutes": {"type": "integer", "minimum": 1}, "webhook_url": {"type": "string"}, "timeout_seconds": {"type": "integer", "minimum": 1}}}}}, "logging": {"type": "object", "properties": {"level": {"type": "string", "enum": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]}, "file_path": {"type": "string"}, "max_size_mb": {"type": "integer", "minimum": 1}, "backup_count": {"type": "integer", "minimum": 0}, "compress_backups": {"type": "boolean"}, "stdout": {"type": "boolean"}}}, "database": {"type": "object", "properties": {"path": {"type": "string"}, "type": {"type": "string", "enum": ["sqlite", "postgres"]}, "backup": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "interval_hours": {"type": "integer", "minimum": 1}, "max_backups": {"type": "integer", "minimum": 0}, "cloud_sync": {"type": "boolean"}}}}}, "system": {"type": "object", "properties": {"heartbeat_interval": {"type": "integer", "minimum": 1}, "memory_limit_mb": {"type": "integer", "minimum": 100}, "auto_restart": {"type": "boolean"}, "timezone": {"type": "string"}}}}}