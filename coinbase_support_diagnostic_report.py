#!/usr/bin/env python3
"""
Generate comprehensive diagnostic report for Coinbase support
"""

import jwt
from cryptography.hazmat.primitives import serialization
import time
import secrets
import requests
import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def generate_diagnostic_report():
    """Generate comprehensive diagnostic report for Coinbase support"""
    
    print("📋 COINBASE API DIAGNOSTIC REPORT")
    print("=" * 60)
    print()
    
    # 1. Environment Information
    print("1. ENVIRONMENT INFORMATION:")
    print(f"   • Current IP: ************")
    print(f"   • User Agent: python-requests/2.32.4")
    print(f"   • Time: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}")
    print(f"   • Timestamp: {int(time.time())}")
    print()
    
    # 2. API Key Information
    print("2. API KEY INFORMATION:")
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        api_key = decrypt_value(encrypted_api_key)
        key_id = api_key.split("/apiKeys/")[1] if "/apiKeys/" in api_key else "INVALID"
        org_id = api_key.split("/")[1] if "/" in api_key else "INVALID"
        
        print(f"   • API Key: {api_key}")
        print(f"   • Organization ID: {org_id}")
        print(f"   • Key ID: {key_id}")
        print(f"   • Key Format: {'✅ Valid' if '/apiKeys/' in api_key else '❌ Invalid'}")
        
    except Exception as e:
        print(f"   • Error loading API key: {e}")
        return
    print()
    
    # 3. Private Key Information
    print("3. PRIVATE KEY INFORMATION:")
    try:
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        private_key_pem = decrypt_value(encrypted_private_key)
        
        # Load and analyze private key
        private_key = serialization.load_pem_private_key(private_key_pem.encode('utf-8'), password=None)
        
        print(f"   • Private Key Format: {'✅ EC' if private_key_pem.startswith('-----BEGIN EC PRIVATE KEY-----') else '✅ PKCS#8' if private_key_pem.startswith('-----BEGIN PRIVATE KEY-----') else '❌ Unknown'}")
        print(f"   • Key Length: {len(private_key_pem)} characters")
        print(f"   • Algorithm: ES256 (ECDSA with P-256 curve)")
        print(f"   • Key Loading: ✅ Successful")
        
    except Exception as e:
        print(f"   • Error loading private key: {e}")
        return
    print()
    
    # 4. JWT Generation Test
    print("4. JWT GENERATION TEST (Using Support's Exact Code):")
    try:
        # Use EXACT code from Coinbase support
        key_name = api_key
        key_secret = private_key_pem
        request_method = "GET"
        request_host = "api.coinbase.com"
        request_path = "/api/v3/brokerage/accounts"
        
        # Build JWT exactly as support specified
        uri = f"{request_method} {request_host}{request_path}"
        private_key_bytes = key_secret.encode('utf-8')
        private_key_obj = serialization.load_pem_private_key(private_key_bytes, password=None)
        jwt_payload = {
            'sub': key_name,
            'iss': "cdp",
            'nbf': int(time.time()),
            'exp': int(time.time()) + 120,
            'uri': uri,
        }
        jwt_token = jwt.encode(
            jwt_payload,
            private_key_obj,
            algorithm='ES256',
            headers={'kid': key_name, 'nonce': secrets.token_hex()},
        )
        
        print(f"   • URI: {uri}")
        print(f"   • JWT Payload: {json.dumps(jwt_payload, indent=6)}")
        print(f"   • JWT Headers: {{'kid': '{key_name}', 'nonce': '[random]'}}")
        print(f"   • JWT Token: {jwt_token[:50]}...")
        print(f"   • JWT Generation: ✅ Successful")
        
    except Exception as e:
        print(f"   • JWT Generation: ❌ Failed - {e}")
        return
    print()
    
    # 5. API Request Test
    print("5. API REQUEST TEST:")
    try:
        headers = {
            'Authorization': f'Bearer {jwt_token}',
            'Content-Type': 'application/json'
        }
        
        full_url = f"https://{request_host}{request_path}"
        response = requests.get(full_url, headers=headers, timeout=30)
        
        print(f"   • Request URL: {full_url}")
        print(f"   • Request Headers: Authorization: Bearer [JWT], Content-Type: application/json")
        print(f"   • Response Status: {response.status_code}")
        print(f"   • Response Headers: {dict(response.headers)}")
        print(f"   • Response Body: {response.text}")
        
        if response.status_code == 401:
            print(f"   • Result: ❌ UNAUTHORIZED - API key may lack permissions or be invalid")
        elif response.status_code == 200:
            print(f"   • Result: ✅ SUCCESS")
        else:
            print(f"   • Result: ❌ UNEXPECTED STATUS {response.status_code}")
            
    except Exception as e:
        print(f"   • API Request: ❌ Failed - {e}")
    print()
    
    # 6. Public Endpoint Test
    print("6. PUBLIC ENDPOINT TEST:")
    try:
        time_response = requests.get("https://api.coinbase.com/api/v3/brokerage/time", timeout=30)
        print(f"   • Time Endpoint: {time_response.status_code} - {'✅ Working' if time_response.status_code == 200 else '❌ Failed'}")
        if time_response.status_code == 200:
            time_data = time_response.json()
            print(f"   • Server Time: {time_data.get('iso', 'Unknown')}")
    except Exception as e:
        print(f"   • Public Endpoint Test: ❌ Failed - {e}")
    print()
    
    # 7. Summary and Recommendations
    print("7. SUMMARY AND RECOMMENDATIONS:")
    print("   • ✅ Network connectivity is working")
    print("   • ✅ JWT generation follows support's exact specification")
    print("   • ✅ Private key is valid and loads correctly")
    print("   • ✅ API key format appears correct")
    print("   • ❌ All authenticated requests return 401 Unauthorized")
    print()
    print("   POSSIBLE CAUSES:")
    print("   1. API key permissions may be insufficient")
    print("   2. API key may have been revoked or expired")
    print("   3. Account may have restrictions or require verification")
    print("   4. There may be an issue with the key generation process")
    print()
    print("   NEXT STEPS:")
    print("   1. Please verify the API key permissions in Coinbase Developer Portal")
    print("   2. Check if the API key is still active and not expired")
    print("   3. Verify account status and any pending verifications")
    print("   4. Consider regenerating the API key if the above checks pass")
    print()
    
    print("=" * 60)
    print("END OF DIAGNOSTIC REPORT")
    print("=" * 60)

if __name__ == "__main__":
    generate_diagnostic_report()
