# src/monitoring/hardware_protector.py
import psutil
import platform
import logging
from time import sleep
import wmi

def get_cpu_temperature():
    """Robust Windows temperature checking"""
    try:
        if platform.system() == 'Windows':
            import wmi
            w = wmi.WMI(namespace="root\\wmi")
            temp_info = w.MSAcpi_ThermalZoneTemperature()[0]
            return (temp_info.CurrentTemperature / 10) - 273.15
        else:
            temps = psutil.sensors_temperatures()
            return temps['coretemp'][0].current
    except Exception as e:
        logging.debug(f"Temperature monitoring disabled: {str(e)}")
        return None

def block_ports(ports):
    """Windows-specific port blocking using firewall"""
    import subprocess
    for port in ports:
        try:
            subprocess.run([
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                f'name="Block Port {port}"',
                'dir=in',
                'action=block',
                f'protocol=TCP',
                f'localport={port}'
            ], check=True)
            logging.info(f"Blocked TCP port {port}")
        except subprocess.CalledProcessError as e:
            logging.error(f"Failed blocking port {port}: {str(e)}")

def get_system_stats():
    """Safe metric collection for all platforms"""
    stats = {
        'cpu': psutil.cpu_percent(interval=1),  # More accurate measurement
        'memory': psutil.virtual_memory().percent,
        'disk': psutil.disk_usage('/').percent
    }
    
    # Platform-specific temperature handling
    try:
        if platform.system() == 'Windows':
            from wmi import WMI
            w = WMI(namespace='root\\wmi')
            stats['temperature'] = w.MSAcpi_ThermalZoneTemperature()[0].CurrentTemperature / 10 - 273.15
        else:
            temps = psutil.sensors_temperatures()
            stats['temperature'] = temps['coretemp'][0].current
    except Exception as e:
        logging.warning(f"Temp monitoring unavailable: {str(e)}")
        stats['temperature'] = None
    
    return stats

def check_ports():
    """Windows-compatible port check"""
    try:
        connections = psutil.net_connections()
        allowed_ports = {6379, 8200, 3000}
        open_ports = {conn.laddr.port for conn in connections if conn.status == 'LISTEN'}
        
        if unauthorized := open_ports - allowed_ports:
            logging.critical(f"Blocked unauthorized ports: {unauthorized}")
            block_ports(unauthorized)
    except Exception as e:
        logging.error(f"Port check failed: {str(e)}")

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'  # Fixed date format
    )
    
    while True:
        stats = get_system_stats()
        logging.info(f"System stats: {stats}")
        
        if stats['cpu'] > 90:
            logging.warning("High CPU usage! Throttling non-critical processes...")
            throttle_processes()
        
        check_ports()
        sleep(30)