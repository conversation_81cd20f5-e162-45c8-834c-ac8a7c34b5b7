#!/usr/bin/env python3
"""
Coinbase API Diagnostic Tool
Comprehensive diagnosis of Coinbase API authentication issues
"""

import os
import sys
import logging
import requests
import time
import jwt
from pathlib import Path
from cryptography.hazmat.primitives import serialization

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("coinbase_diagnostic")

def load_environment():
    """Load environment variables"""
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        logger.info(f"✅ Loaded environment from {env_file}")

def analyze_api_key_format():
    """Analyze the API key format"""
    logger.info("🔍 [DIAGNOSTIC] Analyzing API key format...")
    
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        if not encrypted_api_key:
            logger.error("❌ No encrypted API key found")
            return False
        
        api_key = decrypt_value(encrypted_api_key)
        logger.info(f"📋 API Key: {api_key}")
        
        # Analyze format
        if api_key.startswith("organizations/"):
            parts = api_key.split("/")
            if len(parts) >= 4:
                org_id = parts[1]
                key_id = parts[3] if len(parts) > 3 else "MISSING"
                logger.info(f"✅ Organization ID: {org_id}")
                logger.info(f"✅ Key ID: {key_id}")
                
                # Check if this matches CDP format
                if "apiKeys" in api_key:
                    logger.info("✅ API key is in correct CDP format")
                    return True, org_id, key_id
                else:
                    logger.warning("⚠️ API key missing 'apiKeys' component")
                    return False, org_id, key_id
            else:
                logger.error("❌ API key format is incomplete")
                return False, None, None
        else:
            logger.error("❌ API key does not start with 'organizations/'")
            return False, None, None
            
    except Exception as e:
        logger.error(f"❌ API key analysis failed: {e}")
        return False, None, None

def analyze_private_key():
    """Analyze the private key"""
    logger.info("🔍 [DIAGNOSTIC] Analyzing private key...")
    
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        if not encrypted_private_key:
            logger.error("❌ No encrypted private key found")
            return False
        
        private_key_pem = decrypt_value(encrypted_private_key)
        
        # Check PEM format
        if private_key_pem.startswith("-----BEGIN EC PRIVATE KEY-----"):
            logger.info("✅ Private key is in EC format")
        elif private_key_pem.startswith("-----BEGIN PRIVATE KEY-----"):
            logger.info("✅ Private key is in PKCS#8 format")
        else:
            logger.error("❌ Private key format not recognized")
            return False
        
        # Try to load the key
        try:
            private_key = serialization.load_pem_private_key(
                private_key_pem.encode('utf-8'),
                password=None
            )
            logger.info("✅ Private key loaded successfully")
            
            # Check key type
            key_type = type(private_key).__name__
            logger.info(f"📋 Key type: {key_type}")
            
            if "EC" in key_type or "Elliptic" in key_type:
                logger.info("✅ Key is elliptic curve (required for Coinbase)")
                return True
            else:
                logger.warning(f"⚠️ Key type {key_type} may not be compatible")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to load private key: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Private key analysis failed: {e}")
        return False

def test_jwt_generation():
    """Test JWT token generation"""
    logger.info("🔍 [DIAGNOSTIC] Testing JWT generation...")
    
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        # Get credentials
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        api_key = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)
        
        # Extract key ID from API key
        if "/apiKeys/" in api_key:
            key_id = api_key.split("/apiKeys/")[1]
        else:
            logger.error("❌ Cannot extract key ID from API key")
            return False
        
        # Load private key
        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )
        
        # Create JWT
        now = int(time.time())
        payload = {
            'iss': 'cdp',
            'nbf': now,
            'exp': now + 120,  # 2 minutes
            'sub': api_key,
            'uri': 'GET /api/v1/portfolios'
        }
        
        token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': key_id})
        logger.info("✅ JWT token generated successfully")
        logger.info(f"📋 Token length: {len(token)} characters")
        
        return True, token
        
    except Exception as e:
        logger.error(f"❌ JWT generation failed: {e}")
        return False, None

def test_manual_api_call():
    """Test manual API call with JWT"""
    logger.info("🔍 [DIAGNOSTIC] Testing manual API call...")
    
    try:
        jwt_ok, token = test_jwt_generation()
        if not jwt_ok:
            logger.error("❌ Cannot test API call without valid JWT")
            return False
        
        # Test different endpoints
        endpoints = [
            "https://api.coinbase.com/api/v1/portfolios",
            "https://api.coinbase.com/api/v3/brokerage/accounts",
            "https://api.coinbase.com/api/v3/brokerage/products"
        ]
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        for endpoint in endpoints:
            logger.info(f"🌐 Testing endpoint: {endpoint}")
            try:
                response = requests.get(endpoint, headers=headers, timeout=10)
                logger.info(f"📋 Status: {response.status_code}")
                
                if response.status_code == 200:
                    logger.info("✅ API call successful!")
                    return True
                elif response.status_code == 401:
                    logger.error("❌ 401 Unauthorized")
                    logger.error(f"Response: {response.text[:200]}...")
                elif response.status_code == 403:
                    logger.error("❌ 403 Forbidden - Check permissions")
                else:
                    logger.warning(f"⚠️ Unexpected status: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"❌ Request failed: {e}")
        
        return False
        
    except Exception as e:
        logger.error(f"❌ Manual API test failed: {e}")
        return False

def check_coinbase_status():
    """Check Coinbase API status"""
    logger.info("🔍 [DIAGNOSTIC] Checking Coinbase API status...")
    
    try:
        # Check public status
        response = requests.get("https://status.coinbase.com/api/v2/status.json", timeout=10)
        if response.status_code == 200:
            status_data = response.json()
            status = status_data.get('status', {}).get('indicator', 'unknown')
            logger.info(f"📋 Coinbase Status: {status}")
            
            if status == 'none':
                logger.info("✅ Coinbase services are operational")
                return True
            else:
                logger.warning(f"⚠️ Coinbase may have issues: {status}")
                return False
        else:
            logger.warning("⚠️ Could not check Coinbase status")
            return False
            
    except Exception as e:
        logger.error(f"❌ Status check failed: {e}")
        return False

def main():
    """Main diagnostic function"""
    logger.info("🔧 [COINBASE-DIAGNOSTIC] Starting comprehensive API diagnostic...")
    logger.info("="*80)
    
    # Load environment
    load_environment()
    
    # Test results
    results = {}
    
    # Step 1: API Key Format Analysis
    logger.info("\n" + "="*80)
    logger.info("STEP 1: API Key Format Analysis")
    logger.info("="*80)
    key_ok, org_id, key_id = analyze_api_key_format()
    results['api_key_format'] = key_ok
    
    # Step 2: Private Key Analysis
    logger.info("\n" + "="*80)
    logger.info("STEP 2: Private Key Analysis")
    logger.info("="*80)
    pkey_ok = analyze_private_key()
    results['private_key'] = pkey_ok
    
    # Step 3: JWT Generation Test
    logger.info("\n" + "="*80)
    logger.info("STEP 3: JWT Generation Test")
    logger.info("="*80)
    jwt_ok, token = test_jwt_generation()
    results['jwt_generation'] = jwt_ok
    
    # Step 4: Manual API Call Test
    logger.info("\n" + "="*80)
    logger.info("STEP 4: Manual API Call Test")
    logger.info("="*80)
    api_ok = test_manual_api_call()
    results['api_call'] = api_ok
    
    # Step 5: Coinbase Status Check
    logger.info("\n" + "="*80)
    logger.info("STEP 5: Coinbase Status Check")
    logger.info("="*80)
    status_ok = check_coinbase_status()
    results['coinbase_status'] = status_ok
    
    # Summary
    logger.info("\n" + "="*80)
    logger.info("📊 DIAGNOSTIC SUMMARY")
    logger.info("="*80)
    logger.info(f"🔑 API Key Format: {'✅ OK' if results['api_key_format'] else '❌ ISSUE'}")
    logger.info(f"🔐 Private Key: {'✅ OK' if results['private_key'] else '❌ ISSUE'}")
    logger.info(f"🎫 JWT Generation: {'✅ OK' if results['jwt_generation'] else '❌ ISSUE'}")
    logger.info(f"🌐 API Call: {'✅ OK' if results['api_call'] else '❌ ISSUE'}")
    logger.info(f"📡 Coinbase Status: {'✅ OK' if results['coinbase_status'] else '❌ ISSUE'}")
    
    # Recommendations
    logger.info("\n" + "="*80)
    logger.info("💡 RECOMMENDATIONS")
    logger.info("="*80)
    
    if not results['api_key_format']:
        print("🔧 ISSUE: API key format is incorrect")
        print("   → Regenerate API key at https://cloud.coinbase.com/access/api")
        print("   → Ensure you're using CDP (not legacy) API keys")
    
    if not results['private_key']:
        print("🔧 ISSUE: Private key format or type is incorrect")
        print("   → Ensure private key is EC (Elliptic Curve) format")
        print("   → Regenerate key pair if necessary")
    
    if not results['jwt_generation']:
        print("🔧 ISSUE: JWT token generation failed")
        print("   → Check private key compatibility")
        print("   → Verify key ID extraction from API key")
    
    if not results['api_call']:
        print("🔧 ISSUE: API authentication failed")
        print("   → Check API key permissions in Coinbase Developer Console")
        print("   → Verify IP restrictions (current IP: ************)")
        print("   → Consider regenerating API key")
    
    if not results['coinbase_status']:
        print("🔧 ISSUE: Coinbase services may be experiencing issues")
        print("   → Check https://status.coinbase.com")
        print("   → Wait for service restoration")
    
    # Overall result
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    if passed_tests == total_tests:
        print("\n🎉 ALL DIAGNOSTICS PASSED!")
        print("✅ API should be working correctly")
        return True
    else:
        print(f"\n❌ DIAGNOSTICS FAILED: {passed_tests}/{total_tests} tests passed")
        print("🔧 Follow the recommendations above to resolve issues")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
