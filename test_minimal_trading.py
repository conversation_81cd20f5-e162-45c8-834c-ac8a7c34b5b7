#!/usr/bin/env python3
"""
Minimal trading system test to identify where main.py hangs
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_minimal_trading():
    """Test minimal trading system initialization"""
    try:
        print("🔧 [TEST] Starting minimal trading test...")
        
        # Test 1: Basic imports
        print("📦 [TEST] Testing imports...")
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        print("✅ [TEST] Imports successful")
        
        # Test 2: Credential check
        print("🔐 [TEST] Checking credentials...")
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            print("❌ [TEST] Missing Bybit credentials")
            return False
        
        print("✅ [TEST] Credentials available")
        
        # Test 3: Bybit client initialization
        print("🔧 [TEST] Initializing Bybit client...")
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        print("✅ [TEST] Bybit client created")
        
        # Test 4: Basic balance check
        print("💰 [TEST] Testing balance check...")
        try:
            usdt_balance = await bybit_client.get_balance('USDT')
            print(f"✅ [TEST] USDT balance: {usdt_balance}")
        except Exception as e:
            print(f"⚠️ [TEST] Balance check warning: {e}")
        
        # Test 5: Trading engine initialization
        print("🚀 [TEST] Initializing trading engine...")
        exchange_clients = {'bybit': bybit_client}
        
        trading_system = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={
                'min_usdt_threshold': 10.0,
                'aggressive_trading': True,
                'micro_trading': True,
                'confidence_threshold': 0.60,
                'max_balance_usage': 0.85,
                'enable_balance_validation': True,
                'fail_fast_on_insufficient': True,
                'no_cached_balances': True,
                'enable_dynamic_discovery': True,
                'auto_discover_pairs': True,
                'enable_arbitrage': True,
                'enable_rebalancing': True,
                'enable_advanced_orders': True,
                'enable_liquidity_mgmt': True,
                'enable_capital_management': True,
                'coinbase_wallet_address': '******************************************',
                'profit_split_ratio': 0.5,
                'enable_error_recovery': True,
                'never_halt_trading': True,
                'max_consecutive_errors': 10,
                'error_cooldown_period': 300,
                'scan_interval': 30,
                'execution_timeout': 10,
                'balance_refresh_interval': 60,
                'max_concurrent_trades': 3,
            }
        )
        print("✅ [TEST] Trading engine created")
        
        # Test 6: Trading engine initialization (this might be where it hangs)
        print("🔧 [TEST] Initializing trading engine components...")
        await trading_system.initialize()
        print("✅ [TEST] Trading engine initialized successfully")
        
        # Test 7: Quick trading cycle test
        print("🔄 [TEST] Testing one trading cycle...")
        await trading_system.execute_single_trading_cycle()
        print("✅ [TEST] Trading cycle completed")
        
        print("🎉 [TEST] All tests passed - system is working!")
        return True
        
    except Exception as e:
        print(f"❌ [TEST] Error in minimal trading test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_minimal_trading())
    sys.exit(0 if success else 1)
