# ===============================
# Purge.ps1 - Full Clean Python Setup
# Target: E:\$Root\pip
# User: Mijnheer <PERSON>
# ===============================

Write-Host "`n--- Starting Purge.ps1 Automation ---`n" -ForegroundColor Cyan

# 1. Create E:\$Root\pip if missing
$customPipPath = "E:\$Root\pip"
if (!(Test-Path $customPipPath)) {
    Write-Host "Creating folder: $customPipPath"
    New-Item -ItemType Directory -Path $customPipPath | Out-Null
} else {
    Write-Host "Folder exists: $customPipPath"
}

# 2. Create sitecustomize.py to clean sys.path
$siteCustomizePath = Join-Path $customPipPath "sitecustomize.py"
$siteCustomizeContent = @"
import sys

CUSTOM_PATH = r"E:\$Root\pip"

# Purge ALL Anaconda paths from sys.path
sys.path = [p for p in sys.path if "anaconda3" not in p.lower()]

# Inject custom path
if CUSTOM_PATH not in sys.path:
    sys.path.insert(0, CUSTOM_PATH)

print(f"[BOOTSTRAP] sys.path cleaned & enforced: {sys.path}")
"@
Set-Content -Path $siteCustomizePath -Value $siteCustomizeContent -Encoding UTF8
Write-Host "Written sitecustomize.py to: $siteCustomizePath"

# 3. Set PIP_TARGET & PYTHONPATH Environment Variables (User-level)
[System.Environment]::SetEnvironmentVariable("PIP_TARGET", $customPipPath, "User")
[System.Environment]::SetEnvironmentVariable("PYTHONPATH", $customPipPath, "User")
Write-Host "Environment variables set: PIP_TARGET & PYTHONPATH → $customPipPath"

# 4. Patch PowerShell profile with cleanpy function
$profilePath = $PROFILE
if (!(Test-Path $profilePath)) {
    Write-Host "Creating PowerShell profile: $profilePath"
    New-Item -ItemType File -Path $profilePath -Force | Out-Null
}
$cleanpyFunction = @"
function cleanpy { 
    Remove-Item Env:CONDA_PREFIX -ErrorAction SilentlyContinue
    Remove-Item Env:CONDA_DEFAULT_ENV -ErrorAction SilentlyContinue
    Remove-Item Env:PYTHONHOME -ErrorAction SilentlyContinue
    \$env:PIP_TARGET = "$customPipPath"
    \$env:PYTHONPATH = "$customPipPath"
    python \$args
}
"@
Add-Content -Path $profilePath -Value $cleanpyFunction
Write-Host "Added cleanpy function to profile: $profilePath"

# 5. Reload profile in current session
. $PROFILE
Write-Host "PowerShell profile reloaded."

# 6. Patch VSCode settings.json for terminal environments
$vsCodeSettingsPath = "$PWD\.vscode\settings.json"
if (!(Test-Path $vsCodeSettingsPath)) {
    Write-Host "Creating VSCode settings.json"
    New-Item -ItemType File -Path $vsCodeSettingsPath -Force | Out-Null
}
$settingsContent = @"
{
    "terminal.integrated.env.windows": {
        "PIP_TARGET": "E:\\$Root\\pip",
        "PYTHONPATH": "E:\\$Root\\pip"
    }
}
"@
Set-Content -Path $vsCodeSettingsPath -Value $settingsContent -Encoding UTF8
Write-Host "VSCode terminal environment set in: $vsCodeSettingsPath"

# 7. Final Test: sys.path verification
Write-Host "`n--- Verifying sys.path ---" -ForegroundColor Yellow
python -c "import sys; print(sys.path)"

Write-Host "`n--- Purge.ps1 Complete ---" -ForegroundColor Green
