#!/usr/bin/env python3
"""
Debug script to check Bybit API response structure
"""
import asyncio
import sys
import os
import json
sys.path.append('.')

from main import setup_credentials

async def debug_bybit_api():
    print('🔍 Debugging Bybit API response structure...')
    
    # Setup credentials
    setup_credentials()
    
    try:
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if bybit_api_key and bybit_api_secret:
            bybit_client = BybitClientFixed(
                api_key=bybit_api_key,
                api_secret=bybit_api_secret,
                testnet=False
            )
            
            print('✅ Bybit client initialized')
            
            # Test with one symbol first
            symbol = 'BTCUSDT'
            print(f'\n🔍 Testing {symbol}...')
            
            # Get instrument info
            instrument_info = bybit_client.get_instruments_info(symbol)
            
            print(f'\n📊 Raw API Response for {symbol}:')
            print(json.dumps(instrument_info, indent=2))
            
            # Try to extract data
            if instrument_info and not instrument_info.get('error'):
                instruments = instrument_info.get('list', [])
                if instruments:
                    instrument = instruments[0]
                    print(f'\n📋 First instrument data:')
                    print(json.dumps(instrument, indent=2))
                    
                    # Check all possible minimum-related fields
                    print(f'\n🔍 Checking minimum-related fields:')
                    for key, value in instrument.items():
                        if 'min' in key.lower() or 'qty' in key.lower() or 'amt' in key.lower():
                            print(f'  {key}: {value}')
                else:
                    print('❌ No instruments in list')
            else:
                print(f'❌ Error or no data: {instrument_info}')
                
        else:
            print('❌ Bybit credentials not found')
            
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_bybit_api())
