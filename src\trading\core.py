import os
import hvac
import ccxt
import aiohttp
import logging
from typing import Dict, Any, Optional
from decimal import Decimal
from hvac.exceptions import VaultError
from functools import wraps
from retry import retry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("LiveTrader")

class VaultManager:
    """Secure Vault connection manager with token renewal"""
    
    def __init__(self):
        self.client = self._authenticate()
        self.token_ttl = 3600  # 1 hour
        self._secrets_cache = {}

    def _authenticate(self):
        """Authenticate using AppRole for better security"""
        client = hvac.Client(url=os.getenv('VAULT_ADDR'))
        
        role_id = os.getenv('VAULT_ROLE_ID')
        secret_id = os.getenv('VAULT_SECRET_ID')
        
        auth = client.auth.approle.login(
            role_id=role_id,
            secret_id=secret_id,
            mount_point='approle'
        )
        
        if not auth or not auth.get('auth'):
            raise VaultError("AppRole authentication failed")
            
        client.token = auth['auth']['client_token']
        return client

    @retry(tries=3, delay=2, exceptions=(VaultError,))
    def get_secret(self, path: str, mount_point: str = 'kv') -> Dict[str, Any]:
        """Secure secret retrieval with caching and retry logic"""
        if path in self._secrets_cache:
            return self._secrets_cache[path]
            
        try:
            response = self.client.secrets.kv.v2.read_secret_version(
                path=path,
                mount_point=mount_point
            )
            
            if not response or 'data' not in response:
                raise VaultError(f"Invalid response for secret: {path}")
                
            secret_data = response['data']['data']
            self._secrets_cache[path] = secret_data
            return secret_data
            
        except VaultError as ve:
            logger.error(f"Vault operation failed: {ve}")
            raise
        except Exception as e:
            logger.critical(f"Unexpected error: {e}")
            raise

class LiveCryptoTrader:
    def __init__(self, exchange_id: str):
        self.vault = VaultManager()
        self.exchange_id = exchange_id
        self.exchange = self._init_exchange()
        self.hsm = self._init_hsm()
        self.session = aiohttp.ClientSession()
        self.safety = TradingSafetyManager(self.vault)
        self._verify_environment()

    def _init_hsm(self):
        """Initialize HSM with Vault-managed credentials"""
        hsm_creds = self.vault.get_secret('hsm/creds/production')
        return CloudHSMClient(
            endpoint=hsm_creds['endpoint'],
            api_key=hsm_creds['api_key'],
            rate_limit=100  # req/sec
        )

    def _init_exchange(self) -> ccxt.Exchange:
        """Secure exchange initialization with HSM-backed signing"""
        secrets = self.vault.get_secret(f'exchanges/{self.exchange_id}')
        
        exchange = getattr(ccxt, self.exchange_id)({
            'apiKey': secrets['api_key'],
            'secret': secrets['api_secret'],
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future',
                'adjustForTimeDifference': True,
                'whitelistedIps': self._get_approved_ips(),
                'hmacSigner': self._hsm_signer
            }
        })
        
        exchange.load_markets()
        return exchange

    async def _hsm_signer(self, endpoint: str, data: str, headers: dict):
        """HSM-backed request signing with nonce validation"""
        nonce = str(ccxt.milliseconds())
        signature_payload = f"{endpoint}{data}{nonce}"
        
        try:
            signature = self.hsm.sign(
                data=signature_payload,
                key_id='exchange_signing_key'
            )
            headers.update({
                'X-Signature': signature,
                'X-Nonce': nonce
            })
            return headers
        except HSMError as e:
            logger.error(f"HSM signing failed: {e}")
            raise

    def _verify_environment(self):
        """Pre-flight safety checks"""
        if not os.getenv('TRADING_ENABLED'):
            raise EnvironmentError("Trading disabled by environment config")
            
        if self.exchange.has['testnet']:
            logger.warning("Running in testnet mode - no real funds at risk")

class TradingSafetyManager:
    """Real-time risk management with dynamic Vault configuration"""
    
    def __init__(self, vault: VaultManager):
        self.vault = vault
        self.circuit_breaker = False
        self._load_safety_config()
        
    def _load_safety_config(self):
        """Load dynamic safety parameters with fallback defaults"""
        try:
            config = self.vault.get_secret('trading/safety')
            self.max_drawdown = Decimal(config.get('max_drawdown', '0.05'))
            self.max_position_size = Decimal(config.get('position_size', '0.02'))
            self.daily_loss_limit = Decimal(config.get('daily_loss', '0.01'))
        except VaultError:
            logger.warning("Using default safety parameters")
            self.max_drawdown = Decimal('0.05')
            self.max_position_size = Decimal('0.02')
            self.daily_loss_limit = Decimal('0.01')

    def check_order(self, order: Dict) -> bool:
        """Pre-trade risk validation"""
        if self.circuit_breaker:
            return False
            
        return all([
            self._validate_size(order),
            self._validate_drawdown(),
            self._validate_daily_loss()
        ])

    def _validate_size(self, order: Dict) -> bool:
        """Position size validation"""
        size = Decimal(order['amount'])
        balance = Decimal(self.get_available_balance())
        return size <= balance * self.max_position_size

    def trigger_circuit_breaker(self):
        """Global trading halt mechanism"""
        try:
            self.vault.write_secret(
                path='trading/status',
                data={'status': 'halted'},
                mount_point='system'
            )
            self.circuit_breaker = True
            logger.critical("Trading halted via circuit breaker")
        except VaultError as e:
            logger.error(f"Failed to update trading status: {e}")
            # Fallback to local disable
            self.circuit_breaker = True

class EnhancedSecurity:
    """Additional security layer for regulatory compliance"""
    
    def __init__(self, trader: LiveCryptoTrader):
        self.trader = trader
        self.activity_log = []
        
    def audit_trade(self, trade: Dict):
        """Regulatory audit logging"""
        self.activity_log.append({
            'timestamp': ccxt.milliseconds(),
            'action': 'trade',
            'details': trade,
            'signature': self._sign_audit_entry(trade)
        })
        
    def _sign_audit_entry(self, data: Dict) -> str:
        """HSM-signed audit trail"""
        return self.trader.hsm.sign(
            data=str(data),
            key_id='audit_signing_key'
        )

# Usage example
if __name__ == "__main__":
    trader = LiveCryptoTrader('binance')
    safety = trader.safety
    
    try:
        while not safety.circuit_breaker:
            # Trading logic here
            if safety.check_order(current_order):
                trader.execute_order(current_order)
            else:
                logger.warning("Order blocked by safety checks")
                
    except KeyboardInterrupt:
        logger.info("Graceful shutdown initiated")
    finally:
        trader.session.close()