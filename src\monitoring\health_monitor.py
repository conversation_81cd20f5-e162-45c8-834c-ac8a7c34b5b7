#!/usr/bin/env python3
"""
Enterprise-Grade Component Health Monitoring System
Provides comprehensive health monitoring, diagnostics, and automatic recovery
"""

import asyncio
import logging
import time
import psutil
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import json
from pathlib import Path

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """Component health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    FAILED = "failed"
    RECOVERING = "recovering"
    MAINTENANCE = "maintenance"

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class HealthMetric:
    """Individual health metric"""
    name: str
    value: float
    unit: str
    threshold_warning: float
    threshold_critical: float
    timestamp: datetime = field(default_factory=datetime.now)
    status: HealthStatus = HealthStatus.HEALTHY
    
    def update_status(self):
        """Update status based on thresholds"""
        if self.value >= self.threshold_critical:
            self.status = HealthStatus.CRITICAL
        elif self.value >= self.threshold_warning:
            self.status = HealthStatus.WARNING
        else:
            self.status = HealthStatus.HEALTHY

@dataclass
class ComponentHealth:
    """Health information for a component"""
    component_name: str
    status: HealthStatus = HealthStatus.HEALTHY
    last_check: Optional[datetime] = None
    metrics: Dict[str, HealthMetric] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    uptime_start: datetime = field(default_factory=datetime.now)
    last_restart: Optional[datetime] = None
    restart_count: int = 0
    
    # Performance metrics
    response_times: deque = field(default_factory=lambda: deque(maxlen=100))
    success_rate: float = 100.0
    error_rate: float = 0.0
    
    # Resource usage
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    
    def add_metric(self, name: str, value: float, unit: str, 
                   warning_threshold: float, critical_threshold: float):
        """Add or update a health metric"""
        metric = HealthMetric(
            name=name,
            value=value,
            unit=unit,
            threshold_warning=warning_threshold,
            threshold_critical=critical_threshold
        )
        metric.update_status()
        self.metrics[name] = metric
        
        # Update overall status based on worst metric
        if metric.status == HealthStatus.CRITICAL:
            self.status = HealthStatus.CRITICAL
        elif metric.status == HealthStatus.WARNING and self.status == HealthStatus.HEALTHY:
            self.status = HealthStatus.WARNING
    
    def add_error(self, error_message: str):
        """Add an error message"""
        self.errors.append(f"{datetime.now().isoformat()}: {error_message}")
        if len(self.errors) > 50:  # Keep only last 50 errors
            self.errors.pop(0)
        self.status = HealthStatus.FAILED
    
    def add_warning(self, warning_message: str):
        """Add a warning message"""
        self.warnings.append(f"{datetime.now().isoformat()}: {warning_message}")
        if len(self.warnings) > 50:  # Keep only last 50 warnings
            self.warnings.pop(0)
        if self.status == HealthStatus.HEALTHY:
            self.status = HealthStatus.WARNING
    
    def get_uptime_seconds(self) -> float:
        """Get uptime in seconds"""
        return (datetime.now() - self.uptime_start).total_seconds()
    
    def record_response_time(self, response_time_ms: float):
        """Record a response time measurement"""
        self.response_times.append(response_time_ms)
    
    def get_avg_response_time(self) -> float:
        """Get average response time"""
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)

@dataclass
class SystemHealth:
    """Overall system health information"""
    status: HealthStatus = HealthStatus.HEALTHY
    components: Dict[str, ComponentHealth] = field(default_factory=dict)
    system_metrics: Dict[str, HealthMetric] = field(default_factory=dict)
    alerts: List[Dict[str, Any]] = field(default_factory=list)
    last_update: datetime = field(default_factory=datetime.now)
    
    def get_healthy_components(self) -> List[str]:
        """Get list of healthy component names"""
        return [name for name, health in self.components.items() 
                if health.status == HealthStatus.HEALTHY]
    
    def get_failed_components(self) -> List[str]:
        """Get list of failed component names"""
        return [name for name, health in self.components.items() 
                if health.status == HealthStatus.FAILED]
    
    def get_critical_components(self) -> List[str]:
        """Get list of critical component names"""
        return [name for name, health in self.components.items() 
                if health.status == HealthStatus.CRITICAL]

class HealthMonitor:
    """
    Enterprise-Grade Component Health Monitoring System
    
    Features:
    - Real-time component health monitoring
    - Automatic recovery mechanisms
    - Performance metrics tracking
    - Alert generation and notification
    - System resource monitoring
    - Detailed diagnostics and reporting
    """
    
    def __init__(self, check_interval: int = 30):
        self.check_interval = check_interval
        self.system_health = SystemHealth()
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Recovery mechanisms
        self.recovery_handlers: Dict[str, Callable] = {}
        self.auto_recovery_enabled = True
        self.max_recovery_attempts = 3
        self.recovery_attempts: Dict[str, int] = defaultdict(int)
        
        # Alert configuration
        self.alert_handlers: List[Callable] = []
        self.alert_cooldown: Dict[str, datetime] = {}
        self.alert_cooldown_period = timedelta(minutes=5)
        
        # Performance tracking
        self.performance_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # System thresholds
        self.system_thresholds = {
            'cpu_usage': {'warning': 80.0, 'critical': 95.0},
            'memory_usage': {'warning': 85.0, 'critical': 95.0},
            'disk_usage': {'warning': 85.0, 'critical': 95.0},
            'response_time': {'warning': 5000.0, 'critical': 10000.0}  # milliseconds
        }
        
        logger.info("🏥 [HEALTH] Enterprise health monitoring system initialized")
    
    async def start_monitoring(self) -> bool:
        """Start the health monitoring system"""
        try:
            if self.monitoring_active:
                logger.warning("🏥 [HEALTH] Monitoring already active")
                return True
            
            self.monitoring_active = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info("🏥 [HEALTH] Health monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"❌ [HEALTH] Failed to start monitoring: {e}")
            return False
    
    async def stop_monitoring(self):
        """Stop the health monitoring system"""
        try:
            self.monitoring_active = False
            
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
                self.monitoring_task = None
            
            logger.info("🛑 [HEALTH] Health monitoring stopped")
            
        except Exception as e:
            logger.error(f"❌ [HEALTH] Error stopping monitoring: {e}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        try:
            while self.monitoring_active:
                try:
                    # Update system health
                    await self._update_system_health()
                    
                    # Check component health
                    await self._check_component_health()
                    
                    # Check for alerts
                    await self._check_alerts()
                    
                    # Attempt automatic recovery
                    if self.auto_recovery_enabled:
                        await self._attempt_recovery()
                    
                    # Update performance history
                    self._update_performance_history()
                    
                    # Sleep until next check
                    await asyncio.sleep(self.check_interval)
                    
                except Exception as e:
                    logger.error(f"❌ [HEALTH] Error in monitoring loop: {e}")
                    await asyncio.sleep(10)  # Shorter sleep on error
                    
        except asyncio.CancelledError:
            logger.debug("🏥 [HEALTH] Monitoring loop cancelled")
        except Exception as e:
            logger.error(f"❌ [HEALTH] Monitoring loop failed: {e}")
    
    async def _update_system_health(self):
        """Update system-level health metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.system_health.system_metrics['cpu_usage'] = HealthMetric(
                name='cpu_usage',
                value=cpu_percent,
                unit='%',
                threshold_warning=self.system_thresholds['cpu_usage']['warning'],
                threshold_critical=self.system_thresholds['cpu_usage']['critical']
            )
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.system_health.system_metrics['memory_usage'] = HealthMetric(
                name='memory_usage',
                value=memory.percent,
                unit='%',
                threshold_warning=self.system_thresholds['memory_usage']['warning'],
                threshold_critical=self.system_thresholds['memory_usage']['critical']
            )
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.system_health.system_metrics['disk_usage'] = HealthMetric(
                name='disk_usage',
                value=disk_percent,
                unit='%',
                threshold_warning=self.system_thresholds['disk_usage']['warning'],
                threshold_critical=self.system_thresholds['disk_usage']['critical']
            )
            
            # Update metric statuses
            for metric in self.system_health.system_metrics.values():
                metric.update_status()
            
            # Update overall system status
            self._update_overall_system_status()
            
            self.system_health.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ [HEALTH] Error updating system health: {e}")
    
    def _update_overall_system_status(self):
        """Update overall system health status"""
        try:
            # Check system metrics
            critical_metrics = [m for m in self.system_health.system_metrics.values() 
                              if m.status == HealthStatus.CRITICAL]
            warning_metrics = [m for m in self.system_health.system_metrics.values() 
                             if m.status == HealthStatus.WARNING]
            
            # Check component health
            failed_components = self.system_health.get_failed_components()
            critical_components = self.system_health.get_critical_components()
            
            # Determine overall status
            if critical_metrics or failed_components:
                self.system_health.status = HealthStatus.CRITICAL
            elif warning_metrics or critical_components:
                self.system_health.status = HealthStatus.WARNING
            else:
                self.system_health.status = HealthStatus.HEALTHY
                
        except Exception as e:
            logger.error(f"❌ [HEALTH] Error updating overall status: {e}")
            self.system_health.status = HealthStatus.FAILED

    async def _check_component_health(self):
        """Check health of all registered components"""
        try:
            # This would integrate with the ComponentRegistry
            # For now, we'll check basic component availability
            pass

        except Exception as e:
            logger.error(f"❌ [HEALTH] Error checking component health: {e}")

    async def _check_alerts(self):
        """Check for alert conditions and trigger notifications"""
        try:
            current_time = datetime.now()

            # Check system metric alerts
            for metric_name, metric in self.system_health.system_metrics.items():
                if metric.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
                    alert_key = f"system_{metric_name}_{metric.status.value}"

                    # Check cooldown
                    if self._should_send_alert(alert_key, current_time):
                        await self._send_alert(
                            severity=AlertSeverity.CRITICAL if metric.status == HealthStatus.CRITICAL else AlertSeverity.WARNING,
                            component="system",
                            metric=metric_name,
                            message=f"System {metric_name} is {metric.status.value}: {metric.value}{metric.unit}",
                            value=metric.value,
                            threshold=metric.threshold_critical if metric.status == HealthStatus.CRITICAL else metric.threshold_warning
                        )
                        self.alert_cooldown[alert_key] = current_time

            # Check component alerts
            for component_name, component_health in self.system_health.components.items():
                if component_health.status in [HealthStatus.WARNING, HealthStatus.CRITICAL, HealthStatus.FAILED]:
                    alert_key = f"component_{component_name}_{component_health.status.value}"

                    if self._should_send_alert(alert_key, current_time):
                        await self._send_alert(
                            severity=AlertSeverity.CRITICAL if component_health.status in [HealthStatus.CRITICAL, HealthStatus.FAILED] else AlertSeverity.WARNING,
                            component=component_name,
                            message=f"Component {component_name} is {component_health.status.value}",
                            errors=component_health.errors[-5:] if component_health.errors else []
                        )
                        self.alert_cooldown[alert_key] = current_time

        except Exception as e:
            logger.error(f"❌ [HEALTH] Error checking alerts: {e}")

    def _should_send_alert(self, alert_key: str, current_time: datetime) -> bool:
        """Check if alert should be sent based on cooldown"""
        try:
            last_alert = self.alert_cooldown.get(alert_key)
            if last_alert is None:
                return True

            return current_time - last_alert > self.alert_cooldown_period

        except Exception:
            return True

    async def _send_alert(self, severity: AlertSeverity, component: str,
                         message: str, **kwargs):
        """Send alert to all registered handlers"""
        try:
            alert = {
                'timestamp': datetime.now().isoformat(),
                'severity': severity.value,
                'component': component,
                'message': message,
                **kwargs
            }

            # Add to system alerts
            self.system_health.alerts.append(alert)
            if len(self.system_health.alerts) > 100:  # Keep only last 100 alerts
                self.system_health.alerts.pop(0)

            # Log the alert
            log_level = logging.CRITICAL if severity == AlertSeverity.EMERGENCY else \
                       logging.ERROR if severity == AlertSeverity.CRITICAL else \
                       logging.WARNING
            logger.log(log_level, f"🚨 [HEALTH] {severity.value.upper()}: {message}")

            # Send to alert handlers
            for handler in self.alert_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(alert)
                    else:
                        handler(alert)
                except Exception as e:
                    logger.error(f"❌ [HEALTH] Error in alert handler: {e}")

        except Exception as e:
            logger.error(f"❌ [HEALTH] Error sending alert: {e}")

    async def _attempt_recovery(self):
        """Attempt automatic recovery for failed components"""
        try:
            for component_name, component_health in self.system_health.components.items():
                if component_health.status == HealthStatus.FAILED:
                    # Check if we should attempt recovery
                    if self.recovery_attempts[component_name] < self.max_recovery_attempts:
                        recovery_handler = self.recovery_handlers.get(component_name)

                        if recovery_handler:
                            try:
                                logger.info(f"🔄 [HEALTH] Attempting recovery for {component_name}")
                                component_health.status = HealthStatus.RECOVERING

                                # Attempt recovery
                                if asyncio.iscoroutinefunction(recovery_handler):
                                    success = await recovery_handler()
                                else:
                                    success = recovery_handler()

                                if success:
                                    logger.info(f"✅ [HEALTH] Recovery successful for {component_name}")
                                    component_health.status = HealthStatus.HEALTHY
                                    component_health.last_restart = datetime.now()
                                    component_health.restart_count += 1
                                    self.recovery_attempts[component_name] = 0
                                else:
                                    logger.warning(f"⚠️ [HEALTH] Recovery failed for {component_name}")
                                    component_health.status = HealthStatus.FAILED
                                    self.recovery_attempts[component_name] += 1

                            except Exception as e:
                                logger.error(f"❌ [HEALTH] Recovery error for {component_name}: {e}")
                                component_health.status = HealthStatus.FAILED
                                component_health.add_error(f"Recovery failed: {str(e)}")
                                self.recovery_attempts[component_name] += 1
                        else:
                            logger.debug(f"🔄 [HEALTH] No recovery handler for {component_name}")

        except Exception as e:
            logger.error(f"❌ [HEALTH] Error in recovery attempt: {e}")

    def _update_performance_history(self):
        """Update performance history for trending analysis"""
        try:
            current_time = datetime.now()

            # Store system metrics
            for metric_name, metric in self.system_health.system_metrics.items():
                self.performance_history[f"system_{metric_name}"].append({
                    'timestamp': current_time,
                    'value': metric.value,
                    'status': metric.status.value
                })

            # Store component metrics
            for component_name, component_health in self.system_health.components.items():
                self.performance_history[f"component_{component_name}_status"].append({
                    'timestamp': current_time,
                    'status': component_health.status.value,
                    'error_count': len(component_health.errors),
                    'avg_response_time': component_health.get_avg_response_time()
                })

        except Exception as e:
            logger.error(f"❌ [HEALTH] Error updating performance history: {e}")

    def register_component(self, component_name: str,
                          recovery_handler: Optional[Callable] = None) -> ComponentHealth:
        """Register a component for health monitoring"""
        try:
            component_health = ComponentHealth(component_name=component_name)
            self.system_health.components[component_name] = component_health

            if recovery_handler:
                self.recovery_handlers[component_name] = recovery_handler

            logger.info(f"📋 [HEALTH] Registered component: {component_name}")
            return component_health

        except Exception as e:
            logger.error(f"❌ [HEALTH] Error registering component {component_name}: {e}")
            return ComponentHealth(component_name=component_name)

    def update_component_health(self, component_name: str, status: HealthStatus,
                               error_message: Optional[str] = None,
                               warning_message: Optional[str] = None,
                               response_time_ms: Optional[float] = None):
        """Update health status for a component"""
        try:
            if component_name not in self.system_health.components:
                self.register_component(component_name)

            component_health = self.system_health.components[component_name]
            component_health.status = status
            component_health.last_check = datetime.now()

            if error_message:
                component_health.add_error(error_message)

            if warning_message:
                component_health.add_warning(warning_message)

            if response_time_ms is not None:
                component_health.record_response_time(response_time_ms)

        except Exception as e:
            logger.error(f"❌ [HEALTH] Error updating component health: {e}")

    def add_alert_handler(self, handler: Callable):
        """Add an alert handler function"""
        try:
            self.alert_handlers.append(handler)
            logger.info(f"📢 [HEALTH] Added alert handler: {handler.__name__}")
        except Exception as e:
            logger.error(f"❌ [HEALTH] Error adding alert handler: {e}")

    def get_health_report(self) -> Dict[str, Any]:
        """Get comprehensive health report"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'system_status': self.system_health.status.value,
                'system_metrics': {
                    name: {
                        'value': metric.value,
                        'unit': metric.unit,
                        'status': metric.status.value,
                        'threshold_warning': metric.threshold_warning,
                        'threshold_critical': metric.threshold_critical
                    }
                    for name, metric in self.system_health.system_metrics.items()
                },
                'components': {
                    name: {
                        'status': health.status.value,
                        'uptime_seconds': health.get_uptime_seconds(),
                        'avg_response_time': health.get_avg_response_time(),
                        'error_count': len(health.errors),
                        'warning_count': len(health.warnings),
                        'restart_count': health.restart_count,
                        'last_check': health.last_check.isoformat() if health.last_check else None,
                        'recent_errors': health.errors[-3:] if health.errors else []
                    }
                    for name, health in self.system_health.components.items()
                },
                'summary': {
                    'total_components': len(self.system_health.components),
                    'healthy_components': len(self.system_health.get_healthy_components()),
                    'failed_components': len(self.system_health.get_failed_components()),
                    'critical_components': len(self.system_health.get_critical_components()),
                    'recent_alerts': len([a for a in self.system_health.alerts
                                        if datetime.fromisoformat(a['timestamp']) > datetime.now() - timedelta(hours=1)])
                }
            }

            return report

        except Exception as e:
            logger.error(f"❌ [HEALTH] Error generating health report: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
