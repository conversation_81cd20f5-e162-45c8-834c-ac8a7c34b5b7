#!/usr/bin/env python3
"""
COINBASE CONNECTION TEST
========================

Test the fixed Coinbase credentials and connection.

Author: AutoGPT Trader
Date: June 2025
"""

import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("coinbase_test")

def test_coinbase_connection():
    """Test Coinbase connection with fixed credentials"""
    try:
        # Load credentials
        from credential_decryptor_fixed import setup_credentials
        
        logger.info("🔐 [TEST] Loading encrypted credentials...")
        success = setup_credentials()
        
        if not success:
            logger.error("[ERROR] Failed to load credentials")
            return False
        
        # Get Coinbase credentials
        api_key_name = os.getenv('COINBASE_API_KEY_NAME')
        private_key = os.getenv('COINBASE_PRIVATE_KEY')
        
        if not api_key_name or not private_key:
            logger.error("[ERROR] Coinbase credentials not found in environment")
            return False
        
        logger.info(f"[SUCCESS] API Key: {api_key_name[:50]}...")
        logger.info(f"[SUCCESS] Private Key: {private_key[:50]}...")
        
        # Test Coinbase Advanced API
        try:
            from coinbase.rest import RESTClient
            
            logger.info("🔗 [TEST] Testing Coinbase Advanced API connection...")
            
            # Initialize client
            client = RESTClient(api_key=api_key_name, api_secret=private_key)
            
            # Test public API first
            logger.info("[TEST] Testing public API...")
            products = client.get_products()
            logger.info(f"[SUCCESS] Public API working - found {len(products)} products")
            
            # Test authenticated API
            logger.info("[TEST] Testing authenticated API...")
            accounts = client.get_accounts()
            logger.info(f"[SUCCESS] Authenticated API working - found {len(accounts)} accounts")
            
            # Show account balances
            for account in accounts[:5]:  # Show first 5 accounts
                currency = account.get('currency', 'Unknown')
                balance = account.get('available_balance', {}).get('value', '0')
                logger.info(f"[BALANCE] {currency}: {balance}")
            
            logger.info("✅ [SUCCESS] Coinbase Advanced API connection test passed!")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Coinbase API test failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"[ERROR] Connection test failed: {e}")
        return False

def main():
    """Main function"""
    logger.info("🧪 [COINBASE] Starting connection test...")
    
    success = test_coinbase_connection()
    
    if success:
        print("✅ Coinbase connection test passed!")
        print("🚀 Ready for live trading with Coinbase Advanced API")
        return True
    else:
        print("❌ Coinbase connection test failed")
        print("🔧 Check your API key permissions at https://cloud.coinbase.com/access/api")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
