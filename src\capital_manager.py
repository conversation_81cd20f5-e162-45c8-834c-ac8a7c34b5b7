<<<<<<< Updated upstream
# capital_manager.py
INITIAL_CAPITAL = 0.01  # 1% of total funds
RISK_PER_TRADE = 0.0025 # 0.25% per position

def calculate_position_size():
    balance = exchange.fetch_balance()['free']['USDT']
    return min(
        balance * INITIAL_CAPITAL,
        balance * RISK_PER_TRADE
    )
=======
# Professional-Grade Capital Manager
"""
Advanced capital management system with institutional-level capabilities including:
- Dynamic position sizing based on market conditions
- Risk-adjusted capital allocation
- Real-time portfolio optimization
- Multi-variable capital efficiency monitoring
- Advanced risk management with volatility adjustments
"""

import logging
import asyncio
import time
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timezone
import statistics
import numpy as np

logger = logging.getLogger(__name__)

# Professional-grade capital management constants
class CapitalConstants:
    # Base allocation parameters
    INITIAL_CAPITAL_PCT = Decimal("0.02")  # 2% of total funds for initial positions
    BASE_RISK_PER_TRADE = Decimal("0.005")  # 0.5% base risk per position
    MAX_RISK_PER_TRADE = Decimal("0.025")   # 2.5% maximum risk per position
    MIN_RISK_PER_TRADE = Decimal("0.001")   # 0.1% minimum risk per position

    # Portfolio allocation limits
    MAX_POSITION_SIZE = Decimal("0.25")     # 25% maximum position size
    MIN_POSITION_SIZE = Decimal("0.01")     # 1% minimum position size
    MAX_TOTAL_EXPOSURE = Decimal("0.80")    # 80% maximum total exposure

    # Risk management parameters
    VOLATILITY_ADJUSTMENT_FACTOR = Decimal("2.0")
    CORRELATION_ADJUSTMENT_FACTOR = Decimal("1.5")
    LIQUIDITY_ADJUSTMENT_FACTOR = Decimal("1.2")

    # Rebalancing parameters
    REBALANCE_THRESHOLD = Decimal("0.05")   # 5% deviation triggers rebalancing
    MIN_REBALANCE_AMOUNT = Decimal("10.0")  # $10 minimum for rebalancing
    REBALANCE_COOLDOWN_HOURS = 4            # 4 hours between rebalances

class ProfessionalCapitalManager:
    """
    Professional-grade capital management system with institutional capabilities
    """

    def __init__(self, wallet_manager, profit_tracker, risk_engine=None):
        self.wallet_manager = wallet_manager
        self.profit_tracker = profit_tracker
        self.risk_engine = risk_engine

        # Capital allocation tracking
        self.current_allocations = {}
        self.target_allocations = {}
        self.allocation_history = []

        # Risk management
        self.volatility_cache = {}
        self.correlation_matrix = {}
        self.liquidity_scores = {}

        # Performance tracking
        self.allocation_performance = {}
        self.efficiency_metrics = {}
        self.last_rebalance_time = None

        # Configuration
        self.constants = CapitalConstants()

        logger.info("Professional Capital Manager initialized with institutional capabilities")

    async def calculate_optimal_position_size(self, symbol: str, market_data: Dict,
                                            strategy_confidence: float = 0.5) -> Decimal:
        """
        Calculate optimal position size using multiple risk factors and market conditions
        """
        try:
            # Get base capital allocation
            total_capital = self.profit_tracker.current_total_capital_usd
            base_allocation = total_capital * self.constants.INITIAL_CAPITAL_PCT

            # Calculate risk-adjusted multipliers
            volatility_multiplier = await self._calculate_volatility_adjustment(symbol, market_data)
            liquidity_multiplier = await self._calculate_liquidity_adjustment(symbol, market_data)
            correlation_multiplier = await self._calculate_correlation_adjustment(symbol)
            confidence_multiplier = self._calculate_confidence_adjustment(strategy_confidence)
            market_regime_multiplier = await self._calculate_market_regime_adjustment(market_data)

            # Apply all adjustments
            adjusted_allocation = (
                base_allocation *
                volatility_multiplier *
                liquidity_multiplier *
                correlation_multiplier *
                confidence_multiplier *
                market_regime_multiplier
            )

            # Apply position size limits
            final_position_size = max(
                self.constants.MIN_POSITION_SIZE * total_capital,
                min(
                    self.constants.MAX_POSITION_SIZE * total_capital,
                    adjusted_allocation
                )
            )

            # Convert to percentage of total capital
            position_size_pct = final_position_size / total_capital

            logger.info(f"Optimal position size for {symbol}: {position_size_pct:.2%} (${final_position_size:.2f})")
            logger.debug(f"Multipliers - Vol: {volatility_multiplier:.2f}, Liq: {liquidity_multiplier:.2f}, "
                        f"Corr: {correlation_multiplier:.2f}, Conf: {confidence_multiplier:.2f}, "
                        f"Regime: {market_regime_multiplier:.2f}")

            return position_size_pct

        except Exception as e:
            logger.error(f"Error calculating optimal position size: {e}")
            return self.constants.INITIAL_CAPITAL_PCT

    async def _calculate_volatility_adjustment(self, symbol: str, market_data: Dict) -> Decimal:
        """Calculate position size adjustment based on volatility"""
        try:
            # Get price history
            price_history = market_data.get('price_history', [])
            if len(price_history) < 10:
                return Decimal("1.0")  # No adjustment if insufficient data

            # Calculate returns
            prices = [float(p) for p in price_history[-20:]]  # Last 20 periods
            returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]

            # Calculate volatility (standard deviation of returns)
            if len(returns) > 1:
                volatility = statistics.stdev(returns)

                # Cache volatility
                self.volatility_cache[symbol] = {
                    'value': volatility,
                    'timestamp': time.time()
                }

                # Adjust position size inversely to volatility
                # Higher volatility = smaller position
                volatility_adjustment = 1.0 / (1.0 + volatility * float(self.constants.VOLATILITY_ADJUSTMENT_FACTOR))

                return Decimal(str(max(0.2, min(2.0, volatility_adjustment))))

            return Decimal("1.0")

        except Exception as e:
            logger.error(f"Error calculating volatility adjustment: {e}")
            return Decimal("1.0")

    async def _calculate_liquidity_adjustment(self, symbol: str, market_data: Dict) -> Decimal:
        """Calculate position size adjustment based on liquidity"""
        try:
            # Analyze order book depth
            order_book = market_data.get('order_book', {})
            if not order_book:
                return Decimal("1.0")

            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])

            if not bids or not asks:
                return Decimal("1.0")

            # Calculate liquidity score based on order book depth
            bid_depth = sum(float(bid[1]) for bid in bids[:10])  # Top 10 levels
            ask_depth = sum(float(ask[1]) for ask in asks[:10])
            total_depth = bid_depth + ask_depth

            # Calculate spread
            spread = float(asks[0][0]) - float(bids[0][0])
            mid_price = (float(asks[0][0]) + float(bids[0][0])) / 2
            spread_pct = spread / mid_price if mid_price > 0 else 0.01

            # Liquidity score (higher is better)
            liquidity_score = total_depth / (1 + spread_pct * 100)

            # Cache liquidity score
            self.liquidity_scores[symbol] = {
                'score': liquidity_score,
                'depth': total_depth,
                'spread_pct': spread_pct,
                'timestamp': time.time()
            }

            # Adjust position size based on liquidity
            # Higher liquidity = larger position allowed
            liquidity_adjustment = min(2.0, 0.5 + liquidity_score / 10000)

            return Decimal(str(max(0.3, liquidity_adjustment)))

        except Exception as e:
            logger.error(f"Error calculating liquidity adjustment: {e}")
            return Decimal("1.0")

    async def _calculate_correlation_adjustment(self, symbol: str) -> Decimal:
        """Calculate position size adjustment based on correlation with existing positions"""
        try:
            # Get current positions
            current_positions = await self._get_current_positions()

            if not current_positions or symbol not in current_positions:
                return Decimal("1.0")  # No adjustment if no existing positions

            # Calculate correlation with existing positions
            total_correlation = 0.0
            position_count = 0

            for existing_symbol, position_size in current_positions.items():
                if existing_symbol != symbol:
                    correlation = self._get_symbol_correlation(symbol, existing_symbol)
                    weighted_correlation = correlation * float(position_size)
                    total_correlation += weighted_correlation
                    position_count += 1

            if position_count == 0:
                return Decimal("1.0")

            avg_correlation = total_correlation / position_count

            # Adjust position size inversely to correlation
            # Higher correlation = smaller position to reduce portfolio risk
            correlation_adjustment = 1.0 / (1.0 + abs(avg_correlation) * float(self.constants.CORRELATION_ADJUSTMENT_FACTOR))

            return Decimal(str(max(0.3, min(1.5, correlation_adjustment))))

        except Exception as e:
            logger.error(f"Error calculating correlation adjustment: {e}")
            return Decimal("1.0")

    def _calculate_confidence_adjustment(self, strategy_confidence: float) -> Decimal:
        """Calculate position size adjustment based on strategy confidence"""
        try:
            # Confidence should be between 0 and 1
            confidence = max(0.0, min(1.0, strategy_confidence))

            # Scale confidence to position size multiplier
            # Higher confidence = larger position
            confidence_adjustment = 0.5 + (confidence * 1.0)  # Range: 0.5 to 1.5

            return Decimal(str(confidence_adjustment))

        except Exception as e:
            logger.error(f"Error calculating confidence adjustment: {e}")
            return Decimal("1.0")

    async def _calculate_market_regime_adjustment(self, market_data: Dict) -> Decimal:
        """Calculate position size adjustment based on market regime"""
        try:
            # Analyze market regime indicators
            volatility_regime = self._analyze_volatility_regime(market_data)
            trend_regime = self._analyze_trend_regime(market_data)
            volume_regime = self._analyze_volume_regime(market_data)

            # Combine regime indicators
            regime_score = (volatility_regime + trend_regime + volume_regime) / 3.0

            # Adjust position size based on regime
            # Favorable regime = larger positions, unfavorable = smaller
            if regime_score > 0.7:
                regime_adjustment = 1.3  # Favorable market conditions
            elif regime_score > 0.5:
                regime_adjustment = 1.0  # Neutral market conditions
            elif regime_score > 0.3:
                regime_adjustment = 0.8  # Cautious market conditions
            else:
                regime_adjustment = 0.6  # Defensive market conditions

            return Decimal(str(regime_adjustment))

        except Exception as e:
            logger.error(f"Error calculating market regime adjustment: {e}")
            return Decimal("1.0")

    def _analyze_volatility_regime(self, market_data: Dict) -> float:
        """Analyze volatility regime (0=high volatility, 1=low volatility)"""
        try:
            price_history = market_data.get('price_history', [])
            if len(price_history) < 10:
                return 0.5  # Neutral if insufficient data

            prices = [float(p) for p in price_history[-20:]]
            returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]

            if len(returns) > 1:
                volatility = statistics.stdev(returns)
                # Lower volatility gets higher score
                return max(0.0, min(1.0, 1.0 - volatility * 10))

            return 0.5

        except Exception as e:
            logger.error(f"Error analyzing volatility regime: {e}")
            return 0.5

    def _analyze_trend_regime(self, market_data: Dict) -> float:
        """Analyze trend regime (0=no trend, 1=strong trend)"""
        try:
            price_history = market_data.get('price_history', [])
            if len(price_history) < 10:
                return 0.5

            prices = [float(p) for p in price_history[-10:]]

            # Simple trend analysis using linear regression
            x = list(range(len(prices)))
            n = len(prices)

            sum_x = sum(x)
            sum_y = sum(prices)
            sum_xy = sum(x[i] * prices[i] for i in range(n))
            sum_x2 = sum(xi * xi for xi in x)

            if n * sum_x2 - sum_x * sum_x != 0:
                slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)

                # Calculate R-squared for trend strength
                mean_y = sum_y / n
                ss_tot = sum((prices[i] - mean_y) ** 2 for i in range(n))
                predicted = [slope * x[i] + (sum_y - slope * sum_x) / n for i in range(n)]
                ss_res = sum((prices[i] - predicted[i]) ** 2 for i in range(n))

                r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
                return max(0.0, min(1.0, r_squared))

            return 0.5

        except Exception as e:
            logger.error(f"Error analyzing trend regime: {e}")
            return 0.5

    def _analyze_volume_regime(self, market_data: Dict) -> float:
        """Analyze volume regime (0=low volume, 1=high volume)"""
        try:
            volume_history = market_data.get('volume_history', [])
            if len(volume_history) < 5:
                return 0.5

            volumes = [float(v) for v in volume_history[-10:]]
            current_volume = volumes[-1]
            avg_volume = statistics.mean(volumes[:-1]) if len(volumes) > 1 else current_volume

            # Volume ratio (current vs average)
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0

            # Higher volume gets higher score (up to 2x average)
            return max(0.0, min(1.0, volume_ratio / 2.0))

        except Exception as e:
            logger.error(f"Error analyzing volume regime: {e}")
            return 0.5

    async def _get_current_positions(self) -> Dict[str, Decimal]:
        """Get current trading positions across all platforms"""
        try:
            positions = {}

            # This would typically integrate with the trading engine
            # For now, return empty dict as placeholder
            # In a real implementation, this would query active positions

            return positions

        except Exception as e:
            logger.error(f"Error getting current positions: {e}")
            return {}

    def _get_symbol_correlation(self, symbol1: str, symbol2: str) -> float:
        """Get correlation coefficient between two symbols"""
        try:
            # Check cache first
            cache_key = f"{symbol1}_{symbol2}"
            reverse_key = f"{symbol2}_{symbol1}"

            if cache_key in self.correlation_matrix:
                return self.correlation_matrix[cache_key]
            elif reverse_key in self.correlation_matrix:
                return self.correlation_matrix[reverse_key]

            # Calculate correlation (simplified - in real implementation would use price history)
            # For now, return estimated correlations based on asset types
            correlation = self._estimate_correlation(symbol1, symbol2)

            # Cache the result
            self.correlation_matrix[cache_key] = correlation

            return correlation

        except Exception as e:
            logger.error(f"Error getting symbol correlation: {e}")
            return 0.0

    def _estimate_correlation(self, symbol1: str, symbol2: str) -> float:
        """Estimate correlation between symbols based on asset types"""
        try:
            # Simplified correlation estimation
            crypto_pairs = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT']
            forex_pairs = ['EUR', 'USD', 'GBP', 'JPY']

            # Same asset = perfect correlation
            if symbol1 == symbol2:
                return 1.0

            # Both crypto = high correlation
            if any(crypto in symbol1 for crypto in crypto_pairs) and any(crypto in symbol2 for crypto in crypto_pairs):
                if 'BTC' in symbol1 and 'BTC' in symbol2:
                    return 0.9  # BTC pairs highly correlated
                elif 'ETH' in symbol1 and 'ETH' in symbol2:
                    return 0.85  # ETH pairs highly correlated
                else:
                    return 0.7  # Other crypto pairs moderately correlated

            # Both forex = moderate correlation
            elif any(forex in symbol1 for forex in forex_pairs) and any(forex in symbol2 for forex in forex_pairs):
                return 0.5

            # Mixed asset types = low correlation
            else:
                return 0.2

        except Exception as e:
            logger.error(f"Error estimating correlation: {e}")
            return 0.0

    async def calculate_portfolio_risk(self) -> Dict:
        """Calculate comprehensive portfolio risk metrics"""
        try:
            total_capital = self.profit_tracker.current_total_capital_usd
            current_positions = await self._get_current_positions()

            if not current_positions:
                return {
                    'total_risk': 0.0,
                    'concentration_risk': 0.0,
                    'correlation_risk': 0.0,
                    'liquidity_risk': 0.0,
                    'volatility_risk': 0.0
                }

            # Calculate individual risk components
            concentration_risk = self._calculate_concentration_risk(current_positions, total_capital)
            correlation_risk = self._calculate_correlation_risk(current_positions)
            liquidity_risk = self._calculate_liquidity_risk(current_positions)
            volatility_risk = self._calculate_volatility_risk(current_positions)

            # Combined total risk
            total_risk = (
                concentration_risk * 0.3 +
                correlation_risk * 0.25 +
                liquidity_risk * 0.25 +
                volatility_risk * 0.2
            )

            return {
                'total_risk': total_risk,
                'concentration_risk': concentration_risk,
                'correlation_risk': correlation_risk,
                'liquidity_risk': liquidity_risk,
                'volatility_risk': volatility_risk,
                'risk_level': self._categorize_risk_level(total_risk)
            }

        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
            return {'total_risk': 0.5, 'error': str(e)}

    def _calculate_concentration_risk(self, positions: Dict, total_capital: Decimal) -> float:
        """Calculate concentration risk based on position sizes"""
        try:
            if not positions:
                return 0.0

            position_weights = [float(pos_size / total_capital) for pos_size in positions.values()]

            # Herfindahl-Hirschman Index for concentration
            hhi = sum(weight ** 2 for weight in position_weights)

            # Normalize to 0-1 scale (1 = maximum concentration)
            max_hhi = 1.0  # Single position
            normalized_hhi = hhi / max_hhi

            return normalized_hhi

        except Exception as e:
            logger.error(f"Error calculating concentration risk: {e}")
            return 0.5

    def _calculate_correlation_risk(self, positions: Dict) -> float:
        """Calculate correlation risk across positions"""
        try:
            if len(positions) < 2:
                return 0.0

            symbols = list(positions.keys())
            total_correlation = 0.0
            pair_count = 0

            for i in range(len(symbols)):
                for j in range(i + 1, len(symbols)):
                    correlation = abs(self._get_symbol_correlation(symbols[i], symbols[j]))
                    total_correlation += correlation
                    pair_count += 1

            avg_correlation = total_correlation / pair_count if pair_count > 0 else 0.0

            return avg_correlation

        except Exception as e:
            logger.error(f"Error calculating correlation risk: {e}")
            return 0.5

    def _calculate_liquidity_risk(self, positions: Dict) -> float:
        """Calculate liquidity risk across positions"""
        try:
            if not positions:
                return 0.0

            total_liquidity_score = 0.0
            position_count = 0

            for symbol in positions.keys():
                liquidity_data = self.liquidity_scores.get(symbol, {})
                liquidity_score = liquidity_data.get('score', 5000)  # Default moderate liquidity

                # Normalize liquidity score (higher = better liquidity = lower risk)
                normalized_score = min(1.0, liquidity_score / 10000)
                liquidity_risk = 1.0 - normalized_score

                total_liquidity_score += liquidity_risk
                position_count += 1

            avg_liquidity_risk = total_liquidity_score / position_count if position_count > 0 else 0.0

            return avg_liquidity_risk

        except Exception as e:
            logger.error(f"Error calculating liquidity risk: {e}")
            return 0.5

    def _calculate_volatility_risk(self, positions: Dict) -> float:
        """Calculate volatility risk across positions"""
        try:
            if not positions:
                return 0.0

            total_volatility = 0.0
            position_count = 0

            for symbol in positions.keys():
                volatility_data = self.volatility_cache.get(symbol, {})
                volatility = volatility_data.get('value', 0.02)  # Default 2% volatility

                total_volatility += volatility
                position_count += 1

            avg_volatility = total_volatility / position_count if position_count > 0 else 0.0

            # Normalize volatility (higher volatility = higher risk)
            normalized_volatility = min(1.0, avg_volatility * 20)  # Scale to 0-1

            return normalized_volatility

        except Exception as e:
            logger.error(f"Error calculating volatility risk: {e}")
            return 0.5

    def _categorize_risk_level(self, total_risk: float) -> str:
        """Categorize overall risk level"""
        if total_risk < 0.2:
            return "LOW"
        elif total_risk < 0.4:
            return "MODERATE"
        elif total_risk < 0.6:
            return "HIGH"
        else:
            return "EXTREME"

    async def get_capital_efficiency_metrics(self) -> Dict:
        """Get comprehensive capital efficiency metrics"""
        try:
            total_capital = self.profit_tracker.current_total_capital_usd
            profit_data = self.wallet_manager.calculate_profits()

            # Calculate efficiency metrics
            capital_utilization = await self._calculate_capital_utilization()
            return_on_capital = float(profit_data['total_profit_usd'] / total_capital) if total_capital > 0 else 0.0
            risk_adjusted_return = await self._calculate_risk_adjusted_return()

            # Portfolio metrics
            portfolio_risk = await self.calculate_portfolio_risk()
            sharpe_ratio = self._calculate_sharpe_ratio(return_on_capital, portfolio_risk['total_risk'])

            return {
                'capital_utilization': capital_utilization,
                'return_on_capital': return_on_capital,
                'risk_adjusted_return': risk_adjusted_return,
                'sharpe_ratio': sharpe_ratio,
                'total_capital_usd': float(total_capital),
                'portfolio_risk': portfolio_risk,
                'efficiency_score': self._calculate_efficiency_score(
                    capital_utilization, return_on_capital, risk_adjusted_return
                )
            }

        except Exception as e:
            logger.error(f"Error getting capital efficiency metrics: {e}")
            return {'error': str(e)}

    async def _calculate_capital_utilization(self) -> float:
        """Calculate how efficiently capital is being utilized"""
        try:
            total_capital = self.profit_tracker.current_total_capital_usd
            current_positions = await self._get_current_positions()

            if not current_positions or total_capital == 0:
                return 0.0

            deployed_capital = sum(current_positions.values())
            utilization = float(deployed_capital / total_capital)

            return min(1.0, utilization)

        except Exception as e:
            logger.error(f"Error calculating capital utilization: {e}")
            return 0.0

    async def _calculate_risk_adjusted_return(self) -> float:
        """Calculate risk-adjusted return"""
        try:
            profit_data = self.wallet_manager.calculate_profits()
            portfolio_risk = await self.calculate_portfolio_risk()

            total_return = float(profit_data['total_profit_usd'])
            total_risk = portfolio_risk['total_risk']

            if total_risk > 0:
                return total_return / total_risk
            else:
                return total_return

        except Exception as e:
            logger.error(f"Error calculating risk-adjusted return: {e}")
            return 0.0

    def _calculate_sharpe_ratio(self, return_rate: float, risk_rate: float) -> float:
        """Calculate Sharpe ratio"""
        try:
            risk_free_rate = 0.02  # Assume 2% risk-free rate

            if risk_rate > 0:
                return (return_rate - risk_free_rate) / risk_rate
            else:
                return return_rate - risk_free_rate

        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0

    def _calculate_efficiency_score(self, utilization: float, return_rate: float, risk_adj_return: float) -> float:
        """Calculate overall capital efficiency score"""
        try:
            # Weighted combination of efficiency metrics
            efficiency_score = (
                utilization * 0.3 +
                min(1.0, max(0.0, return_rate + 0.5)) * 0.4 +  # Normalize return rate
                min(1.0, max(0.0, risk_adj_return / 10 + 0.5)) * 0.3  # Normalize risk-adj return
            )

            return max(0.0, min(1.0, efficiency_score))

        except Exception as e:
            logger.error(f"Error calculating efficiency score: {e}")
            return 0.5
>>>>>>> Stashed changes
