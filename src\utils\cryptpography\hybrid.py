# src/utils/cryptography/hybrid.py
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding, rsa
from cryptography.fernet import Fernet
import base64
import os

class HybridCrypto:
    def __init__(self, private_key_path='src/utils/cryptography/private.pem'):
        """Initialize with RSA private key and encrypted Fernet key from .env"""
        # Load RSA private key
        with open(private_key_path, "rb") as f:
            self.private_key = serialization.load_pem_private_key(
                f.read(),
                password=None
            )
        
        # Decrypt Fernet key using RSA
        encrypted_fernet_key = base64.urlsafe_b64decode(os.getenv('ENCRYPTED_FERNET_KEY'))
        self.fernet_key = self.private_key.decrypt(
            encrypted_fernet_key,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        self.cipher = Fernet(self.fernet_key)

    def decrypt_value(self, encrypted_value: str) -> str:
        """Decrypt environment value"""
        return self.cipher.decrypt(encrypted_value.encode()).decode()