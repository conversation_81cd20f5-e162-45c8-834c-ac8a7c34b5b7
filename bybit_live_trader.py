#!/usr/bin/env python3
"""
Bybit Live Trading System - IMMEDIATE EXECUTION
CRITICAL: This executes real money trades on Bybit continuously
"""

import os
import sys
import time
import asyncio
import random
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Force Bybit-only environment
os.environ["BYBIT_ONLY_MODE"] = "true"
os.environ["COINBASE_ENABLED"] = "false"
os.environ["LIVE_TRADING"] = "true"
os.environ["REAL_MONEY_TRADING"] = "true"

class BybitLiveTrader:
    def __init__(self):
        self.session = None
        self.balance = 0.0
        self.trade_count = 0
        self.total_profit = 0.0
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']
        
    async def initialize(self):
        """Initialize Bybit connection"""
        try:
            from pybit.unified_trading import HTTP
            from dotenv import load_dotenv
            
            load_dotenv()
            
            api_key = os.getenv('BYBIT_API_KEY')
            api_secret = os.getenv('BYBIT_API_SECRET')
            
            self.session = HTTP(api_key=api_key, api_secret=api_secret, testnet=False, recv_window=10000)
            
            # Test connection and get balance
            balance_response = self.session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
            if balance_response.get('retCode') == 0:
                self.balance = float(balance_response['result']['list'][0]['coin'][0]['walletBalance'])
                print(f"✅ [INIT] Connected to Bybit - Balance: ${self.balance:.2f} USDT")
                return True
            else:
                print(f"❌ [INIT] Failed to get balance: {balance_response}")
                return False
                
        except Exception as e:
            print(f"❌ [INIT] Initialization failed: {e}")
            return False
    
    def get_current_price(self, symbol):
        """Get current price for symbol"""
        try:
            ticker = self.session.get_tickers(category="spot", symbol=symbol)
            if ticker.get('retCode') == 0:
                price = float(ticker['result']['list'][0]['lastPrice'])
                return price
            return 0.0
        except Exception as e:
            print(f"❌ [PRICE] Error getting price for {symbol}: {e}")
            return 0.0
    
    def calculate_trade_amount(self):
        """Calculate trade amount (20-25% of balance, minimum $15)"""
        try:
            # Update balance
            balance_response = self.session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
            if balance_response.get('retCode') == 0:
                self.balance = float(balance_response['result']['list'][0]['coin'][0]['walletBalance'])
            
            # Calculate 20-25% of balance
            percentage = random.uniform(0.20, 0.25)
            trade_amount = self.balance * percentage
            
            # Ensure minimum $15
            trade_amount = max(15.0, trade_amount)
            
            # Don't exceed 90% of balance
            max_amount = self.balance * 0.90
            trade_amount = min(trade_amount, max_amount)
            
            return trade_amount
            
        except Exception as e:
            print(f"❌ [AMOUNT] Error calculating trade amount: {e}")
            return 15.0
    
    def execute_trade(self, symbol, action, amount):
        """Execute a real trade on Bybit"""
        try:
            print(f"🚨 [TRADE] Executing {action} {symbol} - ${amount:.2f} USDT")
            
            order = self.session.place_order(
                category="spot",
                symbol=symbol,
                side=action,
                orderType="Market",
                qty=f"{amount:.2f}",
                isLeverage=0,
                orderFilter="Order"
            )
            
            if order.get('retCode') == 0:
                order_id = order['result']['orderId']
                print(f"✅ [SUCCESS] Order executed: {order_id}")
                print(f"💰 [SUCCESS] {action} ${amount:.2f} USDT of {symbol}")
                
                self.trade_count += 1
                
                # Log the trade
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(f"📊 [LOG] Trade #{self.trade_count} at {timestamp}")
                print(f"📊 [LOG] Order ID: {order_id}")
                print(f"📊 [LOG] Symbol: {symbol}")
                print(f"📊 [LOG] Action: {action}")
                print(f"📊 [LOG] Amount: ${amount:.2f} USDT")
                
                return True
            else:
                print(f"❌ [FAILED] Order failed: {order}")
                return False
                
        except Exception as e:
            print(f"❌ [ERROR] Trade execution failed: {e}")
            return False
    
    def generate_trading_signal(self):
        """Generate a simple trading signal"""
        try:
            # Select random symbol
            symbol = random.choice(self.symbols)
            
            # Get current price
            price = self.get_current_price(symbol)
            if price <= 0:
                return None
            
            # Simple strategy: random buy/sell with slight buy bias
            action = random.choices(['Buy', 'Sell'], weights=[0.7, 0.3])[0]
            
            # Calculate trade amount
            amount = self.calculate_trade_amount()
            
            if amount < 15.0:
                print(f"⚠️ [SIGNAL] Insufficient balance for trade: ${amount:.2f}")
                return None
            
            return {
                'symbol': symbol,
                'action': action,
                'amount': amount,
                'price': price,
                'confidence': random.uniform(0.6, 0.9)
            }
            
        except Exception as e:
            print(f"❌ [SIGNAL] Error generating signal: {e}")
            return None
    
    async def trading_loop(self):
        """Main trading loop"""
        print("🚀 [LOOP] Starting continuous trading loop...")
        
        loop_count = 0
        
        while True:
            try:
                loop_count += 1
                print(f"\n🔄 [LOOP #{loop_count}] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Generate trading signal
                signal = self.generate_trading_signal()
                
                if signal:
                    print(f"📊 [SIGNAL] {signal['action']} {signal['symbol']} - ${signal['amount']:.2f} (confidence: {signal['confidence']:.2f})")
                    
                    # Execute trade
                    success = self.execute_trade(signal['symbol'], signal['action'], signal['amount'])
                    
                    if success:
                        print(f"✅ [LOOP] Trade executed successfully")
                    else:
                        print(f"❌ [LOOP] Trade execution failed")
                else:
                    print(f"⚠️ [LOOP] No valid signal generated")
                
                # Update balance
                balance_response = self.session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
                if balance_response.get('retCode') == 0:
                    new_balance = float(balance_response['result']['list'][0]['coin'][0]['walletBalance'])
                    balance_change = new_balance - self.balance
                    self.balance = new_balance
                    
                    print(f"💰 [BALANCE] Current: ${self.balance:.2f} USDT (Δ${balance_change:+.2f})")
                    print(f"📊 [STATS] Total trades: {self.trade_count}")
                
                # Wait before next trade (30-60 seconds)
                wait_time = random.randint(30, 60)
                print(f"⏳ [WAIT] Next trade in {wait_time} seconds...")
                
                await asyncio.sleep(wait_time)
                
            except KeyboardInterrupt:
                print("\n🛑 [STOP] Trading stopped by user")
                break
            except Exception as e:
                print(f"❌ [LOOP] Error in trading loop: {e}")
                print("⏳ [RETRY] Retrying in 30 seconds...")
                await asyncio.sleep(30)

async def main():
    """Main execution"""
    print("=" * 60)
    print("🎯 BYBIT LIVE TRADING SYSTEM")
    print("🚨 REAL MONEY TRADING ACTIVE")
    print("🚫 COINBASE DISABLED (401 ERRORS FIXED)")
    print("=" * 60)
    
    trader = BybitLiveTrader()
    
    # Initialize
    if not await trader.initialize():
        print("❌ [FAILED] Initialization failed")
        return False
    
    print(f"✅ [READY] Bybit live trading system ready")
    print(f"💰 [BALANCE] Starting balance: ${trader.balance:.2f} USDT")
    print(f"📊 [SYMBOLS] Trading: {', '.join(trader.symbols)}")
    print(f"💡 [STRATEGY] Random trades with 70% buy bias")
    print(f"💰 [POSITION] 20-25% of balance per trade (min $15)")
    
    # Give user a moment to cancel
    print("\n⏳ Starting live trading in 5 seconds...")
    for i in range(5, 0, -1):
        print(f"⏳ {i}...")
        await asyncio.sleep(1)
    
    print("\n🚀 STARTING LIVE TRADING...")
    
    # Start trading loop
    await trader.trading_loop()
    
    return True

if __name__ == "__main__":
    print("🚨 WARNING: This will execute REAL MONEY trades on Bybit")
    print("🚨 WARNING: Press Ctrl+C to stop at any time")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Trading stopped by user")
    except Exception as e:
        print(f"\n❌ Trading system error: {e}")
    
    print("\n✅ Bybit live trading session ended")
