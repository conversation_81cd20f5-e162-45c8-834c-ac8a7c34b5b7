"""
Robust Error Recovery and Continuous Operation System

Implements comprehensive error handling that never halts trading due to
insufficient funds when other tradeable assets exist. Provides recovery
from API failures, network issues, and ensures continuous operation with
automatic recovery mechanisms.
"""

import asyncio
import logging
from decimal import Decimal
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import time
import traceback
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class ErrorType(Enum):
    """Types of errors that can occur"""
    INSUFFICIENT_BALANCE = "insufficient_balance"
    API_FAILURE = "api_failure"
    NETWORK_ERROR = "network_error"
    EXCHANGE_ERROR = "exchange_error"
    VALIDATION_ERROR = "validation_error"
    TIMEOUT_ERROR = "timeout_error"
    AUTHENTICATION_ERROR = "authentication_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    UNKNOWN_ERROR = "unknown_error"

class RecoveryAction(Enum):
    """Recovery actions that can be taken"""
    RETRY = "retry"
    SWITCH_CURRENCY = "switch_currency"
    SWITCH_EXCHANGE = "switch_exchange"
    REDUCE_ORDER_SIZE = "reduce_order_size"
    WAIT_AND_RETRY = "wait_and_retry"
    SKIP_OPPORTUNITY = "skip_opportunity"
    EMERGENCY_STOP = "emergency_stop"
    CAPITAL_TRANSFER = "capital_transfer"

@dataclass
class ErrorContext:
    """Context information for an error"""
    error_type: ErrorType
    error_message: str
    function_name: str
    exchange_name: str
    currency: str
    amount: Decimal
    timestamp: float
    retry_count: int
    stack_trace: str

@dataclass
class RecoveryStrategy:
    """Recovery strategy for handling errors"""
    error_type: ErrorType
    actions: List[RecoveryAction]
    max_retries: int
    retry_delay: float
    escalation_threshold: int

class RobustErrorRecovery:
    """
    Comprehensive error recovery system that ensures continuous trading
    operation even when facing various types of failures
    """
    
    def __init__(self, trading_engine, config: Dict = None):
        self.trading_engine = trading_engine
        self.config = config or {}
        
        # Error tracking
        self.error_history = []
        self.error_counts = {}
        self.recovery_stats = {
            'total_errors': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'emergency_stops': 0
        }
        
        # Recovery configuration
        self.max_consecutive_errors = self.config.get('max_consecutive_errors', 10)
        self.error_cooldown_period = self.config.get('error_cooldown_period', 300)  # 5 minutes
        self.emergency_stop_threshold = self.config.get('emergency_stop_threshold', 20)
        
        # Define recovery strategies
        self.recovery_strategies = self._initialize_recovery_strategies()
        
        # Circuit breaker state
        self.circuit_breaker_active = False
        self.circuit_breaker_until = 0
        
        logger.info("🛡️ [ERROR-RECOVERY] Initialized robust error recovery system")
    
    def _initialize_recovery_strategies(self) -> Dict[ErrorType, RecoveryStrategy]:
        """Initialize recovery strategies for different error types - OPTIMIZED for aggressive trading"""
        return {
            ErrorType.INSUFFICIENT_BALANCE: RecoveryStrategy(
                error_type=ErrorType.INSUFFICIENT_BALANCE,
                actions=[
                    RecoveryAction.SWITCH_CURRENCY,
                    RecoveryAction.REDUCE_ORDER_SIZE,
                    RecoveryAction.CAPITAL_TRANSFER,
                    RecoveryAction.SKIP_OPPORTUNITY
                ],
                max_retries=3,
                retry_delay=0.5,  # CRITICAL FIX: 500ms instead of 1s for aggressive trading
                escalation_threshold=5
            ),
            ErrorType.API_FAILURE: RecoveryStrategy(
                error_type=ErrorType.API_FAILURE,
                actions=[
                    RecoveryAction.WAIT_AND_RETRY,
                    RecoveryAction.SWITCH_EXCHANGE,
                    RecoveryAction.SKIP_OPPORTUNITY
                ],
                max_retries=5,
                retry_delay=1.0,  # CRITICAL FIX: 1s instead of 5s for aggressive trading
                escalation_threshold=10
            ),
            ErrorType.NETWORK_ERROR: RecoveryStrategy(
                error_type=ErrorType.NETWORK_ERROR,
                actions=[
                    RecoveryAction.WAIT_AND_RETRY,
                    RecoveryAction.RETRY,
                    RecoveryAction.SKIP_OPPORTUNITY
                ],
                max_retries=3,
                retry_delay=2.0,  # CRITICAL FIX: 2s instead of 10s for aggressive trading
                escalation_threshold=8
            ),
            ErrorType.RATE_LIMIT_ERROR: RecoveryStrategy(
                error_type=ErrorType.RATE_LIMIT_ERROR,
                actions=[
                    RecoveryAction.WAIT_AND_RETRY,
                    RecoveryAction.SWITCH_EXCHANGE
                ],
                max_retries=2,
                retry_delay=5.0,  # CRITICAL FIX: 5s instead of 60s for aggressive trading
                escalation_threshold=3
            ),
            ErrorType.AUTHENTICATION_ERROR: RecoveryStrategy(
                error_type=ErrorType.AUTHENTICATION_ERROR,
                actions=[
                    RecoveryAction.WAIT_AND_RETRY,
                    RecoveryAction.EMERGENCY_STOP
                ],
                max_retries=1,
                retry_delay=2.0,  # CRITICAL FIX: 2s instead of 30s for aggressive trading
                escalation_threshold=2
            ),
            # CRITICAL FIX: Add validation error recovery strategy
            ErrorType.VALIDATION_ERROR: RecoveryStrategy(
                error_type=ErrorType.VALIDATION_ERROR,
                actions=[
                    RecoveryAction.SWITCH_CURRENCY,
                    RecoveryAction.REDUCE_ORDER_SIZE,
                    RecoveryAction.SKIP_OPPORTUNITY
                ],
                max_retries=3,
                retry_delay=0.5,  # Fast recovery for validation errors
                escalation_threshold=5
            )
        }
    
    @asynccontextmanager
    async def error_recovery_context(self, operation_name: str, **kwargs):
        """Context manager for automatic error recovery"""
        try:
            yield
        except Exception as e:
            await self.handle_error(e, operation_name, **kwargs)
            raise
    
    async def handle_error(self, error: Exception, function_name: str, **context) -> bool:
        """Handle an error with automatic recovery attempts"""
        try:
            # Classify the error
            error_type = self._classify_error(error)
            
            # Enhanced error context creation with intelligent extraction
            exchange_name = self._extract_exchange_name(error, context)
            currency = self._extract_currency_info(error, context)
            amount = self._extract_amount_info(error, context)

            error_context = ErrorContext(
                error_type=error_type,
                error_message=str(error),
                function_name=function_name,
                exchange_name=exchange_name,
                currency=currency,
                amount=amount,
                timestamp=time.time(),
                retry_count=context.get('retry_count', 0),
                stack_trace=traceback.format_exc()
            )
            
            # Log the error
            await self._log_error(error_context)
            
            # Check circuit breaker
            if self._should_activate_circuit_breaker(error_context):
                await self._activate_circuit_breaker()
                return False
            
            # Attempt recovery
            recovery_success = await self._attempt_recovery(error_context, **context)
            
            # Update statistics
            self.recovery_stats['total_errors'] += 1
            if recovery_success:
                self.recovery_stats['successful_recoveries'] += 1
            else:
                self.recovery_stats['failed_recoveries'] += 1
            
            return recovery_success
            
        except Exception as recovery_error:
            logger.error(f"❌ [ERROR-RECOVERY] Error in recovery system: {recovery_error}")
            return False
    
    async def _attempt_recovery(self, error_context: ErrorContext, **kwargs) -> bool:
        """Attempt to recover from an error using defined strategies"""
        try:
            strategy = self.recovery_strategies.get(error_context.error_type)
            if not strategy:
                logger.warning(f"⚠️ [RECOVERY] No strategy for {error_context.error_type.value}")
                return False
            
            logger.info(f"🔧 [RECOVERY] Attempting recovery for {error_context.error_type.value}")
            
            for action in strategy.actions:
                try:
                    success = await self._execute_recovery_action(action, error_context, **kwargs)
                    if success:
                        logger.info(f"✅ [RECOVERY] Successful recovery with {action.value}")
                        return True
                except Exception as e:
                    logger.warning(f"⚠️ [RECOVERY] Recovery action {action.value} failed: {e}")
                    continue
            
            logger.warning(f"⚠️ [RECOVERY] All recovery actions failed for {error_context.error_type.value}")
            return False
            
        except Exception as e:
            logger.error(f"❌ [RECOVERY] Error in recovery attempt: {e}")
            return False
    
    async def _execute_recovery_action(self, action: RecoveryAction, error_context: ErrorContext, **kwargs) -> bool:
        """Execute a specific recovery action"""
        try:
            if action == RecoveryAction.RETRY:
                return await self._retry_operation(error_context, **kwargs)
            
            elif action == RecoveryAction.SWITCH_CURRENCY:
                return await self._switch_currency(error_context, **kwargs)
            
            elif action == RecoveryAction.SWITCH_EXCHANGE:
                return await self._switch_exchange(error_context, **kwargs)
            
            elif action == RecoveryAction.REDUCE_ORDER_SIZE:
                return await self._reduce_order_size(error_context, **kwargs)
            
            elif action == RecoveryAction.WAIT_AND_RETRY:
                return await self._wait_and_retry(error_context, **kwargs)
            
            elif action == RecoveryAction.SKIP_OPPORTUNITY:
                return await self._skip_opportunity(error_context, **kwargs)
            
            elif action == RecoveryAction.CAPITAL_TRANSFER:
                return await self._trigger_capital_transfer(error_context, **kwargs)
            
            elif action == RecoveryAction.EMERGENCY_STOP:
                return await self._emergency_stop(error_context, **kwargs)
            
            else:
                logger.warning(f"⚠️ [RECOVERY] Unknown recovery action: {action.value}")
                return False
                
        except Exception as e:
            logger.error(f"❌ [RECOVERY] Error executing {action.value}: {e}")
            return False
    
    async def _retry_operation(self, error_context: ErrorContext, **kwargs) -> bool:
        """Retry the failed operation"""
        try:
            if error_context.retry_count >= 3:  # Max 3 retries
                return False
            
            logger.info(f"🔄 [RETRY] Retrying operation (attempt {error_context.retry_count + 1})")
            await asyncio.sleep(1.0)  # Brief delay
            
            # The actual retry would be handled by the calling code
            return True
            
        except Exception as e:
            logger.error(f"❌ [RETRY] Error in retry: {e}")
            return False
    
    async def _switch_currency(self, error_context: ErrorContext, **kwargs) -> bool:
        """Switch to an alternative currency for trading"""
        try:
            if not self.trading_engine.currency_switcher:
                return False
            
            logger.info(f"🔄 [CURRENCY-SWITCH] Switching from {error_context.currency}")
            
            # Get trading cycle recommendation
            cycle_info = await self.trading_engine.currency_switcher.maintain_continuous_trading_cycle()
            
            if cycle_info.get('trading_mode') in ['SELL', 'ALTERNATIVE']:
                logger.info(f"✅ [CURRENCY-SWITCH] Switched to {cycle_info['trading_mode']} mode")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [CURRENCY-SWITCH] Error switching currency: {e}")
            return False
    
    async def _switch_exchange(self, error_context: ErrorContext, **kwargs) -> bool:
        """Switch to an alternative exchange"""
        try:
            available_exchanges = list(self.trading_engine.exchange_clients.keys())
            current_exchange = error_context.exchange_name
            
            alternative_exchanges = [ex for ex in available_exchanges if ex != current_exchange]
            
            if alternative_exchanges:
                alternative = alternative_exchanges[0]
                logger.info(f"🔄 [EXCHANGE-SWITCH] Switching from {current_exchange} to {alternative}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [EXCHANGE-SWITCH] Error switching exchange: {e}")
            return False
    
    async def _reduce_order_size(self, error_context: ErrorContext, **kwargs) -> bool:
        """Reduce order size to fit available balance"""
        try:
            if error_context.amount <= Decimal('1'):  # Already very small
                return False
            
            reduced_amount = error_context.amount * Decimal('0.5')  # Reduce by 50%
            logger.info(f"📉 [SIZE-REDUCTION] Reducing order from {error_context.amount} to {reduced_amount}")
            
            # The actual size reduction would be handled by the calling code
            return True
            
        except Exception as e:
            logger.error(f"❌ [SIZE-REDUCTION] Error reducing order size: {e}")
            return False

    async def _execute_balance_aware_fallback(self, error_context: ErrorContext, **kwargs) -> bool:
        """Execute balance-aware fallback strategies using the balance manager"""
        try:
            logger.info(f"🔄 [BALANCE-FALLBACK] Executing balance-aware fallback for {error_context.error_type.value}")

            # Get balance manager from trading engine
            balance_manager = None
            if hasattr(self.trading_engine, 'balance_manager'):
                balance_manager = self.trading_engine.balance_manager
            elif hasattr(self.trading_engine, 'multi_currency_engine') and hasattr(self.trading_engine.multi_currency_engine, 'balance_manager'):
                balance_manager = self.trading_engine.multi_currency_engine.balance_manager

            if not balance_manager:
                logger.warning("⚠️ [BALANCE-FALLBACK] No balance manager available")
                return False

            # Extract trading parameters from error context
            symbol = kwargs.get('symbol', 'BTCUSDT')  # Default fallback
            side = kwargs.get('side', 'buy')
            amount = error_context.amount or Decimal('10.0')  # Default to minimum order
            exchange = error_context.exchange_name or 'bybit'

            # Execute fallback strategy
            fallback_result = await balance_manager.execute_fallback_trading_strategy(
                original_symbol=symbol,
                original_side=side,
                original_amount=amount,
                exchange=exchange,
                failure_reason=error_context.error_message
            )

            if fallback_result.get('success', False):
                logger.info(f"✅ [BALANCE-FALLBACK] Fallback strategy successful: {fallback_result.get('fallback_strategy')}")
                return True
            else:
                logger.warning(f"⚠️ [BALANCE-FALLBACK] Fallback strategy failed: {fallback_result.get('error')}")
                return False

        except Exception as e:
            logger.error(f"❌ [BALANCE-FALLBACK] Error executing balance-aware fallback: {e}")
            return False
    
    async def _wait_and_retry(self, error_context: ErrorContext, **kwargs) -> bool:
        """Wait for a period and then retry - OPTIMIZED for aggressive trading"""
        try:
            strategy = self.recovery_strategies.get(error_context.error_type)
            base_wait_time = strategy.retry_delay if strategy else 5.0

            # CRITICAL FIX: Reduce wait times for aggressive micro-trading
            # Use maximum 2 seconds for any retry delay
            wait_time = min(base_wait_time, 2.0)

            logger.info(f"⏱️ [WAIT-RETRY] Brief wait {wait_time}s before retry (optimized for aggressive trading)")
            await asyncio.sleep(wait_time)

            return True

        except Exception as e:
            logger.error(f"❌ [WAIT-RETRY] Error in wait and retry: {e}")
            return False
    
    async def _skip_opportunity(self, error_context: ErrorContext, **kwargs) -> bool:
        """Skip the current trading opportunity"""
        try:
            logger.info(f"⏭️ [SKIP] Skipping opportunity due to {error_context.error_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [SKIP] Error skipping opportunity: {e}")
            return False
    
    async def _trigger_capital_transfer(self, error_context: ErrorContext, **kwargs) -> bool:
        """Trigger capital transfer from Coinbase"""
        try:
            if not self.trading_engine.capital_manager:
                return False
            
            logger.info(f"🏦 [CAPITAL-TRANSFER] Triggering capital transfer for {error_context.exchange_name}")
            
            result = await self.trading_engine.handle_insufficient_capital(error_context.exchange_name)
            
            if result.get('success', False):
                logger.info("✅ [CAPITAL-TRANSFER] Capital transfer initiated")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL-TRANSFER] Error triggering transfer: {e}")
            return False
    
    async def _emergency_stop(self, error_context: ErrorContext, **kwargs) -> bool:
        """Trigger emergency stop"""
        try:
            logger.error(f"🚨 [EMERGENCY-STOP] Emergency stop triggered due to {error_context.error_type.value}")
            
            self.recovery_stats['emergency_stops'] += 1
            
            # Activate circuit breaker
            await self._activate_circuit_breaker()
            
            return False  # Emergency stop is not a successful recovery
            
        except Exception as e:
            logger.error(f"❌ [EMERGENCY-STOP] Error in emergency stop: {e}")
            return False
    
    def _classify_error(self, error: Exception) -> ErrorType:
        """Classify an error into a specific error type with enhanced pattern matching"""
        error_message = str(error).lower()

        # Enhanced balance-related error detection
        balance_patterns = [
            'insufficient', 'balance', 'not enough', 'exceed', 'exceeds',
            'available', 'funds', 'capital', 'money', 'usdt', 'btc', 'eth'
        ]
        if any(pattern in error_message for pattern in balance_patterns):
            return ErrorType.INSUFFICIENT_BALANCE

        # Enhanced validation error detection
        validation_patterns = [
            'validation', 'invalid', 'minimum', 'maximum', 'order size',
            'quantity', 'price', 'symbol', 'requirements', 'failed'
        ]
        if any(pattern in error_message for pattern in validation_patterns):
            return ErrorType.VALIDATION_ERROR

        # Enhanced timeout error detection
        timeout_patterns = ['timeout', 'timed out', 'time out', 'deadline', 'expired']
        if any(pattern in error_message for pattern in timeout_patterns):
            return ErrorType.TIMEOUT_ERROR

        # Enhanced rate limit error detection
        rate_limit_patterns = ['rate limit', 'too many requests', '429', 'throttle', 'quota']
        if any(pattern in error_message for pattern in rate_limit_patterns):
            return ErrorType.RATE_LIMIT_ERROR

        # Enhanced authentication error detection
        auth_patterns = ['authentication', 'unauthorized', '401', 'forbidden', '403', 'api key', 'signature']
        if any(pattern in error_message for pattern in auth_patterns):
            return ErrorType.AUTHENTICATION_ERROR

        # Enhanced network error detection
        network_patterns = ['network', 'connection', 'connect', 'dns', 'host', 'unreachable', 'socket']
        if any(pattern in error_message for pattern in network_patterns):
            return ErrorType.NETWORK_ERROR

        # Enhanced API failure detection
        api_patterns = ['api', 'server', '500', '502', '503', '504', 'internal', 'service']
        if any(pattern in error_message for pattern in api_patterns):
            return ErrorType.API_FAILURE

        # Enhanced exchange-specific error detection
        exchange_patterns = ['bybit', 'coinbase', 'binance', 'exchange', 'order', 'trade']
        if any(pattern in error_message for pattern in exchange_patterns):
            return ErrorType.EXCHANGE_ERROR

        return ErrorType.UNKNOWN_ERROR

    def _extract_exchange_name(self, error: Exception, context: dict) -> str:
        """Extract exchange name from error message or context"""
        # First check context
        if context.get('exchange_name') and context['exchange_name'] != 'unknown':
            return context['exchange_name']
        if context.get('exchange') and context['exchange'] != 'unknown':
            return context['exchange']

        # Extract from error message
        error_message = str(error).lower()
        if 'bybit' in error_message:
            return 'bybit'
        elif 'coinbase' in error_message:
            return 'coinbase'
        elif 'binance' in error_message:
            return 'binance'
        elif 'phantom' in error_message:
            return 'phantom'

        # Check if we have trading engine reference
        if hasattr(self, 'trading_engine') and self.trading_engine:
            if hasattr(self.trading_engine, 'primary_exchange_name'):
                return self.trading_engine.primary_exchange_name

        return 'unknown'

    def _extract_currency_info(self, error: Exception, context: dict) -> str:
        """Extract currency information from error message or context"""
        # First check context
        if context.get('currency') and context['currency'] != 'unknown':
            return context['currency']
        if context.get('symbol'):
            symbol = context['symbol']
            # Extract base currency from symbol
            if 'USDT' in symbol:
                return symbol.replace('USDT', '')
            elif 'USD' in symbol:
                return symbol.replace('USD', '')
            return symbol

        # Extract from error message
        error_message = str(error).lower()
        currencies = ['usdt', 'btc', 'eth', 'sol', 'ada', 'dot', 'bnb', 'usd']
        for currency in currencies:
            if currency in error_message:
                return currency.upper()

        # Check for balance-related currencies in error message
        import re
        balance_match = re.search(r'(\w+)\s*balance', error_message)
        if balance_match:
            return balance_match.group(1).upper()

        return 'unknown'

    def _extract_amount_info(self, error: Exception, context: dict) -> Decimal:
        """Extract amount information from error message or context"""
        # First check context
        if context.get('amount') and context['amount'] != 0:
            return Decimal(str(context['amount']))
        if context.get('quantity') and context['quantity'] != 0:
            return Decimal(str(context['quantity']))

        # Extract from error message
        error_message = str(error)
        import re

        # Look for decimal numbers in error message
        amount_patterns = [
            r'(\d+\.?\d*)\s*(?:USDT|USD|BTC|ETH|SOL)',
            r'need\s+(\d+\.?\d*)',
            r'have\s+(\d+\.?\d*)',
            r'balance:\s*(\d+\.?\d*)',
            r'amount:\s*(\d+\.?\d*)',
            r'quantity:\s*(\d+\.?\d*)'
        ]

        for pattern in amount_patterns:
            match = re.search(pattern, error_message, re.IGNORECASE)
            if match:
                try:
                    return Decimal(match.group(1))
                except:
                    continue

        return Decimal('0')

    async def _log_error(self, error_context: ErrorContext):
        """Log error information"""
        logger.error(f"❌ [ERROR] {error_context.error_type.value} in {error_context.function_name}")
        logger.error(f"❌ [ERROR] Exchange: {error_context.exchange_name}, Currency: {error_context.currency}")
        logger.error(f"❌ [ERROR] Amount: {error_context.amount}, Message: {error_context.error_message}")
        
        # Store in error history
        self.error_history.append(error_context)
        
        # Update error counts
        error_key = f"{error_context.error_type.value}_{error_context.exchange_name}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Keep only recent errors (last 1000)
        if len(self.error_history) > 1000:
            self.error_history = self.error_history[-1000:]
    
    def _should_activate_circuit_breaker(self, error_context: ErrorContext) -> bool:
        """Determine if circuit breaker should be activated"""
        # Count recent errors (last 5 minutes)
        recent_errors = [
            err for err in self.error_history
            if time.time() - err.timestamp < 300  # 5 minutes
        ]
        
        return len(recent_errors) >= self.emergency_stop_threshold
    
    async def _activate_circuit_breaker(self):
        """Activate circuit breaker to pause trading"""
        self.circuit_breaker_active = True
        # CRITICAL FIX: Reduce cooldown period for aggressive micro-trading
        reduced_cooldown = min(self.error_cooldown_period, 30)  # Max 30 seconds for micro-trading
        self.circuit_breaker_until = time.time() + reduced_cooldown

        logger.warning(f"🔴 [CIRCUIT-BREAKER] Activated for {reduced_cooldown}s (reduced for aggressive trading)")
    
    def is_circuit_breaker_active(self) -> bool:
        """Check if circuit breaker is currently active with automatic reset"""
        if self.circuit_breaker_active and time.time() > self.circuit_breaker_until:
            self.circuit_breaker_active = False
            # CRITICAL FIX: Clear error history on reset to prevent immediate re-activation
            recent_errors = [
                err for err in self.error_history
                if time.time() - err.timestamp < 60  # Keep only last minute of errors
            ]
            self.error_history = recent_errors
            logger.info("🟢 [CIRCUIT-BREAKER] Deactivated - resuming operations with cleared error history")

        return self.circuit_breaker_active
    
    def get_error_recovery_report(self) -> Dict[str, Any]:
        """Generate error recovery status report"""
        try:
            recent_errors = [
                err for err in self.error_history
                if time.time() - err.timestamp < 3600  # Last hour
            ]
            
            error_types_count = {}
            for error in recent_errors:
                error_types_count[error.error_type.value] = error_types_count.get(error.error_type.value, 0) + 1
            
            return {
                'timestamp': time.time(),
                'circuit_breaker_active': self.circuit_breaker_active,
                'total_errors_last_hour': len(recent_errors),
                'error_types_last_hour': error_types_count,
                'recovery_statistics': self.recovery_stats.copy(),
                'error_counts_by_exchange': self.error_counts.copy(),
                'recovery_success_rate': (
                    self.recovery_stats['successful_recoveries'] / 
                    max(1, self.recovery_stats['total_errors'])
                ) * 100
            }
            
        except Exception as e:
            logger.error(f"❌ [ERROR-REPORT] Error generating report: {e}")
            return {'error': str(e)}
