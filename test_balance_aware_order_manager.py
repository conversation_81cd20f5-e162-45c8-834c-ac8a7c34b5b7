#!/usr/bin/env python3
"""
Test Balance-Aware Order Manager

This script tests the balance-aware order management system to ensure it can
properly validate balances, calculate optimal order sizes, and execute orders
with comprehensive balance validation and fail-fast behavior.
"""

import asyncio
import logging
import sys
import os
from decimal import Decimal

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_balance_aware_order_manager():
    """Test the balance-aware order manager functionality"""
    try:
        logger.info("🧪 [TEST] Starting balance-aware order manager test...")
        
        # Import required modules
        from src.trading.balance_aware_order_manager import BalanceAwareOrderManager, BalanceStatus, OrderSizingStrategy
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            logger.error("❌ [TEST] Missing Bybit API credentials")
            return False
        
        # Initialize exchange clients
        logger.info("🔧 [TEST] Initializing exchange clients...")
        
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        
        if not bybit_client.session:
            logger.error("❌ [TEST] Failed to initialize Bybit client")
            return False
        
        exchange_clients = {
            'bybit': bybit_client
        }
        
        # Initialize balance-aware order manager
        logger.info("⚖️ [TEST] Initializing balance-aware order manager...")
        
        balance_manager = BalanceAwareOrderManager(
            exchange_clients=exchange_clients,
            config={
                'balance_buffer': 0.05,           # 5% buffer for testing
                'min_order_value': 3.0,          # $3 minimum for testing
                'max_balance_usage': 0.85,       # 85% max usage for testing
                'critical_threshold': 0.1,       # 10% critical threshold
                'sizing_strategy': 'dynamic',    # Dynamic sizing
                'aggressive_trading': True,      # Enable aggressive trading
                'micro_trading': True,           # Enable micro-trading
                'balance_percentage': 0.8        # 80% for percentage strategy
            }
        )
        
        # Test 1: Initialize balance manager
        logger.info("🧪 [TEST-1] Testing balance manager initialization...")
        await balance_manager.initialize()
        
        if not balance_manager.balance_cache:
            logger.error("❌ [TEST-1] No balance cache initialized")
            return False
        
        logger.info("✅ [TEST-1] Balance manager initialized successfully")
        
        # Show current balances
        for exchange_name, cache_data in balance_manager.balance_cache.items():
            balances = cache_data['balances']
            logger.info(f"✅ [TEST-1] {exchange_name}: {len(balances)} currencies")
            
            # Show some balances
            for currency, balance in list(balances.items())[:5]:
                if balance > 0:
                    logger.info(f"  {currency}: {balance:.6f}")
        
        # Test 2: Balance validation
        logger.info("🧪 [TEST-2] Testing balance validation...")
        
        # Test buy order validation
        buy_balance_check = await balance_manager.validate_order_balance(
            symbol="BTCUSDT",
            side="buy",
            amount=Decimal('0.001'),  # Small amount for testing
            exchange="bybit"
        )
        
        logger.info(f"✅ [TEST-2] Buy order validation:")
        logger.info(f"  Currency: {buy_balance_check.currency}")
        logger.info(f"  Status: {buy_balance_check.status.value}")
        logger.info(f"  Available: {buy_balance_check.available_balance:.6f}")
        logger.info(f"  Required: {buy_balance_check.requested_amount:.6f}")
        logger.info(f"  Usable: {buy_balance_check.usable_amount:.6f}")
        logger.info(f"  Confidence: {buy_balance_check.confidence:.2f}")
        
        # Test sell order validation
        sell_balance_check = await balance_manager.validate_order_balance(
            symbol="BTCUSDT",
            side="sell",
            amount=Decimal('0.001'),  # Small amount for testing
            exchange="bybit"
        )
        
        logger.info(f"✅ [TEST-2] Sell order validation:")
        logger.info(f"  Currency: {sell_balance_check.currency}")
        logger.info(f"  Status: {sell_balance_check.status.value}")
        logger.info(f"  Available: {sell_balance_check.available_balance:.6f}")
        logger.info(f"  Required: {sell_balance_check.requested_amount:.6f}")
        logger.info(f"  Usable: {sell_balance_check.usable_amount:.6f}")
        
        # Test 3: Order sizing strategies
        logger.info("🧪 [TEST-3] Testing order sizing strategies...")
        
        strategies_to_test = [
            OrderSizingStrategy.FULL_AMOUNT,
            OrderSizingStrategy.AVAILABLE_BALANCE,
            OrderSizingStrategy.PERCENTAGE,
            OrderSizingStrategy.DYNAMIC,
            OrderSizingStrategy.CONSERVATIVE
        ]
        
        test_amount = Decimal('0.001')
        
        for strategy in strategies_to_test:
            try:
                order_sizing = await balance_manager.calculate_optimal_order_size(
                    symbol="ETHUSDT",
                    side="buy",
                    requested_amount=test_amount,
                    exchange="bybit",
                    strategy=strategy
                )
                
                logger.info(f"✅ [TEST-3] {strategy.value} strategy:")
                logger.info(f"  Original: {order_sizing.original_amount:.6f}")
                logger.info(f"  Recommended: {order_sizing.recommended_amount:.6f}")
                logger.info(f"  Confidence: {order_sizing.confidence:.2f}")
                logger.info(f"  Risk Score: {order_sizing.risk_score:.2f}")
                logger.info(f"  Reason: {order_sizing.reason}")
                
            except Exception as e:
                logger.warning(f"⚠️ [TEST-3] Error testing {strategy.value}: {e}")
        
        # Test 4: Balance confidence calculation
        logger.info("🧪 [TEST-4] Testing balance confidence calculation...")
        
        for exchange_name in balance_manager.balance_cache.keys():
            confidence = balance_manager._calculate_balance_confidence(exchange_name)
            logger.info(f"✅ [TEST-4] {exchange_name} confidence: {confidence:.2f}")
        
        # Test 5: Current balance retrieval
        logger.info("🧪 [TEST-5] Testing current balance retrieval...")
        
        test_currencies = ['USDT', 'BTC', 'ETH', 'SOL']
        
        for currency in test_currencies:
            balance = await balance_manager._get_current_balance(currency, 'bybit')
            logger.info(f"✅ [TEST-5] {currency} balance: {balance:.6f}")
        
        # Test 6: Price retrieval
        logger.info("🧪 [TEST-6] Testing price retrieval...")
        
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        
        for symbol in test_symbols:
            price = await balance_manager._get_current_price(symbol, 'bybit')
            logger.info(f"✅ [TEST-6] {symbol} price: ${price:.2f}")
        
        # Test 7: Minimum order amount calculation
        logger.info("🧪 [TEST-7] Testing minimum order amount calculation...")
        
        for symbol in test_symbols:
            min_amount = await balance_manager._calculate_minimum_order_amount(symbol, 'bybit')
            logger.info(f"✅ [TEST-7] {symbol} minimum amount: {min_amount:.6f}")
        
        # Test 8: Alternative currency finding
        logger.info("🧪 [TEST-8] Testing alternative currency finding...")
        
        alternatives = await balance_manager._find_alternative_currencies('BTCUSDT', 'bybit')
        logger.info(f"✅ [TEST-8] Alternative currencies: {alternatives}")
        
        # Test 9: USD value calculation
        logger.info("🧪 [TEST-9] Testing USD value calculation...")
        
        for currency in test_currencies:
            if currency != 'USDT':
                usd_value = await balance_manager._get_usd_value(currency, 1.0, 'bybit')
                logger.info(f"✅ [TEST-9] 1 {currency} = ${usd_value:.2f}")
        
        # Test 10: Balance-aware order execution (simulation)
        logger.info("🧪 [TEST-10] Testing balance-aware order execution...")
        
        # Test with a very small amount to avoid actual trading
        test_order_result = await balance_manager.execute_balance_aware_order(
            symbol="SOLUSDT",
            side="buy",
            amount=Decimal('0.01'),  # Very small amount
            exchange="bybit",
            order_type="market"
        )
        
        logger.info(f"✅ [TEST-10] Order execution result:")
        logger.info(f"  Success: {test_order_result.get('success', False)}")
        
        if test_order_result.get('success', False):
            logger.info(f"  Original amount: {test_order_result.get('original_amount', 0):.6f}")
            logger.info(f"  Executed amount: {test_order_result.get('executed_amount', 0):.6f}")
        else:
            logger.info(f"  Error: {test_order_result.get('error', 'Unknown error')}")
            
            # Check for alternatives
            if 'alternatives' in test_order_result:
                logger.info(f"  Alternatives: {test_order_result['alternatives']}")
        
        # Test 11: Balance status report
        logger.info("🧪 [TEST-11] Testing balance status report...")
        
        status_report = await balance_manager.get_balance_status_report()
        
        if 'error' not in status_report:
            logger.info("✅ [TEST-11] Balance status report generated")
            logger.info(f"  Exchanges: {status_report['exchanges']}")
            logger.info(f"  Total currencies: {status_report['total_currencies']}")
            logger.info(f"  Critical balances: {len(status_report['critical_balances'])}")
            
            # Show order statistics
            order_stats = status_report['order_statistics']
            logger.info(f"  Order statistics:")
            logger.info(f"    Total orders: {order_stats['total_orders']}")
            logger.info(f"    Successful orders: {order_stats['successful_orders']}")
            logger.info(f"    Failed orders: {order_stats['failed_orders']}")
            logger.info(f"    Balance failures: {order_stats['balance_failures']}")
            
            # Show recommendations
            for rec in status_report['recommendations']:
                logger.info(f"  Recommendation: {rec}")
            
            # Show critical balances
            for critical in status_report['critical_balances'][:3]:  # Show first 3
                logger.info(f"  Critical: {critical['currency']} on {critical['exchange']}")
                logger.info(f"    Balance: {critical['balance']:.6f} (${critical['usd_value']:.2f})")
        else:
            logger.error(f"❌ [TEST-11] Status report failed: {status_report['error']}")
        
        # Test 12: Balance status enumeration
        logger.info("🧪 [TEST-12] Testing balance status enumeration...")
        
        status_values = [status.value for status in BalanceStatus]
        logger.info(f"✅ [TEST-12] Balance statuses: {status_values}")
        
        # Test 13: Order sizing strategy enumeration
        logger.info("🧪 [TEST-13] Testing order sizing strategy enumeration...")
        
        strategy_values = [strategy.value for strategy in OrderSizingStrategy]
        logger.info(f"✅ [TEST-13] Sizing strategies: {strategy_values}")
        
        # Test 14: Balance cache refresh
        logger.info("🧪 [TEST-14] Testing balance cache refresh...")
        
        initial_update_time = balance_manager.balance_cache['bybit']['last_update']
        await asyncio.sleep(1)  # Wait 1 second
        await balance_manager.refresh_all_balances()
        new_update_time = balance_manager.balance_cache['bybit']['last_update']
        
        if new_update_time > initial_update_time:
            logger.info("✅ [TEST-14] Balance cache refreshed successfully")
        else:
            logger.warning("⚠️ [TEST-14] Balance cache may not have refreshed")
        
        # Summary
        logger.info("📊 [TEST-SUMMARY] Balance-aware order manager test results:")
        logger.info(f"  - Balance manager initialized: ✅")
        logger.info(f"  - Balance validation working: ✅")
        logger.info(f"  - Order sizing strategies tested: {len(strategies_to_test)}")
        logger.info(f"  - Balance confidence calculation: ✅")
        logger.info(f"  - Current balance retrieval: ✅")
        logger.info(f"  - Price retrieval working: ✅")
        logger.info(f"  - Minimum order calculation: ✅")
        logger.info(f"  - Alternative currency finding: ✅")
        logger.info(f"  - USD value calculation: ✅")
        logger.info(f"  - Order execution tested: ✅")
        logger.info(f"  - Status report generation: ✅")
        logger.info(f"  - Balance cache refresh: ✅")
        
        logger.info("✅ [TEST] Balance-aware order manager test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Balance-aware order manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    try:
        logger.info("🚀 Starting balance-aware order manager tests...")
        
        success = await test_balance_aware_order_manager()
        
        if success:
            logger.info("✅ All tests passed!")
            return 0
        else:
            logger.error("❌ Some tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
