import os
import json
from pathlib import Path
from dotenv import load_dotenv
from jsonschema import Draft7Validator
from typing import Any, Dict

class ConfigManager:
    _instance = None
    _validator = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._config = {}
            cls._instance._schema = cls._load_schema()
            cls._instance._validator = Draft7Validator(cls._instance._schema)
        return cls._instance

    @staticmethod
    def _load_schema() -> Dict[str, Any]:
        schema_path = Path("/app/config/config.schema.json")
        with open(schema_path) as f:
            return json.load(f)

    def load(self, config_path: str = "config/config.json") -> Dict[str, Any]:
        """Load and validate configuration"""
        try:
            # Load base config
            config_file = Path(__file__).parent.parent / config_path
            with open(config_file) as f:
                self._config = json.load(f)

            # Load environment variables
            env_path = Path("/app/.env")
            load_dotenv(env_path)

            # Merge environment variables
            self._apply_env_overrides()

            # Validate configuration
            errors = list(self._validator.iter_errors(self._config))
            if errors:
                raise ValueError(f"Config validation errors: {errors}")

            return self._config

        except Exception as e:
            raise RuntimeError(f"Config loading failed: {str(e)}") from e

    def _apply_env_overrides(self):
        """Override config with environment variables"""
        for env_key, env_value in os.environ.items():
            if not env_value:
                continue

            keys = env_key.lower().split('__')
            current = self._config

            for key in keys[:-1]:
                current = current.setdefault(key, {})
            current[keys[-1]] = self._parse_value(env_value)

    def _parse_value(self, value: str) -> Any:
        """Convert env strings to proper types"""
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        try:
            return int(value)
        except ValueError:
            try:
                return float(value)
            except ValueError:
                return value

# Singleton instance
config = ConfigManager().load()