# Create credential loader script (src/utils/load_credentials.py)
import hvac
import getpass

def main():
<<<<<<< Updated upstream
    print("🛡️ Enter Coinbase Pro Credentials Securely:")
    key = getpass.getpass("API Key: ")
    secret = getpass.getpass("API Secret: ")
    passphrase = getpass.getpass("Passphrase: ")
=======
    print("[SEC] Enter Coinbase Advanced API (CDP) Credentials Securely:")
    print("NOTE: Use NEW Coinbase Advanced API credentials, not old Pro API!")
    print("API Key format: organizations/{org_id}/apiKeys/{key_id}")
    print("API Secret format: EC Private Key (PEM format)")
    
    key = "": ")
    secret = "": ")
    
    # Validate key format
    if not key.startswith("organizations/") or "/apiKeys/" not in key:
        print("❌ Invalid API key format! Must be: organizations/{org_id}/apiKeys/{key_id}")
        return
    
    # Validate secret format
    if not secret.startswith("-----BEGIN EC PRIVATE KEY-----"):
        print("❌ Invalid API secret format! Must be EC Private Key in PEM format")
        return
>>>>>>> Stashed changes
    
    vault = hvac.Client(url='http://localhost:8200', token=os.environ['VAULT_TOKEN'])
    vault.secrets.kv.v2.create_or_update_secret(
        path='coinbase/pro',
        secret={
            'api_key': key,
            'api_secret': secret,
            'passphrase': passphrase
        }
    )
    print("🔐 Credentials stored in Vault with version 2 encryption")

if __name__ == "__main__":
    main()