#!/usr/bin/env python3
"""
Web Crawling Infrastructure for Real-Time Crypto Data
Comprehensive web crawling for price discovery and sentiment analysis
"""

import asyncio
import aiohttp
import logging
import time
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from bs4 import BeautifulSoup
import feedparser
from urllib.parse import urljoin, urlparse

logger = logging.getLogger(__name__)

class CrawlerStatus(Enum):
    """Crawler status enumeration"""
    ACTIVE = "active"
    RATE_LIMITED = "rate_limited"
    BLOCKED = "blocked"
    ERROR = "error"
    DISABLED = "disabled"

@dataclass
class CrawledData:
    """Crawled data structure"""
    symbol: str
    price: Optional[float] = None
    volume: Optional[float] = None
    market_cap: Optional[float] = None
    sentiment_score: Optional[float] = None
    news_count: int = 0
    source: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class NewsItem:
    """News item structure"""
    title: str
    content: str
    url: str
    source: str
    timestamp: datetime
    sentiment_score: Optional[float] = None
    relevance_score: Optional[float] = None

class CryptoPriceCrawler:
    """Web crawler for real-time crypto price data"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.status = CrawlerStatus.ACTIVE
        self.rate_limits = {
            'coinmarketcap.com': 2,  # seconds between requests
            'coingecko.com': 1,
            'dextools.io': 3,
            'coinbase.com': 1
        }
        self.last_request_times = {}
        
        # Headers to avoid blocking
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        logger.info("🕷️ [PRICE-CRAWLER] Crypto price crawler initialized")
    
    async def initialize(self) -> bool:
        """Initialize the crawler"""
        try:
            connector = aiohttp.TCPConnector(
                limit=50,
                limit_per_host=10,
                ttl_dns_cache=300
            )
            
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                connector=connector,
                headers=self.headers
            )
            
            logger.info("✅ [PRICE-CRAWLER] Initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ [PRICE-CRAWLER] Failed to initialize: {e}")
            return False
    
    async def crawl_coinmarketcap(self, symbol: str) -> Optional[CrawledData]:
        """Crawl CoinMarketCap for price data"""
        try:
            if not await self._check_rate_limit('coinmarketcap.com'):
                return None
            
            # Convert symbol to CMC format
            coin_name = self._symbol_to_coin_name(symbol)
            url = f"https://coinmarketcap.com/currencies/{coin_name}/"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Extract price data
                    price_element = soup.find('div', {'class': re.compile(r'priceValue')})
                    if price_element:
                        price_text = price_element.get_text().strip()
                        price = self._extract_price_from_text(price_text)
                        
                        # Extract market cap
                        market_cap = self._extract_market_cap(soup)
                        
                        # Extract volume
                        volume = self._extract_volume(soup)
                        
                        return CrawledData(
                            symbol=symbol,
                            price=price,
                            volume=volume,
                            market_cap=market_cap,
                            source="coinmarketcap",
                            metadata={'url': url}
                        )
                else:
                    logger.warning(f"⚠️ [CMC-CRAWLER] HTTP {response.status} for {symbol}")
                    
        except Exception as e:
            logger.error(f"❌ [CMC-CRAWLER] Error crawling {symbol}: {e}")
        
        return None
    
    async def crawl_coingecko(self, symbol: str) -> Optional[CrawledData]:
        """Crawl CoinGecko for price data"""
        try:
            if not await self._check_rate_limit('coingecko.com'):
                return None
            
            coin_id = self._symbol_to_coingecko_id(symbol)
            url = f"https://www.coingecko.com/en/coins/{coin_id}"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Extract price data using CoinGecko's structure
                    price_element = soup.find('span', {'data-coin-id': coin_id})
                    if not price_element:
                        price_element = soup.find('span', {'class': re.compile(r'price')})
                    
                    if price_element:
                        price_text = price_element.get_text().strip()
                        price = self._extract_price_from_text(price_text)
                        
                        return CrawledData(
                            symbol=symbol,
                            price=price,
                            source="coingecko",
                            metadata={'url': url, 'coin_id': coin_id}
                        )
                else:
                    logger.warning(f"⚠️ [CG-CRAWLER] HTTP {response.status} for {symbol}")
                    
        except Exception as e:
            logger.error(f"❌ [CG-CRAWLER] Error crawling {symbol}: {e}")
        
        return None
    
    async def crawl_dextools(self, contract_address: str) -> Optional[CrawledData]:
        """Crawl DEXTools for DeFi token data"""
        try:
            if not await self._check_rate_limit('dextools.io'):
                return None
            
            url = f"https://www.dextools.io/app/ethereum/pair-explorer/{contract_address}"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Extract price data from DEXTools
                    price_element = soup.find('div', {'class': re.compile(r'price')})
                    if price_element:
                        price_text = price_element.get_text().strip()
                        price = self._extract_price_from_text(price_text)
                        
                        return CrawledData(
                            symbol=contract_address,
                            price=price,
                            source="dextools",
                            metadata={'url': url, 'contract': contract_address}
                        )
                else:
                    logger.warning(f"⚠️ [DEXTOOLS-CRAWLER] HTTP {response.status} for {contract_address}")
                    
        except Exception as e:
            logger.error(f"❌ [DEXTOOLS-CRAWLER] Error crawling {contract_address}: {e}")
        
        return None
    
    def _symbol_to_coin_name(self, symbol: str) -> str:
        """Convert symbol to coin name for CMC URLs"""
        symbol_map = {
            'BTC': 'bitcoin',
            'ETH': 'ethereum',
            'SOL': 'solana',
            'ADA': 'cardano',
            'DOT': 'polkadot',
            'LINK': 'chainlink',
            'UNI': 'uniswap',
            'AVAX': 'avalanche',
            'MATIC': 'polygon'
        }
        
        base_symbol = symbol.replace('USDT', '').replace('USD', '')
        return symbol_map.get(base_symbol, base_symbol.lower())
    
    def _symbol_to_coingecko_id(self, symbol: str) -> str:
        """Convert symbol to CoinGecko ID"""
        symbol_map = {
            'BTC': 'bitcoin',
            'ETH': 'ethereum',
            'SOL': 'solana',
            'ADA': 'cardano',
            'DOT': 'polkadot',
            'LINK': 'chainlink',
            'UNI': 'uniswap',
            'AVAX': 'avalanche-2',
            'MATIC': 'matic-network'
        }
        
        base_symbol = symbol.replace('USDT', '').replace('USD', '')
        return symbol_map.get(base_symbol, base_symbol.lower())
    
    def _extract_price_from_text(self, text: str) -> Optional[float]:
        """Extract price from text"""
        try:
            # Remove currency symbols and commas
            cleaned_text = re.sub(r'[^\d.]', '', text.replace(',', ''))
            
            # Extract first number that looks like a price
            price_match = re.search(r'\d+\.?\d*', cleaned_text)
            if price_match:
                return float(price_match.group())
                
        except Exception as e:
            logger.debug(f"🔍 [PRICE-EXTRACT] Error extracting price from '{text}': {e}")
        
        return None
    
    def _extract_market_cap(self, soup: BeautifulSoup) -> Optional[float]:
        """Extract market cap from soup"""
        try:
            # Look for market cap elements
            market_cap_elements = soup.find_all(text=re.compile(r'Market Cap', re.IGNORECASE))
            for element in market_cap_elements:
                parent = element.parent
                if parent:
                    # Look for price in nearby elements
                    siblings = parent.find_next_siblings()
                    for sibling in siblings[:3]:  # Check next 3 siblings
                        if sibling.get_text():
                            price = self._extract_price_from_text(sibling.get_text())
                            if price and price > 1000:  # Market cap should be substantial
                                return price
                                
        except Exception as e:
            logger.debug(f"🔍 [MARKET-CAP] Error extracting market cap: {e}")
        
        return None
    
    def _extract_volume(self, soup: BeautifulSoup) -> Optional[float]:
        """Extract volume from soup"""
        try:
            # Look for volume elements
            volume_elements = soup.find_all(text=re.compile(r'Volume', re.IGNORECASE))
            for element in volume_elements:
                parent = element.parent
                if parent:
                    # Look for price in nearby elements
                    siblings = parent.find_next_siblings()
                    for sibling in siblings[:3]:
                        if sibling.get_text():
                            price = self._extract_price_from_text(sibling.get_text())
                            if price and price > 0:
                                return price
                                
        except Exception as e:
            logger.debug(f"🔍 [VOLUME] Error extracting volume: {e}")
        
        return None
    
    async def _check_rate_limit(self, domain: str) -> bool:
        """Check rate limit for domain"""
        try:
            now = time.time()
            rate_limit = self.rate_limits.get(domain, 1)
            last_request = self.last_request_times.get(domain, 0)
            
            if now - last_request < rate_limit:
                return False
            
            self.last_request_times[domain] = now
            return True
            
        except Exception as e:
            logger.error(f"❌ [RATE-LIMIT] Error checking rate limit: {e}")
            return True
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            
            logger.info("🧹 [PRICE-CRAWLER] Cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ [PRICE-CRAWLER] Error during cleanup: {e}")

class CryptoNewsCrawler:
    """News and sentiment data crawler"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.news_sources = {
            'cointelegraph': 'https://cointelegraph.com/rss',
            'cryptonews': 'https://cryptonews.com/news/feed/',
            'decrypt': 'https://decrypt.co/feed',
            'coindesk': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
            'bitcoinist': 'https://bitcoinist.com/feed/'
        }
        
        logger.info("📰 [NEWS-CRAWLER] Crypto news crawler initialized")
    
    async def initialize(self) -> bool:
        """Initialize the news crawler"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            logger.info("✅ [NEWS-CRAWLER] Initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ [NEWS-CRAWLER] Failed to initialize: {e}")
            return False
    
    async def crawl_crypto_news(self, symbol: str = None) -> List[NewsItem]:
        """Crawl multiple crypto news sources"""
        try:
            news_items = []
            
            for source_name, rss_url in self.news_sources.items():
                try:
                    source_news = await self._crawl_rss_feed(source_name, rss_url, symbol)
                    news_items.extend(source_news)
                except Exception as e:
                    logger.debug(f"🔍 [NEWS-SOURCE] {source_name} failed: {e}")
            
            # Sort by timestamp (newest first)
            news_items.sort(key=lambda x: x.timestamp, reverse=True)
            
            logger.info(f"📰 [NEWS-SUCCESS] Crawled {len(news_items)} news items")
            return news_items[:50]  # Return top 50 items
            
        except Exception as e:
            logger.error(f"❌ [NEWS-CRAWLER] Error crawling news: {e}")
            return []
    
    async def _crawl_rss_feed(self, source_name: str, rss_url: str, symbol: str = None) -> List[NewsItem]:
        """Crawl RSS feed for news items"""
        try:
            async with self.session.get(rss_url) as response:
                if response.status == 200:
                    rss_content = await response.text()
                    feed = feedparser.parse(rss_content)
                    
                    news_items = []
                    for entry in feed.entries[:20]:  # Limit to 20 items per source
                        # Filter by symbol if specified
                        if symbol and symbol.upper() not in entry.title.upper() and symbol.upper() not in entry.get('summary', '').upper():
                            continue
                        
                        news_item = NewsItem(
                            title=entry.title,
                            content=entry.get('summary', ''),
                            url=entry.link,
                            source=source_name,
                            timestamp=datetime.now()  # RSS timestamps can be inconsistent
                        )
                        
                        news_items.append(news_item)
                    
                    return news_items
                else:
                    logger.warning(f"⚠️ [RSS-CRAWLER] HTTP {response.status} for {source_name}")
                    
        except Exception as e:
            logger.debug(f"🔍 [RSS-CRAWLER] Error crawling {source_name}: {e}")
        
        return []
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            
            logger.info("🧹 [NEWS-CRAWLER] Cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ [NEWS-CRAWLER] Error during cleanup: {e}")

# Global instances
price_crawler = CryptoPriceCrawler()
news_crawler = CryptoNewsCrawler()

async def initialize_web_crawling_infrastructure() -> bool:
    """Initialize all web crawling components"""
    try:
        price_success = await price_crawler.initialize()
        news_success = await news_crawler.initialize()
        
        return price_success and news_success
        
    except Exception as e:
        logger.error(f"❌ [WEB-CRAWLING] Failed to initialize: {e}")
        return False

async def get_crawled_price_data(symbol: str) -> List[CrawledData]:
    """Get price data from web crawlers"""
    try:
        tasks = [
            price_crawler.crawl_coinmarketcap(symbol),
            price_crawler.crawl_coingecko(symbol)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        crawled_data = []
        for result in results:
            if isinstance(result, CrawledData):
                crawled_data.append(result)
        
        return crawled_data
        
    except Exception as e:
        logger.error(f"❌ [CRAWLED-PRICE] Error getting crawled data: {e}")
        return []

async def get_crypto_news(symbol: str = None) -> List[NewsItem]:
    """Get crypto news items"""
    return await news_crawler.crawl_crypto_news(symbol)
