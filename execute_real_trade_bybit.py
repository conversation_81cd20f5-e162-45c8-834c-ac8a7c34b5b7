#!/usr/bin/env python3
"""
Execute a real money trade on Bybit to verify live trading system
CRITICAL: This executes REAL MONEY trades - NOT simulation
"""

import os
import sys
import time
from pathlib import Path
from decimal import Decimal

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def execute_real_trade():
    """Execute a real money trade on Bybit"""
    print("🚨 [LIVE TRADE] EXECUTING REAL MONEY TRADE ON BYBIT")
    print("🚨 [WARNING] This will use REAL MONEY - NOT simulation")
    print("=" * 60)
    
    try:
        from pybit.unified_trading import HTTP
        
        # Get credentials
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        testnet = os.getenv('BYBIT_TESTNET', 'false').lower() == 'true'
        
        if testnet:
            print("❌ [CRITICAL] Testnet mode detected - ABORTING")
            return False
        
        # Initialize session
        session = HTTP(api_key=api_key, api_secret=api_secret, testnet=testnet)
        print("✅ [BYBIT] Live trading session initialized")
        
        # Get initial balance
        print("\n🔧 [STEP 1] Getting initial balance...")
        initial_balance = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
        
        if initial_balance.get('retCode') != 0:
            print(f"❌ [STEP 1] Failed to get initial balance: {initial_balance}")
            return False
        
        initial_usdt = float(initial_balance['result']['list'][0]['coin'][0]['walletBalance'])
        print(f"💰 [STEP 1] Initial USDT balance: ${initial_usdt:.2f}")
        
        # Calculate trade amount (use minimum $15 for Bybit requirements)
        trade_amount_usd = max(15.0, min(20.0, initial_usdt * 0.25))  # Use $15-20 or 25% of balance
        print(f"📊 [STEP 1] Trade amount: ${trade_amount_usd:.2f} USDT")

        if trade_amount_usd > initial_usdt:
            print("❌ [STEP 1] Insufficient balance for minimum trade")
            return False
        
        # Get BTC price to calculate quantity
        print("\n🔧 [STEP 2] Getting BTC price...")
        ticker = session.get_tickers(category="spot", symbol="BTCUSDT")
        
        if ticker.get('retCode') != 0:
            print(f"❌ [STEP 2] Failed to get BTC price: {ticker}")
            return False
        
        btc_price = float(ticker['result']['list'][0]['lastPrice'])
        btc_quantity = trade_amount_usd / btc_price
        print(f"📊 [STEP 2] BTC price: ${btc_price:.2f}")
        print(f"📊 [STEP 2] BTC quantity to buy: {btc_quantity:.6f}")
        
        # Check minimum order requirements
        instruments = session.get_instruments_info(category="spot", symbol="BTCUSDT")
        if instruments.get('retCode') == 0:
            lot_filter = instruments['result']['list'][0]['lotSizeFilter']
            min_qty = float(lot_filter['minOrderQty'])

            print(f"📊 [STEP 2] Minimum order quantity: {min_qty:.6f} BTC")

            if btc_quantity < min_qty:
                btc_quantity = min_qty
                trade_amount_usd = btc_quantity * btc_price
                print(f"📊 [STEP 2] Adjusted to minimum: {btc_quantity:.6f} BTC (${trade_amount_usd:.2f})")

            # Round to 6 decimal places for Bybit
            btc_quantity = round(btc_quantity, 6)
            print(f"📊 [STEP 2] Final quantity: {btc_quantity:.6f} BTC")
        
        # Execute BUY order using quote amount (USDT) instead of base amount (BTC)
        print(f"\n🚨 [STEP 3] EXECUTING REAL BUY ORDER")
        print(f"🚨 [LIVE TRADE] Buying ${trade_amount_usd:.2f} USDT worth of BTC")

        buy_order = session.place_order(
            category="spot",
            symbol="BTCUSDT",
            side="Buy",
            orderType="Market",
            qty=f"{trade_amount_usd:.2f}",  # Use USDT amount
            isLeverage=0,
            orderFilter="Order"  # Use quote currency for market orders
        )
        
        print(f"📊 [STEP 3] Buy order response: {buy_order}")
        
        if buy_order.get('retCode') != 0:
            print(f"❌ [STEP 3] Buy order failed: {buy_order}")
            return False
        
        buy_order_id = buy_order['result']['orderId']
        print(f"✅ [STEP 3] BUY ORDER EXECUTED - Order ID: {buy_order_id}")
        
        # Wait for order to fill
        print("\n🔧 [STEP 4] Waiting for order to fill...")
        time.sleep(3)
        
        # Check order status
        order_status = session.get_order_history(category="spot", orderId=buy_order_id)
        if order_status.get('retCode') == 0 and order_status['result']['list']:
            order_info = order_status['result']['list'][0]
            print(f"📊 [STEP 4] Order status: {order_info.get('orderStatus', 'Unknown')}")
            print(f"📊 [STEP 4] Executed quantity: {order_info.get('cumExecQty', '0')}")
            print(f"📊 [STEP 4] Executed value: ${order_info.get('cumExecValue', '0')}")
        
        # Get updated balance
        print("\n🔧 [STEP 5] Checking balance changes...")
        time.sleep(2)
        
        final_balance = session.get_wallet_balance(accountType="UNIFIED")
        if final_balance.get('retCode') == 0:
            # Check USDT balance
            usdt_balance = 0
            btc_balance = 0
            
            for coin_data in final_balance['result']['list'][0]['coin']:
                if coin_data['coin'] == 'USDT':
                    usdt_balance = float(coin_data['walletBalance'])
                elif coin_data['coin'] == 'BTC':
                    btc_balance = float(coin_data['walletBalance'])
            
            print(f"💰 [STEP 5] Final USDT balance: ${usdt_balance:.2f}")
            print(f"💰 [STEP 5] Final BTC balance: {btc_balance:.6f}")
            print(f"💰 [STEP 5] USDT change: ${usdt_balance - initial_usdt:.2f}")
            
            if abs(usdt_balance - initial_usdt) > 1.0:  # Significant change
                print("✅ [STEP 5] BALANCE CHANGE DETECTED - REAL MONEY TRADING CONFIRMED")
                
                # Now sell the BTC back to complete the round trip
                print(f"\n🚨 [STEP 6] EXECUTING REAL SELL ORDER")
                print(f"🚨 [LIVE TRADE] Selling {btc_balance:.6f} BTC back to USDT")
                
                sell_order = session.place_order(
                    category="spot",
                    symbol="BTCUSDT",
                    side="Sell",
                    orderType="Market",
                    qty=f"{btc_balance:.6f}",  # Format to 6 decimal places
                    timeInForce="IOC"
                )
                
                if sell_order.get('retCode') == 0:
                    sell_order_id = sell_order['result']['orderId']
                    print(f"✅ [STEP 6] SELL ORDER EXECUTED - Order ID: {sell_order_id}")
                    
                    # Final verification
                    time.sleep(3)
                    verification_balance = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
                    if verification_balance.get('retCode') == 0:
                        final_usdt = float(verification_balance['result']['list'][0]['coin'][0]['walletBalance'])
                        print(f"💰 [FINAL] Final USDT balance: ${final_usdt:.2f}")
                        print(f"💰 [FINAL] Net change: ${final_usdt - initial_usdt:.2f}")
                    
                    print("\n" + "=" * 60)
                    print("✅ REAL MONEY TRADING VERIFICATION COMPLETE")
                    print("✅ Successfully executed BUY and SELL orders")
                    print("✅ Balance changes confirmed")
                    print("✅ System ready for continuous live trading")
                    print("=" * 60)
                    return True
                else:
                    print(f"⚠️ [STEP 6] Sell order failed: {sell_order}")
                    print("⚠️ BTC position remains - manual intervention may be needed")
                    return True  # Still consider successful since buy worked
            else:
                print("⚠️ [STEP 5] No significant balance change detected")
                return False
        else:
            print(f"❌ [STEP 5] Failed to get final balance: {final_balance}")
            return False
            
    except Exception as e:
        print(f"❌ [ERROR] Trade execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚨 BYBIT REAL MONEY TRADE VERIFICATION")
    print("🚨 WARNING: This executes REAL trades with REAL money")
    print("=" * 60)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Execute the trade
    success = execute_real_trade()
    
    if success:
        print("\n🎉 LIVE TRADING VERIFICATION SUCCESSFUL")
        sys.exit(0)
    else:
        print("\n❌ LIVE TRADING VERIFICATION FAILED")
        sys.exit(1)
