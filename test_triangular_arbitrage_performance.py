#!/usr/bin/env python3
"""
Test Triangular Arbitrage Performance Fixes

This script tests the performance optimizations implemented to fix the
triangular arbitrage hanging issue with 545 currencies.

TESTS:
1. Verify triangular arbitrage completes within timeout
2. Verify direct trading opportunities execute independently
3. Verify trading loop doesn't hang
4. Verify system proceeds to trade execution within 5 minutes
"""

import asyncio
import time
import logging
import sys
import os
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))
sys.path.insert(0, str(PROJECT_ROOT / "src"))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_triangular_arbitrage_performance():
    """Test that triangular arbitrage scanning completes within reasonable time"""
    logger.info("🧪 [TEST-1] Testing triangular arbitrage performance...")
    
    try:
        # Import the trading engine
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        
        # Create mock exchange clients
        class MockClient:
            def get_price(self, symbol):
                return 1.0  # Mock price

        mock_clients = {'bybit': MockClient()}

        # Create a mock trading engine for testing
        engine = MultiCurrencyTradingEngine(exchange_clients=mock_clients)
        
        # Create mock exchange pairs to simulate 545 currencies scenario
        mock_pairs = {}
        currencies = [f"COIN{i}" for i in range(100)]  # 100 currencies for testing
        currencies.extend(['USDT', 'BTC', 'ETH', 'SOL', 'ADA', 'DOT'])  # Add real currencies
        
        # Create mock pair data
        from dataclasses import dataclass
        
        @dataclass
        class MockPairInfo:
            base: str
            quote: str
            symbol: str
        
        # Generate pairs
        for i, base in enumerate(currencies[:50]):  # Limit to 50 for testing
            for quote in ['USDT', 'BTC', 'ETH']:
                if base != quote:
                    symbol = f"{base}{quote}"
                    mock_pairs[symbol] = MockPairInfo(base=base, quote=quote, symbol=symbol)
        
        # Set up mock active pairs
        engine.active_pairs = {'bybit': mock_pairs}
        
        # Test triangular arbitrage with timeout
        start_time = time.time()
        
        logger.info(f"🔍 [TEST-1] Starting triangular arbitrage scan with {len(currencies)} currencies...")
        opportunities = await engine._find_triangular_arbitrage()
        
        elapsed_time = time.time() - start_time
        
        # Verify it completed within timeout (should be < 30 seconds)
        if elapsed_time < 30:
            logger.info(f"✅ [TEST-1] PASSED: Triangular arbitrage completed in {elapsed_time:.2f}s")
            return True
        else:
            logger.error(f"❌ [TEST-1] FAILED: Triangular arbitrage took {elapsed_time:.2f}s (timeout: 30s)")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-1] FAILED: Exception during triangular arbitrage test: {e}")
        return False

async def test_opportunity_scanning_with_timeout():
    """Test that opportunity scanning completes within timeout"""
    logger.info("🧪 [TEST-2] Testing opportunity scanning with timeout protection...")
    
    try:
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine

        # Create mock exchange clients
        class MockClient:
            def get_price(self, symbol):
                return 1.0

        mock_clients = {'bybit': MockClient()}
        engine = MultiCurrencyTradingEngine(exchange_clients=mock_clients)
        
        # Mock the methods to simulate slow operations
        async def mock_slow_arbitrage():
            await asyncio.sleep(35)  # Simulate slow operation that would timeout
            return []
        
        async def mock_fast_direct():
            await asyncio.sleep(1)  # Fast direct opportunities
            return []
        
        # Replace methods with mocks
        engine._find_arbitrage_opportunities = mock_slow_arbitrage
        engine._find_direct_trading_opportunities = mock_fast_direct
        engine._find_rebalancing_opportunities = lambda: []
        engine._find_portfolio_rebalancing_opportunities = lambda: []
        
        start_time = time.time()
        
        logger.info("🔍 [TEST-2] Starting opportunity scan with timeout protection...")
        opportunities = await engine.find_trading_opportunities()
        
        elapsed_time = time.time() - start_time
        
        # Should complete within 2 minutes (120s timeout)
        if elapsed_time < 120:
            logger.info(f"✅ [TEST-2] PASSED: Opportunity scanning completed in {elapsed_time:.2f}s")
            return True
        else:
            logger.error(f"❌ [TEST-2] FAILED: Opportunity scanning took {elapsed_time:.2f}s")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-2] FAILED: Exception during opportunity scanning test: {e}")
        return False

async def test_trading_cycle_execution():
    """Test that trading cycle executes without hanging"""
    logger.info("🧪 [TEST-3] Testing trading cycle execution...")
    
    try:
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine

        # Create mock exchange clients
        class MockClient:
            def get_price(self, symbol):
                return 1.0

        mock_clients = {'bybit': MockClient()}
        engine = MultiCurrencyTradingEngine(exchange_clients=mock_clients)
        
        # Mock methods for testing
        async def mock_find_opportunities():
            # Simulate finding direct trading opportunities
            from src.trading.multi_currency_trading_engine import TradingOpportunity
            from decimal import Decimal
            
            # Create a mock opportunity
            @dataclass
            class MockPair:
                symbol: str = "BTCUSDT"
                base: str = "BTC"
                quote: str = "USDT"
            
            opportunity = TradingOpportunity(
                pair=MockPair(),
                side='buy',
                amount=Decimal('0.001'),
                price=Decimal('50000'),
                expected_profit=Decimal('1.5'),
                confidence=0.75,
                strategy='direct_trading',
                risk_score=0.3,
                execution_priority=1
            )
            return [opportunity]
        
        async def mock_execute_opportunity(opportunity):
            return {"success": True, "message": "Mock execution successful"}
        
        # Replace methods
        engine.find_trading_opportunities = mock_find_opportunities
        engine.execute_trading_opportunity = mock_execute_opportunity
        
        start_time = time.time()
        
        logger.info("🔍 [TEST-3] Executing trading cycle...")
        result = await engine._execute_trading_cycle()
        
        elapsed_time = time.time() - start_time
        
        # Should complete quickly (< 10 seconds)
        if elapsed_time < 10 and result.get('success', False):
            logger.info(f"✅ [TEST-3] PASSED: Trading cycle completed in {elapsed_time:.2f}s")
            return True
        else:
            logger.error(f"❌ [TEST-3] FAILED: Trading cycle took {elapsed_time:.2f}s or failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-3] FAILED: Exception during trading cycle test: {e}")
        return False

async def test_five_minute_execution_requirement():
    """Test that system can execute trades within 5 minutes"""
    logger.info("🧪 [TEST-4] Testing 5-minute execution requirement...")
    
    try:
        start_time = time.time()
        
        # Run all previous tests to simulate full system startup
        test1_result = await test_triangular_arbitrage_performance()
        test2_result = await test_opportunity_scanning_with_timeout()
        test3_result = await test_trading_cycle_execution()
        
        elapsed_time = time.time() - start_time
        
        # Should complete within 5 minutes (300 seconds)
        if elapsed_time < 300 and all([test1_result, test2_result, test3_result]):
            logger.info(f"✅ [TEST-4] PASSED: Full system execution completed in {elapsed_time:.2f}s (< 5 minutes)")
            return True
        else:
            logger.error(f"❌ [TEST-4] FAILED: System took {elapsed_time:.2f}s or tests failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-4] FAILED: Exception during 5-minute test: {e}")
        return False

async def main():
    """Run all performance tests"""
    logger.info("=" * 60)
    logger.info("🧪 TRIANGULAR ARBITRAGE PERFORMANCE TESTS")
    logger.info("🎯 Testing fixes for 545 currency hanging issue")
    logger.info("=" * 60)
    
    tests = [
        ("Triangular Arbitrage Performance", test_triangular_arbitrage_performance),
        ("Opportunity Scanning Timeout", test_opportunity_scanning_with_timeout),
        ("Trading Cycle Execution", test_trading_cycle_execution),
        ("5-Minute Execution Requirement", test_five_minute_execution_requirement),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running {test_name}...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("✅ ALL TESTS PASSED - Performance fixes are working correctly!")
        return 0
    else:
        logger.error("❌ SOME TESTS FAILED - Performance issues may still exist")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
