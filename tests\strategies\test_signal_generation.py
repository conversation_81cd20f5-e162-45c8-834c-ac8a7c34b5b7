#!/usr/bin/env python3
"""
Test signal generation with actual market data
"""
import asyncio
import sys
import os
sys.path.append('src')

# Set up environment
os.environ['LIVE_TRADING'] = 'true'
os.environ['REAL_MONEY_TRADING'] = 'true'

async def test_signal_generation():
    try:
        from main import ComprehensiveLiveTradingSystem
        
        system = ComprehensiveLiveTradingSystem()
        
        # Test with actual market data structure from debug
        market_data = {
            'price_data': {
                'bybit_client': {
                    'BTC-USD': {
                        'price': 104936.4,
                        'bid': 104936.8,
                        'ask': 104936.9,
                        'volume': 5179.312972,
                        'high': 106216.1,
                        'low': 104355.0,
                        'change': -359.20000000001164,
                        'percentage': -0.34113486223547007,
                        'orderbook': {'bids': [], 'asks': [], 'spread': 0.0}
                    },
                    'ETH-USD': {
                        'price': 3000.0,
                        'volume': 1000.0,
                        'percentage': 0.5,  # Should trigger moderate buy
                        'change': 15.0
                    },
                    'SOL-USD': {
                        'price': 100.0,
                        'volume': 500.0,
                        'percentage': -0.8,  # Should trigger moderate sell
                        'change': -0.8
                    }
                }
            }
        }
        
        print("Testing signal generation with actual market data structure...")
        print(f"BTC: {market_data['price_data']['bybit_client']['BTC-USD']['percentage']:.2f}% (volume: {market_data['price_data']['bybit_client']['BTC-USD']['volume']:.0f})")
        print(f"ETH: {market_data['price_data']['bybit_client']['ETH-USD']['percentage']:.2f}% (volume: {market_data['price_data']['bybit_client']['ETH-USD']['volume']:.0f})")
        print(f"SOL: {market_data['price_data']['bybit_client']['SOL-USD']['percentage']:.2f}% (volume: {market_data['price_data']['bybit_client']['SOL-USD']['volume']:.0f})")
        
        # Test simple price signals
        signals = await system._generate_simple_price_signals(market_data)
        print(f'\nGenerated {len(signals)} simple price signals:')
        for signal_id, signal in signals.items():
            print(f'  {signal_id}: {signal["action"]} {signal["symbol"]} - {signal["reasoning"]} (confidence: {signal["confidence"]:.2f})')
        
        # Test if the issue is in the data processing
        print("\nDebugging signal generation logic...")
        for exchange, symbols_data in market_data['price_data'].items():
            print(f"Exchange: {exchange}")
            for symbol, data in symbols_data.items():
                print(f"  Symbol: {symbol}")
                print(f"    Data type: {type(data)}")
                print(f"    Data keys: {list(data.keys()) if isinstance(data, dict) else 'Not dict'}")
                
                if isinstance(data, dict):
                    try:
                        price = float(data.get('price', 0))
                        volume = float(data.get('volume', 0))
                        percentage = float(data.get('percentage', 0))
                        
                        print(f"    Price: {price}, Volume: {volume}, Percentage: {percentage}")
                        
                        # Test signal conditions
                        if percentage > 0.2 and volume > 10:
                            print(f"    ✅ Should generate BUY signal (>{percentage:.2f}% > 0.2%, volume {volume:.0f} > 10)")
                        elif percentage < -0.2 and volume > 10:
                            print(f"    ✅ Should generate SELL signal ({percentage:.2f}% < -0.2%, volume {volume:.0f} > 10)")
                        else:
                            print(f"    ❌ No signal conditions met")
                            print(f"       Percentage check: {percentage} > 0.2 = {percentage > 0.2} OR {percentage} < -0.2 = {percentage < -0.2}")
                            print(f"       Volume check: {volume} > 10 = {volume > 10}")
                    
                    except Exception as e:
                        print(f"    Error processing data: {e}")
            
    except Exception as e:
        print(f"Error testing signal generation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_signal_generation())
