#!/usr/bin/env python3
"""
Complete Code Sample for Coinbase Support
This shows the exact code used to generate JWT and make API requests
"""

import os
import sys
import time
import jwt
import json
import requests
from pathlib import Path
from dotenv import load_dotenv
from cryptography.hazmat.primitives import serialization

def load_credentials():
    """Load and decrypt Coinbase credentials"""
    load_dotenv()

    # Add src to path for imports
    sys.path.append(str(Path(__file__).parent / "src"))

    try:
        from src.utils.cryptography.secure_credentials import decrypt_value

        # Get encrypted credentials from .env
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')

        if not encrypted_api_key or not encrypted_private_key:
            raise ValueError("Missing encrypted credentials in .env file")

        # Decrypt credentials
        api_key_name = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)

        # Extract key ID from API key
        if "/apiKeys/" in api_key_name:
            key_id = api_key_name.split("/apiKeys/")[1]
        else:
            raise ValueError("Cannot extract key ID from API key format")

        return api_key_name, key_id, private_key_pem

    except Exception as e:
        print(f"❌ Failed to load credentials: {e}")
        return None, None, None

def create_jwt_token(api_key_name, private_key_pem, key_id, method, endpoint):
    """Create JWT token exactly as per Coinbase documentation"""
    
    # Load private key
    private_key = serialization.load_pem_private_key(
        private_key_pem.encode('utf-8'),
        password=None
    )
    
    # Create JWT payload - exactly as per your docs
    now = int(time.time())
    payload = {
        'iss': 'cdp',                    # Issuer: Coinbase Developer Platform
        'nbf': now,                      # Not before: current timestamp
        'exp': now + 120,                # Expires: 2 minutes from now
        'sub': api_key_name,             # Subject: full API key name
        'uri': f'{method} {endpoint}'    # URI: HTTP method + endpoint path
    }
    
    # Create JWT headers - exactly as per your docs
    headers = {
        'alg': 'ES256',                  # Algorithm: ECDSA with SHA-256
        'kid': key_id,                   # Key ID
        'typ': 'JWT'                     # Token type
    }
    
    # Generate JWT token
    token = jwt.encode(
        payload,
        private_key,
        algorithm='ES256',
        headers=headers
    )
    
    return token

def make_api_request(endpoint, method='GET'):
    """Make API request with JWT authentication"""

    # Get credentials
    api_key_name, key_id, private_key_pem = load_credentials()

    if not all([api_key_name, key_id, private_key_pem]):
        print("❌ Failed to load credentials")
        return None

    # Generate JWT token
    jwt_token = create_jwt_token(api_key_name, private_key_pem, key_id, method, endpoint)
    
    # Create request headers
    request_headers = {
        'Authorization': f'Bearer {jwt_token}',
        'Content-Type': 'application/json',
        'User-Agent': 'AutoGPT-Trader/1.0'
    }
    
    # Make the API request
    url = f'https://api.coinbase.com{endpoint}'
    
    print(f"🔧 Making {method} request to: {url}")
    print(f"📋 JWT Token: {jwt_token}")
    print(f"📋 Headers: {json.dumps(request_headers, indent=2)}")
    
    try:
        response = requests.request(
            method=method,
            url=url,
            headers=request_headers,
            timeout=10
        )
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        print(f"📄 Response Body: {response.text}")
        
        return response
        
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def test_public_endpoint():
    """Test public endpoint that works"""
    print("=" * 60)
    print("🔍 Testing PUBLIC endpoint (this works):")
    print("=" * 60)
    
    response = make_api_request('/api/v3/brokerage/time', 'GET')
    return response

def test_private_endpoint():
    """Test private endpoint that fails"""
    print("\n" + "=" * 60)
    print("🔍 Testing PRIVATE endpoint (this fails with 401):")
    print("=" * 60)
    
    response = make_api_request('/api/v3/brokerage/accounts', 'GET')
    return response

def main():
    """Main function to demonstrate the issue"""
    print("🔧 Complete Code Sample for Coinbase Support")
    print("This shows the exact code and requests that demonstrate the issue")
    print("\n")
    
    # Test public endpoint (works)
    public_response = test_public_endpoint()
    
    # Test private endpoint (fails)
    private_response = test_private_endpoint()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY:")
    print("=" * 60)
    
    if public_response and public_response.status_code == 200:
        print("✅ Public endpoint (/api/v3/brokerage/time): SUCCESS")
    else:
        print("❌ Public endpoint (/api/v3/brokerage/time): FAILED")
    
    if private_response and private_response.status_code == 200:
        print("✅ Private endpoint (/api/v3/brokerage/accounts): SUCCESS")
    else:
        print("❌ Private endpoint (/api/v3/brokerage/accounts): FAILED")
    
    print("\n🔍 Key Observation:")
    print("   Same JWT generation code works for public, fails for private endpoints")
    print("   This indicates an account-level permission issue, not a technical JWT problem")

if __name__ == "__main__":
    main()
