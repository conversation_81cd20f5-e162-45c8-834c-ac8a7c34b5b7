import websockets
import asyncio
import json

class MarketDataStreamer:
    def __init__(self, symbols, url='wss://stream.example.com'):
        self.symbols = symbols
        self.url = url
        self.subscribers = []

    async def stream_data(self):
        async with websockets.connect(self.url) as ws:
            await self.subscribe(ws)
            while True:
                data = await ws.recv()
                processed = self.process_data(json.loads(data))
                self.notify_subscribers(processed)

    def process_data(self, raw_data):
        # Add data validation and transformation
        return {
            'timestamp': raw_data['t'],
            'symbol': raw_data['s'],
            'price': float(raw_data['p']),
            'volume': float(raw_data['v'])
        }

    def subscribe(self, subscriber):
        self.subscribers.append(subscriber)

    def notify_subscribers(self, data):
        for subscriber in self.subscribers:
            subscriber.update(data)

import os

class MarketDataStreamer:
    def __init__(self, symbols):
        self.api_key = os.getenv('EXCHANGE_API_KEY')
        self.api_secret = os.getenv('EXCHANGE_API_SECRET')
        
    async def _authenticate(self, ws):
        timestamp = str(int(time.time() * 1000))
        signature = hmac.new(
            self.api_secret.encode(),
            timestamp.encode(),
            hashlib.sha256
        ).hexdigest()
        
        auth_msg = {
            'action': 'authenticate',
            'key': self.api_key,
            'signature': signature,
            'timestamp': timestamp
        }
        await ws.send(json.dumps(auth_msg))

        async def _subscribe(self, ws, symbol):
            subscribe_msg = {
                'action': 'subscribe',
                'channel': 'ticker',
                'symbol': symbol
            }
            await ws.send(json.dumps(subscribe_msg))

        async def _listen(self, ws):
            while True:
                data = await ws.recv()
                processed_data = self.process_data(data)
                self.notify_subscribers(processed_data)

        async def stream_data(self, symbols):
            async with websockets.connect(self.url) as ws:
                await self._authenticate(ws)
                for symbol in symbols:
                    await self._subscribe(ws, symbol)
                    await self._listen(ws)
                await ws.close()

    def process_data(self, data):
        # Process the received data and return it as a dictionary
        # You can customize this function to handle the specific data format and structure
        # based on your requirements
        return data

    def notify_subscribers(self, data):
        # Notify subscribers about the received data
        # You can customize this function to handle the notification logic
        pass

    def start_streaming(self, symbols):
        self.symbols = symbols
        self.stream_data(self.symbols)

class AlphaFactory:
    def generate_alpha(self, data):
        return (
            data['volume_imbalance'] * 0.3 +
            data['correlation_matrix'].rank() * 0.4 -
            data['microprice'].diff(10) * 0.3
        )
