"""
Variational Autoencoder for Market Anomaly Detection
Detects unusual market patterns and regime changes
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class VAEConfig:
    """Configuration for Variational Autoencoder"""
    input_dim: int = 100
    latent_dim: int = 20
    hidden_dims: List[int] = None
    dropout: float = 0.1
    beta: float = 1.0  # KL divergence weight
    
    def __post_init__(self):
        if self.hidden_dims is None:
            self.hidden_dims = [512, 256, 128, 64]

class MarketVAE(nn.Module):
    """
    Variational Autoencoder for market anomaly detection
    Features:
    - Probabilistic latent space modeling
    - Anomaly detection through reconstruction error
    - Market regime identification
    - Uncertainty quantification
    """
    
    def __init__(self, config: VAEConfig):
        super().__init__()
        
        self.config = config
        self.latent_dim = config.latent_dim
        
        # Build encoder
        encoder_layers = []
        in_dim = config.input_dim
        
        for hidden_dim in config.hidden_dims:
            encoder_layers.extend([
                nn.Linear(in_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(config.dropout)
            ])
            in_dim = hidden_dim
        
        self.encoder = nn.Sequential(*encoder_layers)
        
        # Latent space parameters
        self.fc_mu = nn.Linear(config.hidden_dims[-1], config.latent_dim)
        self.fc_logvar = nn.Linear(config.hidden_dims[-1], config.latent_dim)
        
        # Build decoder
        decoder_layers = []
        hidden_dims_reversed = list(reversed(config.hidden_dims))
        
        # First layer from latent space
        decoder_layers.extend([
            nn.Linear(config.latent_dim, hidden_dims_reversed[0]),
            nn.BatchNorm1d(hidden_dims_reversed[0]),
            nn.ReLU(),
            nn.Dropout(config.dropout)
        ])
        
        # Hidden layers
        for i in range(len(hidden_dims_reversed) - 1):
            decoder_layers.extend([
                nn.Linear(hidden_dims_reversed[i], hidden_dims_reversed[i + 1]),
                nn.BatchNorm1d(hidden_dims_reversed[i + 1]),
                nn.ReLU(),
                nn.Dropout(config.dropout)
            ])
        
        # Output layer
        decoder_layers.append(nn.Linear(hidden_dims_reversed[-1], config.input_dim))
        
        self.decoder = nn.Sequential(*decoder_layers)
        
        # Anomaly detection threshold (learned parameter)
        self.anomaly_threshold = nn.Parameter(torch.tensor(2.0))
        
        # Market regime classifier
        self.regime_classifier = nn.Sequential(
            nn.Linear(config.latent_dim, config.latent_dim * 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.latent_dim * 2, 4)  # Bull, Bear, Sideways, High Vol
        )
        
    def encode(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Encode input to latent space parameters"""
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar
    
    def reparameterize(self, mu: torch.Tensor, logvar: torch.Tensor) -> torch.Tensor:
        """Reparameterization trick for sampling"""
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z: torch.Tensor) -> torch.Tensor:
        """Decode latent representation to output"""
        return self.decoder(z)
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Forward pass through VAE"""
        # Encode
        mu, logvar = self.encode(x)
        
        # Sample from latent space
        z = self.reparameterize(mu, logvar)
        
        # Decode
        x_recon = self.decode(z)
        
        # Market regime prediction
        regime_logits = self.regime_classifier(z)
        
        return {
            'reconstruction': x_recon,
            'mu': mu,
            'logvar': logvar,
            'z': z,
            'regime_logits': regime_logits
        }
    
    def compute_loss(self, x: torch.Tensor, outputs: Dict[str, torch.Tensor],
                    regime_labels: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Compute VAE loss components"""
        x_recon = outputs['reconstruction']
        mu = outputs['mu']
        logvar = outputs['logvar']
        regime_logits = outputs['regime_logits']
        
        # Reconstruction loss
        recon_loss = F.mse_loss(x_recon, x, reduction='sum')
        
        # KL divergence loss
        kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
        
        # Total VAE loss
        vae_loss = recon_loss + self.config.beta * kl_loss
        
        losses = {
            'total_loss': vae_loss,
            'reconstruction_loss': recon_loss,
            'kl_loss': kl_loss
        }
        
        # Add regime classification loss if labels provided
        if regime_labels is not None:
            regime_loss = F.cross_entropy(regime_logits, regime_labels)
            losses['regime_loss'] = regime_loss
            losses['total_loss'] = losses['total_loss'] + regime_loss
        
        return losses
    
    def detect_anomalies(self, x: torch.Tensor, threshold_multiplier: float = 1.0) -> Dict[str, torch.Tensor]:
        """Detect anomalies based on reconstruction error"""
        with torch.no_grad():
            outputs = self.forward(x)
            x_recon = outputs['reconstruction']
            
            # Calculate reconstruction error
            recon_error = torch.mean((x - x_recon) ** 2, dim=1)
            
            # Adaptive threshold
            threshold = self.anomaly_threshold * threshold_multiplier
            
            # Anomaly scores and binary predictions
            anomaly_scores = recon_error / threshold
            is_anomaly = recon_error > threshold
            
            # Confidence based on latent space uncertainty
            mu = outputs['mu']
            logvar = outputs['logvar']
            uncertainty = torch.mean(torch.exp(logvar), dim=1)
            confidence = 1.0 / (1.0 + uncertainty)
            
        return {
            'anomaly_scores': anomaly_scores,
            'is_anomaly': is_anomaly,
            'reconstruction_error': recon_error,
            'confidence': confidence,
            'threshold': threshold
        }
    
    def generate_samples(self, num_samples: int, device: str = 'cpu') -> torch.Tensor:
        """Generate synthetic market data samples"""
        with torch.no_grad():
            # Sample from prior distribution
            z = torch.randn(num_samples, self.latent_dim).to(device)
            
            # Decode to generate samples
            generated_samples = self.decode(z)
            
        return generated_samples
    
    def interpolate_latent_space(self, x1: torch.Tensor, x2: torch.Tensor, 
                                num_steps: int = 10) -> torch.Tensor:
        """Interpolate between two points in latent space"""
        with torch.no_grad():
            # Encode both inputs
            mu1, _ = self.encode(x1)
            mu2, _ = self.encode(x2)
            
            # Create interpolation steps
            alphas = torch.linspace(0, 1, num_steps).unsqueeze(1)
            
            # Interpolate in latent space
            z_interp = alphas * mu2 + (1 - alphas) * mu1
            
            # Decode interpolated latent vectors
            x_interp = self.decode(z_interp)
            
        return x_interp
    
    def analyze_latent_space(self, x: torch.Tensor) -> Dict[str, Any]:
        """Analyze latent space structure"""
        with torch.no_grad():
            outputs = self.forward(x)
            mu = outputs['mu']
            logvar = outputs['logvar']
            z = outputs['z']
            
            # Calculate latent space statistics
            latent_mean = torch.mean(mu, dim=0)
            latent_std = torch.std(mu, dim=0)
            latent_var = torch.mean(torch.exp(logvar), dim=0)
            
            # Calculate pairwise distances in latent space
            distances = torch.cdist(z, z, p=2)
            avg_distance = torch.mean(distances)
            
            # Identify clusters using simple k-means-like approach
            num_clusters = min(5, x.size(0) // 10)
            if num_clusters > 1:
                cluster_centers = self._simple_kmeans(z, num_clusters)
            else:
                cluster_centers = None
            
        analysis = {
            'latent_mean': latent_mean.cpu().numpy(),
            'latent_std': latent_std.cpu().numpy(),
            'latent_variance': latent_var.cpu().numpy(),
            'average_distance': avg_distance.item(),
            'cluster_centers': cluster_centers.cpu().numpy() if cluster_centers is not None else None,
            'latent_embeddings': z.cpu().numpy()
        }
        
        return analysis
    
    def _simple_kmeans(self, data: torch.Tensor, k: int, max_iters: int = 100) -> torch.Tensor:
        """Simple k-means clustering for latent space analysis"""
        n, d = data.shape
        
        # Initialize centers randomly
        centers = data[torch.randperm(n)[:k]]
        
        for _ in range(max_iters):
            # Assign points to closest centers
            distances = torch.cdist(data, centers)
            assignments = torch.argmin(distances, dim=1)
            
            # Update centers
            new_centers = torch.zeros_like(centers)
            for i in range(k):
                mask = assignments == i
                if mask.sum() > 0:
                    new_centers[i] = data[mask].mean(dim=0)
                else:
                    new_centers[i] = centers[i]
            
            # Check for convergence
            if torch.allclose(centers, new_centers, atol=1e-4):
                break
            
            centers = new_centers
        
        return centers
    
    def get_regime_probabilities(self, x: torch.Tensor) -> torch.Tensor:
        """Get market regime probabilities"""
        with torch.no_grad():
            outputs = self.forward(x)
            regime_logits = outputs['regime_logits']
            regime_probs = F.softmax(regime_logits, dim=1)
        
        return regime_probs
    
    def update_anomaly_threshold(self, reconstruction_errors: torch.Tensor, 
                                percentile: float = 95.0):
        """Update anomaly threshold based on reconstruction errors"""
        threshold = torch.quantile(reconstruction_errors, percentile / 100.0)
        self.anomaly_threshold.data = threshold
        logger.info(f"Updated anomaly threshold to {threshold.item():.4f}")
    
    def save_model(self, path: str):
        """Save model with configuration"""
        torch.save({
            'model_state_dict': self.state_dict(),
            'config': self.config
        }, path)
        logger.info(f"VAE model saved to {path}")
    
    def load_model(self, path: str):
        """Load model with configuration"""
        checkpoint = torch.load(path, map_location='cpu')
        self.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"VAE model loaded from {path}")
    
    def get_model_complexity(self) -> Dict[str, int]:
        """Get model complexity metrics"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'latent_dimension': self.latent_dim,
            'input_dimension': self.config.input_dim
        }
