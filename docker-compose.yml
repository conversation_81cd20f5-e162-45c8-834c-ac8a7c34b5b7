version: '3.8'
services:
  trading-engine:
    build:
      context: .
      dockerfile: ./Dockerfile  # Changed to relative path for clarity
    restart: unless-stopped
    security_opt:
      - no-new-privileges
    read_only: true
    volumes:
      - type: bind
        source: ./config/tls
        target: /app/config/tls
        read_only: true
      - type: bind
        source: /var/run/pcscd  # Mount the host's pcscd socket
        target: /var/run/pcscd
        read_only: false
    environment:
      - VAULT_ADDR=https://vault:8200
      - HSM_MODULE=/usr/lib/libykcs11.so
      - PYTHONUNBUFFERED=1
      - ENVIRONMENT=production
    env_file:
      - .env
    depends_on:
      vault:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import socket; s = socket.socket(socket.AF_INET, socket.SOCK_STREAM); s.connect(('0.0.0.0', 8000)); s.close()"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s
    ports: # Explicitly expose the port
      - "8000:8000"
    
  vault:
    image: vault:latest
    volumes:
      - vault-data:/vault/data
      - ./config/vault.hcl:/vault/config/vault.hcl
    environment:
      - VAULT_RAFT_NODE_ID=node1
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
      - VAULT_API_ADDR=http://0.0.0.0:8200
      - VAULT_CLUSTER_ADDR=https://0.0.0.0:8201
    ports:
      - "8200:8200"
    cap_drop:
      - ALL
    healthcheck:
      test: ["CMD", "vault", "status", "-tls-skip-verify"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s
    command: ["vault", "server", "-config=/vault/config/vault.hcl"]
    
  redis:
    image: redis:7-alpine
    command: ["redis-server", "--appendonly", "yes"]
    networks:
      - trader-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    volumes:
      - redis_data:/data
    
volumes:
  hsm-socket: # Declare, but the source is defined in the service.
  vault-data:
  redis_data:

networks:
  trader-network:
    driver: bridge
