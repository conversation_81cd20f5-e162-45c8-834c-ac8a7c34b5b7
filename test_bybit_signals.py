#!/usr/bin/env python3
"""
Test Bybit Signal Generation
Verify that the signal generator produces Bybit symbols when BYBIT_ONLY_MODE is enabled
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_bybit_signal_generation():
    """Test that signal generator produces Bybit symbols"""
    print("🔧 [TEST] Testing Bybit signal generation...")
    
    # Setup Bybit-only environment
    os.environ["BYBIT_ONLY_MODE"] = "true"
    os.environ["COINBASE_ENABLED"] = "false"
    os.environ["LIVE_TRADING"] = "true"
    
    try:
        from trading.enhanced_signal_generator import EnhancedSignalGenerator
        from dotenv import load_dotenv
        
        load_dotenv()
        
        # Create a mock exchange manager
        class MockExchangeManager:
            def __init__(self):
                pass
            
            async def get_all_trading_pairs(self):
                return {}
        
        # Initialize signal generator
        exchange_manager = MockExchangeManager()
        signal_generator = EnhancedSignalGenerator(exchange_manager)
        
        # Test 1: Check symbol discovery for different exchanges
        print("\n🔍 [TEST 1] Testing symbol discovery...")
        
        exchanges = ['bybit', 'coinbase', 'binance']
        for exchange in exchanges:
            symbols = await signal_generator._get_symbols_for_exchange(exchange)
            print(f"   {exchange}: {symbols}")
            
            # Verify all symbols are Bybit format (USDT pairs)
            bybit_format = all('USDT' in symbol for symbol in symbols)
            if bybit_format:
                print(f"   ✅ {exchange}: All symbols in Bybit format")
            else:
                print(f"   ❌ {exchange}: Non-Bybit symbols detected")
        
        # Test 2: Generate time-based signals
        print("\n🔍 [TEST 2] Testing time-based signal generation...")
        
        for exchange in exchanges:
            signals = await signal_generator._generate_time_based_signals_for_exchange(exchange)
            print(f"   {exchange}: Generated {len(signals)} signals")
            
            for signal_id, signal in signals.items():
                symbol = signal.get('symbol', '')
                action = signal.get('action', '')
                amount = signal.get('amount', 0)
                print(f"      Signal: {action} {symbol} - Amount: {amount:.6f}")
                
                # Verify signal uses Bybit format
                if 'USDT' in symbol:
                    print(f"      ✅ Bybit format: {symbol}")
                else:
                    print(f"      ❌ Non-Bybit format: {symbol}")
        
        # Test 3: Test with mock market data
        print("\n🔍 [TEST 3] Testing signal generation with mock data...")
        
        mock_market_data = {
            'price_data': {
                'bybit': {
                    'BTCUSDT': {'price': 95000, 'change_24h': 2.5},
                    'ETHUSDT': {'price': 3500, 'change_24h': 1.8},
                    'SOLUSDT': {'price': 180, 'change_24h': -0.5}
                }
            }
        }
        
        active_exchanges = ['bybit']
        signals = await signal_generator.generate_reliable_signals(mock_market_data, {})
        
        print(f"   Generated {len(signals)} total signals")
        
        for signal_id, signal in signals.items():
            symbol = signal.get('symbol', '')
            action = signal.get('action', '')
            exchange = signal.get('exchange', '')
            confidence = signal.get('confidence', 0)
            
            print(f"   Signal: {signal_id}")
            print(f"      Action: {action}")
            print(f"      Symbol: {symbol}")
            print(f"      Exchange: {exchange}")
            print(f"      Confidence: {confidence}")
            
            # Verify signal properties
            if 'USDT' in symbol:
                print(f"      ✅ Bybit symbol format")
            else:
                print(f"      ❌ Non-Bybit symbol format")
            
            if 'bybit' in exchange.lower():
                print(f"      ✅ Bybit exchange")
            else:
                print(f"      ❌ Non-Bybit exchange: {exchange}")
        
        print("\n" + "=" * 60)
        print("✅ BYBIT SIGNAL GENERATION TEST COMPLETE")
        print("✅ All signals should now use Bybit format (USDT pairs)")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ [ERROR] Signal generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_immediate_trade_execution():
    """Test immediate trade execution with Bybit"""
    print("\n🚨 [TRADE-TEST] Testing immediate trade execution...")
    
    try:
        from pybit.unified_trading import HTTP
        from dotenv import load_dotenv
        
        load_dotenv()
        
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        session = HTTP(api_key=api_key, api_secret=api_secret, testnet=False, recv_window=10000)
        
        # Get balance
        balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
        if balance_response.get('retCode') == 0:
            balance = float(balance_response['result']['list'][0]['coin'][0]['walletBalance'])
            print(f"💰 [BALANCE] Current: ${balance:.2f} USDT")
            
            if balance >= 15.0:
                trade_amount = max(15.0, min(20.0, balance * 0.25))  # Use minimum $15
                print(f"📊 [TRADE] Executing ${trade_amount:.2f} USDT buy order for BTCUSDT...")
                
                buy_order = session.place_order(
                    category="spot",
                    symbol="BTCUSDT",
                    side="Buy",
                    orderType="Market",
                    qty=f"{trade_amount:.2f}",
                    isLeverage=0,
                    orderFilter="Order"
                )
                
                if buy_order.get('retCode') == 0:
                    order_id = buy_order['result']['orderId']
                    print(f"✅ [SUCCESS] Order executed: {order_id}")
                    print(f"💰 [SUCCESS] Amount: ${trade_amount:.2f} USDT")
                    print("✅ [SUCCESS] BYBIT TRADING WORKING!")
                    return True
                else:
                    print(f"❌ [ERROR] Order failed: {buy_order}")
                    return False
            else:
                print(f"⚠️ [WARNING] Insufficient balance: ${balance:.2f}")
                return False
        else:
            print(f"❌ [ERROR] Balance check failed: {balance_response}")
            return False
            
    except Exception as e:
        print(f"❌ [ERROR] Trade test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test execution"""
    print("=" * 60)
    print("🎯 BYBIT SIGNAL GENERATION TEST")
    print("🚨 VERIFYING BYBIT-ONLY MODE")
    print("=" * 60)
    
    # Run signal generation test
    signal_test_success = asyncio.run(test_bybit_signal_generation())
    
    if signal_test_success:
        print("\n🚨 [NEXT] Testing immediate trade execution...")
        trade_test_success = asyncio.run(test_immediate_trade_execution())
        
        if trade_test_success:
            print("\n✅ ALL TESTS PASSED")
            print("✅ Bybit-only mode working correctly")
            print("✅ Ready for continuous trading")
            return True
        else:
            print("\n⚠️ SIGNAL TEST PASSED, TRADE TEST FAILED")
            print("⚠️ Signals are correct but trade execution needs fixing")
            return False
    else:
        print("\n❌ SIGNAL TEST FAILED")
        print("❌ Signal generation still producing wrong symbols")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 BYBIT-ONLY MODE READY FOR LIVE TRADING")
    else:
        print("\n❌ BYBIT-ONLY MODE NEEDS FURTHER FIXES")
    
    sys.exit(0 if success else 1)
