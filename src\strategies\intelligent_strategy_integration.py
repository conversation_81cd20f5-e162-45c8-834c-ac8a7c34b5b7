"""
Intelligent Strategy Integration
Integrates the sophisticated strategy selector into the main trading system
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from .intelligent_strategy_selector import IntelligentStrategySelector, create_intelligent_strategy_selector
from .neural_strategy_manager import NeuralStrategyManager
from .base import SignalData, MarketContext

logger = logging.getLogger(__name__)

class IntelligentTradingOrchestrator:
    """
    Orchestrates intelligent strategy selection and execution
    """
    
    def __init__(self, components: Dict[str, Any]):
        self.components = components
        self.strategy_selector = None
        self.neural_strategy_manager = None
        self.last_strategy_selection = None
        self.selection_history = []
        
        # Configuration
        self.config = {
            'min_confidence': 0.60,
            'require_consensus': True,
            'consensus_threshold': 0.70,
            'min_score_difference': 0.05,
            'learning_rate': 0.01,
            'reselection_interval': 300,  # 5 minutes
            'performance_tracking_enabled': True
        }
    
    async def initialize(self) -> bool:
        """Initialize the intelligent trading orchestrator"""
        try:
            logger.info("🧠 [INTELLIGENT] Initializing intelligent trading orchestrator...")
            
            # 1. Initialize neural strategy manager if not already done
            if 'neural_strategy_manager' not in self.components:
                await self._initialize_neural_strategy_manager()
            
            self.neural_strategy_manager = self.components.get('neural_strategy_manager')
            
            # 2. Initialize intelligent strategy selector
            if self.neural_strategy_manager:
                strategies = self.neural_strategy_manager.strategies
                self.strategy_selector = create_intelligent_strategy_selector(strategies, self.config)
                
                logger.info("✅ [INTELLIGENT] Intelligent strategy selector initialized")
                logger.info(f"✅ [INTELLIGENT] Monitoring {len(strategies)} strategies with adaptive learning")
            else:
                logger.error("❌ [INTELLIGENT] Neural strategy manager not available")
                return False
            
            # 3. Initialize performance tracking
            await self._initialize_performance_tracking()
            
            logger.info("✅ [INTELLIGENT] Intelligent trading orchestrator ready")
            return True
            
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT] Initialization failed: {e}")
            return False
    
    async def _initialize_neural_strategy_manager(self):
        """Initialize neural strategy manager if needed"""
        try:
            strategy_config = {
                'momentum': {
                    'lookback_period': 20,
                    'momentum_threshold': 0.02,
                    'max_position_size': 0.25,
                    'min_confidence_threshold': 0.6
                },
                'mean_reversion': {
                    'lookback_period': 50,
                    'bollinger_period': 20,
                    'bollinger_std': 2.0,
                    'max_position_size': 0.20,
                    'min_confidence_threshold': 0.6
                },
                'adaptive_neural': {
                    'learning_rate': 0.001,
                    'adaptation_frequency': 100,
                    'max_position_size': 0.30,
                    'min_confidence_threshold': 0.6
                }
            }
            
            self.neural_strategy_manager = NeuralStrategyManager(strategy_config)
            self.components['neural_strategy_manager'] = self.neural_strategy_manager
            
            logger.info("✅ [INTELLIGENT] Neural strategy manager initialized")
            
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT] Failed to initialize neural strategy manager: {e}")
            raise
    
    async def _initialize_performance_tracking(self):
        """Initialize performance tracking for strategies"""
        try:
            # Set up hooks to track trade outcomes
            if 'enhanced_trade_logger' in self.components:
                trade_logger = self.components['enhanced_trade_logger']
                # In a real implementation, you'd hook into the trade logger
                # to automatically record outcomes for the strategy selector
                logger.info("✅ [INTELLIGENT] Performance tracking connected to trade logger")
            
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT] Performance tracking setup failed: {e}")
    
    async def get_intelligent_trading_signals(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get intelligent trading signals using sophisticated strategy selection
        """
        try:
            if not self.strategy_selector:
                logger.warning("⚠️ [INTELLIGENT] Strategy selector not initialized")
                return {}
            
            # 1. Select optimal strategy based on current conditions
            strategy_selection = await self.strategy_selector.select_optimal_strategy(market_data)
            
            if not strategy_selection:
                logger.info("🔍 [INTELLIGENT] No optimal strategy selected - holding position")
                return {}
            
            strategy_name, strategy_score = strategy_selection
            self.last_strategy_selection = {
                'strategy': strategy_name,
                'score': strategy_score,
                'timestamp': datetime.now()
            }
            
            # 2. Get signal from selected strategy
            signal = await self._get_strategy_signal(strategy_name, market_data)
            
            if not signal:
                logger.info(f"🔍 [INTELLIGENT] Strategy {strategy_name} generated no signal")
                return {}
            
            # 3. Enhance signal with intelligent insights
            enhanced_signals = await self._enhance_signal_with_intelligence(signal, strategy_score, market_data)
            
            # 4. Record strategy selection for learning
            self.selection_history.append(self.last_strategy_selection)
            
            logger.info(f"🎯 [INTELLIGENT] Generated signal using {strategy_name}")
            logger.info(f"🎯 [INTELLIGENT] Strategy confidence: {strategy_score.confidence:.3f}")
            logger.info(f"🎯 [INTELLIGENT] Expected return: {strategy_score.expected_return:.2%}")
            
            return enhanced_signals
            
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT] Error generating intelligent signals: {e}")
            return {}
    
    async def _get_strategy_signal(self, strategy_name: str, market_data: Dict[str, Any]) -> Optional[SignalData]:
        """Get signal from the selected strategy"""
        try:
            if not self.neural_strategy_manager:
                return None
            
            # Create market context for the strategy
            market_context = self._create_market_context(market_data)
            
            # Force the neural strategy manager to use the selected strategy
            original_strategy = self.neural_strategy_manager.active_strategy
            self.neural_strategy_manager.active_strategy = strategy_name
            
            try:
                # Get signal from the selected strategy
                signal = await self.neural_strategy_manager.get_trading_signal(market_context)
                return signal
            finally:
                # Restore original strategy (though it will be updated by the manager)
                self.neural_strategy_manager.active_strategy = original_strategy
            
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT] Error getting signal from {strategy_name}: {e}")
            return None
    
    def _create_market_context(self, market_data: Dict[str, Any]) -> MarketContext:
        """Create market context from market data"""
        try:
            # Create a comprehensive market context
            # This is a simplified version - you'd extract more data in practice
            
            return MarketContext(
                price_data=market_data.get('price_data', {}),
                order_books=market_data.get('order_books', {}),
                technical_indicators=market_data.get('technical_indicators', {}),
                volume_data=market_data.get('volume_data', {}),
                news_data=market_data.get('news_data', {}),
                sentiment_data=market_data.get('sentiment_data', {'market_regime': 'sideways'}),
                arbitrage_opportunities=market_data.get('arbitrage_opportunities', []),
                portfolio_state=market_data.get('portfolio_state', {}),
                risk_metrics=market_data.get('risk_metrics', {}),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT] Error creating market context: {e}")
            # Return minimal context
            return MarketContext(
                price_data={},
                order_books={},
                technical_indicators={},
                volume_data={},
                news_data={},
                sentiment_data={'market_regime': 'sideways'},
                arbitrage_opportunities=[],
                portfolio_state={},
                risk_metrics={},
                timestamp=datetime.now()
            )
    
    async def _enhance_signal_with_intelligence(self, signal: SignalData, strategy_score, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance trading signal with intelligent insights"""
        try:
            # Convert SignalData to trading system format
            enhanced_signal = {
                'action': signal.action,
                'symbol': signal.symbol,
                'confidence': signal.confidence,
                'quantity': signal.quantity,
                'price': signal.price,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'strategy': f"intelligent_{strategy_score.strategy_name}",
                'reasoning': signal.reasoning,
                'priority': 1,  # High priority for intelligent signals
                'source': 'intelligent_strategy_selector',
                'urgency': signal.urgency,
                
                # Enhanced with intelligent insights
                'intelligent_enhanced': True,
                'strategy_score': strategy_score.total_score,
                'selection_confidence': strategy_score.confidence,
                'expected_return': strategy_score.expected_return,
                'expected_risk': strategy_score.expected_risk,
                'regime_fit': strategy_score.regime_fit_score,
                'performance_score': strategy_score.performance_score,
                'consistency_score': strategy_score.consistency_score,
                
                # Risk management enhancements
                'position_size_multiplier': self._calculate_position_size_multiplier(strategy_score),
                'risk_adjustment': self._calculate_risk_adjustment(strategy_score),
                
                # Execution timing
                'execution_priority': self._calculate_execution_priority(strategy_score),
                'hold_time_estimate': self._estimate_hold_time(strategy_score.strategy_name)
            }
            
            # Package as signals dictionary
            signal_key = f"intelligent_{signal.symbol}_{strategy_score.strategy_name}"
            return {signal_key: enhanced_signal}
            
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT] Error enhancing signal: {e}")
            return {}
    
    def _calculate_position_size_multiplier(self, strategy_score) -> float:
        """Calculate position size multiplier based on strategy confidence"""
        # Higher confidence and better scores = larger position sizes
        base_multiplier = 1.0
        confidence_bonus = (strategy_score.confidence - 0.6) * 2  # 0-0.8 bonus
        score_bonus = (strategy_score.total_score - 0.5) * 1  # 0-0.5 bonus
        
        multiplier = base_multiplier + confidence_bonus + score_bonus
        return max(0.5, min(2.0, multiplier))  # Clamp between 0.5x and 2.0x
    
    def _calculate_risk_adjustment(self, strategy_score) -> float:
        """Calculate risk adjustment factor"""
        # Lower risk for higher confidence strategies
        base_risk = 1.0
        confidence_adjustment = (1 - strategy_score.confidence) * 0.5
        consistency_adjustment = (1 - strategy_score.consistency_score) * 0.3
        
        risk_factor = base_risk + confidence_adjustment + consistency_adjustment
        return max(0.5, min(2.0, risk_factor))
    
    def _calculate_execution_priority(self, strategy_score) -> int:
        """Calculate execution priority (1=highest, 5=lowest)"""
        if strategy_score.confidence >= 0.8 and strategy_score.total_score >= 0.8:
            return 1  # Highest priority
        elif strategy_score.confidence >= 0.7 and strategy_score.total_score >= 0.7:
            return 2  # High priority
        elif strategy_score.confidence >= 0.6 and strategy_score.total_score >= 0.6:
            return 3  # Medium priority
        else:
            return 4  # Lower priority
    
    def _estimate_hold_time(self, strategy_name: str) -> int:
        """Estimate hold time in minutes based on strategy type"""
        hold_times = {
            'momentum': 60,      # 1 hour
            'mean_reversion': 30, # 30 minutes
            'adaptive_neural': 45 # 45 minutes
        }
        return hold_times.get(strategy_name, 45)
    
    async def record_trade_outcome(self, trade_data: Dict[str, Any]):
        """Record trade outcome for strategy learning"""
        try:
            if not self.strategy_selector:
                return
            
            # Extract strategy name from trade data
            strategy_name = trade_data.get('strategy', '').replace('intelligent_', '')
            
            if strategy_name and strategy_name in self.neural_strategy_manager.strategies:
                # Record outcome for strategy selector learning
                await self.strategy_selector.record_trade_outcome(strategy_name, trade_data)
                
                logger.info(f"📊 [INTELLIGENT] Recorded outcome for {strategy_name}: {trade_data.get('return', 0):.2%}")
            
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT] Error recording trade outcome: {e}")
    
    def get_strategy_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of all strategies"""
        try:
            if not self.strategy_selector:
                return {}
            
            summary = {
                'total_strategies': len(self.strategy_selector.strategies),
                'total_trades_recorded': len(self.strategy_selector.trade_history),
                'last_strategy_selection': self.last_strategy_selection,
                'selection_history_count': len(self.selection_history),
                'current_scoring_weights': self.strategy_selector.scoring_weights.copy()
            }
            
            # Add individual strategy performance
            strategy_performance = {}
            for strategy_name, perf_data in self.strategy_selector.strategy_performance.items():
                metrics = perf_data['metrics']
                strategy_performance[strategy_name] = {
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'profit_loss': metrics.profit_loss,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'max_drawdown': metrics.max_drawdown,
                    'recent_24h_performance': metrics.recent_24h_performance,
                    'consistency_score': metrics.consistency_score
                }
            
            summary['strategy_performance'] = strategy_performance
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ [INTELLIGENT] Error getting performance summary: {e}")
            return {}

# Global instance for integration
_intelligent_orchestrator = None

def get_intelligent_trading_orchestrator(components: Dict[str, Any]) -> IntelligentTradingOrchestrator:
    """Get or create intelligent trading orchestrator"""
    global _intelligent_orchestrator
    if _intelligent_orchestrator is None:
        _intelligent_orchestrator = IntelligentTradingOrchestrator(components)
    return _intelligent_orchestrator

async def initialize_intelligent_trading(components: Dict[str, Any]) -> bool:
    """Initialize intelligent trading system"""
    orchestrator = get_intelligent_trading_orchestrator(components)
    return await orchestrator.initialize()

async def get_intelligent_trading_signals(components: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
    """Get intelligent trading signals"""
    orchestrator = get_intelligent_trading_orchestrator(components)
    return await orchestrator.get_intelligent_trading_signals(market_data)

async def record_intelligent_trade_outcome(components: Dict[str, Any], trade_data: Dict[str, Any]):
    """Record trade outcome for intelligent learning"""
    orchestrator = get_intelligent_trading_orchestrator(components)
    await orchestrator.record_trade_outcome(trade_data)
