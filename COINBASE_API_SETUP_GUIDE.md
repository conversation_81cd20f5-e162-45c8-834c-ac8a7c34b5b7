# Coinbase API Setup Guide

## Complete Configuration Guide for API Key: `66c4c378-f65b-4a7d-a23f-37d8936dc66e`

This guide provides step-by-step instructions to resolve 401 authentication errors and properly configure your Coinbase API key for the AutoGPT Trading System.

---

## 🎯 Current Status

- **API Key ID**: `66c4c378-f65b-4a7d-a23f-37d8936dc66e`
- **Organization ID**: `7405b51f-cfea-4f54-a52d-02838b5cb217`
- **Full API Key**: `organizations/7405b51f-cfea-4f54-a52d-02838b5cb217/apiKeys/66c4c378-f65b-4a7d-a23f-37d8936dc66e`
- **Current IP**: `************` ✅ (Verified)
- **Issue**: 401 Unauthorized errors despite correct credentials

---

## 🔧 Step-by-Step Configuration

### Step 1: Access Coinbase Developer Console

1. **Navigate to**: https://cloud.coinbase.com/access/api
2. **Login** with your Coinbase account credentials
3. **Select** the correct project/organization
4. **Click** on the "API Keys" tab

### Step 2: Locate Your API Key

1. **Find** the API key with nickname "HERMUS" 
2. **Verify** the Key ID shows: `66c4***c66e` (partial display)
3. **Check** the status shows "Enabled" with a blue toggle
4. **Note** the current permissions displayed

### Step 3: Configure Permissions (CRITICAL)

Your API key **MUST** have the following permissions enabled:

#### ✅ Required Permissions for Trading:

1. **View Permission**
   - ✅ Should already be enabled
   - Required for: Account access, balance checking, order history
   - API Endpoints: `/accounts`, `/portfolios`, `/products`

2. **Trade Permission** 
   - ✅ Should already be enabled  
   - Required for: Order placement, order cancellation
   - API Endpoints: `/orders` (POST), `/orders/{id}` (DELETE)

3. **Transfer Permission**
   - ⚠️ **VERIFY THIS IS ENABLED**
   - Required for: Wallet operations, fund transfers
   - API Endpoints: `/transfers`, `/deposits`, `/withdrawals`

#### 🔧 How to Enable Permissions:

1. **Click** the "Configure" button next to your API key
2. **Scroll down** to the "Permissions" section
3. **Ensure ALL THREE checkboxes are checked**:
   - ☑️ View
   - ☑️ Trade  
   - ☑️ Transfer
4. **Click** "Update" or "Save" to apply changes

### Step 4: Verify IP Restrictions

Your current configuration shows:
```
Portfolio: Default (************/32, 0.0.0.0/0, ************/32)
```

#### ✅ Current IP Configuration:
- **Your IP**: `************` ✅
- **Restriction**: Includes your IP ✅
- **Status**: Should be working ✅

#### 🔧 If IP Issues Persist:

1. **Click** "Configure" next to your API key
2. **Find** the "IP Restrictions" section
3. **Verify** `************` is listed
4. **Consider** temporarily adding `0.0.0.0/0` for testing
5. **Remove** `0.0.0.0/0` after confirming it works

### Step 5: Check API Key Status

1. **Verify** the API key status is "Enabled" (blue toggle)
2. **Check** the "Generated" date: `04/29/2025, 8:06PM`
3. **Confirm** no expiration date is set
4. **Look for** any warning messages or alerts

### Step 6: Advanced Troubleshooting

#### If Permissions Appear Correct:

1. **Regenerate** the API key:
   - Click "Rotate" next to your API key
   - **IMPORTANT**: Update your `.env` file with new credentials
   - Re-encrypt the new credentials

2. **Check Account Status**:
   - Verify your Coinbase account is in good standing
   - Ensure no trading restrictions are applied
   - Confirm account verification is complete

3. **Test Different Endpoints**:
   - Run the endpoint testing script: `python test_coinbase_endpoints.py`
   - Check which specific endpoints are failing
   - Focus on permission-specific issues

---

## 🧪 Verification Steps

### Immediate Verification:

1. **Run the diagnostic**: `python coinbase_api_diagnostic.py`
2. **Check all steps pass** except API calls
3. **Run endpoint tests**: `python test_coinbase_endpoints.py`
4. **Review the compatibility matrix**

### Expected Results After Configuration:

```
✅ VPN Status: OK
✅ IP Match: OK  
✅ Credential Decryption: OK
✅ API Permissions: OK  ← Should change from ❌ to ✅
✅ Integration: OK      ← Should change from ❌ to ✅
```

### Test Commands:

```bash
# Test the verification script
python test_cdp_verification.py

# Test specific endpoints
python test_coinbase_endpoints.py

# Run the main trading system test
python main.py --test-mode
```

---

## 🚨 Common Issues & Solutions

### Issue 1: "Transfer Permission Missing"
**Symptoms**: Can view accounts but cannot place orders
**Solution**: Enable Transfer permission in API key configuration

### Issue 2: "API Key Disabled"
**Symptoms**: All endpoints return 401
**Solution**: Check API key status toggle is enabled (blue)

### Issue 3: "IP Restriction Mismatch"  
**Symptoms**: Intermittent 401 errors
**Solution**: Verify IP restrictions include `************`

### Issue 4: "Endpoint Not Found (404)"
**Symptoms**: Some endpoints return 404
**Solution**: Use Advanced Trading API endpoints (`/api/v3/brokerage/`)

### Issue 5: "Rate Limiting"
**Symptoms**: 429 errors or temporary failures
**Solution**: Implement rate limiting in trading system

---

## 🔄 Alternative Approaches

### Option 1: Regenerate API Key

If configuration changes don't resolve the issue:

1. **Create new API key**:
   - Go to https://cloud.coinbase.com/access/api
   - Click "Create API key"
   - Select "Secret API Key" type
   - Enable ALL permissions: View, Trade, Transfer
   - Set IP restriction to `************`

2. **Update credentials**:
   - Replace values in `.env` file
   - Re-encrypt using the credential encryption system
   - Test with new credentials

### Option 2: Contact Coinbase Support

If technical solutions fail:

1. **Document the issue**:
   - API Key ID: `66c4c378-f65b-4a7d-a23f-37d8936dc66e`
   - Error: 401 Unauthorized on Advanced Trading API
   - Confirmed: Permissions enabled, IP correct, key active

2. **Submit support ticket**:
   - Use Coinbase Developer Support
   - Include diagnostic output
   - Reference this guide

---

## 📋 Checklist

Before proceeding with trading:

- [ ] API key status is "Enabled"
- [ ] View permission is enabled
- [ ] Trade permission is enabled  
- [ ] Transfer permission is enabled
- [ ] IP restriction includes `************`
- [ ] Diagnostic tests pass
- [ ] Endpoint tests show working authentication
- [ ] Integration tests succeed

---

## 🔗 Useful Links

- **Coinbase Developer Console**: https://cloud.coinbase.com/access/api
- **Advanced Trading API Docs**: https://docs.cloud.coinbase.com/advanced-trade-api/docs/welcome
- **API Reference**: https://docs.cloud.coinbase.com/advanced-trade-api/reference
- **Support**: https://help.coinbase.com/en/developers

---

## 📞 Next Steps

1. **Follow this guide** step by step
2. **Run verification tests** after each change
3. **Document results** of each step
4. **Contact support** if issues persist after following all steps

**Remember**: The trading system is designed to work with partial API access, so even if some endpoints fail, basic trading functionality may still be available.
