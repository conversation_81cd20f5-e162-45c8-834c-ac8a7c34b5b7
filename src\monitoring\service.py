# backend/src/monitoring/service.py
import asyncio
import smtplib
from slack_sdk import WebClient
from config import ALERT_RULES

class MonitoringService:
    def __init__(self):
        self.db_session = sessionmaker(bind=engine)
        self.slack = WebClient(os.getenv("SLACK_TOKEN"))
        self.email_settings = {
            'smtp_server': os.getenv("SMTP_SERVER"),
            'email_from': os.getenv("ALERT_EMAIL_FROM")
        }

    async def start(self):
        """Main monitoring loop"""
        while True:
            await self._check_metrics()
            await self._process_alerts()
            await asyncio.sleep(60)

    async def _check_metrics(self):
        with self.db_session() as session:
            metrics = session.query(PerformanceMetric).order_by(PerformanceMetric.timestamp.desc()).first()
            
            # Check alert rules
            if metrics.max_drawdown > ALERT_RULES['drawdown']['critical']:
                self._trigger_alert("CRITICAL", f"Drawdown exceeded: {metrics.max_drawdown:.2%}")
                
            if metrics.volatility > ALERT_RULES['volatility']['critical']:
                self._trigger_alert("CRITICAL", f"Volatility spike: {metrics.volatility:.2%}")

    def _trigger_alert(self, severity, message):
        """Store alert and notify"""
        with self.db_session() as session:
            alert = Alert(severity=severity, message=message)
            session.add(alert)
            session.commit()
        
        self._send_slack(f"[{severity}] {message}")
        self._send_email(f"Trading Alert: {severity}", message)

    def _send_slack(self, message):
        self.slack.chat_postMessage(
            channel=os.getenv("SLACK_CHANNEL"),
            text=message
        )

    def _send_email(self, subject, body):
        with smtplib.SMTP(self.email_settings['smtp_server']) as server:
            server.sendmail(
                self.email_settings['email_from'],
                os.getenv("ALERT_EMAILS").split(','),
                f"Subject: {subject}\n\n{body}"
            )