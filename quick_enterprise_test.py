#!/usr/bin/env python3
"""
QUICK ENTERPRISE FIXES VERIFICATION
Simplified test to verify critical fixes are working
"""

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_precision_fix():
    """Test P1: Dynamic precision configuration"""
    print("🔧 [P1] Testing dynamic precision fix...")
    
    try:
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Create mock client
        class MockBybitClient(BybitClientFixed):
            def __init__(self):
                self.session = None
                self.name = "bybit"
        
        client = MockBybitClient()
        
        # Test precision values
        eth_req = client._get_bybit_minimum_requirements('ETHUSDT')
        sol_req = client._get_bybit_minimum_requirements('SOLUSDT')
        btc_req = client._get_bybit_minimum_requirements('BTCUSDT')
        
        eth_precision = eth_req.get('qty_precision', 0)
        sol_precision = sol_req.get('qty_precision', 0)
        btc_precision = btc_req.get('qty_precision', 0)
        
        print(f"   ETH precision: {eth_precision} (expected: 4)")
        print(f"   SOL precision: {sol_precision} (expected: 4)")
        print(f"   BTC precision: {btc_precision} (expected: 6)")
        
        if eth_precision == 4 and sol_precision == 4 and btc_precision == 6:
            print("✅ [P1] Precision fix verified")
            return True
        else:
            print("❌ [P1] Precision values incorrect")
            return False
            
    except Exception as e:
        print(f"❌ [P1] Precision test failed: {e}")
        return False

def test_hardcoded_elimination():
    """Test P3: Hardcoded fallback elimination"""
    print("🚫 [P3] Testing hardcoded fallback elimination...")
    
    try:
        import inspect
        from src.trading.enhanced_signal_generator import EnhancedSignalGenerator
        
        # Check if hardcoded fallbacks are eliminated
        source = inspect.getsource(EnhancedSignalGenerator._get_price_from_market_data)
        
        # Check for old hardcoded patterns
        hardcoded_patterns = ['95000.0', '3500.0', '180.0', 'price_defaults']
        hardcoded_found = any(pattern in source for pattern in hardcoded_patterns)
        
        # Check for new fail-fast patterns
        failfast_patterns = ['raise ValueError', 'Real-time price data unavailable', 'refusing to use hardcoded']
        failfast_found = any(pattern in source for pattern in failfast_patterns)
        
        print(f"   Hardcoded fallbacks found: {hardcoded_found}")
        print(f"   Fail-fast behavior found: {failfast_found}")
        
        if not hardcoded_found and failfast_found:
            print("✅ [P3] Hardcoded fallback elimination verified")
            return True
        else:
            print("❌ [P3] Hardcoded fallbacks still present or fail-fast missing")
            return False
            
    except Exception as e:
        print(f"❌ [P3] Hardcoded elimination test failed: {e}")
        return False

def test_neural_memory_system():
    """Test P2: Neural memory system availability"""
    print("🧠 [P2] Testing neural memory system...")
    
    try:
        from src.neural.enterprise_memory_system import EnterpriseNeuralMemorySystem
        
        # Test initialization
        config = {
            'memory_path': 'test_data/neural_memory',
            'embedding_dim': 256
        }
        
        memory_system = EnterpriseNeuralMemorySystem(config)
        
        # Check if key components exist
        has_encoder = hasattr(memory_system, 'memory_encoder')
        has_attention = hasattr(memory_system, 'attention_mechanism')
        has_persistence = hasattr(memory_system, 'persistence')
        
        print(f"   Memory encoder: {has_encoder}")
        print(f"   Attention mechanism: {has_attention}")
        print(f"   Persistence layer: {has_persistence}")
        
        if has_encoder and has_attention and has_persistence:
            print("✅ [P2] Neural memory system verified")
            return True
        else:
            print("❌ [P2] Neural memory system components missing")
            return False
            
    except Exception as e:
        print(f"❌ [P2] Neural memory test failed: {e}")
        return False

def test_data_validator():
    """Test P2: Real-time data validator"""
    print("🔍 [P2] Testing real-time data validator...")
    
    try:
        from src.data_feeds.real_time_validator import RealTimeDataValidator
        
        # Test initialization
        config = {
            'outlier_threshold': 0.05,
            'min_sources_required': 2
        }
        
        validator = RealTimeDataValidator(config)
        
        # Check data sources
        source_count = len(validator.data_sources)
        has_binance = 'binance' in validator.data_sources
        has_bybit = 'bybit' in validator.data_sources
        
        print(f"   Data sources configured: {source_count}")
        print(f"   Binance available: {has_binance}")
        print(f"   Bybit available: {has_bybit}")
        
        if source_count >= 3 and has_binance and has_bybit:
            print("✅ [P2] Data validator verified")
            return True
        else:
            print("❌ [P2] Data validator insufficient sources")
            return False
            
    except Exception as e:
        print(f"❌ [P2] Data validator test failed: {e}")
        return False

def test_loop_validator():
    """Test P3: Endless loop validator"""
    print("🔄 [P3] Testing endless loop validator...")
    
    try:
        from src.monitoring.endless_loop_validator import EndlessLoopValidator
        
        # Test initialization
        config = {
            'max_iteration_time': 60.0,
            'auto_recovery_enabled': True
        }
        
        loop_validator = EndlessLoopValidator(config)
        
        # Test loop registration
        def mock_loop():
            return True
        
        registration_success = loop_validator.register_loop('test_loop', mock_loop)
        
        print(f"   Loop registration: {registration_success}")
        print(f"   Auto recovery enabled: {loop_validator.auto_recovery_enabled}")
        
        if registration_success:
            print("✅ [P3] Loop validator verified")
            return True
        else:
            print("❌ [P3] Loop validator registration failed")
            return False
            
    except Exception as e:
        print(f"❌ [P3] Loop validator test failed: {e}")
        return False

def main():
    """Run quick enterprise fixes verification"""
    print("🚀 QUICK ENTERPRISE FIXES VERIFICATION")
    print("=" * 50)
    print(f"⏰ Started: {datetime.now().strftime('%H:%M:%S')}")
    print()
    
    # Run tests
    results = []
    results.append(("Precision Fix", test_precision_fix()))
    results.append(("Hardcoded Elimination", test_hardcoded_elimination()))
    results.append(("Neural Memory System", test_neural_memory_system()))
    results.append(("Data Validator", test_data_validator()))
    results.append(("Loop Validator", test_loop_validator()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 QUICK TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🏆 OVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL CRITICAL FIXES VERIFIED!")
        print("🚀 System ready for enhanced live trading")
    elif passed >= total * 0.8:
        print("✅ MOST FIXES WORKING")
        print("🔧 Minor issues remain")
    else:
        print("⚠️ SIGNIFICANT ISSUES DETECTED")
        print("🛠️ Additional work required")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
