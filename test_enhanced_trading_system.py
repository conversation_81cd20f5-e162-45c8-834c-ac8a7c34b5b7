#!/usr/bin/env python3
"""
Enhanced Trading System Verification Test
Tests the robust fallback exchange system and real money trading verification
"""

import asyncio
import logging
import sys
from datetime import datetime
from decimal import Decimal

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_enhanced_trading_system():
    """Comprehensive test of the enhanced trading system"""
    print("🚀 ENHANCED TRADING SYSTEM VERIFICATION TEST")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Import the enhanced trading system
        from main import ComprehensiveLiveTradingSystem
        
        # Create and initialize the system
        print("🔧 [INIT] Creating enhanced trading system...")
        system = ComprehensiveLiveTradingSystem()
        
        print("🔧 [INIT] Initializing comprehensive system...")
        await system.initialize_comprehensive_system()
        
        # Test 1: Verify enhanced components are loaded
        print("\n📊 [TEST 1] Enhanced Components Verification")
        print("-" * 40)
        
        enhanced_components = [
            ('enhanced_exchange_manager', 'Exchange Manager'),
            ('enhanced_signal_generator', 'Signal Generator'),
            ('enhanced_capital_manager', 'Capital Manager')
        ]
        
        for attr_name, display_name in enhanced_components:
            if hasattr(system, attr_name):
                print(f"✅ {display_name}: LOADED")
            else:
                print(f"❌ {display_name}: NOT LOADED")
        
        # Test 2: Exchange Manager Status
        if hasattr(system, 'enhanced_exchange_manager'):
            print("\n📊 [TEST 2] Exchange Manager Status")
            print("-" * 40)
            
            status_report = system.enhanced_exchange_manager.get_status_report()
            print(f"Capital Management Mode: {status_report['capital_management_mode']}")
            print(f"Active Exchanges: {status_report['active_exchanges']}")
            print(f"Primary Exchange: {status_report['primary_exchange']}")
            print(f"Monitoring Active: {status_report['monitoring_active']}")
            
            # Test exchange status details
            for exchange, status in status_report['exchange_status'].items():
                print(f"  {exchange.upper()}:")
                print(f"    Active: {'✅' if status['active'] else '❌'}")
                print(f"    Role: {status['role']}")
                print(f"    Balance: ${status['balance_usd']:.2f}")
                print(f"    Failures: {status['consecutive_failures']}")
        
        # Test 3: Real Money Trading Verification
        print("\n📊 [TEST 3] Real Money Trading Verification")
        print("-" * 40)
        
        real_money_verified = await system._verify_real_money_trading_mode()
        if real_money_verified:
            print("✅ Real money trading verification: PASSED")
        else:
            print("❌ Real money trading verification: FAILED")
            print("⚠️  System would terminate here in production")
        
        # Test 4: Signal Generation
        print("\n📊 [TEST 4] Enhanced Signal Generation")
        print("-" * 40)
        
        if hasattr(system, 'enhanced_signal_generator'):
            # Create mock market data
            mock_market_data = {
                'price_data': {
                    'bybit': {
                        'BTC-USD': {'price': 50000.0, 'change_24h': 2.5, 'volume': 1000000},
                        'ETH-USD': {'price': 3000.0, 'change_24h': 1.8, 'volume': 500000},
                        'SOL-USD': {'price': 150.0, 'change_24h': 3.2, 'volume': 200000}
                    }
                },
                'timestamp': datetime.now()
            }
            
            signals = await system.enhanced_signal_generator.generate_reliable_signals(
                mock_market_data, system.components
            )
            
            print(f"Generated Signals: {len(signals)}")
            for signal_id, signal in signals.items():
                print(f"  {signal_id}:")
                print(f"    Action: {signal['action']}")
                print(f"    Symbol: {signal['symbol']}")
                print(f"    Exchange: {signal['exchange']}")
                print(f"    Amount: {signal['amount']:.6f}")
                print(f"    USD Value: ${signal['usd_value']:.2f}")
                print(f"    Confidence: {signal['confidence']:.2%}")
        
        # Test 5: Capital Management
        print("\n📊 [TEST 5] Enhanced Capital Management")
        print("-" * 40)
        
        if hasattr(system, 'enhanced_capital_manager'):
            capital_status = system.enhanced_capital_manager.get_capital_status()
            print(f"Capital Management Mode: {capital_status['mode']}")
            print(f"Total Capital: ${capital_status['total_capital']:.2f}")
            print(f"Total Available: ${capital_status['total_available']:.2f}")
            print(f"Total Profit: ${capital_status['total_profit']:.2f}")
            
            # Test position sizing
            test_scenarios = [
                ('BTC-USD', 50000.0, 'BUY'),
                ('ETH-USD', 3000.0, 'BUY'),
                ('SOL-USD', 150.0, 'BUY')
            ]
            
            for symbol, price, action in test_scenarios:
                exchange, position_size = await system.enhanced_capital_manager.calculate_position_size(
                    symbol, price, action
                )
                usd_value = float(position_size * Decimal(str(price)))
                print(f"  {symbol}: {position_size:.6f} on {exchange} = ${usd_value:.2f}")
        
        # Test 6: System Health Check
        print("\n📊 [TEST 6] System Health Check")
        print("-" * 40)
        
        await system._perform_system_health_check()
        print("✅ Health check completed")
        
        # Test 7: Simulation Detection
        print("\n📊 [TEST 7] Simulation Mode Detection")
        print("-" * 40)
        
        simulation_detected = await system._detect_simulation_modes()
        if simulation_detected:
            print("⚠️  Simulation mode indicators detected")
        else:
            print("✅ No simulation mode indicators found")
        
        # Test 8: Order Placement Capability
        print("\n📊 [TEST 8] Order Placement Capability")
        print("-" * 40)
        
        order_capability = await system._verify_order_placement_capability()
        if order_capability:
            print("✅ Order placement capability verified")
        else:
            print("❌ Order placement capability verification failed")
        
        # Test 9: Balance Information
        print("\n📊 [TEST 9] Balance Information")
        print("-" * 40)
        
        try:
            balance_info = await system._get_comprehensive_balance_info()
            print("Balance Information:")
            for exchange, info in balance_info.items():
                if isinstance(info, dict):
                    print(f"  {exchange.upper()}:")
                    for key, value in info.items():
                        if isinstance(value, (int, float)):
                            print(f"    {key}: ${value:.2f}")
                        elif isinstance(value, dict):
                            print(f"    {key}: {len(value)} items")
                        else:
                            print(f"    {key}: {value}")
        except Exception as e:
            print(f"⚠️  Balance information error: {e}")
        
        # Test Summary
        print("\n🎯 [SUMMARY] Enhanced Trading System Test Results")
        print("=" * 60)
        
        test_results = {
            'Enhanced Components': hasattr(system, 'enhanced_exchange_manager'),
            'Exchange Manager': hasattr(system, 'enhanced_exchange_manager'),
            'Signal Generator': hasattr(system, 'enhanced_signal_generator'),
            'Capital Manager': hasattr(system, 'enhanced_capital_manager'),
            'Real Money Verification': real_money_verified,
            'Order Capability': order_capability,
            'No Simulation Detected': not simulation_detected
        }
        
        passed_tests = sum(1 for result in test_results.values() if result)
        total_tests = len(test_results)
        
        print(f"Tests Passed: {passed_tests}/{total_tests}")
        print()
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name}: {status}")
        
        print()
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED - Enhanced trading system is ready!")
            print("🚀 System can start live trading with robust fallback capabilities")
        else:
            print("⚠️  Some tests failed - review system configuration")
        
        print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_exchange_fallback_scenarios():
    """Test specific exchange fallback scenarios"""
    print("\n🔄 [FALLBACK TEST] Exchange Fallback Scenarios")
    print("=" * 50)
    
    try:
        from main import ComprehensiveLiveTradingSystem
        
        system = ComprehensiveLiveTradingSystem()
        await system.initialize_comprehensive_system()
        
        if not hasattr(system, 'enhanced_exchange_manager'):
            print("❌ Enhanced exchange manager not available")
            return False
        
        exchange_manager = system.enhanced_exchange_manager
        
        # Test scenario 1: Dual exchange mode
        print("📊 Scenario 1: Dual Exchange Mode")
        if 'bybit' in exchange_manager.exchange_status and 'coinbase' in exchange_manager.exchange_status:
            print("✅ Both exchanges available - dual mode active")
        else:
            print("⚠️  Only single exchange available")
        
        # Test scenario 2: Exchange failure simulation
        print("📊 Scenario 2: Exchange Failure Handling")
        for exchange_name in exchange_manager.exchange_status.keys():
            await exchange_manager.record_trade_result(exchange_name, False)
            await exchange_manager.record_trade_result(exchange_name, False)
            await exchange_manager.record_trade_result(exchange_name, False)
            print(f"  Simulated 3 failures for {exchange_name}")
        
        # Check status after failures
        status_after_failures = exchange_manager.get_status_report()
        print(f"Status after failures: {status_after_failures['active_exchanges']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Fallback test failed: {e}")
        return False

if __name__ == "__main__":
    async def main():
        print("🧪 ENHANCED TRADING SYSTEM TEST SUITE")
        print("=" * 60)
        
        # Run main test
        main_test_passed = await test_enhanced_trading_system()
        
        # Run fallback test
        fallback_test_passed = await test_exchange_fallback_scenarios()
        
        print("\n🏁 FINAL RESULTS")
        print("=" * 30)
        print(f"Main Test: {'✅ PASS' if main_test_passed else '❌ FAIL'}")
        print(f"Fallback Test: {'✅ PASS' if fallback_test_passed else '❌ FAIL'}")
        
        if main_test_passed and fallback_test_passed:
            print("\n🎉 ALL TESTS PASSED!")
            print("🚀 Enhanced trading system is ready for live trading")
            print("💰 Real money trading with robust fallback capabilities")
            sys.exit(0)
        else:
            print("\n⚠️  SOME TESTS FAILED")
            print("🔧 Review system configuration before live trading")
            sys.exit(1)
    
    asyncio.run(main())
