// Updated Windows-compatible configuration
module.exports = {
    apps: [
      {
        name: "trading-dashboard",
        cwd: "D:/The_real_deal/autogpt-trader/backend/src/monitoring",
        script: "streamlit",
        args: "run dashboard.py",
        interpreter: "python",
        windowsHide: true,
        env: {
          PYTHONPATH: "D:/The_real_deal/autogpt-trader/backend/src"
        }
      },
      {
        name: "hardware-monitor",
        cwd: "D:/The_real_deal/autogpt-trader/backend/src/monitoring",
        script: "hardware_monitor.py",
        interpreter: "python",
        windowsHide: true
      }
    ]
  }