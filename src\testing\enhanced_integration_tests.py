#!/usr/bin/env python3
"""
Enhanced Integration Testing System
Comprehensive testing for the enhanced data infrastructure and component system
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import json
import traceback

# Import enhanced components
from ..core.enhanced_system_integration import EnhancedSystemIntegration
from ..core.execution_results_validator import ExecutionResultsValidator, ValidationResult
from ..data_feeds.enhanced_data_aggregator import EnhancedDataAggregator

logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    """Individual test result"""
    test_name: str
    passed: bool
    execution_time_ms: float
    error_message: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class TestSuite:
    """Test suite results"""
    suite_name: str
    total_tests: int = 0
    passed_tests: int = 0
    failed_tests: int = 0
    total_execution_time_ms: float = 0.0
    results: List[TestResult] = field(default_factory=list)
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    def add_result(self, result: TestResult):
        """Add a test result"""
        self.results.append(result)
        self.total_tests += 1
        self.total_execution_time_ms += result.execution_time_ms
        
        if result.passed:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
    
    def get_success_rate(self) -> float:
        """Get success rate percentage"""
        if self.total_tests == 0:
            return 0.0
        return (self.passed_tests / self.total_tests) * 100

class EnhancedIntegrationTester:
    """
    Enhanced Integration Testing System
    
    Tests:
    - Component discovery and registration
    - Data aggregation from multiple sources
    - Execution results validation
    - Health monitoring and recovery
    - System resilience under failures
    - Real money trading validation
    """
    
    def __init__(self):
        self.integration_system: Optional[EnhancedSystemIntegration] = None
        self.test_suites: List[TestSuite] = []
        
        logger.info("🧪 [TESTING] Enhanced integration tester initialized")
    
    async def run_comprehensive_tests(self, external_components: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run comprehensive integration tests
        
        Args:
            external_components: External components to test with
            
        Returns:
            Comprehensive test results
        """
        try:
            logger.info("🚀 [TESTING] Starting comprehensive integration tests...")
            
            # Initialize integration system
            self.integration_system = EnhancedSystemIntegration()
            
            # Test suites to run
            test_suites = [
                ("System Initialization", self._test_system_initialization),
                ("Component Discovery", self._test_component_discovery),
                ("Data Aggregation", self._test_data_aggregation),
                ("Execution Validation", self._test_execution_validation),
                ("Health Monitoring", self._test_health_monitoring),
                ("Resilience Testing", self._test_system_resilience),
                ("Real Money Validation", self._test_real_money_validation)
            ]
            
            # Run each test suite
            for suite_name, test_function in test_suites:
                try:
                    suite = TestSuite(suite_name=suite_name)
                    await test_function(suite, external_components)
                    suite.end_time = datetime.now()
                    self.test_suites.append(suite)
                    
                    logger.info(f"✅ [TESTING] {suite_name}: {suite.passed_tests}/{suite.total_tests} passed "
                              f"({suite.get_success_rate():.1f}%)")
                    
                except Exception as e:
                    logger.error(f"❌ [TESTING] Test suite {suite_name} failed: {e}")
                    error_suite = TestSuite(suite_name=suite_name)
                    error_suite.add_result(TestResult(
                        test_name=f"{suite_name}_suite_error",
                        passed=False,
                        execution_time_ms=0.0,
                        error_message=str(e)
                    ))
                    self.test_suites.append(error_suite)
            
            # Generate comprehensive report
            return self._generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ [TESTING] Comprehensive testing failed: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
        finally:
            # Cleanup
            if self.integration_system:
                await self.integration_system.shutdown()
    
    async def _test_system_initialization(self, suite: TestSuite, external_components: Optional[Dict[str, Any]]):
        """Test system initialization"""
        try:
            # Test 1: Basic initialization
            start_time = time.time()
            success = await self.integration_system.initialize(external_components)
            execution_time = (time.time() - start_time) * 1000
            
            suite.add_result(TestResult(
                test_name="basic_initialization",
                passed=success,
                execution_time_ms=execution_time,
                error_message=None if success else "System initialization failed",
                details={'operational': self.integration_system.is_operational()}
            ))
            
            # Test 2: Component registry status
            registry_status = self.integration_system.system_status.component_registry_status
            suite.add_result(TestResult(
                test_name="component_registry_status",
                passed=registry_status == "operational",
                execution_time_ms=0.0,
                details={'status': registry_status}
            ))
            
            # Test 3: Health monitor status
            health_status = self.integration_system.system_status.health_monitor_status
            suite.add_result(TestResult(
                test_name="health_monitor_status",
                passed=health_status == "operational",
                execution_time_ms=0.0,
                details={'status': health_status}
            ))
            
            # Test 4: Data aggregator status
            data_status = self.integration_system.system_status.data_aggregator_status
            suite.add_result(TestResult(
                test_name="data_aggregator_status",
                passed=data_status == "operational",
                execution_time_ms=0.0,
                details={'status': data_status}
            ))
            
        except Exception as e:
            suite.add_result(TestResult(
                test_name="initialization_error",
                passed=False,
                execution_time_ms=0.0,
                error_message=str(e)
            ))
    
    async def _test_component_discovery(self, suite: TestSuite, external_components: Optional[Dict[str, Any]]):
        """Test component discovery and registration"""
        try:
            # Test 1: Component count
            active_components = self.integration_system.system_status.active_components
            suite.add_result(TestResult(
                test_name="component_count",
                passed=active_components > 0,
                execution_time_ms=0.0,
                details={'active_components': active_components}
            ))
            
            # Test 2: Exchange client discovery
            exchange_components = self.integration_system.get_healthy_components()
            exchange_clients = [c for c in exchange_components if 'client' in c]
            suite.add_result(TestResult(
                test_name="exchange_client_discovery",
                passed=len(exchange_clients) > 0,
                execution_time_ms=0.0,
                details={'exchange_clients': exchange_clients}
            ))
            
            # Test 3: Component health status
            healthy_count = self.integration_system.system_status.healthy_components
            total_count = self.integration_system.system_status.active_components
            health_rate = (healthy_count / total_count * 100) if total_count > 0 else 0
            
            suite.add_result(TestResult(
                test_name="component_health_rate",
                passed=health_rate >= 70.0,  # At least 70% healthy
                execution_time_ms=0.0,
                details={
                    'healthy_components': healthy_count,
                    'total_components': total_count,
                    'health_rate': health_rate
                }
            ))
            
        except Exception as e:
            suite.add_result(TestResult(
                test_name="component_discovery_error",
                passed=False,
                execution_time_ms=0.0,
                error_message=str(e)
            ))
    
    async def _test_data_aggregation(self, suite: TestSuite, external_components: Optional[Dict[str, Any]]):
        """Test data aggregation functionality"""
        try:
            test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
            
            for symbol in test_symbols:
                # Test price data retrieval
                start_time = time.time()
                price_data = await self.integration_system.get_aggregated_price_with_validation(symbol)
                execution_time = (time.time() - start_time) * 1000
                
                suite.add_result(TestResult(
                    test_name=f"price_data_{symbol}",
                    passed=price_data is not None,
                    execution_time_ms=execution_time,
                    details={
                        'symbol': symbol,
                        'price': price_data.get('price') if price_data else None,
                        'source_count': price_data.get('source_count') if price_data else 0,
                        'confidence': price_data.get('confidence') if price_data else 0
                    }
                ))
                
                # Test data freshness (within last 5 minutes)
                if price_data:
                    timestamp = datetime.fromisoformat(price_data['timestamp'])
                    age_seconds = (datetime.now() - timestamp).total_seconds()
                    
                    suite.add_result(TestResult(
                        test_name=f"data_freshness_{symbol}",
                        passed=age_seconds < 300,  # Less than 5 minutes old
                        execution_time_ms=0.0,
                        details={'age_seconds': age_seconds}
                    ))
            
            # Test data source availability
            data_sources = self.integration_system.system_status.data_sources_available
            suite.add_result(TestResult(
                test_name="data_source_availability",
                passed=data_sources >= 2,  # At least 2 data sources
                execution_time_ms=0.0,
                details={'available_sources': data_sources}
            ))
            
        except Exception as e:
            suite.add_result(TestResult(
                test_name="data_aggregation_error",
                passed=False,
                execution_time_ms=0.0,
                error_message=str(e)
            ))
    
    async def _test_execution_validation(self, suite: TestSuite, external_components: Optional[Dict[str, Any]]):
        """Test execution results validation"""
        try:
            # Test 1: Valid execution result
            valid_result = {
                'status': 'success',
                'action': 'buy',
                'symbol': 'BTCUSDT',
                'amount': 0.001,
                'price': 50000.0,
                'exchange': 'bybit',
                'order_id': 'test_order_123'
            }
            
            validation = self.integration_system.validate_execution_result(valid_result)
            suite.add_result(TestResult(
                test_name="valid_execution_validation",
                passed=validation.is_valid,
                execution_time_ms=0.0,
                details={'issues': len(validation.issues)}
            ))
            
            # Test 2: Invalid execution result
            invalid_result = {
                'status': 'invalid_status',
                'action': 'buy',
                'amount': -1.0,  # Invalid negative amount
                'exchange': 'unknown_exchange'
                # Missing required fields
            }
            
            validation = self.integration_system.validate_execution_result(invalid_result)
            suite.add_result(TestResult(
                test_name="invalid_execution_validation",
                passed=not validation.is_valid,  # Should fail validation
                execution_time_ms=0.0,
                details={'issues': len(validation.issues)}
            ))
            
            # Test 3: Batch validation
            batch_results = [valid_result, invalid_result, valid_result]
            validator = ExecutionResultsValidator()
            batch_validation = validator.validate_batch(batch_results)
            
            valid_count = sum(1 for v in batch_validation if v.is_valid)
            suite.add_result(TestResult(
                test_name="batch_validation",
                passed=valid_count == 2,  # 2 out of 3 should be valid
                execution_time_ms=0.0,
                details={'valid_count': valid_count, 'total_count': len(batch_validation)}
            ))
            
        except Exception as e:
            suite.add_result(TestResult(
                test_name="execution_validation_error",
                passed=False,
                execution_time_ms=0.0,
                error_message=str(e)
            ))
    
    async def _test_health_monitoring(self, suite: TestSuite, external_components: Optional[Dict[str, Any]]):
        """Test health monitoring functionality"""
        try:
            # Test 1: Health report generation
            start_time = time.time()
            health_report = await self.integration_system.get_system_health_report()
            execution_time = (time.time() - start_time) * 1000
            
            suite.add_result(TestResult(
                test_name="health_report_generation",
                passed='error' not in health_report,
                execution_time_ms=execution_time,
                details={'report_keys': list(health_report.keys())}
            ))
            
            # Test 2: System operational status
            operational = health_report.get('system_status', {}).get('operational', False)
            suite.add_result(TestResult(
                test_name="system_operational_status",
                passed=operational,
                execution_time_ms=0.0,
                details={'operational': operational}
            ))
            
            # Test 3: Component monitoring
            component_summary = health_report.get('component_summary', {})
            total_components = component_summary.get('total', 0)
            healthy_components = component_summary.get('healthy', 0)
            
            suite.add_result(TestResult(
                test_name="component_monitoring",
                passed=total_components > 0 and healthy_components > 0,
                execution_time_ms=0.0,
                details=component_summary
            ))
            
        except Exception as e:
            suite.add_result(TestResult(
                test_name="health_monitoring_error",
                passed=False,
                execution_time_ms=0.0,
                error_message=str(e)
            ))
    
    async def _test_system_resilience(self, suite: TestSuite, external_components: Optional[Dict[str, Any]]):
        """Test system resilience under various failure scenarios"""
        try:
            # Test 1: Data source failure simulation
            # Disable a data source and verify system continues
            aggregator = self.integration_system.data_aggregator
            original_sources = list(aggregator.data_sources.keys())
            
            if original_sources:
                # Disable first source
                test_source = original_sources[0]
                aggregator.disable_source(test_source)
                
                # Try to get price data
                price_data = await self.integration_system.get_aggregated_price_with_validation('BTCUSDT')
                
                # Re-enable source
                aggregator.enable_source(test_source)
                
                suite.add_result(TestResult(
                    test_name="data_source_failure_resilience",
                    passed=price_data is not None,
                    execution_time_ms=0.0,
                    details={'disabled_source': test_source, 'data_available': price_data is not None}
                ))
            
            # Test 2: Invalid data handling
            invalid_execution = {'invalid': 'data'}
            validation = self.integration_system.validate_execution_result(invalid_execution)
            
            suite.add_result(TestResult(
                test_name="invalid_data_handling",
                passed=not validation.is_valid,  # Should handle gracefully
                execution_time_ms=0.0,
                details={'handled_gracefully': True}
            ))
            
        except Exception as e:
            suite.add_result(TestResult(
                test_name="resilience_testing_error",
                passed=False,
                execution_time_ms=0.0,
                error_message=str(e)
            ))
    
    async def _test_real_money_validation(self, suite: TestSuite, external_components: Optional[Dict[str, Any]]):
        """Test real money trading validation capabilities"""
        try:
            # Test 1: Exchange client availability
            exchange_clients = []
            if external_components:
                for name, component in external_components.items():
                    if 'client' in name and hasattr(component, 'get_balance'):
                        exchange_clients.append(name)
            
            suite.add_result(TestResult(
                test_name="exchange_client_availability",
                passed=len(exchange_clients) > 0,
                execution_time_ms=0.0,
                details={'available_clients': exchange_clients}
            ))
            
            # Test 2: Real money validation standards
            real_money_result = {
                'status': 'success',
                'action': 'buy',
                'symbol': 'BTCUSDT',
                'amount': 10.0,  # $10 trade
                'price': 50000.0,
                'exchange': 'bybit',
                'order_id': 'real_order_456',
                'timestamp': datetime.now().isoformat()
            }
            
            validation = self.integration_system.validate_execution_result(real_money_result)
            suite.add_result(TestResult(
                test_name="real_money_validation_standards",
                passed=validation.is_valid,
                execution_time_ms=0.0,
                details={'validation_passed': validation.is_valid}
            ))
            
        except Exception as e:
            suite.add_result(TestResult(
                test_name="real_money_validation_error",
                passed=False,
                execution_time_ms=0.0,
                error_message=str(e)
            ))
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        try:
            total_tests = sum(suite.total_tests for suite in self.test_suites)
            total_passed = sum(suite.passed_tests for suite in self.test_suites)
            total_failed = sum(suite.failed_tests for suite in self.test_suites)
            overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_test_suites': len(self.test_suites),
                    'total_tests': total_tests,
                    'total_passed': total_passed,
                    'total_failed': total_failed,
                    'overall_success_rate': overall_success_rate,
                    'status': 'PASSED' if overall_success_rate >= 80 else 'FAILED'
                },
                'test_suites': [
                    {
                        'name': suite.suite_name,
                        'total_tests': suite.total_tests,
                        'passed_tests': suite.passed_tests,
                        'failed_tests': suite.failed_tests,
                        'success_rate': suite.get_success_rate(),
                        'execution_time_ms': suite.total_execution_time_ms,
                        'results': [
                            {
                                'test_name': result.test_name,
                                'passed': result.passed,
                                'execution_time_ms': result.execution_time_ms,
                                'error_message': result.error_message,
                                'details': result.details
                            }
                            for result in suite.results
                        ]
                    }
                    for suite in self.test_suites
                ]
            }
            
            return report
            
        except Exception as e:
            logger.error(f"❌ [TESTING] Error generating test report: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
