#!/usr/bin/env python3
"""
Comprehensive Bybit API debugging and minimum order requirements research
Fixes ErrCode 170140: Order value exceeded lower limit
"""
import asyncio
import json
import os
import sys
from decimal import Decimal
from dotenv import load_dotenv

sys.path.append('.')

async def debug_bybit_comprehensive():
    """Comprehensive Bybit API debugging to fix order execution issues"""
    try:
        load_dotenv()
        
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not api_key or not api_secret:
            print('❌ Bybit credentials not found in environment')
            return False
            
        print(f'🔑 [BYBIT] Using API key: {api_key[:8]}...')
        
        from pybit.unified_trading import HTTP
        
        session = HTTP(
            api_key=api_key,
            api_secret=api_secret,
            testnet=False,
            recv_window=10000
        )
        
        # Test 1: Connection and account info
        print(f'\n🔍 [STEP 1] Testing connection and account info...')
        try:
            account_info = session.get_account_info()
            print(f'📊 [ACCOUNT] Status: {account_info.get("retCode", "Unknown")}')
            if account_info.get('retCode') == 0:
                print(f'✅ [ACCOUNT] Connection successful')
            else:
                print(f'❌ [ACCOUNT] Connection failed: {account_info}')
                return False
        except Exception as e:
            print(f'❌ [ACCOUNT] Connection error: {e}')
            return False
        
        # Test 2: Get current balance
        print(f'\n🔍 [STEP 2] Getting current USDT balance...')
        try:
            balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
            print(f'📊 [BALANCE-RAW] Response: {balance_response}')

            if balance_response.get('retCode') == 0:
                balance_list = balance_response['result']['list']
                if balance_list:
                    coin_list = balance_list[0].get('coin', [])
                    if coin_list:
                        balance_data = coin_list[0]

                        # Handle empty string balances
                        wallet_balance = balance_data.get('walletBalance', '0')
                        available_balance = balance_data.get('availableToWithdraw', wallet_balance)

                        current_balance = float(wallet_balance) if wallet_balance else 0.0
                        available_balance = float(available_balance) if available_balance else current_balance

                        print(f'💰 [BALANCE] Total: ${current_balance:.2f} USDT')
                        print(f'💰 [BALANCE] Available: ${available_balance:.2f} USDT')

                        if current_balance < 5.0:
                            print(f'⚠️ [WARNING] Balance may be insufficient for minimum orders')
                    else:
                        print(f'❌ [BALANCE] No coin data in response')
                        return False
                else:
                    print(f'❌ [BALANCE] No balance list in response')
                    return False
            else:
                print(f'❌ [BALANCE] Failed to get balance: {balance_response}')
                return False
        except Exception as e:
            print(f'❌ [BALANCE] Balance check error: {e}')
            import traceback
            traceback.print_exc()
            return False
        
        # Test 3: Get comprehensive instrument information
        print(f'\n🔍 [STEP 3] Analyzing instrument minimum requirements...')
        
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']
        minimum_requirements = {}
        
        for symbol in test_symbols:
            print(f'\n📊 [SYMBOL] Analyzing {symbol}...')
            try:
                instrument_info = session.get_instruments_info(category="spot", symbol=symbol)
                
                if instrument_info.get('retCode') == 0:
                    instruments = instrument_info.get('result', {}).get('list', [])
                    if instruments:
                        instrument = instruments[0]
                        
                        # Extract lot size filter (contains minimum requirements)
                        lot_size_filter = instrument.get('lotSizeFilter', {})
                        
                        min_order_qty = lot_size_filter.get('minOrderQty', '0')
                        min_order_amt = lot_size_filter.get('minOrderAmt', '0')
                        base_precision = lot_size_filter.get('basePrecision', '0.000001')
                        quote_precision = lot_size_filter.get('quotePrecision', '0.01')
                        qty_step = lot_size_filter.get('qtyStep', '0.000001')
                        
                        # Extract price filter
                        price_filter = instrument.get('priceFilter', {})
                        tick_size = price_filter.get('tickSize', '0.01')
                        
                        minimum_requirements[symbol] = {
                            'min_order_qty': float(min_order_qty) if min_order_qty else 0,
                            'min_order_amt': float(min_order_amt) if min_order_amt else 0,
                            'base_precision': float(base_precision) if base_precision else 0.000001,
                            'quote_precision': float(quote_precision) if quote_precision else 0.01,
                            'qty_step': float(qty_step) if qty_step else 0.000001,
                            'tick_size': float(tick_size) if tick_size else 0.01,
                            'status': instrument.get('status', 'Unknown')
                        }
                        
                        print(f'  ✅ Status: {instrument.get("status", "Unknown")}')
                        print(f'  ✅ Min Order Qty: {min_order_qty}')
                        print(f'  ✅ Min Order Amt: ${min_order_amt}')
                        print(f'  ✅ Base Precision: {base_precision}')
                        print(f'  ✅ Quote Precision: {quote_precision}')
                        print(f'  ✅ Qty Step: {qty_step}')
                        print(f'  ✅ Tick Size: {tick_size}')
                        
                    else:
                        print(f'  ❌ No instrument data found for {symbol}')
                else:
                    print(f'  ❌ API error for {symbol}: {instrument_info}')
                    
            except Exception as e:
                print(f'  ❌ Exception for {symbol}: {e}')
        
        # Test 4: Calculate safe order values
        print(f'\n🔍 [STEP 4] Calculating safe order values...')
        
        safe_orders = {}
        for symbol, requirements in minimum_requirements.items():
            min_amt = requirements['min_order_amt']
            
            if min_amt > 0 and requirements['status'] == 'Trading':
                print(f'\n📊 [CALC] {symbol}:')
                print(f'  💰 Available balance: ${available_balance:.2f}')
                print(f'  📏 Minimum order amount: ${min_amt:.2f}')
                
                if available_balance >= min_amt:
                    # Calculate different order sizes
                    order_25pct = available_balance * 0.25
                    order_20pct = available_balance * 0.20
                    order_min_safe = min_amt * 1.2  # 20% safety margin
                    order_min_plus = min_amt + 1.0   # +$1 safety
                    
                    # Choose the safest option that meets requirements
                    recommended_order = max(order_min_safe, order_min_plus)
                    if recommended_order > available_balance:
                        recommended_order = max(min_amt * 1.1, min_amt + 0.5)  # Smaller safety margin
                    
                    safe_orders[symbol] = {
                        'min_required': min_amt,
                        'recommended': recommended_order,
                        'order_25pct': order_25pct,
                        'order_20pct': order_20pct,
                        'feasible': recommended_order <= available_balance
                    }
                    
                    print(f'  ✅ 25% of balance: ${order_25pct:.2f}')
                    print(f'  ✅ 20% of balance: ${order_20pct:.2f}')
                    print(f'  ✅ Min + safety: ${recommended_order:.2f}')
                    print(f'  🎯 Recommended: ${recommended_order:.2f}')
                    print(f'  ✅ Feasible: {recommended_order <= available_balance}')
                else:
                    print(f'  ❌ Insufficient balance (need ${min_amt:.2f}, have ${available_balance:.2f})')
        
        # Test 5: Get current prices for order calculations
        print(f'\n🔍 [STEP 5] Getting current prices...')
        
        current_prices = {}
        for symbol in minimum_requirements.keys():
            try:
                ticker = session.get_tickers(category="spot", symbol=symbol)
                if ticker.get('retCode') == 0:
                    price_data = ticker['result']['list'][0]
                    current_price = float(price_data['lastPrice'])
                    current_prices[symbol] = current_price
                    print(f'  📊 {symbol}: ${current_price:.2f}')
                else:
                    print(f'  ❌ Failed to get price for {symbol}')
            except Exception as e:
                print(f'  ❌ Price error for {symbol}: {e}')
        
        # Test 6: Validate order calculations
        print(f'\n🔍 [STEP 6] Validating order calculations...')
        
        for symbol in safe_orders.keys():
            if symbol in current_prices and safe_orders[symbol]['feasible']:
                requirements = minimum_requirements[symbol]
                price = current_prices[symbol]
                order_amount = safe_orders[symbol]['recommended']
                
                # Calculate quantity
                quantity = order_amount / price
                min_qty = requirements['min_order_qty']
                qty_step = requirements['qty_step']
                
                # Round quantity to proper step
                rounded_qty = round(quantity / qty_step) * qty_step
                
                print(f'\n📊 [VALIDATION] {symbol}:')
                print(f'  💰 Order amount: ${order_amount:.2f}')
                print(f'  📊 Current price: ${price:.2f}')
                print(f'  📏 Raw quantity: {quantity:.8f}')
                print(f'  📏 Rounded quantity: {rounded_qty:.8f}')
                print(f'  📏 Min quantity: {min_qty:.8f}')
                print(f'  ✅ Quantity valid: {rounded_qty >= min_qty}')
                
                # Final order value check
                final_order_value = rounded_qty * price
                print(f'  💰 Final order value: ${final_order_value:.2f}')
                print(f'  ✅ Meets minimum: ${final_order_value:.2f} >= ${requirements["min_order_amt"]:.2f}')
        
        print(f'\n✅ [COMPLETE] Comprehensive analysis complete')
        print(f'📊 [SUMMARY] Found {len(minimum_requirements)} symbols')
        print(f'📊 [SUMMARY] {len(safe_orders)} symbols have feasible orders')
        print(f'📊 [SUMMARY] Current balance: ${available_balance:.2f} USDT')
        
        return True
        
    except Exception as e:
        print(f'❌ [ERROR] Comprehensive debug failed: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(debug_bybit_comprehensive())
    if success:
        print(f'\n🎯 [RESULT] Analysis successful - ready to implement fixes')
    else:
        print(f'\n❌ [RESULT] Analysis failed - check credentials and connection')
