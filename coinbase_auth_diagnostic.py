#!/usr/bin/env python3
"""
Coinbase API Authentication Diagnostic Tool
Comprehensive testing of credential decryption, JWT generation, and API authentication
"""

import os
import sys
import traceback
import requests

# Add project root to path
sys.path.append('.')

# Load environment
from dotenv import load_dotenv
load_dotenv()

def main():
    print('=== COINBASE API AUTHENTICATION DIAGNOSTIC ===')
    print()

    # Check environment variables
    print('1. ENVIRONMENT VARIABLES:')
    encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
    encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')

    print(f'   ENCRYPTED_COINBASE_API_KEY_NAME: {"Found" if encrypted_api_key else "Missing"}')
    print(f'   ENCRYPTED_COINBASE_PRIVATE_KEY: {"Found" if encrypted_private_key else "Missing"}')
    
    if not encrypted_api_key or not encrypted_private_key:
        print('   ❌ Missing encrypted credentials in environment')
        return False
    
    print()

    # Test decryption
    print('2. CREDENTIAL DECRYPTION:')
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        api_key_name = decrypt_value(encrypted_api_key)
        print(f'   ✅ API Key decrypted: {api_key_name[:50]}...')
        
        # Extract key ID
        if '/apiKeys/' in api_key_name:
            key_id = api_key_name.split('/apiKeys/')[1]
            print(f'   ✅ Key ID extracted: {key_id}')
        else:
            print(f'   ❌ Invalid API key format: {api_key_name}')
            return False
            
    except Exception as e:
        print(f'   ❌ API Key decryption failed: {e}')
        return False

    try:
        private_key_pem = decrypt_value(encrypted_private_key)
        print(f'   ✅ Private Key decrypted: {len(private_key_pem)} characters')
        
        # Validate PEM format
        if 'BEGIN EC PRIVATE KEY' in private_key_pem and 'END EC PRIVATE KEY' in private_key_pem:
            print('   ✅ Private Key format valid (EC PEM)')
        else:
            print('   ❌ Private Key format invalid')
            print(f'   Key preview: {private_key_pem[:100]}...')
            return False
            
    except Exception as e:
        print(f'   ❌ Private Key decryption failed: {e}')
        return False

    print()

    # Test JWT generation
    print('3. JWT TOKEN GENERATION:')
    try:
        import jwt
        import time
        from cryptography.hazmat.primitives import serialization

        # Load private key
        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )

        # Create JWT
        now = int(time.time())
        payload = {
            'iss': 'cdp',
            'nbf': now,
            'exp': now + 120,
            'sub': api_key_name,
            'uri': 'GET /api/v3/brokerage/accounts'
        }

        token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': key_id})
        print(f'   ✅ JWT token generated: {len(token)} characters')

    except Exception as e:
        print(f'   ❌ JWT generation failed: {e}')
        print(f'   Error details: {traceback.format_exc()}')
        return False

    print()

    # Test different API endpoints to identify the issue
    print('5. ENDPOINT TESTING:')
    endpoints_to_test = [
        ('Public Products', 'GET', '/api/v3/brokerage/products', False),
        ('Account Info', 'GET', '/api/v3/brokerage/accounts', True),
        ('Portfolio Info', 'GET', '/api/v1/portfolios', True),
        ('User Info', 'GET', '/api/v3/brokerage/user', True)
    ]

    import requests

    for name, method, endpoint, requires_auth in endpoints_to_test:
        print(f'   Testing {name} ({endpoint})...')

        try:
            if requires_auth:
                # Create fresh JWT for each request
                now = int(time.time())
                payload = {
                    'iss': 'cdp',
                    'nbf': now,
                    'exp': now + 120,
                    'sub': api_key_name,
                    'uri': f'{method} {endpoint}'
                }

                token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': key_id})
                headers = {
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json',
                    'User-Agent': 'AutoGPT-Trader/1.0'
                }
            else:
                headers = {'Content-Type': 'application/json'}

            response = requests.get(f'https://api.coinbase.com{endpoint}', headers=headers, timeout=10)

            if response.status_code == 200:
                print(f'      ✅ {response.status_code} - Success')
            elif response.status_code == 401:
                print(f'      ❌ {response.status_code} - Unauthorized (API key/account issue)')
                if requires_auth:
                    print(f'         Response: {response.text[:200]}')
            elif response.status_code == 403:
                print(f'      ❌ {response.status_code} - Forbidden (Account verification required)')
                print(f'         Response: {response.text[:200]}')
            else:
                print(f'      ⚠️  {response.status_code} - {response.text[:100]}')

        except Exception as e:
            print(f'      ❌ Request failed: {e}')

    print()

    # Account status check
    print('6. ACCOUNT STATUS ANALYSIS:')
    print('   Based on the 401 errors, possible causes:')
    print('   1. Account not fully verified for API access')
    print('   2. Organization-level restrictions on API usage')
    print('   3. Geographic restrictions (VPN interference)')
    print('   4. Account compliance hold or review')
    print('   5. API key permissions insufficient')
    print()
    print('   RECOMMENDED ACTIONS:')
    print('   1. Contact Coinbase Support with Organization ID: 7405b51f-cfea-4f54-a52d-02838b5cb217')
    print('   2. Verify account is fully verified (ID, address, etc.)')
    print('   3. Check if account has any compliance holds')
    print('   4. Ensure API key has proper permissions enabled')
    print('   5. Try accessing from your actual IP (************) without VPN')

    print()

    # Test API call
    print('4. API AUTHENTICATION TEST:')
    try:
        import requests
        
        # Make authenticated request
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            'https://api.coinbase.com/api/v3/brokerage/accounts',
            headers=headers,
            timeout=10
        )
        
        print(f'   Status Code: {response.status_code}')
        
        if response.status_code == 200:
            print('   ✅ Authentication successful!')
            data = response.json()
            accounts = data.get('accounts', [])
            print(f'   Found {len(accounts)} accounts')
            
            # Show account details
            for account in accounts[:3]:  # Show first 3 accounts
                currency = account.get('currency', 'Unknown')
                balance = account.get('available_balance', {}).get('value', '0')
                print(f'   Account: {currency} = {balance}')
                
            return True
        else:
            print(f'   ❌ Authentication failed')
            print(f'   Response: {response.text[:500]}...')
            
            # Analyze error
            if response.status_code == 401:
                print('   🔍 401 Unauthorized - Check API key permissions and format')
            elif response.status_code == 403:
                print('   🔍 403 Forbidden - Check IP whitelist and API permissions')
            elif response.status_code == 404:
                print('   🔍 404 Not Found - Check API endpoint URL')
            
            return False
            
    except Exception as e:
        print(f'   ❌ API call failed: {e}')
        return False

    print()
    print('=== DIAGNOSTIC COMPLETE ===')

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
