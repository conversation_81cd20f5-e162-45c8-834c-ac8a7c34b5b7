#!/usr/bin/env python3
"""
Test script for Bybit Order Validator
Verifies that order validation fixes ErrCode 170140
"""
import asyncio
import os
import sys
from dotenv import load_dotenv

sys.path.append('.')

async def test_bybit_order_validator():
    """Test the Bybit order validator with real API"""
    try:
        load_dotenv()
        
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not api_key or not api_secret:
            print('❌ Bybit credentials not found')
            return False
        
        print(f'🔑 [TEST] Using API key: {api_key[:8]}...')
        
        from pybit.unified_trading import HTTP
        from src.exchanges.bybit_order_validator import BybitOrderValidator
        
        # Initialize session and validator
        session = HTTP(
            api_key=api_key,
            api_secret=api_secret,
            testnet=False,
            recv_window=10000
        )
        
        validator = BybitOrderValidator(session)
        
        print(f'✅ [TEST] Validator initialized')
        
        # Get current balance
        balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
        if balance_response.get('retCode') == 0:
            balance_data = balance_response['result']['list'][0]['coin'][0]
            current_balance = float(balance_data.get('walletBalance', '0'))
            print(f'💰 [TEST] Current balance: ${current_balance:.2f} USDT')
        else:
            print(f'❌ [TEST] Failed to get balance')
            return False
        
        # Test 1: Validate small order that would normally fail
        print(f'\n🔍 [TEST 1] Testing small order validation...')
        
        test_cases = [
            {'symbol': 'BTCUSDT', 'amount': 3.0, 'expected': 'adjusted'},  # Below $5 minimum
            {'symbol': 'BTCUSDT', 'amount': 6.0, 'expected': 'valid'},    # Above minimum
            {'symbol': 'ETHUSDT', 'amount': 4.5, 'expected': 'adjusted'}, # Below minimum
            {'symbol': 'SOLUSDT', 'amount': 8.0, 'expected': 'valid'},    # Above minimum
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            symbol = test_case['symbol']
            amount = test_case['amount']
            expected = test_case['expected']
            
            print(f'\n📊 [TEST 1.{i}] Validating {symbol} order: ${amount:.2f}')
            
            is_valid, order_params = await validator.validate_and_adjust_order(
                symbol=symbol,
                side="Buy",
                amount=amount,
                order_type="Market"
            )
            
            if is_valid:
                final_amount = float(order_params['qty'])
                calculated_value = order_params['calculated_value']
                
                print(f'  ✅ Order validated')
                print(f'  📊 Original amount: ${amount:.2f}')
                print(f'  📊 Final amount: ${final_amount:.2f}')
                print(f'  📊 Calculated value: ${calculated_value:.2f}')
                print(f'  📊 Price: ${order_params["price"]:.2f}')
                
                if expected == 'adjusted' and final_amount > amount:
                    print(f'  ✅ Correctly adjusted upward')
                elif expected == 'valid':
                    print(f'  ✅ Order was already valid')
            else:
                print(f'  ❌ Order validation failed: {order_params.get("error", "Unknown error")}')
        
        # Test 2: Test safe order amount calculation
        print(f'\n🔍 [TEST 2] Testing safe order amount calculation...')
        
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        for symbol in test_symbols:
            safe_amount = await validator.get_safe_order_amount(symbol, current_balance, 0.25)
            print(f'  📊 {symbol}: Safe order amount = ${safe_amount:.2f} (25% of ${current_balance:.2f})')
        
        # Test 3: Test actual order placement (if balance sufficient)
        print(f'\n🔍 [TEST 3] Testing actual order placement...')
        
        if current_balance >= 6.0:  # Minimum for safe testing
            symbol = 'BTCUSDT'
            test_amount = 6.0  # Safe amount above minimum
            
            print(f'🚨 [TEST 3] Attempting REAL order placement: ${test_amount:.2f} {symbol}')
            print(f'⚠️ [WARNING] This will place a REAL order with REAL money!')
            
            # Validate the order first
            is_valid, order_params = await validator.validate_and_adjust_order(
                symbol=symbol,
                side="Buy",
                amount=test_amount,
                order_type="Market"
            )
            
            if is_valid:
                print(f'✅ [TEST 3] Order validation passed')
                print(f'📊 [TEST 3] Order params: {order_params}')
                
                # Place the actual order
                try:
                    order_result = session.place_order(
                        category=order_params['category'],
                        symbol=order_params['symbol'],
                        side=order_params['side'],
                        orderType=order_params['orderType'],
                        qty=order_params['qty'],
                        isLeverage=order_params['isLeverage'],
                        orderFilter=order_params.get('orderFilter', 'Order')
                    )
                    
                    print(f'📊 [TEST 3] Order result: {order_result}')
                    
                    if order_result.get('retCode') == 0:
                        order_id = order_result['result']['orderId']
                        print(f'✅ [TEST 3] ORDER SUCCESSFUL! Order ID: {order_id}')
                        print(f'🎯 [SUCCESS] Real money trade executed - ErrCode 170140 FIXED!')
                        
                        # Get updated balance to verify trade
                        await asyncio.sleep(2)  # Wait for order to process
                        new_balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
                        if new_balance_response.get('retCode') == 0:
                            new_balance = float(new_balance_response['result']['list'][0]['coin'][0]['walletBalance'])
                            balance_change = current_balance - new_balance
                            print(f'💰 [VERIFICATION] Balance change: ${balance_change:.2f}')
                            print(f'💰 [VERIFICATION] New balance: ${new_balance:.2f}')
                            
                            if balance_change > 0:
                                print(f'✅ [VERIFICATION] REAL MONEY TRADE CONFIRMED!')
                                return True
                        
                    else:
                        error_code = order_result.get('retCode')
                        error_msg = order_result.get('retMsg', 'Unknown error')
                        print(f'❌ [TEST 3] Order failed: Code {error_code}, Message: {error_msg}')
                        
                        if error_code == 170140:
                            print(f'❌ [CRITICAL] ErrCode 170140 still occurring - validation failed!')
                            return False
                        
                except Exception as e:
                    print(f'❌ [TEST 3] Order placement exception: {e}')
            else:
                print(f'❌ [TEST 3] Order validation failed: {order_params}')
        else:
            print(f'⚠️ [TEST 3] Insufficient balance for real order test (${current_balance:.2f} < $6.00)')
        
        print(f'\n✅ [COMPLETE] Order validator testing complete')
        return True
        
    except Exception as e:
        print(f'❌ [ERROR] Test failed: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_bybit_order_validator())
    if success:
        print(f'\n🎯 [RESULT] Order validator test SUCCESSFUL')
    else:
        print(f'\n❌ [RESULT] Order validator test FAILED')
