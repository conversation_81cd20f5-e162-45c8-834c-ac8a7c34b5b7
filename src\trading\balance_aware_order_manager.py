"""
Intelligent Multi-Currency Balance-Aware Order Management

Comprehensive balance validation and order sizing system that prevents trading
halts due to insufficient balance in any single currency. Includes real-time
balance checks, dynamic order sizing based on available funds, and fail-fast
behavior when balance validation fails.
"""

import asyncio
import logging
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import time
import math

logger = logging.getLogger(__name__)

class BalanceStatus(Enum):
    """Balance status for trading"""
    SUFFICIENT = "sufficient"           # Enough balance for full order
    PARTIAL = "partial"                # Partial balance available
    INSUFFICIENT = "insufficient"      # Not enough balance
    CRITICAL = "critical"              # Balance below critical threshold
    UNAVAILABLE = "unavailable"        # Currency not available

class OrderSizingStrategy(Enum):
    """Order sizing strategies"""
    FULL_AMOUNT = "full_amount"        # Use full requested amount
    AVAILABLE_BALANCE = "available_balance"  # Use all available balance
    PERCENTAGE = "percentage"          # Use percentage of available
    DYNAMIC = "dynamic"                # Dynamic sizing based on conditions
    CONSERVATIVE = "conservative"      # Conservative sizing with buffer

@dataclass
class BalanceCheck:
    """Result of balance validation"""
    currency: str
    exchange: str
    requested_amount: Decimal
    available_balance: Decimal
    status: BalanceStatus
    usable_amount: Decimal
    buffer_amount: Decimal
    last_update: float
    confidence: float

@dataclass
class OrderSizing:
    """Order sizing recommendation"""
    original_amount: Decimal
    recommended_amount: Decimal
    sizing_strategy: OrderSizingStrategy
    confidence: float
    risk_score: float
    reason: str
    alternative_currencies: List[str]

class BalanceAwareOrderManager:
    """
    Intelligent multi-currency balance-aware order management system
    that ensures continuous trading capability through comprehensive
    balance validation and dynamic order sizing
    """
    
    def __init__(self, exchange_clients: Dict[str, Any], config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}
        
        # Balance management configuration
        self.balance_buffer = self.config.get('balance_buffer', 0.05)  # 5% buffer
        self.min_order_value = self.config.get('min_order_value', 10.0)  # CRITICAL FIX: $10 minimum for Bybit
        self.max_balance_usage = self.config.get('max_balance_usage', 0.9)  # 90% max usage
        self.critical_threshold = self.config.get('critical_threshold', 0.1)  # 10% critical
        
        # Order sizing configuration
        self.default_sizing_strategy = OrderSizingStrategy(
            self.config.get('sizing_strategy', 'dynamic')
        )
        self.aggressive_trading = self.config.get('aggressive_trading', True)
        self.micro_trading_enabled = self.config.get('micro_trading', True)
        
        # Balance tracking
        self.balance_cache = {}
        self.balance_history = {}
        self.failed_orders = []
        
        # Performance metrics
        self.order_stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'balance_failures': 0,
            'average_sizing_accuracy': 0.0
        }
        
        logger.info("⚖️ [BALANCE-MANAGER] Initialized balance-aware order manager")
    
    async def initialize(self):
        """Initialize the balance-aware order manager"""
        try:
            logger.info("🔧 [BALANCE-MANAGER] Initializing order manager...")
            
            # Initialize balance cache
            await self.refresh_all_balances()
            
            # Set up real-time balance monitoring
            await self.setup_balance_monitoring()
            
            logger.info("✅ [BALANCE-MANAGER] Order manager initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ [BALANCE-MANAGER] Error initializing order manager: {e}")
            raise
    
    async def refresh_all_balances(self):
        """Refresh balance cache for all exchanges"""
        try:
            logger.info("🔄 [BALANCE-REFRESH] Refreshing all balances...")
            
            for exchange_name, client in self.exchange_clients.items():
                try:
                    if hasattr(client, 'get_all_available_balances'):
                        # Add timeout to prevent hanging
                        try:
                            balances = await asyncio.wait_for(
                                client.get_all_available_balances(),
                                timeout=15.0
                            )
                        except asyncio.TimeoutError:
                            logger.warning(f"⚠️ [BALANCE-REFRESH] {exchange_name} balance refresh timed out")
                            continue
                        
                        self.balance_cache[exchange_name] = {
                            'balances': balances,
                            'last_update': time.time(),
                            'update_count': self.balance_cache.get(exchange_name, {}).get('update_count', 0) + 1
                        }
                        
                        logger.info(f"🔄 [BALANCE-REFRESH] {exchange_name}: {len(balances)} currencies")
                
                except Exception as e:
                    logger.warning(f"⚠️ [BALANCE-REFRESH] Error refreshing {exchange_name}: {e}")
                    continue
            
            logger.info(f"🔄 [BALANCE-REFRESH] Refreshed balances for {len(self.balance_cache)} exchanges")
            
        except Exception as e:
            logger.error(f"❌ [BALANCE-REFRESH] Error refreshing balances: {e}")
    
    async def validate_order_balance(self, symbol: str, side: str, amount: Decimal, 
                                   exchange: str, price: Optional[Decimal] = None) -> BalanceCheck:
        """Validate balance for a specific order"""
        try:
            logger.info(f"✅ [BALANCE-CHECK] Validating {side} {amount} {symbol} on {exchange}")
            
            # Determine required currency and amount
            if side.lower() == 'buy':
                # For buy orders, need quote currency
                if 'USDT' in symbol:
                    required_currency = 'USDT'
                    base_currency = symbol.replace('USDT', '')
                elif 'USD' in symbol:
                    required_currency = 'USD'
                    base_currency = symbol.replace('USD', '')
                else:
                    # Try to parse symbol
                    required_currency = 'USDT'  # Default fallback
                    base_currency = symbol
                
                # Calculate required amount (amount * price for buy orders)
                if price and price > 0:
                    required_amount = amount * price
                else:
                    # Get current price
                    current_price = await self._get_current_price(symbol, exchange)
                    required_amount = amount * current_price if current_price > 0 else amount
            else:
                # For sell orders, need base currency
                if 'USDT' in symbol:
                    required_currency = symbol.replace('USDT', '')
                elif 'USD' in symbol:
                    required_currency = symbol.replace('USD', '')
                else:
                    required_currency = symbol
                
                required_amount = amount
            
            # Get current balance
            available_balance = await self._get_current_balance(required_currency, exchange)
            
            # Calculate usable amount (with buffer)
            buffer_amount = available_balance * Decimal(str(self.balance_buffer))
            usable_balance = available_balance - buffer_amount
            
            # Determine status
            if usable_balance >= required_amount:
                status = BalanceStatus.SUFFICIENT
                usable_amount = required_amount
            elif available_balance >= required_amount:
                status = BalanceStatus.PARTIAL
                usable_amount = usable_balance
            elif usable_balance > 0:
                status = BalanceStatus.PARTIAL
                usable_amount = usable_balance
            elif available_balance <= available_balance * Decimal(str(self.critical_threshold)):
                status = BalanceStatus.CRITICAL
                usable_amount = Decimal('0')
            else:
                status = BalanceStatus.INSUFFICIENT
                usable_amount = Decimal('0')
            
            # Calculate confidence based on balance age and update frequency
            confidence = self._calculate_balance_confidence(exchange)
            
            balance_check = BalanceCheck(
                currency=required_currency,
                exchange=exchange,
                requested_amount=required_amount,
                available_balance=available_balance,
                status=status,
                usable_amount=usable_amount,
                buffer_amount=buffer_amount,
                last_update=time.time(),
                confidence=confidence
            )
            
            logger.info(f"✅ [BALANCE-CHECK] {required_currency}: {status.value}")
            logger.info(f"  Available: {available_balance:.6f}, Required: {required_amount:.6f}")
            logger.info(f"  Usable: {usable_amount:.6f}, Buffer: {buffer_amount:.6f}")
            
            return balance_check
            
        except Exception as e:
            logger.error(f"❌ [BALANCE-CHECK] Error validating balance: {e}")
            return BalanceCheck(
                currency="unknown",
                exchange=exchange,
                requested_amount=amount,
                available_balance=Decimal('0'),
                status=BalanceStatus.UNAVAILABLE,
                usable_amount=Decimal('0'),
                buffer_amount=Decimal('0'),
                last_update=time.time(),
                confidence=0.0
            )
    
    async def calculate_optimal_order_size(self, symbol: str, side: str, 
                                         requested_amount: Decimal, exchange: str,
                                         strategy: Optional[OrderSizingStrategy] = None) -> OrderSizing:
        """Calculate optimal order size based on available balance and strategy"""
        try:
            logger.info(f"📏 [ORDER-SIZING] Calculating optimal size for {side} {requested_amount} {symbol}")
            
            # Use default strategy if not specified
            if not strategy:
                strategy = self.default_sizing_strategy
            
            # Validate current balance
            balance_check = await self.validate_order_balance(symbol, side, requested_amount, exchange)
            
            # Calculate recommended amount based on strategy
            if strategy == OrderSizingStrategy.FULL_AMOUNT:
                recommended_amount = await self._calculate_full_amount_sizing(balance_check, requested_amount)
            elif strategy == OrderSizingStrategy.AVAILABLE_BALANCE:
                recommended_amount = await self._calculate_available_balance_sizing(balance_check)
            elif strategy == OrderSizingStrategy.PERCENTAGE:
                recommended_amount = await self._calculate_percentage_sizing(balance_check, requested_amount)
            elif strategy == OrderSizingStrategy.DYNAMIC:
                recommended_amount = await self._calculate_dynamic_sizing(balance_check, requested_amount, symbol, side)
            elif strategy == OrderSizingStrategy.CONSERVATIVE:
                recommended_amount = await self._calculate_conservative_sizing(balance_check, requested_amount)
            else:
                recommended_amount = requested_amount
            
            # Ensure minimum order value
            min_amount = await self._calculate_minimum_order_amount(symbol, exchange)
            if recommended_amount < min_amount:
                if balance_check.usable_amount >= min_amount:
                    recommended_amount = min_amount
                else:
                    recommended_amount = Decimal('0')
            
            # Calculate confidence and risk
            confidence = self._calculate_sizing_confidence(balance_check, recommended_amount, requested_amount)
            risk_score = self._calculate_sizing_risk(balance_check, recommended_amount)
            
            # Find alternative currencies if needed
            alternative_currencies = await self._find_alternative_currencies(symbol, exchange) if recommended_amount == 0 else []
            
            # Generate reason
            reason = self._generate_sizing_reason(balance_check, strategy, recommended_amount, requested_amount)
            
            order_sizing = OrderSizing(
                original_amount=requested_amount,
                recommended_amount=recommended_amount,
                sizing_strategy=strategy,
                confidence=confidence,
                risk_score=risk_score,
                reason=reason,
                alternative_currencies=alternative_currencies
            )
            
            logger.info(f"📏 [ORDER-SIZING] Recommendation: {requested_amount:.6f} -> {recommended_amount:.6f}")
            logger.info(f"📏 [ORDER-SIZING] Strategy: {strategy.value}, Confidence: {confidence:.2f}")
            logger.info(f"📏 [ORDER-SIZING] Reason: {reason}")
            
            return order_sizing
            
        except Exception as e:
            logger.error(f"❌ [ORDER-SIZING] Error calculating order size: {e}")
            return OrderSizing(
                original_amount=requested_amount,
                recommended_amount=Decimal('0'),
                sizing_strategy=strategy or self.default_sizing_strategy,
                confidence=0.0,
                risk_score=1.0,
                reason=f"Error calculating size: {str(e)}",
                alternative_currencies=[]
            )
    
    async def _get_current_balance(self, currency: str, exchange: str) -> Decimal:
        """Get current balance for a currency on an exchange"""
        try:
            # Check cache first
            if exchange in self.balance_cache:
                cache_data = self.balance_cache[exchange]
                balances = cache_data['balances']
                last_update = cache_data['last_update']
                
                # Refresh if cache is older than 30 seconds
                if time.time() - last_update > 30:
                    await self.refresh_all_balances()
                    balances = self.balance_cache.get(exchange, {}).get('balances', {})
                
                return Decimal(str(balances.get(currency, 0)))
            else:
                # Refresh cache if exchange not found
                await self.refresh_all_balances()
                balances = self.balance_cache.get(exchange, {}).get('balances', {})
                return Decimal(str(balances.get(currency, 0)))
                
        except Exception as e:
            logger.error(f"❌ [BALANCE] Error getting balance for {currency} on {exchange}: {e}")
            return Decimal('0')
    
    async def _get_current_price(self, symbol: str, exchange: str) -> Decimal:
        """Get current price for a symbol"""
        try:
            client = self.exchange_clients.get(exchange)
            if client and hasattr(client, 'get_price'):
                price = client.get_price(symbol)
                return Decimal(str(price)) if price and float(price) > 0 else Decimal('0')
            return Decimal('0')
        except Exception as e:
            logger.error(f"❌ [PRICE] Error getting price for {symbol}: {e}")
            return Decimal('0')
    
    def _calculate_balance_confidence(self, exchange: str) -> float:
        """Calculate confidence in balance data"""
        try:
            if exchange not in self.balance_cache:
                return 0.0
            
            cache_data = self.balance_cache[exchange]
            last_update = cache_data['last_update']
            update_count = cache_data.get('update_count', 0)
            
            # Age factor (newer is better)
            age_seconds = time.time() - last_update
            age_factor = max(0.0, 1.0 - (age_seconds / 300))  # Decay over 5 minutes
            
            # Update frequency factor (more updates = higher confidence)
            frequency_factor = min(1.0, update_count / 10)  # Max confidence at 10 updates
            
            return (age_factor * 0.7) + (frequency_factor * 0.3)
            
        except Exception as e:
            logger.error(f"❌ [CONFIDENCE] Error calculating confidence: {e}")
            return 0.5
    
    async def setup_balance_monitoring(self):
        """Set up real-time balance monitoring"""
        logger.info("📡 [BALANCE-MONITOR] Setting up balance monitoring...")
        # Implementation for real-time balance monitoring
        pass

    async def _calculate_full_amount_sizing(self, balance_check: BalanceCheck, requested_amount: Decimal) -> Decimal:
        """Calculate sizing using full amount strategy"""
        if balance_check.status == BalanceStatus.SUFFICIENT:
            return requested_amount
        else:
            return Decimal('0')

    async def _calculate_available_balance_sizing(self, balance_check: BalanceCheck) -> Decimal:
        """Calculate sizing using available balance strategy"""
        if self.aggressive_trading:
            return balance_check.usable_amount * Decimal(str(self.max_balance_usage))
        else:
            return balance_check.usable_amount

    async def _calculate_percentage_sizing(self, balance_check: BalanceCheck, requested_amount: Decimal) -> Decimal:
        """Calculate sizing using percentage strategy"""
        percentage = self.config.get('balance_percentage', 0.8)  # 80% default
        max_amount = balance_check.usable_amount * Decimal(str(percentage))
        return min(requested_amount, max_amount)

    async def _calculate_dynamic_sizing(self, balance_check: BalanceCheck, requested_amount: Decimal,
                                      symbol: str, side: str) -> Decimal:
        """Calculate sizing using dynamic strategy"""
        try:
            # Dynamic sizing based on multiple factors

            # Factor 1: Balance availability
            if balance_check.status == BalanceStatus.SUFFICIENT:
                balance_factor = 1.0
            elif balance_check.status == BalanceStatus.PARTIAL:
                balance_factor = float(balance_check.usable_amount / requested_amount)
            else:
                balance_factor = 0.0

            # Factor 2: Market conditions (simplified)
            market_factor = 0.9  # Assume slightly conservative

            # Factor 3: Micro-trading enablement
            if self.micro_trading_enabled and balance_check.usable_amount > 0:
                micro_factor = 1.0
            else:
                micro_factor = 0.8

            # Factor 4: Aggressive trading setting
            aggression_factor = 0.9 if self.aggressive_trading else 0.7

            # Calculate final sizing factor
            sizing_factor = balance_factor * market_factor * micro_factor * aggression_factor

            # Apply sizing factor
            if balance_check.status == BalanceStatus.SUFFICIENT:
                return requested_amount * Decimal(str(sizing_factor))
            else:
                return balance_check.usable_amount * Decimal(str(sizing_factor))

        except Exception as e:
            logger.error(f"❌ [DYNAMIC-SIZING] Error in dynamic sizing: {e}")
            return min(requested_amount, balance_check.usable_amount)

    async def _calculate_conservative_sizing(self, balance_check: BalanceCheck, requested_amount: Decimal) -> Decimal:
        """Calculate sizing using conservative strategy"""
        conservative_factor = 0.6  # Use only 60% of available balance
        max_amount = balance_check.usable_amount * Decimal(str(conservative_factor))
        return min(requested_amount, max_amount)

    async def _calculate_minimum_order_amount(self, symbol: str, exchange: str) -> Decimal:
        """Calculate minimum order amount for a symbol"""
        try:
            # Get current price to calculate minimum amount
            current_price = await self._get_current_price(symbol, exchange)

            if current_price > 0:
                # Calculate minimum amount based on minimum order value
                min_amount = Decimal(str(self.min_order_value)) / current_price
                return min_amount
            else:
                return Decimal('0.001')  # Default minimum

        except Exception as e:
            logger.error(f"❌ [MIN-ORDER] Error calculating minimum order: {e}")
            return Decimal('0.001')

    def _calculate_sizing_confidence(self, balance_check: BalanceCheck,
                                   recommended_amount: Decimal, requested_amount: Decimal) -> float:
        """Calculate confidence in sizing recommendation"""
        try:
            # Base confidence from balance check
            base_confidence = balance_check.confidence

            # Sizing accuracy factor
            if requested_amount > 0:
                sizing_accuracy = float(recommended_amount / requested_amount)
                sizing_accuracy = min(1.0, sizing_accuracy)  # Cap at 1.0
            else:
                sizing_accuracy = 0.0

            # Balance status factor
            status_factor = {
                BalanceStatus.SUFFICIENT: 1.0,
                BalanceStatus.PARTIAL: 0.7,
                BalanceStatus.INSUFFICIENT: 0.3,
                BalanceStatus.CRITICAL: 0.1,
                BalanceStatus.UNAVAILABLE: 0.0
            }.get(balance_check.status, 0.5)

            # Combined confidence
            confidence = (base_confidence * 0.4) + (sizing_accuracy * 0.3) + (status_factor * 0.3)

            return max(0.0, min(1.0, confidence))

        except Exception as e:
            logger.error(f"❌ [SIZING-CONFIDENCE] Error calculating confidence: {e}")
            return 0.5

    def _calculate_sizing_risk(self, balance_check: BalanceCheck, recommended_amount: Decimal) -> float:
        """Calculate risk score for sizing recommendation"""
        try:
            # Balance utilization risk
            if balance_check.available_balance > 0:
                utilization = float(recommended_amount / balance_check.available_balance)
            else:
                utilization = 1.0

            # Risk increases with higher utilization
            utilization_risk = utilization * 0.5

            # Balance status risk
            status_risk = {
                BalanceStatus.SUFFICIENT: 0.1,
                BalanceStatus.PARTIAL: 0.3,
                BalanceStatus.INSUFFICIENT: 0.7,
                BalanceStatus.CRITICAL: 0.9,
                BalanceStatus.UNAVAILABLE: 1.0
            }.get(balance_check.status, 0.5)

            # Confidence risk (lower confidence = higher risk)
            confidence_risk = 1.0 - balance_check.confidence

            # Combined risk
            total_risk = (utilization_risk * 0.4) + (status_risk * 0.4) + (confidence_risk * 0.2)

            return max(0.0, min(1.0, total_risk))

        except Exception as e:
            logger.error(f"❌ [SIZING-RISK] Error calculating risk: {e}")
            return 0.5

    async def _find_alternative_currencies(self, symbol: str, exchange: str) -> List[str]:
        """Find alternative currencies that could be used for trading"""
        try:
            alternatives = []

            # Get available balances
            if exchange in self.balance_cache:
                balances = self.balance_cache[exchange]['balances']

                # Look for currencies with sufficient balance
                for currency, balance in balances.items():
                    if balance > 0 and currency != symbol:
                        usd_value = await self._get_usd_value(currency, balance, exchange)
                        if usd_value >= self.min_order_value:
                            alternatives.append(currency)

            return alternatives[:5]  # Return top 5 alternatives

        except Exception as e:
            logger.error(f"❌ [ALTERNATIVES] Error finding alternatives: {e}")
            return []

    async def _get_usd_value(self, currency: str, amount: float, exchange: str) -> float:
        """Get USD value of a currency amount"""
        try:
            if currency in ['USD', 'USDT', 'USDC']:
                return amount

            # Get price
            symbol = f"{currency}USDT"
            price = await self._get_current_price(symbol, exchange)

            if price > 0:
                return float(amount * price)
            else:
                return 0.0

        except Exception as e:
            logger.error(f"❌ [USD-VALUE] Error getting USD value: {e}")
            return 0.0

    def _generate_sizing_reason(self, balance_check: BalanceCheck, strategy: OrderSizingStrategy,
                              recommended_amount: Decimal, requested_amount: Decimal) -> str:
        """Generate human-readable reason for sizing decision"""
        try:
            if balance_check.status == BalanceStatus.SUFFICIENT:
                if recommended_amount == requested_amount:
                    return f"Sufficient {balance_check.currency} balance for full order"
                else:
                    return f"Sufficient balance but {strategy.value} strategy applied"
            elif balance_check.status == BalanceStatus.PARTIAL:
                return f"Partial {balance_check.currency} balance - using {recommended_amount:.6f} of {requested_amount:.6f}"
            elif balance_check.status == BalanceStatus.INSUFFICIENT:
                return f"Insufficient {balance_check.currency} balance - cannot execute order"
            elif balance_check.status == BalanceStatus.CRITICAL:
                return f"Critical {balance_check.currency} balance - trading halted"
            else:
                return f"{balance_check.currency} balance unavailable"

        except Exception as e:
            logger.error(f"❌ [SIZING-REASON] Error generating reason: {e}")
            return "Unknown sizing reason"

    async def execute_balance_aware_order(self, symbol: str, side: str, amount: Decimal,
                                        exchange: str, order_type: str = "market") -> Dict[str, Any]:
        """Execute an order with comprehensive balance validation and aggressive sizing"""
        try:
            logger.info(f"⚖️ [BALANCE-ORDER] Executing balance-aware order")
            logger.info(f"⚖️ [BALANCE-ORDER] {side} {amount} {symbol} on {exchange}")

            # CRITICAL FIX: Get real-time balance and use 80-90% as per user requirements
            client = self.exchange_clients.get(exchange)
            if not client:
                return {"success": False, "error": f"Exchange {exchange} not available"}

            # Step 1: Get real-time balance for required currency
            if side.lower() == 'buy':
                # For BUY orders, need quote currency (USDT)
                required_currency = 'USDT'
            else:
                # For SELL orders, need base currency (BTC, ETH, etc.)
                required_currency = symbol.replace('USDT', '').replace('USD', '')

            try:
                if hasattr(client, 'get_balance'):
                    actual_balance = await client.get_balance(required_currency)
                    actual_balance_float = float(actual_balance) if actual_balance else 0.0
                else:
                    actual_balance_float = 0.0

                logger.info(f"💰 [REAL-TIME-BALANCE] {required_currency}: {actual_balance_float:.8f}")
            except Exception as e:
                logger.error(f"❌ [BALANCE-FETCH] Failed to get {required_currency} balance: {e}")
                return {"success": False, "error": f"Could not get {required_currency} balance: {e}"}

            # Step 2: CRITICAL FIX - Check minimum order requirements BEFORE calculating order size
            if actual_balance_float <= 0:
                return {"success": False, "error": f"No {required_currency} balance available"}

            # Get current price for minimum order validation
            try:
                current_price = float(client.get_price(symbol)) if hasattr(client, 'get_price') else 0.0
            except:
                current_price = 0.0

            # CRITICAL FIX: Validate minimum order requirements first
            min_order_value_usd = self.min_order_value  # $10 for Bybit

            if side.lower() == 'buy':
                # For BUY orders: check if we have enough USDT for minimum order
                if actual_balance_float < min_order_value_usd:
                    # CRITICAL FIX: Instead of failing, try fallback strategy immediately
                    logger.warning(f"⚠️ [INSUFFICIENT-BALANCE] {required_currency} balance ${actual_balance_float:.2f} < minimum ${min_order_value_usd:.2f}")
                    logger.info(f"🔄 [AUTO-FALLBACK] Executing fallback strategy for insufficient {required_currency}")

                    fallback_result = await self.execute_fallback_trading_strategy(
                        symbol, side, amount, exchange,
                        f"Insufficient {required_currency} balance: need ${min_order_value_usd:.2f}, have ${actual_balance_float:.2f}"
                    )

                    if fallback_result.get('success', False):
                        return fallback_result
                    else:
                        return {
                            "success": False,
                            "error": f"Insufficient {required_currency} balance: need ${min_order_value_usd:.2f}, have ${actual_balance_float:.2f}",
                            "alternatives": await self._suggest_alternative_currencies(symbol, side, min_order_value_usd, exchange)
                        }
                # Use 80-90% of available balance, but ensure it meets minimum
                aggressive_percentage = 0.85
                max_usable = actual_balance_float * aggressive_percentage
                if max_usable < min_order_value_usd:
                    max_usable = min(actual_balance_float * 0.95, actual_balance_float - 0.01)  # Use 95% if needed
            else:
                # For SELL orders: check if crypto holding value meets minimum
                if current_price > 0:
                    holding_value_usd = actual_balance_float * current_price
                    min_crypto_amount = min_order_value_usd / current_price

                    if holding_value_usd < min_order_value_usd:
                        # CRITICAL FIX: Instead of failing, try fallback strategy immediately
                        logger.warning(f"⚠️ [INSUFFICIENT-BALANCE] {required_currency} value ${holding_value_usd:.2f} < minimum ${min_order_value_usd:.2f}")
                        logger.info(f"🔄 [AUTO-FALLBACK] Executing fallback strategy for insufficient {required_currency}")

                        fallback_result = await self.execute_fallback_trading_strategy(
                            symbol, side, amount, exchange,
                            f"Insufficient {required_currency} balance: need {min_crypto_amount:.6f}, have {actual_balance_float:.6f}"
                        )

                        if fallback_result.get('success', False):
                            return fallback_result
                        else:
                            return {
                                "success": False,
                                "error": f"Insufficient {required_currency} balance: need {min_crypto_amount:.6f}, have {actual_balance_float:.6f} (max sellable: {actual_balance_float * 0.95:.6f})",
                                "alternatives": await self._suggest_alternative_currencies(symbol, side, min_order_value_usd, exchange)
                            }
                # Use 80-90% of available crypto balance
                aggressive_percentage = 0.85
                max_usable = actual_balance_float * aggressive_percentage

            # Step 3: CRITICAL FIX - Calculate final amount using max_usable, not original amount
            try:
                if hasattr(client, 'validate_order_requirements'):
                    # Get current price for validation
                    current_price = 0.0
                    if hasattr(client, 'get_price'):
                        current_price = float(client.get_price(symbol))

                    if current_price > 0:
                        if side.lower() == 'buy':
                            # For BUY: Use max_usable USDT amount (not original amount)
                            final_amount = max_usable
                            # Convert to base quantity for validation
                            base_quantity = final_amount / current_price
                            validation = await client.validate_order_requirements(symbol, base_quantity, current_price, side)
                        else:
                            # For SELL: Use max_usable crypto amount (not original amount)
                            final_amount = max_usable
                            validation = await client.validate_order_requirements(symbol, final_amount, current_price, side)

                        if not validation.get('valid', False):
                            logger.error(f"❌ [ORDER-VALIDATION] {'; '.join(validation.get('errors', []))}")
                            # CRITICAL FIX: Try fallback strategy instead of just failing
                            fallback_result = await self.execute_fallback_trading_strategy(
                                symbol, side, Decimal(str(final_amount)), exchange,
                                f"Order validation failed: {'; '.join(validation.get('errors', []))}"
                            )
                            if fallback_result.get('success', False):
                                return fallback_result
                            else:
                                return {"success": False, "error": f"Order validation failed: {'; '.join(validation.get('errors', []))}"}

                        # Use validated quantity
                        if side.lower() == 'buy':
                            # Convert back to quote amount
                            final_amount = validation['adjusted_quantity'] * current_price
                        else:
                            final_amount = validation['adjusted_quantity']

                        logger.info(f"✅ [ORDER-VALIDATION] Using validated amount: {final_amount:.8f}")
                    else:
                        final_amount = max_usable
                else:
                    final_amount = max_usable
            except Exception as e:
                logger.warning(f"⚠️ [ORDER-VALIDATION] Validation error, using max_usable: {e}")
                final_amount = max_usable

            # Step 4: Final safety check
            if final_amount <= 0:
                return {"success": False, "error": "Final amount is zero or negative"}

            logger.info(f"🎯 [AGGRESSIVE-SIZING] Final order amount: {final_amount:.8f} {required_currency}")
            logger.info(f"🎯 [AGGRESSIVE-SIZING] Using {(final_amount/actual_balance_float)*100:.1f}% of available balance")

            # Step 5: Execute the order
            if hasattr(client, 'place_order'):
                result = await client.place_order(
                    symbol=symbol,
                    side=side,
                    amount=final_amount,
                    order_type=order_type
                )
            elif hasattr(client, 'execute_order'):
                result = await client.execute_order(
                    symbol=symbol,
                    side=side,
                    amount=final_amount,
                    order_type=order_type
                )
            else:
                return {"success": False, "error": f"No order execution method available on {exchange}"}

            # Update statistics
            self.order_stats['total_orders'] += 1

            if result.get('success', False) or 'error' not in result:
                self.order_stats['successful_orders'] += 1

                # Update balance cache (subtract used amount)
                if exchange in self.balance_cache:
                    if required_currency in self.balance_cache[exchange]['balances']:
                        self.balance_cache[exchange]['balances'][required_currency] -= final_amount
                        logger.info(f"📊 [BALANCE-UPDATE] Updated {required_currency} balance after order")

                logger.info("✅ [BALANCE-ORDER] Order executed successfully with aggressive sizing")
                return {
                    "success": True,
                    "order_result": result,
                    "original_amount": float(amount),
                    "executed_amount": final_amount,
                    "currency": required_currency,
                    "balance_used_percentage": (final_amount/actual_balance_float)*100,
                    "aggressive_sizing": True
                }
            else:
                self.order_stats['failed_orders'] += 1
                error_msg = result.get('error', 'Unknown execution error')
                logger.error(f"❌ [BALANCE-ORDER] Order execution failed: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "original_amount": float(amount),
                    "attempted_amount": final_amount,
                    "currency": required_currency
                }

        except Exception as e:
            self.order_stats['failed_orders'] += 1
            logger.error(f"❌ [BALANCE-ORDER] Error executing balance-aware order: {e}")
            return {"success": False, "error": str(e)}

    async def _update_balance_after_order(self, balance_check: BalanceCheck, used_amount: Decimal):
        """Update balance cache after order execution"""
        try:
            exchange = balance_check.exchange
            currency = balance_check.currency

            if exchange in self.balance_cache:
                balances = self.balance_cache[exchange]['balances']
                current_balance = Decimal(str(balances.get(currency, 0)))
                new_balance = max(Decimal('0'), current_balance - used_amount)

                balances[currency] = float(new_balance)
                self.balance_cache[exchange]['last_update'] = time.time()

                logger.info(f"💰 [BALANCE-UPDATE] {currency}: {current_balance:.6f} -> {new_balance:.6f}")

        except Exception as e:
            logger.error(f"❌ [BALANCE-UPDATE] Error updating balance: {e}")

    async def _suggest_alternative_currencies(self, symbol: str, side: str, min_value_usd: float, exchange: str) -> List[str]:
        """Suggest alternative currencies when primary currency has insufficient balance"""
        try:
            alternatives = []
            client = self.exchange_clients.get(exchange)
            if not client:
                return alternatives

            # Get all available balances
            try:
                if hasattr(client, 'get_all_balances'):
                    all_balances = client.get_all_balances()
                else:
                    return alternatives

                # Check each currency for sufficient balance
                for currency, balance in all_balances.items():
                    if currency == 'USDT' and float(balance) >= min_value_usd:
                        alternatives.append('USDT')
                    elif currency != 'USDT' and float(balance) > 0:
                        # Check if crypto holding value meets minimum
                        try:
                            price_symbol = f"{currency}USDT"
                            if hasattr(client, 'get_price'):
                                price = float(client.get_price(price_symbol))
                                if price > 0:
                                    value_usd = float(balance) * price
                                    if value_usd >= min_value_usd:
                                        alternatives.append(currency)
                        except:
                            continue

                logger.info(f"🔄 [ALTERNATIVES] Found {len(alternatives)} alternative currencies: {alternatives}")
                return alternatives[:3]  # Return top 3 alternatives

            except Exception as e:
                logger.error(f"❌ [ALTERNATIVES] Error getting balances: {e}")
                return alternatives

        except Exception as e:
            logger.error(f"❌ [ALTERNATIVES] Error suggesting alternatives: {e}")
            return []

    async def _get_real_time_balance_with_validation(self, currency: str, exchange: str) -> Decimal:
        """Get real-time balance with fail-fast validation"""
        try:
            client = self.exchange_clients.get(exchange)
            if not client:
                logger.error(f"❌ [REAL-TIME-BALANCE] Exchange {exchange} not available")
                return Decimal('0')

            # CRITICAL FIX: Always fetch fresh balance from exchange
            if hasattr(client, 'get_balance'):
                balance = await client.get_balance(currency)

                # CRITICAL FIX: Handle different balance return types
                try:
                    if balance is None:
                        balance_decimal = Decimal('0')
                    elif isinstance(balance, (int, float)):
                        balance_decimal = Decimal(str(balance))
                    elif isinstance(balance, Decimal):
                        balance_decimal = balance
                    elif isinstance(balance, str):
                        balance_decimal = Decimal(balance) if balance else Decimal('0')
                    else:
                        logger.warning(f"⚠️ [BALANCE-TYPE] Unexpected balance type: {type(balance)} = {balance}")
                        balance_decimal = Decimal(str(balance)) if balance else Decimal('0')
                except Exception as decimal_error:
                    logger.error(f"❌ [DECIMAL-CONVERSION] Error converting balance {balance} to Decimal: {decimal_error}")
                    balance_decimal = Decimal('0')

                # Update cache with fresh data
                if exchange not in self.balance_cache:
                    self.balance_cache[exchange] = {'balances': {}, 'last_update': time.time()}

                self.balance_cache[exchange]['balances'][currency] = float(balance_decimal)
                self.balance_cache[exchange]['last_update'] = time.time()

                logger.info(f"💰 [REAL-TIME-BALANCE] {currency} on {exchange}: {balance_decimal:.8f}")
                return balance_decimal
            else:
                logger.error(f"❌ [REAL-TIME-BALANCE] Client {exchange} does not support get_balance")
                return Decimal('0')

        except Exception as e:
            logger.error(f"❌ [REAL-TIME-BALANCE] Error getting {currency} balance from {exchange}: {e}")
            # CRITICAL FIX: Fail-fast behavior - return 0 instead of cached value
            return Decimal('0')

    async def execute_fallback_trading_strategy(self, original_symbol: str, original_side: str,
                                              original_amount: Decimal, exchange: str,
                                              failure_reason: str) -> Dict[str, Any]:
        """Execute fallback trading strategies when primary order fails"""
        try:
            logger.info(f"🔄 [FALLBACK] Executing fallback strategy for failed {original_side} {original_symbol}")
            logger.info(f"🔄 [FALLBACK] Failure reason: {failure_reason}")

            client = self.exchange_clients.get(exchange)
            if not client:
                return {"success": False, "error": f"Exchange {exchange} not available for fallback"}

            # Strategy 1: Currency switching for insufficient balance
            if "insufficient" in failure_reason.lower() or "balance" in failure_reason.lower():
                logger.info("🔄 [FALLBACK-STRATEGY-1] Attempting currency switching...")

                # Get alternative currencies with sufficient balance
                alternatives = await self._suggest_alternative_currencies(original_symbol, original_side, self.min_order_value, exchange)

                for alt_currency in alternatives:
                    try:
                        if alt_currency == 'USDT' and original_side.lower() == 'sell':
                            # Switch to buying with USDT instead of selling crypto
                            alt_symbol = original_symbol  # Keep same symbol
                            alt_side = 'buy'
                            alt_amount = Decimal(str(self.min_order_value))  # Use minimum order value

                            logger.info(f"🔄 [CURRENCY-SWITCH] Switching to BUY {alt_symbol} with USDT")

                        elif alt_currency != 'USDT' and original_side.lower() == 'buy':
                            # Switch to selling alternative crypto instead of buying
                            alt_symbol = f"{alt_currency}USDT"
                            alt_side = 'sell'

                            # Get balance and use 85% of it
                            alt_balance = await self._get_real_time_balance_with_validation(alt_currency, exchange)
                            alt_amount = alt_balance * Decimal('0.85')

                            logger.info(f"🔄 [CURRENCY-SWITCH] Switching to SELL {alt_symbol}")

                        else:
                            continue  # Skip this alternative

                        # Validate the alternative order
                        validation_result = await self.validate_order_balance(alt_symbol, alt_side, alt_amount, exchange)

                        if validation_result.status == BalanceStatus.SUFFICIENT:
                            # CRITICAL FIX: Execute order directly through client to avoid recursion
                            logger.info(f"🔄 [DIRECT-EXECUTION] Executing {alt_side} {alt_amount} {alt_symbol} directly")

                            try:
                                if hasattr(client, 'place_order'):
                                    # Execute directly through exchange client
                                    if alt_side.lower() == 'buy':
                                        result = client.place_order(alt_symbol, alt_side, float(alt_amount),
                                                                   order_type='market', is_quote_amount=True)
                                    else:
                                        result = client.place_order(alt_symbol, alt_side, float(alt_amount),
                                                                   order_type='market', is_quote_amount=False)

                                    # Check if order was successful
                                    if (result.get('retCode') == 0 or 'orderId' in result.get('result', {}) or
                                        'order_id' in result or result.get('status') == 'submitted'):

                                        order_id = (result.get('result', {}).get('orderId') or
                                                   result.get('order_id') or 'N/A')

                                        logger.info(f"✅ [FALLBACK-SUCCESS] Alternative order executed: {alt_side} {alt_amount} {alt_symbol}")
                                        logger.info(f"✅ [FALLBACK-SUCCESS] Order ID: {order_id}")

                                        return {
                                            "success": True,
                                            "fallback_strategy": "currency_switching",
                                            "original_order": f"{original_side} {original_amount} {original_symbol}",
                                            "executed_order": f"{alt_side} {alt_amount} {alt_symbol}",
                                            "order_id": order_id,
                                            "result": result
                                        }
                                    else:
                                        logger.warning(f"⚠️ [FALLBACK] Order execution failed: {result}")

                            except Exception as order_error:
                                logger.warning(f"⚠️ [FALLBACK] Direct order execution failed: {order_error}")
                                continue

                    except Exception as e:
                        logger.warning(f"⚠️ [FALLBACK] Alternative {alt_currency} failed: {e}")
                        continue

            # Strategy 2: USDT-based BUY when crypto insufficient
            logger.info("🔄 [FALLBACK-STRATEGY-2] Checking USDT balance for BUY orders...")
            try:
                usdt_balance = await self._get_real_time_balance_with_validation('USDT', exchange)
                logger.info(f"💰 [USDT-CHECK] Available USDT: ${float(usdt_balance):.2f}")

                if usdt_balance >= Decimal(str(self.min_order_value)):
                    # We have sufficient USDT, switch to BUY orders
                    logger.info(f"✅ [USDT-SWITCH] Sufficient USDT (${float(usdt_balance):.2f}) - switching to BUY")

                    # Use 80% of available USDT for aggressive trading
                    buy_amount = usdt_balance * Decimal('0.8')
                    buy_symbol = original_symbol  # Keep same symbol

                    logger.info(f"🔄 [USDT-BUY] Executing BUY ${float(buy_amount):.2f} of {buy_symbol}")

                    try:
                        if hasattr(client, 'place_order'):
                            result = client.place_order(buy_symbol, 'buy', float(buy_amount),
                                                       order_type='market', is_quote_amount=True)

                            if (result.get('retCode') == 0 or 'orderId' in result.get('result', {}) or
                                'order_id' in result or result.get('status') == 'submitted'):

                                order_id = (result.get('result', {}).get('orderId') or
                                           result.get('order_id') or 'N/A')

                                logger.info(f"✅ [USDT-BUY-SUCCESS] BUY order executed: ${float(buy_amount):.2f} of {buy_symbol}")
                                logger.info(f"✅ [USDT-BUY-SUCCESS] Order ID: {order_id}")

                                return {
                                    "success": True,
                                    "fallback_strategy": "usdt_buy_switch",
                                    "original_order": f"{original_side} {original_amount} {original_symbol}",
                                    "executed_order": f"buy ${float(buy_amount):.2f} {buy_symbol}",
                                    "order_id": order_id,
                                    "result": result
                                }
                            else:
                                logger.warning(f"⚠️ [USDT-BUY] Order execution failed: {result}")

                    except Exception as order_error:
                        logger.warning(f"⚠️ [USDT-BUY] Direct order execution failed: {order_error}")

            except Exception as e:
                logger.warning(f"⚠️ [USDT-CHECK] Error checking USDT balance: {e}")

            # Strategy 3: Minimum order adjustment
            if "minimum" in failure_reason.lower() or "value" in failure_reason.lower():
                logger.info("🔄 [FALLBACK-STRATEGY-2] Attempting minimum order adjustment...")

                try:
                    # Try to adjust to exactly meet minimum requirements
                    if original_side.lower() == 'buy':
                        # For BUY orders, use exactly the minimum USDT amount
                        adjusted_amount = Decimal(str(self.min_order_value))

                        # Check if we have enough USDT
                        usdt_balance = await self._get_real_time_balance_with_validation('USDT', exchange)
                        if usdt_balance >= adjusted_amount:
                            # CRITICAL FIX: Execute directly to avoid recursion
                            try:
                                if hasattr(client, 'place_order'):
                                    result = client.place_order(original_symbol, original_side, float(adjusted_amount),
                                                               order_type='market', is_quote_amount=True)

                                    if (result.get('retCode') == 0 or 'orderId' in result.get('result', {}) or
                                        'order_id' in result or result.get('status') == 'submitted'):

                                        order_id = (result.get('result', {}).get('orderId') or
                                                   result.get('order_id') or 'N/A')

                                        logger.info(f"✅ [FALLBACK-SUCCESS] Minimum-adjusted order executed: {original_side} {adjusted_amount} {original_symbol}")
                                        logger.info(f"✅ [FALLBACK-SUCCESS] Order ID: {order_id}")

                                        return {
                                            "success": True,
                                            "fallback_strategy": "minimum_adjustment",
                                            "original_amount": float(original_amount),
                                            "adjusted_amount": float(adjusted_amount),
                                            "order_id": order_id,
                                            "result": result
                                        }
                                    else:
                                        logger.warning(f"⚠️ [MINIMUM-ADJUST] Order execution failed: {result}")

                            except Exception as order_error:
                                logger.warning(f"⚠️ [MINIMUM-ADJUST] Direct order execution failed: {order_error}")

                except Exception as e:
                    logger.warning(f"⚠️ [FALLBACK] Minimum adjustment failed: {e}")

            # Strategy 3: Wait and retry with fresh balance
            logger.info("🔄 [FALLBACK-STRATEGY-3] Attempting wait-and-retry...")
            try:
                # Wait briefly for any pending transactions to settle
                await asyncio.sleep(2)

                # Refresh balance and try again with conservative sizing
                if original_side.lower() == 'buy':
                    fresh_balance = await self._get_real_time_balance_with_validation('USDT', exchange)
                    if fresh_balance >= Decimal(str(self.min_order_value)):
                        conservative_amount = fresh_balance * Decimal('0.5')  # Use only 50%
                        if conservative_amount >= Decimal(str(self.min_order_value)):
                            result = await self.execute_balance_aware_order(original_symbol, original_side, conservative_amount, exchange)
                            if result.get('success', False):
                                logger.info(f"✅ [FALLBACK-SUCCESS] Conservative retry executed: {original_side} {conservative_amount} {original_symbol}")
                                return {
                                    "success": True,
                                    "fallback_strategy": "conservative_retry",
                                    "conservative_amount": float(conservative_amount),
                                    "result": result
                                }

            except Exception as e:
                logger.warning(f"⚠️ [FALLBACK] Conservative retry failed: {e}")

            # All fallback strategies failed
            logger.error("❌ [FALLBACK] All fallback strategies failed")
            return {
                "success": False,
                "error": "All fallback strategies failed",
                "original_failure": failure_reason,
                "strategies_attempted": ["currency_switching", "minimum_adjustment", "conservative_retry"]
            }

        except Exception as e:
            logger.error(f"❌ [FALLBACK] Error in fallback strategy execution: {e}")
            return {"success": False, "error": f"Fallback strategy error: {str(e)}"}

    async def get_balance_status_report(self) -> Dict[str, Any]:
        """Generate comprehensive balance status report"""
        try:
            report = {
                'timestamp': time.time(),
                'exchanges': len(self.exchange_clients),
                'total_currencies': 0,
                'balance_by_exchange': {},
                'critical_balances': [],
                'order_statistics': self.order_stats.copy(),
                'recommendations': []
            }

            total_currencies = set()
            critical_balances = []

            for exchange_name, cache_data in self.balance_cache.items():
                balances = cache_data['balances']
                exchange_report = {
                    'currencies': len(balances),
                    'last_update': cache_data['last_update'],
                    'total_value_usd': 0.0,
                    'balances': {}
                }

                for currency, balance in balances.items():
                    if balance > 0:
                        total_currencies.add(currency)
                        usd_value = await self._get_usd_value(currency, balance, exchange_name)
                        exchange_report['total_value_usd'] += usd_value
                        exchange_report['balances'][currency] = {
                            'amount': balance,
                            'usd_value': usd_value
                        }

                        # Check for critical balances
                        if usd_value < self.min_order_value:
                            critical_balances.append({
                                'currency': currency,
                                'exchange': exchange_name,
                                'balance': balance,
                                'usd_value': usd_value
                            })

                report['balance_by_exchange'][exchange_name] = exchange_report

            report['total_currencies'] = len(total_currencies)
            report['critical_balances'] = critical_balances

            # Generate recommendations
            if critical_balances:
                report['recommendations'].append(f"Monitor {len(critical_balances)} currencies with low balances")

            if self.order_stats['balance_failures'] > 0:
                failure_rate = self.order_stats['balance_failures'] / max(1, self.order_stats['total_orders'])
                if failure_rate > 0.1:  # 10% failure rate
                    report['recommendations'].append("High balance failure rate - consider increasing minimum balances")

            if not report['recommendations']:
                report['recommendations'].append("Balance management is operating normally")

            return report

        except Exception as e:
            logger.error(f"❌ [BALANCE-REPORT] Error generating report: {e}")
            return {'error': str(e)}
