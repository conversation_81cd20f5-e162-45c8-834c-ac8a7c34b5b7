#!/usr/bin/env python3
"""
OFFICIAL COINBASE CDP SDK TEST
==============================

Test using the official Coinbase CDP SDK instead of coinbase-advanced-py.

Author: AutoGPT Trader
Date: June 2025
"""

import os
import json
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("cdp_sdk_test")

def test_official_cdp_sdk():
    """Test using the official Coinbase CDP SDK"""
    try:
        # Load credentials
        from credential_decryptor_fixed import setup_credentials
        
        logger.info("🔐 [CDP] Loading encrypted credentials...")
        success = setup_credentials()
        
        if not success:
            logger.error("❌ [CDP] Failed to load credentials")
            return False
        
        api_key_name = os.getenv('COINBASE_API_KEY_NAME')
        private_key = os.getenv('COINBASE_PRIVATE_KEY')
        
        logger.info(f"✅ [CDP] API Key: {api_key_name}")
        logger.info(f"✅ [CDP] Private Key Length: {len(private_key)} chars")
        
        # Method 1: Try official CDP SDK
        logger.info("🧪 [TEST] Method 1: Official Coinbase CDP SDK")
        try:
            # Install CDP SDK if not available
            try:
                import cdp
                logger.info("✅ [CDP] CDP SDK already installed")
            except ImportError:
                logger.info("📦 [CDP] Installing CDP SDK...")
                import subprocess
                subprocess.check_call(["pip", "install", "cdp-sdk"])
                import cdp
                logger.info("✅ [CDP] CDP SDK installed successfully")
            
            # Configure CDP with credentials
            logger.info("🔧 [CDP] Configuring CDP SDK...")
            
            # Extract org ID and key ID from the API key name
            parts = api_key_name.split('/')
            if len(parts) >= 4:
                org_id = parts[1]
                key_id = parts[3]
                logger.info(f"📋 [CDP] Organization ID: {org_id}")
                logger.info(f"📋 [CDP] Key ID: {key_id}")
            else:
                logger.error("❌ [CDP] Invalid API key format")
                return False
            
            # Configure CDP
            cdp.Cdp.configure(api_key_name, private_key)
            logger.info("✅ [CDP] CDP SDK configured successfully")
            
            # Test basic functionality
            logger.info("🧪 [TEST] Testing CDP SDK functionality...")
            
            # List wallets (this should work with proper authentication)
            try:
                wallets = cdp.Wallet.list()
                logger.info(f"✅ [CDP] Successfully listed wallets: {len(wallets)} found")
                return True
                
            except Exception as e:
                logger.error(f"❌ [CDP] Wallet listing failed: {e}")
                
                # Try creating a wallet instead
                try:
                    logger.info("🔧 [CDP] Trying to create a test wallet...")
                    wallet = cdp.Wallet.create()
                    logger.info(f"✅ [CDP] Successfully created wallet: {wallet.id}")
                    return True
                    
                except Exception as e2:
                    logger.error(f"❌ [CDP] Wallet creation failed: {e2}")
                    return False
                
        except ImportError as e:
            logger.error(f"❌ [CDP] CDP SDK not available: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ [CDP] CDP SDK test failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ [CDP] Test failed: {e}")
        return False

def test_alternative_authentication():
    """Test alternative authentication methods"""
    try:
        # Load credentials
        from credential_decryptor_fixed import setup_credentials
        setup_credentials()
        
        api_key_name = os.getenv('COINBASE_API_KEY_NAME')
        private_key = os.getenv('COINBASE_PRIVATE_KEY')
        
        # Method 2: Try direct REST API calls with proper JWT
        logger.info("🧪 [TEST] Method 2: Direct REST API with JWT")
        try:
            import requests
            import jwt
            import time
            from urllib.parse import urlencode
            
            # Create JWT token
            def create_jwt_token():
                uri = "GET api.coinbase.com/api/v3/brokerage/accounts"
                
                payload = {
                    'iss': "cdp",
                    'nbf': int(time.time()),
                    'exp': int(time.time()) + 120,
                    'sub': api_key_name,
                    'uri': uri,
                }
                
                token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': api_key_name})
                return token
            
            # Make authenticated request
            jwt_token = create_jwt_token()
            
            headers = {
                'Authorization': f'Bearer {jwt_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get('https://api.coinbase.com/api/v3/brokerage/accounts', headers=headers)
            
            if response.status_code == 200:
                logger.info("✅ [JWT] Direct JWT authentication successful")
                accounts = response.json()
                logger.info(f"✅ [JWT] Found {len(accounts.get('accounts', []))} accounts")
                return True
            else:
                logger.error(f"❌ [JWT] Direct JWT failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ [JWT] JWT method failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ [ALT] Alternative test failed: {e}")
        return False

def main():
    """Main function"""
    logger.info("🔍 [COINBASE] Testing official CDP SDK and alternative methods...")
    
    # Test 1: Official CDP SDK
    logger.info("\n" + "="*50)
    logger.info("TEST 1: Official Coinbase CDP SDK")
    logger.info("="*50)
    cdp_ok = test_official_cdp_sdk()
    
    # Test 2: Alternative authentication
    logger.info("\n" + "="*50)
    logger.info("TEST 2: Alternative JWT Authentication")
    logger.info("="*50)
    jwt_ok = test_alternative_authentication()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 [SUMMARY] Authentication Test Results:")
    logger.info(f"🔧 Official CDP SDK: {'✅ OK' if cdp_ok else '❌ ISSUE'}")
    logger.info(f"🔐 Direct JWT Auth: {'✅ OK' if jwt_ok else '❌ ISSUE'}")
    logger.info("="*60)
    
    if cdp_ok or jwt_ok:
        print("✅ Found working authentication method!")
        print("🚀 Ready to integrate with trading system")
        return True
    else:
        print("❌ All authentication methods failed")
        print("🔧 May need to regenerate API key or check Coinbase status")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
