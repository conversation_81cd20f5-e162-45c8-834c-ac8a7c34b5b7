#!/usr/bin/env python3
"""
DEBUG COINBASE AUTHENTICATION
==============================

Debug script to test different approaches to Coinbase authentication.
Test various methods to identify the exact issue.

Author: AutoGPT Trader
Date: June 2025
"""

import os
import time
import requests
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("coinbase_debug")

def test_ip_and_timing():
    """Test current IP and timing issues"""
    try:
        # Check current IP
        logger.info("🌐 [IP-CHECK] Checking current public IP...")
        response = requests.get('https://api.ipify.org?format=json', timeout=10)
        current_ip = response.json().get('ip', 'Unknown')
        logger.info(f"🌐 [IP] Current public IP: {current_ip}")
        
        # Check if it matches expected
        expected_ip = "************"
        if current_ip == expected_ip:
            logger.info("✅ [IP-MATCH] Current IP matches Coinbase restriction")
        else:
            logger.warning(f"⚠️ [IP-MISMATCH] Current IP ({current_ip}) ≠ Expected IP ({expected_ip})")
        
        return current_ip
        
    except Exception as e:
        logger.error(f"❌ [IP-CHECK] Failed: {e}")
        return "Unknown"

def test_coinbase_with_different_methods():
    """Test Coinbase API with different authentication methods"""
    try:
        # Load credentials
        from credential_decryptor_fixed import setup_credentials
        setup_credentials()
        
        api_key = os.getenv('COINBASE_API_KEY_NAME')
        private_key = os.getenv('COINBASE_PRIVATE_KEY')
        
        logger.info(f"🔑 [CREDS] API Key: {api_key}")
        logger.info(f"🔑 [CREDS] Private Key Length: {len(private_key)} chars")
        
        # Method 1: Test with requests and manual JWT
        logger.info("🧪 [TEST-1] Testing with manual JWT creation...")
        try:
            import jwt
            
            # Create JWT token with different parameters
            payload = {
                'iss': "cdp",
                'nbf': int(time.time()),
                'exp': int(time.time()) + 120,
                'sub': api_key,
                'uri': "GET api.coinbase.com/api/v3/brokerage/products",
            }
            
            token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': api_key})
            
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json',
                'User-Agent': 'AutoGPT-Trader/1.0'
            }
            
            # Test different endpoints
            endpoints = [
                '/api/v3/brokerage/products',
                '/api/v3/brokerage/accounts',
                '/v2/accounts'  # Try older API version
            ]
            
            for endpoint in endpoints:
                logger.info(f"🧪 [TEST] Trying endpoint: {endpoint}")
                try:
                    response = requests.get(f'https://api.coinbase.com{endpoint}', headers=headers, timeout=30)
                    logger.info(f"📊 [RESPONSE] Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        logger.info("✅ [SUCCESS] Coinbase API working!")
                        data = response.json()
                        logger.info(f"📊 [DATA] Response keys: {list(data.keys())}")
                        return True
                    else:
                        logger.error(f"❌ [ERROR] {response.status_code}: {response.text[:200]}")
                        
                except Exception as e:
                    logger.error(f"❌ [REQUEST] Failed for {endpoint}: {e}")
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [JWT] JWT method failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST] Authentication test failed: {e}")
        return False

def test_propagation_delay():
    """Test if this is a propagation delay issue"""
    logger.info("⏰ [TIMING] Testing if this is a propagation delay...")
    logger.info("⏰ [INFO] Coinbase API changes can take 2-5 minutes to propagate")
    logger.info("⏰ [INFO] Testing every 30 seconds for 3 minutes...")
    
    for attempt in range(6):  # 6 attempts = 3 minutes
        logger.info(f"🔄 [ATTEMPT] {attempt + 1}/6 - Testing connection...")
        
        success = test_coinbase_with_different_methods()
        if success:
            logger.info("✅ [SUCCESS] Connection working - propagation complete!")
            return True
        
        if attempt < 5:  # Don't wait after last attempt
            logger.info("⏰ [WAIT] Waiting 30 seconds for propagation...")
            time.sleep(30)
    
    logger.error("❌ [TIMEOUT] Propagation timeout - changes may not have taken effect")
    return False

def main():
    """Main function"""
    logger.info("🔍 [DEBUG] Coinbase Authentication Debug Script")
    
    # Step 1: Check IP
    logger.info("\n" + "="*50)
    logger.info("STEP 1: IP Address Check")
    logger.info("="*50)
    current_ip = test_ip_and_timing()
    
    # Step 2: Test immediate connection
    logger.info("\n" + "="*50)
    logger.info("STEP 2: Immediate Connection Test")
    logger.info("="*50)
    immediate_success = test_coinbase_with_different_methods()
    
    if immediate_success:
        print("✅ Coinbase API working immediately!")
        return True
    
    # Step 3: Test with propagation delay
    logger.info("\n" + "="*50)
    logger.info("STEP 3: Propagation Delay Test")
    logger.info("="*50)
    delayed_success = test_propagation_delay()
    
    if delayed_success:
        print("✅ Coinbase API working after propagation delay!")
        return True
    
    # Step 4: Recommendations
    logger.info("\n" + "="*60)
    logger.info("📋 [RECOMMENDATIONS] Troubleshooting Steps:")
    logger.info("="*60)
    logger.info("1. 🔧 Try setting IP restriction to ONLY '0.0.0.0/0' (remove specific IP)")
    logger.info("2. ⏰ Wait 5-10 minutes for full propagation")
    logger.info("3. 🔄 Try regenerating the API key completely")
    logger.info("4. 📞 Contact Coinbase support if issue persists")
    logger.info(f"5. 🌐 Current IP: {current_ip} - verify this matches your restriction")
    
    return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
