#!/usr/bin/env python3
"""
VPN THEORY TEST
===============

Test if VPN is causing the Coinbase API authentication issue.

Author: AutoGPT Trader
Date: June 2025
"""

import requests
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("vpn_test")

def check_current_ip():
    """Check current public IP address"""
    try:
        # Method 1: ipify.org
        response = requests.get('https://api.ipify.org?format=json', timeout=10)
        ip1 = response.json().get('ip', 'Unknown')
        
        # Method 2: httpbin.org
        response2 = requests.get('https://httpbin.org/ip', timeout=10)
        ip2 = response2.json().get('origin', 'Unknown')
        
        logger.info(f"🌐 [IP-CHECK] Method 1 (ipify): {ip1}")
        logger.info(f"🌐 [IP-CHECK] Method 2 (httpbin): {ip2}")
        
        # Expected IP from Coinbase restriction
        expected_ip = "************"
        
        if ip1 == expected_ip:
            logger.info("✅ [IP-MATCH] Current IP matches Coinbase restriction")
            return True, ip1
        else:
            logger.warning(f"⚠️ [IP-MISMATCH] Current IP ({ip1}) ≠ Expected IP ({expected_ip})")
            logger.warning("🔍 [DIAGNOSIS] This confirms VPN is causing the 401 error!")
            return False, ip1
            
    except Exception as e:
        logger.error(f"❌ [IP-CHECK] Failed to check IP: {e}")
        return False, "Unknown"

def test_coinbase_with_current_ip():
    """Test Coinbase API with current IP"""
    try:
        # Load credentials
        from credential_decryptor_fixed import setup_credentials
        
        logger.info("🔐 [TEST] Loading credentials...")
        setup_credentials()
        
        # Import the direct API client
        from coinbase_direct_api import CoinbaseDirectClient
        import os
        
        api_key = os.getenv('COINBASE_API_KEY_NAME')
        private_key = os.getenv('COINBASE_PRIVATE_KEY')
        
        # Create client and test
        client = CoinbaseDirectClient(api_key, private_key)
        
        logger.info("🧪 [TEST] Testing Coinbase API with current IP...")
        try:
            products = client.get_products()
            logger.info("✅ [SUCCESS] Coinbase API working with current IP!")
            return True
        except Exception as e:
            if "401" in str(e):
                logger.error("❌ [401] Unauthorized - IP mismatch confirmed!")
                logger.error("🔧 [SOLUTION] Disable VPN or update IP restriction")
            else:
                logger.error(f"❌ [ERROR] Other error: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST] Test failed: {e}")
        return False

def main():
    """Main function"""
    logger.info("🔍 [VPN-TEST] Testing VPN theory for Coinbase API issue...")
    
    # Check current IP
    logger.info("\n" + "="*50)
    logger.info("STEP 1: IP Address Check")
    logger.info("="*50)
    ip_match, current_ip = check_current_ip()
    
    # Test Coinbase API
    logger.info("\n" + "="*50)
    logger.info("STEP 2: Coinbase API Test")
    logger.info("="*50)
    api_works = test_coinbase_with_current_ip()
    
    # Analysis
    logger.info("\n" + "="*60)
    logger.info("📊 [ANALYSIS] VPN Theory Test Results:")
    logger.info(f"🌐 Current IP: {current_ip}")
    logger.info(f"🎯 Expected IP: ************")
    logger.info(f"🔍 IP Match: {'✅ YES' if ip_match else '❌ NO'}")
    logger.info(f"🔐 API Works: {'✅ YES' if api_works else '❌ NO'}")
    logger.info("="*60)
    
    if not ip_match and not api_works:
        print("🎯 VPN THEORY CONFIRMED!")
        print("🔧 SOLUTIONS:")
        print("   1. Disable VPN temporarily for trading")
        print("   2. Update Coinbase API restriction to 0.0.0.0/0")
        print(f"   3. Add your VPN IP ({current_ip}) to the restriction")
        return True
    elif ip_match and api_works:
        print("✅ No VPN issue - API working correctly!")
        return True
    else:
        print("🤔 Mixed results - may need further investigation")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
