"""
Advanced Data Utilization System
Professional-grade algorithms for comprehensive market data analysis and decision making
"""

import logging
import asyncio
import time
import statistics
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timezone
import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)

class AdvancedDataUtilizationEngine:
    """
    Professional-grade data utilization engine that processes multiple data sources
    for optimal trading decisions with time-weighted analysis
    """
    
    def __init__(self):
        self.data_sources = {
            'price_feeds': True,
            'order_books': True,
            'sentiment_analysis': True,
            'technical_indicators': True,
            'volume_analysis': True,
            'correlation_data': True,
            'news_sentiment': True,
            'social_media': True,
            'market_microstructure': True
        }
        
        # Data processing weights
        self.source_weights = {
            'price_feeds': 0.25,
            'technical_indicators': 0.20,
            'sentiment_analysis': 0.15,
            'volume_analysis': 0.15,
            'order_books': 0.10,
            'correlation_data': 0.08,
            'news_sentiment': 0.04,
            'social_media': 0.03
        }
        
        # Time-weighted factors
        self.time_decay_factors = {
            'immediate': 1.0,      # Current data
            'recent': 0.8,         # Last 5 minutes
            'short_term': 0.6,     # Last 30 minutes
            'medium_term': 0.4,    # Last 2 hours
            'long_term': 0.2       # Last 24 hours
        }
        
        # Data quality thresholds
        self.quality_thresholds = {
            'minimum_data_points': 10,
            'maximum_age_seconds': 300,  # 5 minutes
            'minimum_confidence': 0.6,
            'minimum_sources': 3
        }
        
        # Cache for processed data
        self.processed_data_cache = {}
        self.analysis_history = {}
        
    async def process_comprehensive_market_data(self, symbol: str, raw_data: Dict) -> Dict:
        """
        Process comprehensive market data using advanced algorithms
        """
        try:
            logger.info(f"Processing comprehensive market data for {symbol}")
            
            # 1. Data quality assessment
            quality_assessment = await self._assess_data_quality(symbol, raw_data)
            
            if quality_assessment['overall_quality'] < self.quality_thresholds['minimum_confidence']:
                logger.warning(f"Data quality below threshold for {symbol}: {quality_assessment['overall_quality']:.2f}")
                return self._get_fallback_analysis(symbol)
            
            # 2. Time-weighted data processing
            time_weighted_data = await self._apply_time_weighting(symbol, raw_data)
            
            # 3. Multi-source data fusion
            fused_data = await self._fuse_multi_source_data(symbol, time_weighted_data)
            
            # 4. Advanced pattern recognition
            patterns = await self._recognize_market_patterns(symbol, fused_data)
            
            # 5. Predictive analysis
            predictions = await self._generate_predictive_signals(symbol, fused_data, patterns)
            
            # 6. Risk-adjusted scoring
            risk_adjusted_scores = await self._calculate_risk_adjusted_scores(symbol, predictions)
            
            # 7. Final decision synthesis
            final_analysis = await self._synthesize_final_decision(
                symbol, quality_assessment, fused_data, patterns, predictions, risk_adjusted_scores
            )
            
            # Cache the results
            self._cache_analysis_results(symbol, final_analysis)
            
            return final_analysis
            
        except Exception as e:
            logger.error(f"Error processing comprehensive market data for {symbol}: {e}")
            return self._get_fallback_analysis(symbol)
    
    async def _assess_data_quality(self, symbol: str, raw_data: Dict) -> Dict:
        """Assess the quality of incoming data"""
        try:
            quality_scores = {}
            
            # Assess each data source
            for source_name, weight in self.source_weights.items():
                if source_name in raw_data:
                    source_data = raw_data[source_name]
                    quality_score = self._calculate_source_quality(source_name, source_data)
                    quality_scores[source_name] = {
                        'quality': quality_score,
                        'weight': weight,
                        'weighted_quality': quality_score * weight
                    }
            
            # Calculate overall quality
            total_weighted_quality = sum(item['weighted_quality'] for item in quality_scores.values())
            total_weight = sum(item['weight'] for item in quality_scores.values())
            overall_quality = total_weighted_quality / total_weight if total_weight > 0 else 0.0
            
            return {
                'overall_quality': overall_quality,
                'source_qualities': quality_scores,
                'data_freshness': self._calculate_data_freshness(raw_data),
                'completeness': len(quality_scores) / len(self.source_weights)
            }
            
        except Exception as e:
            logger.error(f"Error assessing data quality: {e}")
            return {'overall_quality': 0.5, 'source_qualities': {}, 'data_freshness': 0.5, 'completeness': 0.5}
    
    def _calculate_source_quality(self, source_name: str, source_data: Any) -> float:
        """Calculate quality score for a specific data source"""
        try:
            if not source_data:
                return 0.0
            
            quality_score = 0.5  # Base score
            
            # Source-specific quality assessment
            if source_name == 'price_feeds':
                if isinstance(source_data, dict) and 'price_history' in source_data:
                    price_history = source_data['price_history']
                    if len(price_history) >= self.quality_thresholds['minimum_data_points']:
                        quality_score = 0.9
                    elif len(price_history) >= 5:
                        quality_score = 0.7
                        
            elif source_name == 'technical_indicators':
                if isinstance(source_data, dict):
                    indicator_count = len([k for k, v in source_data.items() if v is not None])
                    quality_score = min(0.9, 0.3 + (indicator_count * 0.1))
                    
            elif source_name == 'sentiment_analysis':
                if isinstance(source_data, dict) and 'confidence' in source_data:
                    confidence = source_data.get('confidence', 0.5)
                    quality_score = min(0.9, confidence)
                    
            elif source_name == 'volume_analysis':
                if isinstance(source_data, list) and len(source_data) >= 5:
                    quality_score = 0.8
                    
            elif source_name == 'order_books':
                if isinstance(source_data, dict):
                    bids = source_data.get('bids', [])
                    asks = source_data.get('asks', [])
                    if len(bids) >= 5 and len(asks) >= 5:
                        quality_score = 0.9
            
            return quality_score
            
        except Exception as e:
            logger.error(f"Error calculating source quality for {source_name}: {e}")
            return 0.5
    
    def _calculate_data_freshness(self, raw_data: Dict) -> float:
        """Calculate how fresh the data is"""
        try:
            current_time = time.time()
            freshness_scores = []
            
            for source_name, source_data in raw_data.items():
                if isinstance(source_data, dict) and 'timestamp' in source_data:
                    data_timestamp = source_data['timestamp']
                    if isinstance(data_timestamp, str):
                        # Parse ISO timestamp
                        try:
                            dt = datetime.fromisoformat(data_timestamp.replace('Z', '+00:00'))
                            data_timestamp = dt.timestamp()
                        except:
                            continue
                    
                    age_seconds = current_time - data_timestamp
                    freshness = max(0.0, 1.0 - (age_seconds / self.quality_thresholds['maximum_age_seconds']))
                    freshness_scores.append(freshness)
            
            return statistics.mean(freshness_scores) if freshness_scores else 0.5
            
        except Exception as e:
            logger.error(f"Error calculating data freshness: {e}")
            return 0.5
    
    async def _apply_time_weighting(self, symbol: str, raw_data: Dict) -> Dict:
        """Apply time-weighted factors to data"""
        try:
            weighted_data = {}
            current_time = time.time()
            
            for source_name, source_data in raw_data.items():
                if not source_data:
                    continue
                
                # Determine time category and apply weighting
                time_category = self._categorize_data_age(source_data, current_time)
                time_weight = self.time_decay_factors.get(time_category, 0.5)
                
                # Apply time weighting to numerical data
                weighted_source_data = self._apply_time_weight_to_source(source_data, time_weight)
                
                weighted_data[source_name] = {
                    'data': weighted_source_data,
                    'time_weight': time_weight,
                    'time_category': time_category
                }
            
            return weighted_data
            
        except Exception as e:
            logger.error(f"Error applying time weighting: {e}")
            return raw_data
    
    def _categorize_data_age(self, source_data: Any, current_time: float) -> str:
        """Categorize data based on its age"""
        try:
            if isinstance(source_data, dict) and 'timestamp' in source_data:
                data_timestamp = source_data['timestamp']
                if isinstance(data_timestamp, str):
                    try:
                        dt = datetime.fromisoformat(data_timestamp.replace('Z', '+00:00'))
                        data_timestamp = dt.timestamp()
                    except:
                        return 'medium_term'
                
                age_seconds = current_time - data_timestamp
                
                if age_seconds <= 60:  # 1 minute
                    return 'immediate'
                elif age_seconds <= 300:  # 5 minutes
                    return 'recent'
                elif age_seconds <= 1800:  # 30 minutes
                    return 'short_term'
                elif age_seconds <= 7200:  # 2 hours
                    return 'medium_term'
                else:
                    return 'long_term'
            
            return 'medium_term'  # Default
            
        except Exception as e:
            logger.error(f"Error categorizing data age: {e}")
            return 'medium_term'
    
    def _apply_time_weight_to_source(self, source_data: Any, time_weight: float) -> Any:
        """Apply time weight to source data"""
        try:
            if isinstance(source_data, dict):
                weighted_data = {}
                for key, value in source_data.items():
                    if isinstance(value, (int, float)) and key not in ['timestamp']:
                        # Apply time weighting to numerical values
                        weighted_data[key] = value * time_weight
                    else:
                        weighted_data[key] = value
                return weighted_data
            elif isinstance(source_data, list):
                # For lists, apply weighting to numerical elements
                return [item * time_weight if isinstance(item, (int, float)) else item for item in source_data]
            else:
                return source_data
                
        except Exception as e:
            logger.error(f"Error applying time weight to source: {e}")
            return source_data

    async def _fuse_multi_source_data(self, symbol: str, weighted_data: Dict) -> Dict:
        """Fuse data from multiple sources using advanced algorithms"""
        try:
            fused_data = {
                'price_signals': {},
                'sentiment_signals': {},
                'technical_signals': {},
                'volume_signals': {},
                'liquidity_signals': {},
                'composite_score': 0.0,
                'confidence': 0.0
            }

            # Fuse price-related signals
            price_sources = ['price_feeds', 'technical_indicators']
            fused_data['price_signals'] = self._fuse_price_signals(weighted_data, price_sources)

            # Fuse sentiment signals
            sentiment_sources = ['sentiment_analysis', 'news_sentiment', 'social_media']
            fused_data['sentiment_signals'] = self._fuse_sentiment_signals(weighted_data, sentiment_sources)

            # Fuse volume signals
            volume_sources = ['volume_analysis', 'order_books']
            fused_data['volume_signals'] = self._fuse_volume_signals(weighted_data, volume_sources)

            # Calculate composite score
            fused_data['composite_score'] = self._calculate_composite_score(fused_data)
            fused_data['confidence'] = self._calculate_fusion_confidence(weighted_data)

            return fused_data

        except Exception as e:
            logger.error(f"Error fusing multi-source data: {e}")
            return {'composite_score': 0.5, 'confidence': 0.5}

    def _fuse_price_signals(self, weighted_data: Dict, sources: List[str]) -> Dict:
        """Fuse price-related signals"""
        try:
            price_signals = {
                'trend_strength': 0.5,
                'momentum': 0.5,
                'volatility': 0.5,
                'support_resistance': 0.5
            }

            signal_values = []
            weights = []

            for source in sources:
                if source in weighted_data:
                    source_data = weighted_data[source]['data']
                    time_weight = weighted_data[source]['time_weight']

                    if source == 'price_feeds' and isinstance(source_data, dict):
                        if 'price_history' in source_data:
                            price_history = source_data['price_history']
                            if len(price_history) >= 5:
                                # Calculate trend from price history
                                prices = [float(p) for p in price_history[-10:]]
                                trend = self._calculate_trend_strength(prices)
                                signal_values.append(trend)
                                weights.append(time_weight * 0.6)

                    elif source == 'technical_indicators' and isinstance(source_data, dict):
                        # Process technical indicators
                        tech_signal = self._process_technical_indicators(source_data)
                        signal_values.append(tech_signal)
                        weights.append(time_weight * 0.4)

            # Calculate weighted average
            if signal_values and weights:
                weighted_sum = sum(s * w for s, w in zip(signal_values, weights))
                total_weight = sum(weights)
                price_signals['trend_strength'] = weighted_sum / total_weight if total_weight > 0 else 0.5

            return price_signals

        except Exception as e:
            logger.error(f"Error fusing price signals: {e}")
            return {'trend_strength': 0.5, 'momentum': 0.5, 'volatility': 0.5, 'support_resistance': 0.5}

    def _calculate_trend_strength(self, prices: List[float]) -> float:
        """Calculate trend strength from price data"""
        try:
            if len(prices) < 3:
                return 0.5

            # Linear regression for trend
            x = list(range(len(prices)))
            n = len(prices)

            sum_x = sum(x)
            sum_y = sum(prices)
            sum_xy = sum(x[i] * prices[i] for i in range(n))
            sum_x2 = sum(xi * xi for xi in x)

            if n * sum_x2 - sum_x * sum_x == 0:
                return 0.5

            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)

            # Calculate R-squared for trend strength
            mean_y = sum_y / n
            ss_tot = sum((prices[i] - mean_y) ** 2 for i in range(n))
            predicted = [slope * x[i] + (sum_y - slope * sum_x) / n for i in range(n)]
            ss_res = sum((prices[i] - predicted[i]) ** 2 for i in range(n))

            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

            # Convert to 0-1 scale with direction
            trend_strength = 0.5 + (slope / abs(slope) if slope != 0 else 0) * r_squared * 0.5
            return max(0.0, min(1.0, trend_strength))

        except Exception as e:
            logger.error(f"Error calculating trend strength: {e}")
            return 0.5

    def _process_technical_indicators(self, tech_data: Dict) -> float:
        """Process technical indicators into a single signal"""
        try:
            signals = []

            # RSI signal
            if 'rsi' in tech_data:
                rsi = tech_data['rsi']
                if rsi < 30:
                    signals.append(0.8)  # Oversold - bullish
                elif rsi > 70:
                    signals.append(0.2)  # Overbought - bearish
                else:
                    signals.append(0.5)  # Neutral

            # MACD signal
            if 'macd' in tech_data:
                macd_data = tech_data['macd']
                if isinstance(macd_data, dict):
                    macd_line = macd_data.get('macd', 0)
                    signal_line = macd_data.get('signal', 0)
                    if macd_line > signal_line:
                        signals.append(0.7)  # Bullish
                    else:
                        signals.append(0.3)  # Bearish

            # Moving average signals
            if 'sma_20' in tech_data and 'sma_50' in tech_data:
                sma_20 = tech_data['sma_20']
                sma_50 = tech_data['sma_50']
                if sma_20 > sma_50:
                    signals.append(0.7)  # Bullish
                else:
                    signals.append(0.3)  # Bearish

            return statistics.mean(signals) if signals else 0.5

        except Exception as e:
            logger.error(f"Error processing technical indicators: {e}")
            return 0.5

    def _fuse_sentiment_signals(self, weighted_data: Dict, sources: List[str]) -> Dict:
        """Fuse sentiment-related signals"""
        try:
            sentiment_signals = {
                'overall_sentiment': 0.5,
                'sentiment_strength': 0.5,
                'sentiment_trend': 'neutral'
            }

            sentiment_values = []
            weights = []

            for source in sources:
                if source in weighted_data:
                    source_data = weighted_data[source]['data']
                    time_weight = weighted_data[source]['time_weight']

                    if isinstance(source_data, dict):
                        if 'aggregated_sentiment' in source_data:
                            sentiment = source_data['aggregated_sentiment']
                            # Convert from -1,1 to 0,1 scale
                            normalized_sentiment = (sentiment + 1) / 2
                            sentiment_values.append(normalized_sentiment)
                            weights.append(time_weight)
                        elif 'sentiment_score' in source_data:
                            sentiment_values.append(source_data['sentiment_score'])
                            weights.append(time_weight)

            # Calculate weighted sentiment
            if sentiment_values and weights:
                weighted_sum = sum(s * w for s, w in zip(sentiment_values, weights))
                total_weight = sum(weights)
                overall_sentiment = weighted_sum / total_weight if total_weight > 0 else 0.5

                sentiment_signals['overall_sentiment'] = overall_sentiment
                sentiment_signals['sentiment_strength'] = abs(overall_sentiment - 0.5) * 2

                if overall_sentiment > 0.6:
                    sentiment_signals['sentiment_trend'] = 'bullish'
                elif overall_sentiment < 0.4:
                    sentiment_signals['sentiment_trend'] = 'bearish'
                else:
                    sentiment_signals['sentiment_trend'] = 'neutral'

            return sentiment_signals

        except Exception as e:
            logger.error(f"Error fusing sentiment signals: {e}")
            return {'overall_sentiment': 0.5, 'sentiment_strength': 0.5, 'sentiment_trend': 'neutral'}

    def _fuse_volume_signals(self, weighted_data: Dict, sources: List[str]) -> Dict:
        """Fuse volume-related signals"""
        try:
            volume_signals = {
                'volume_trend': 0.5,
                'volume_spike': False,
                'liquidity_score': 0.5
            }

            for source in sources:
                if source in weighted_data:
                    source_data = weighted_data[source]['data']

                    if source == 'volume_analysis' and isinstance(source_data, list):
                        if len(source_data) >= 5:
                            volumes = [float(v) for v in source_data[-10:]]
                            volume_signals['volume_trend'] = self._calculate_volume_trend(volumes)
                            volume_signals['volume_spike'] = self._detect_volume_spike(volumes)

                    elif source == 'order_books' and isinstance(source_data, dict):
                        volume_signals['liquidity_score'] = self._calculate_liquidity_score(source_data)

            return volume_signals

        except Exception as e:
            logger.error(f"Error fusing volume signals: {e}")
            return {'volume_trend': 0.5, 'volume_spike': False, 'liquidity_score': 0.5}

    def _calculate_volume_trend(self, volumes: List[float]) -> float:
        """Calculate volume trend from volume data"""
        try:
            if len(volumes) < 5:
                return 0.5

            # Compare recent vs historical volume
            recent_avg = statistics.mean(volumes[-3:])
            historical_avg = statistics.mean(volumes[:-3])

            if historical_avg > 0:
                volume_change = (recent_avg - historical_avg) / historical_avg
                # Normalize to 0-1 scale
                trend_score = 0.5 + (volume_change * 0.5)
                return max(0.0, min(1.0, trend_score))

            return 0.5

        except Exception as e:
            logger.error(f"Error calculating volume trend: {e}")
            return 0.5

    def _detect_volume_spike(self, volumes: List[float]) -> bool:
        """Detect volume spikes"""
        try:
            if len(volumes) < 5:
                return False

            current_volume = volumes[-1]
            avg_volume = statistics.mean(volumes[:-1])

            # Spike if current volume is 2x average
            return current_volume > avg_volume * 2 if avg_volume > 0 else False

        except Exception as e:
            logger.error(f"Error detecting volume spike: {e}")
            return False

    def _calculate_liquidity_score(self, order_book: Dict) -> float:
        """Calculate liquidity score from order book"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])

            if not bids or not asks:
                return 0.5

            # Calculate total depth
            bid_depth = sum(float(bid[1]) for bid in bids[:10])
            ask_depth = sum(float(ask[1]) for ask in asks[:10])
            total_depth = bid_depth + ask_depth

            # Normalize liquidity score
            return min(1.0, total_depth / 10000)

        except Exception as e:
            logger.error(f"Error calculating liquidity score: {e}")
            return 0.5

    def _calculate_composite_score(self, fused_data: Dict) -> float:
        """Calculate composite score from fused data"""
        try:
            # Weight different signal types
            weights = {
                'price_signals': 0.4,
                'sentiment_signals': 0.3,
                'volume_signals': 0.3
            }

            composite_score = 0.5  # Base score

            # Price signals contribution
            price_signals = fused_data.get('price_signals', {})
            if price_signals:
                price_score = price_signals.get('trend_strength', 0.5)
                composite_score += (price_score - 0.5) * weights['price_signals']

            # Sentiment signals contribution
            sentiment_signals = fused_data.get('sentiment_signals', {})
            if sentiment_signals:
                sentiment_score = sentiment_signals.get('overall_sentiment', 0.5)
                composite_score += (sentiment_score - 0.5) * weights['sentiment_signals']

            # Volume signals contribution
            volume_signals = fused_data.get('volume_signals', {})
            if volume_signals:
                volume_score = volume_signals.get('volume_trend', 0.5)
                composite_score += (volume_score - 0.5) * weights['volume_signals']

            return max(0.0, min(1.0, composite_score))

        except Exception as e:
            logger.error(f"Error calculating composite score: {e}")
            return 0.5

    def _calculate_fusion_confidence(self, weighted_data: Dict) -> float:
        """Calculate confidence in data fusion"""
        try:
            confidence_scores = []

            for source_name, source_info in weighted_data.items():
                time_weight = source_info.get('time_weight', 0.5)
                source_weight = self.source_weights.get(source_name, 0.1)

                # Confidence based on time weight and source importance
                source_confidence = time_weight * source_weight
                confidence_scores.append(source_confidence)

            return statistics.mean(confidence_scores) if confidence_scores else 0.5

        except Exception as e:
            logger.error(f"Error calculating fusion confidence: {e}")
            return 0.5

    async def _recognize_market_patterns(self, symbol: str, fused_data: Dict) -> Dict:
        """Recognize market patterns from fused data"""
        try:
            logger.debug(f"Recognizing market patterns for {symbol}")
            patterns = {
                'trend_pattern': 'neutral',
                'reversal_signals': [],
                'breakout_potential': 0.5,
                'pattern_confidence': 0.5
            }

            # Analyze trend patterns
            price_signals = fused_data.get('price_signals', {})
            sentiment_signals = fused_data.get('sentiment_signals', {})
            volume_signals = fused_data.get('volume_signals', {})

            # Determine trend pattern
            trend_strength = price_signals.get('trend_strength', 0.5)
            if trend_strength > 0.7:
                patterns['trend_pattern'] = 'strong_uptrend'
            elif trend_strength < 0.3:
                patterns['trend_pattern'] = 'strong_downtrend'
            elif trend_strength > 0.6:
                patterns['trend_pattern'] = 'uptrend'
            elif trend_strength < 0.4:
                patterns['trend_pattern'] = 'downtrend'
            else:
                patterns['trend_pattern'] = 'sideways'

            # Detect reversal signals
            if sentiment_signals.get('sentiment_trend') == 'bullish' and trend_strength < 0.4:
                patterns['reversal_signals'].append('bullish_divergence')
            elif sentiment_signals.get('sentiment_trend') == 'bearish' and trend_strength > 0.6:
                patterns['reversal_signals'].append('bearish_divergence')

            # Volume confirmation
            if volume_signals.get('volume_spike', False):
                patterns['reversal_signals'].append('volume_confirmation')

            # Breakout potential
            volume_trend = volume_signals.get('volume_trend', 0.5)
            sentiment_strength = sentiment_signals.get('sentiment_strength', 0.5)
            patterns['breakout_potential'] = (volume_trend + sentiment_strength) / 2

            # Pattern confidence
            patterns['pattern_confidence'] = fused_data.get('confidence', 0.5)

            return patterns

        except Exception as e:
            logger.error(f"Error recognizing market patterns: {e}")
            return {'trend_pattern': 'neutral', 'reversal_signals': [], 'breakout_potential': 0.5, 'pattern_confidence': 0.5}

    async def _generate_predictive_signals(self, symbol: str, fused_data: Dict, patterns: Dict) -> Dict:
        """Generate predictive trading signals"""
        try:
            logger.debug(f"Generating predictive signals for {symbol}")
            predictions = {
                'direction': 'neutral',
                'strength': 0.5,
                'time_horizon': 'short_term',
                'confidence': 0.5,
                'entry_score': 0.5,
                'exit_score': 0.5
            }

            composite_score = fused_data.get('composite_score', 0.5)
            pattern_confidence = patterns.get('pattern_confidence', 0.5)

            # Determine direction
            if composite_score > 0.6:
                predictions['direction'] = 'bullish'
                predictions['strength'] = (composite_score - 0.5) * 2
            elif composite_score < 0.4:
                predictions['direction'] = 'bearish'
                predictions['strength'] = (0.5 - composite_score) * 2
            else:
                predictions['direction'] = 'neutral'
                predictions['strength'] = 0.5

            # Adjust based on patterns
            trend_pattern = patterns.get('trend_pattern', 'neutral')
            if 'uptrend' in trend_pattern and predictions['direction'] == 'bullish':
                predictions['strength'] *= 1.2  # Boost strength for trend alignment
            elif 'downtrend' in trend_pattern and predictions['direction'] == 'bearish':
                predictions['strength'] *= 1.2

            # Entry and exit scores
            breakout_potential = patterns.get('breakout_potential', 0.5)
            predictions['entry_score'] = (composite_score + breakout_potential) / 2
            predictions['exit_score'] = 1.0 - predictions['entry_score']  # Inverse relationship

            # Overall confidence
            predictions['confidence'] = (pattern_confidence + fused_data.get('confidence', 0.5)) / 2

            return predictions

        except Exception as e:
            logger.error(f"Error generating predictive signals: {e}")
            return {'direction': 'neutral', 'strength': 0.5, 'confidence': 0.5}

    async def _calculate_risk_adjusted_scores(self, symbol: str, predictions: Dict) -> Dict:
        """Calculate risk-adjusted scores"""
        try:
            risk_adjusted = {
                'risk_score': 0.5,
                'reward_potential': 0.5,
                'risk_reward_ratio': 1.0,
                'adjusted_confidence': 0.5
            }

            # Base risk from prediction strength
            strength = predictions.get('strength', 0.5)
            confidence = predictions.get('confidence', 0.5)

            # Higher strength = higher potential reward but also higher risk
            risk_adjusted['reward_potential'] = strength
            risk_adjusted['risk_score'] = strength * 0.8  # Risk is slightly lower than reward potential

            # Risk-reward ratio
            if risk_adjusted['risk_score'] > 0:
                risk_adjusted['risk_reward_ratio'] = risk_adjusted['reward_potential'] / risk_adjusted['risk_score']
            else:
                risk_adjusted['risk_reward_ratio'] = 2.0  # Default favorable ratio

            # Adjust confidence based on risk
            risk_adjusted['adjusted_confidence'] = confidence * (1.0 - risk_adjusted['risk_score'] * 0.3)

            return risk_adjusted

        except Exception as e:
            logger.error(f"Error calculating risk-adjusted scores for {symbol}: {e}")
            return {'risk_score': 0.5, 'reward_potential': 0.5, 'risk_reward_ratio': 1.0, 'adjusted_confidence': 0.5}

    async def _synthesize_final_decision(self, symbol: str, quality_assessment: Dict,
                                       fused_data: Dict, patterns: Dict,
                                       predictions: Dict, risk_adjusted_scores: Dict) -> Dict:
        """Synthesize final trading decision from all analyses"""
        try:
            logger.debug(f"Synthesizing final decision for {symbol}")

            final_decision = {
                'symbol': symbol,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'action': 'hold',
                'confidence': 0.5,
                'strength': 0.5,
                'reasoning': [],
                'data_quality': quality_assessment.get('overall_quality', 0.5),
                'composite_score': fused_data.get('composite_score', 0.5),
                'risk_reward_ratio': risk_adjusted_scores.get('risk_reward_ratio', 1.0),
                'components': {
                    'quality': quality_assessment,
                    'fused_data': fused_data,
                    'patterns': patterns,
                    'predictions': predictions,
                    'risk_adjusted': risk_adjusted_scores
                }
            }

            # Determine action based on predictions and risk
            direction = predictions.get('direction', 'neutral')
            prediction_confidence = predictions.get('confidence', 0.5)
            risk_reward_ratio = risk_adjusted_scores.get('risk_reward_ratio', 1.0)

            # Decision logic
            if direction == 'bullish' and prediction_confidence > 0.6 and risk_reward_ratio > 1.2:
                final_decision['action'] = 'buy'
                final_decision['reasoning'].append('Strong bullish signals with favorable risk/reward')
            elif direction == 'bearish' and prediction_confidence > 0.6 and risk_reward_ratio > 1.2:
                final_decision['action'] = 'sell'
                final_decision['reasoning'].append('Strong bearish signals with favorable risk/reward')
            else:
                final_decision['action'] = 'hold'
                final_decision['reasoning'].append('Insufficient signal strength or unfavorable risk/reward')

            # Adjust confidence based on data quality
            data_quality = quality_assessment.get('overall_quality', 0.5)
            final_decision['confidence'] = prediction_confidence * data_quality
            final_decision['strength'] = predictions.get('strength', 0.5)

            # Add pattern-based reasoning
            trend_pattern = patterns.get('trend_pattern', 'neutral')
            if trend_pattern != 'neutral':
                final_decision['reasoning'].append(f'Market pattern: {trend_pattern}')

            reversal_signals = patterns.get('reversal_signals', [])
            if reversal_signals:
                final_decision['reasoning'].append(f'Reversal signals: {", ".join(reversal_signals)}')

            return final_decision

        except Exception as e:
            logger.error(f"Error synthesizing final decision for {symbol}: {e}")
            return {
                'symbol': symbol,
                'action': 'hold',
                'confidence': 0.5,
                'reasoning': ['Error in analysis'],
                'data_quality': 0.5
            }

    def _cache_analysis_results(self, symbol: str, analysis: Dict):
        """Cache analysis results for future reference"""
        try:
            if symbol not in self.analysis_history:
                self.analysis_history[symbol] = []

            # Add timestamp and cache
            analysis_with_timestamp = {
                'timestamp': time.time(),
                'analysis': analysis
            }

            self.analysis_history[symbol].append(analysis_with_timestamp)

            # Keep only last 100 analyses per symbol
            if len(self.analysis_history[symbol]) > 100:
                self.analysis_history[symbol] = self.analysis_history[symbol][-100:]

            # Update processed data cache
            self.processed_data_cache[symbol] = {
                'last_analysis': analysis,
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error caching analysis results for {symbol}: {e}")

    def _get_fallback_analysis(self, symbol: str) -> Dict:
        """Get fallback analysis when data quality is insufficient"""
        return {
            'symbol': symbol,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'action': 'hold',
            'confidence': 0.3,
            'strength': 0.5,
            'reasoning': ['Insufficient data quality for reliable analysis'],
            'data_quality': 0.3,
            'composite_score': 0.5,
            'risk_reward_ratio': 1.0,
            'fallback': True
        }

    def get_analysis_summary(self, symbol: str) -> Dict:
        """Get summary of recent analyses for a symbol"""
        try:
            if symbol not in self.analysis_history:
                return {'error': 'No analysis history found'}

            recent_analyses = self.analysis_history[symbol][-10:]  # Last 10 analyses

            # Calculate summary statistics
            actions = [a['analysis'].get('action', 'hold') for a in recent_analyses]
            confidences = [a['analysis'].get('confidence', 0.5) for a in recent_analyses]

            action_counts = {action: actions.count(action) for action in set(actions)}
            avg_confidence = statistics.mean(confidences) if confidences else 0.5

            return {
                'symbol': symbol,
                'total_analyses': len(self.analysis_history[symbol]),
                'recent_analyses': len(recent_analyses),
                'action_distribution': action_counts,
                'average_confidence': avg_confidence,
                'last_analysis': recent_analyses[-1]['analysis'] if recent_analyses else None
            }

        except Exception as e:
            logger.error(f"Error getting analysis summary for {symbol}: {e}")
            return {'error': str(e)}
