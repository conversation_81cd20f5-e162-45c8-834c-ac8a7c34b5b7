#!/usr/bin/env python3
"""
Test Dynamic Portfolio Rebalancer

This script tests the dynamic portfolio rebalancing system to ensure it can
properly analyze portfolio allocations, identify rebalancing needs, and
execute rebalancing actions with risk-based allocation and volatility adjustment.
"""

import asyncio
import logging
import sys
import os
from decimal import Decimal

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_dynamic_portfolio_rebalancer():
    """Test the dynamic portfolio rebalancer functionality"""
    try:
        logger.info("🧪 [TEST] Starting dynamic portfolio rebalancer test...")
        
        # Import required modules
        from src.trading.dynamic_portfolio_rebalancer import DynamicPortfolioRebalancer, RebalancingStrategy
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            logger.error("❌ [TEST] Missing Bybit API credentials")
            return False
        
        # Initialize exchange clients
        logger.info("🔧 [TEST] Initializing exchange clients...")
        
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        
        if not bybit_client.session:
            logger.error("❌ [TEST] Failed to initialize Bybit client")
            return False
        
        exchange_clients = {
            'bybit': bybit_client
        }
        
        # Initialize portfolio rebalancer
        logger.info("📊 [TEST] Initializing dynamic portfolio rebalancer...")
        
        rebalancer = DynamicPortfolioRebalancer(
            exchange_clients=exchange_clients,
            config={
                'strategy': 'volatility_adjusted',
                'threshold': 0.03,      # 3% threshold for testing
                'min_amount': 5.0,      # $5 minimum for testing
                'max_single': 0.3,      # 30% max single rebalance
                'max_allocation': 0.5,  # 50% max per currency
                'min_allocation': 0.02, # 2% min per currency
                'volatility_days': 30
            }
        )
        
        # Test 1: Initialize rebalancer
        logger.info("🧪 [TEST-1] Testing rebalancer initialization...")
        await rebalancer.initialize()
        
        if not rebalancer.current_allocations:
            logger.error("❌ [TEST-1] No current allocations found")
            return False
        
        logger.info(f"✅ [TEST-1] Found {len(rebalancer.current_allocations)} currencies in portfolio")
        
        # Show current allocations
        total_value = sum(data['value'] for data in rebalancer.current_allocations.values())
        logger.info(f"✅ [TEST-1] Total portfolio value: ${total_value:.2f}")
        
        for currency, data in rebalancer.current_allocations.items():
            percentage = data['percentage'] * 100
            value = data['value']
            logger.info(f"✅ [TEST-1] {currency}: {percentage:.2f}% (${value:.2f})")
        
        # Test 2: Volatility calculation
        logger.info("🧪 [TEST-2] Testing volatility calculation...")
        
        if not rebalancer.volatility_data:
            logger.error("❌ [TEST-2] No volatility data calculated")
            return False
        
        logger.info(f"✅ [TEST-2] Calculated volatilities for {len(rebalancer.volatility_data)} currencies")
        
        # Show volatility data
        for currency, vol_data in rebalancer.volatility_data.items():
            annual_vol = vol_data['annual_volatility']
            risk_score = vol_data['risk_score']
            logger.info(f"✅ [TEST-2] {currency}: {annual_vol*100:.1f}% volatility, risk score: {risk_score:.2f}")
        
        # Test 3: Correlation calculation
        logger.info("🧪 [TEST-3] Testing correlation calculation...")
        
        if not rebalancer.correlation_matrix:
            logger.error("❌ [TEST-3] No correlation matrix calculated")
            return False
        
        logger.info(f"✅ [TEST-3] Built correlation matrix with {len(rebalancer.correlation_matrix)} entries")
        
        # Show some example correlations
        currencies = list(rebalancer.current_allocations.keys())
        for i, curr1 in enumerate(currencies[:3]):
            for j, curr2 in enumerate(currencies[:3]):
                if i < j:
                    correlation = rebalancer.correlation_matrix.get((curr1, curr2), 0)
                    logger.info(f"✅ [TEST-3] {curr1}-{curr2} correlation: {correlation:.2f}")
        
        # Test 4: Target allocation calculation
        logger.info("🧪 [TEST-4] Testing target allocation calculation...")
        
        target_allocations = await rebalancer.calculate_target_allocations()
        
        if not target_allocations:
            logger.error("❌ [TEST-4] No target allocations calculated")
            return False
        
        logger.info(f"✅ [TEST-4] Calculated targets for {len(target_allocations)} currencies")
        
        # Show target vs current allocations
        for currency, target in target_allocations.items():
            current_pct = target.current_percentage * 100
            target_pct = target.target_percentage * 100
            deviation = target.deviation * 100
            
            logger.info(f"✅ [TEST-4] {currency}:")
            logger.info(f"  Current: {current_pct:.2f}%, Target: {target_pct:.2f}%, Deviation: {deviation:+.2f}%")
            logger.info(f"  Risk Score: {target.risk_score:.2f}, Volatility: {target.volatility*100:.1f}%")
        
        # Test 5: Rebalancing need identification
        logger.info("🧪 [TEST-5] Testing rebalancing need identification...")
        
        rebalancing_actions = await rebalancer.identify_rebalancing_needs()
        
        logger.info(f"✅ [TEST-5] Identified {len(rebalancing_actions)} rebalancing actions")
        
        # Show rebalancing actions
        for i, action in enumerate(rebalancing_actions):
            logger.info(f"✅ [TEST-5] Action {i+1}:")
            logger.info(f"  {action.currency_from} -> {action.currency_to}")
            logger.info(f"  Amount: ${action.amount:.2f} ({action.percentage*100:.2f}%)")
            logger.info(f"  Reason: {action.reason}")
            logger.info(f"  Priority: {action.priority}")
            logger.info(f"  Risk Adjustment: {action.risk_adjustment:.2f}")
        
        # Test 6: Different rebalancing strategies
        logger.info("🧪 [TEST-6] Testing different rebalancing strategies...")
        
        strategies_to_test = [
            RebalancingStrategy.EQUAL_WEIGHT,
            RebalancingStrategy.RISK_PARITY,
            RebalancingStrategy.VOLATILITY_ADJUSTED
        ]
        
        for strategy in strategies_to_test:
            try:
                rebalancer.rebalancing_strategy = strategy
                targets = await rebalancer.calculate_target_allocations()
                
                logger.info(f"✅ [TEST-6] {strategy.value} strategy:")
                for currency, target in list(targets.items())[:3]:  # Show first 3
                    target_pct = target.target_percentage * 100
                    logger.info(f"  {currency}: {target_pct:.2f}%")
                    
            except Exception as e:
                logger.warning(f"⚠️ [TEST-6] Error testing {strategy.value}: {e}")
        
        # Reset to original strategy
        rebalancer.rebalancing_strategy = RebalancingStrategy.VOLATILITY_ADJUSTED
        
        # Test 7: Execution validation (without actual execution)
        logger.info("🧪 [TEST-7] Testing rebalancing execution validation...")
        
        if rebalancing_actions:
            test_action = rebalancing_actions[0]
            
            # Test finding best exchange
            exchange_name = await rebalancer._find_best_exchange_for_rebalancing(test_action)
            if exchange_name:
                logger.info(f"✅ [TEST-7] Best exchange for rebalancing: {exchange_name}")
            else:
                logger.warning("⚠️ [TEST-7] No suitable exchange found")
            
            # Test trading parameter determination
            if exchange_name:
                symbol, side = await rebalancer._determine_trading_parameters(test_action, exchange_name)
                if symbol and side:
                    logger.info(f"✅ [TEST-7] Trading parameters: {side} {symbol}")
                else:
                    logger.warning("⚠️ [TEST-7] Could not determine trading parameters")
                
                # Test trade amount calculation
                if symbol and side:
                    trade_amount = await rebalancer._calculate_trade_amount(test_action, symbol, side, exchange_name)
                    logger.info(f"✅ [TEST-7] Trade amount: {trade_amount:.6f}")
        else:
            logger.info("ℹ️ [TEST-7] No rebalancing actions to test execution")
        
        # Test 8: Portfolio optimization analysis
        logger.info("🧪 [TEST-8] Testing portfolio optimization analysis...")
        
        if target_allocations:
            # Calculate portfolio risk metrics
            total_risk = Decimal('0')
            weighted_volatility = Decimal('0')
            
            for currency, target in target_allocations.items():
                weight = target.target_percentage
                volatility = Decimal(str(target.volatility))
                risk_contribution = weight * volatility
                
                total_risk += risk_contribution
                weighted_volatility += weight * volatility
            
            logger.info(f"✅ [TEST-8] Portfolio risk metrics:")
            logger.info(f"  Total risk contribution: {float(total_risk)*100:.2f}%")
            logger.info(f"  Weighted volatility: {float(weighted_volatility)*100:.2f}%")
            
            # Find most and least risky allocations
            sorted_by_risk = sorted(target_allocations.items(), key=lambda x: x[1].risk_score)
            lowest_risk = sorted_by_risk[0]
            highest_risk = sorted_by_risk[-1]
            
            logger.info(f"✅ [TEST-8] Lowest risk currency: {lowest_risk[0]} (risk: {lowest_risk[1].risk_score:.2f})")
            logger.info(f"✅ [TEST-8] Highest risk currency: {highest_risk[0]} (risk: {highest_risk[1].risk_score:.2f})")
        
        # Test 9: Rebalancing threshold sensitivity
        logger.info("🧪 [TEST-9] Testing rebalancing threshold sensitivity...")
        
        original_threshold = rebalancer.rebalancing_threshold
        
        thresholds_to_test = [Decimal('0.01'), Decimal('0.05'), Decimal('0.10')]  # 1%, 5%, 10%
        
        for threshold in thresholds_to_test:
            rebalancer.rebalancing_threshold = threshold
            actions = await rebalancer.identify_rebalancing_needs()
            logger.info(f"✅ [TEST-9] Threshold {threshold*100:.0f}%: {len(actions)} actions needed")
        
        # Reset original threshold
        rebalancer.rebalancing_threshold = original_threshold
        
        # Summary
        logger.info("📊 [TEST-SUMMARY] Dynamic portfolio rebalancer test results:")
        logger.info(f"  - Portfolio currencies: {len(rebalancer.current_allocations)}")
        logger.info(f"  - Total portfolio value: ${total_value:.2f}")
        logger.info(f"  - Volatility data points: {len(rebalancer.volatility_data)}")
        logger.info(f"  - Correlation matrix entries: {len(rebalancer.correlation_matrix)}")
        logger.info(f"  - Target allocations: {len(target_allocations)}")
        logger.info(f"  - Rebalancing actions needed: {len(rebalancing_actions)}")
        
        if rebalancing_actions:
            total_rebalance_amount = sum(action.amount for action in rebalancing_actions)
            logger.info(f"  - Total rebalancing amount: ${total_rebalance_amount:.2f}")
        
        logger.info("✅ [TEST] Dynamic portfolio rebalancer test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Dynamic portfolio rebalancer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    try:
        logger.info("🚀 Starting dynamic portfolio rebalancer tests...")
        
        success = await test_dynamic_portfolio_rebalancer()
        
        if success:
            logger.info("✅ All tests passed!")
            return 0
        else:
            logger.error("❌ Some tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
