#!/usr/bin/env python3
"""
Enhanced Exchange Manager for Robust Fallback Trading System
Handles automatic exchange switching, monitoring, and capital management
"""

import asyncio
import logging
import time
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from decimal import Decimal
from dataclasses import dataclass, field
from enum import Enum
import statistics
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime classification for adaptive trading"""
    BULL = "bull"
    BEAR = "bear"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"

class OrderType(Enum):
    """Advanced order types for institutional trading"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"
    ICEBERG = "iceberg"
    TWAP = "twap"
    VWAP = "vwap"
    HIDDEN = "hidden"

@dataclass
class RiskMetrics:
    """Comprehensive risk metrics for institutional trading"""
    var_95: float = 0.0  # Value at Risk 95%
    var_99: float = 0.0  # Value at Risk 99%
    expected_shortfall: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    volatility: float = 0.0
    beta: float = 0.0
    correlation_btc: float = 0.0

@dataclass
class PerformanceMetrics:
    """Real-time performance tracking"""
    total_return: float = 0.0
    annualized_return: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    max_consecutive_wins: int = 0
    max_consecutive_losses: int = 0
    trades_count: int = 0
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class MarketData:
    """Multi-timeframe market data"""
    symbol: str
    price: float
    volume: float
    bid: float
    ask: float
    spread: float
    timestamp: datetime
    timeframe_data: Dict[str, Dict] = field(default_factory=dict)  # 1m, 5m, 15m, 1h, 4h, 1d

@dataclass
class ExchangeStatus:
    """Enhanced exchange status tracking with institutional metrics"""
    name: str
    active: bool
    last_test: Optional[datetime]
    error_count: int
    role: str  # PRIMARY, SECONDARY, FALLBACK, TRADING, ARBITRAGE
    priority: int
    api_restored: bool = False
    monitoring_enabled: bool = True
    balance_usd: Decimal = Decimal('0')
    last_successful_trade: Optional[datetime] = None
    consecutive_failures: int = 0

    # Professional-grade metrics
    latency_ms: float = 0.0
    uptime_percentage: float = 100.0
    trade_success_rate: float = 0.0
    avg_execution_time: float = 0.0
    liquidity_score: float = 0.0
    fee_tier: str = "standard"
    supported_order_types: List[str] = field(default_factory=lambda: ["market", "limit"])
    max_position_size: Decimal = Decimal('1000000')
    min_order_size: Decimal = Decimal('1')

    # Risk metrics
    risk_metrics: RiskMetrics = field(default_factory=RiskMetrics)
    performance_metrics: PerformanceMetrics = field(default_factory=PerformanceMetrics)

    # Market data quality
    data_quality_score: float = 1.0
    last_data_update: Optional[datetime] = None
    market_data_latency: float = 0.0

class ProfessionalExchangeManager:
    """
    Professional-Grade Exchange Manager with Institutional Capabilities
    - Multi-timeframe analysis and market regime detection
    - Advanced risk management and position sizing
    - Cross-exchange arbitrage detection
    - Professional order management (TWAP, VWAP, Iceberg)
    - Real-time performance analytics
    """

    def __init__(self):
        self.exchanges = {}
        self.exchange_status = {}
        self.monitoring_active = False
        self.monitoring_task = None
        self.last_health_check = None

        # Professional-grade configuration
        self.capital_management_mode = 'institutional'
        self.coinbase_primary_mode = True

        # Advanced configuration
        self.health_check_interval = 300  # 5 minutes for professional monitoring
        self.max_consecutive_failures = 3
        self.min_balance_threshold = Decimal('5.0')
        self.max_position_size_percentage = 0.25  # 25% max position size

        # Multi-timeframe analysis
        self.timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']
        self.market_data_cache = defaultdict(lambda: defaultdict(deque))
        self.market_regime = MarketRegime.SIDEWAYS

        # Risk management
        self.volatility_threshold = 0.15
        self.correlation_threshold = 0.8
        self.max_drawdown_limit = 0.10  # 10% max drawdown

        # Performance tracking
        self.trade_history = deque(maxlen=10000)
        self.performance_window = deque(maxlen=1000)
        self.arbitrage_opportunities = deque(maxlen=100)

        # Order management
        self.active_orders = {}
        self.order_execution_stats = defaultdict(list)

        # Liquidity analysis
        self.liquidity_cache = {}
        self.spread_cache = {}

        logger.info("🏛️ [PROFESSIONAL-MGR] Institutional-grade exchange manager initialized")

    async def _get_multi_timeframe_data(self, symbol: str) -> Dict[str, List[Dict]]:
        """Get multi-timeframe market data for analysis"""
        try:
            data = {}
            primary_exchange = self.get_primary_exchange()

            if not primary_exchange or primary_exchange not in self.exchanges:
                return data

            exchange_client = self.exchanges[primary_exchange]

            # Get data for each timeframe
            for timeframe in self.timeframes:
                try:
                    if hasattr(exchange_client, 'get_klines'):
                        klines = await exchange_client.get_klines(symbol, timeframe, limit=100)
                        if klines:
                            data[timeframe] = klines
                    elif hasattr(exchange_client, 'get_historical_data'):
                        historical = await exchange_client.get_historical_data(symbol, timeframe, 100)
                        if historical:
                            data[timeframe] = historical
                except Exception as e:
                    logger.debug(f"Could not get {timeframe} data: {e}")

            return data

        except Exception as e:
            logger.error(f"❌ [DATA] Error getting multi-timeframe data: {e}")
            return {}

    async def analyze_market_regime(self, symbol: str) -> MarketRegime:
        """Analyze current market regime using multi-timeframe analysis"""
        try:
            market_data = await self._get_multi_timeframe_data(symbol)

            # Calculate volatility across timeframes
            volatilities = []
            trends = []

            for timeframe, data in market_data.items():
                if len(data) >= 20:
                    prices = [d['close'] for d in data]
                    returns = np.diff(np.log(prices))
                    volatility = np.std(returns) * np.sqrt(252)  # Annualized
                    trend = (prices[-1] - prices[0]) / prices[0]

                    volatilities.append(volatility)
                    trends.append(trend)

            if not volatilities:
                return MarketRegime.SIDEWAYS

            avg_volatility = np.mean(volatilities)
            avg_trend = np.mean(trends)

            # Classify market regime
            if avg_volatility > 0.4:
                regime = MarketRegime.HIGH_VOLATILITY
            elif avg_volatility < 0.15:
                regime = MarketRegime.LOW_VOLATILITY
            elif avg_trend > 0.05:
                regime = MarketRegime.BULL
            elif avg_trend < -0.05:
                regime = MarketRegime.BEAR
            else:
                regime = MarketRegime.SIDEWAYS

            self.market_regime = regime
            logger.debug(f"📊 [REGIME] Market regime for {symbol}: {regime.value}")
            return regime

        except Exception as e:
            logger.error(f"❌ [REGIME] Error analyzing market regime: {e}")
            return MarketRegime.SIDEWAYS

    async def calculate_dynamic_position_size(self, symbol: str, price: float,
                                            available_balance: Decimal) -> Decimal:
        """Calculate dynamic position size based on volatility and market conditions"""
        try:
            # Get market regime
            regime = await self.analyze_market_regime(symbol)

            # Base position size (20-25% of available balance)
            base_percentage = 0.225  # 22.5% base

            # Adjust based on market regime
            regime_multipliers = {
                MarketRegime.LOW_VOLATILITY: 1.2,
                MarketRegime.SIDEWAYS: 1.0,
                MarketRegime.BULL: 1.1,
                MarketRegime.BEAR: 0.8,
                MarketRegime.HIGH_VOLATILITY: 0.6
            }

            multiplier = regime_multipliers.get(regime, 1.0)
            adjusted_percentage = base_percentage * multiplier

            # Calculate position size
            position_value = available_balance * Decimal(str(adjusted_percentage))
            position_size = position_value / Decimal(str(price))

            logger.debug(f"💰 [POSITION] Dynamic size for {symbol}: {position_size:.6f} "
                        f"(regime: {regime.value}, multiplier: {multiplier:.2f})")

            return position_size

        except Exception as e:
            logger.error(f"❌ [POSITION] Error calculating position size: {e}")
            return Decimal('0')

    async def detect_arbitrage_opportunities(self, symbol: str) -> List[Dict[str, Any]]:
        """Detect cross-exchange arbitrage opportunities"""
        try:
            opportunities = []
            active_exchanges = self.get_active_exchanges()

            if len(active_exchanges) < 2:
                return opportunities

            # Get current prices from all exchanges
            prices = {}
            for exchange in active_exchanges:
                try:
                    if exchange in self.exchanges:
                        ticker = await self.exchanges[exchange].get_ticker(symbol)
                        if ticker:
                            prices[exchange] = {
                                'bid': float(ticker.get('bid', 0)),
                                'ask': float(ticker.get('ask', 0)),
                                'spread': float(ticker.get('ask', 0)) - float(ticker.get('bid', 0))
                            }
                except Exception as e:
                    logger.debug(f"Could not get price from {exchange}: {e}")

            # Find arbitrage opportunities
            for buy_exchange, buy_data in prices.items():
                for sell_exchange, sell_data in prices.items():
                    if buy_exchange != sell_exchange:
                        # Calculate potential profit
                        buy_price = buy_data['ask']
                        sell_price = sell_data['bid']

                        if sell_price > buy_price:
                            profit_percentage = (sell_price - buy_price) / buy_price

                            # Consider fees (estimate 0.1% per side)
                            estimated_fees = 0.002
                            net_profit = profit_percentage - estimated_fees

                            if net_profit > 0.001:  # Minimum 0.1% profit
                                opportunity = {
                                    'symbol': symbol,
                                    'buy_exchange': buy_exchange,
                                    'sell_exchange': sell_exchange,
                                    'buy_price': buy_price,
                                    'sell_price': sell_price,
                                    'gross_profit_pct': profit_percentage,
                                    'net_profit_pct': net_profit,
                                    'timestamp': datetime.now()
                                }
                                opportunities.append(opportunity)

            # Store opportunities for analysis
            self.arbitrage_opportunities.extend(opportunities)

            if opportunities:
                logger.info(f"🔍 [ARBITRAGE] Found {len(opportunities)} opportunities for {symbol}")

            return opportunities

        except Exception as e:
            logger.error(f"❌ [ARBITRAGE] Error detecting opportunities: {e}")
            return []

    async def _execute_market_slice(self, symbol: str, side: str, amount: float) -> Dict[str, Any]:
        """Execute a market order slice"""
        try:
            primary_exchange = self.get_primary_exchange()
            if not primary_exchange or primary_exchange not in self.exchanges:
                return {'success': False, 'error': 'No primary exchange available'}

            exchange_client = self.exchanges[primary_exchange]

            # Execute market order
            if hasattr(exchange_client, 'execute_order'):
                result = await exchange_client.execute_order(symbol, side, amount, 'market')
            else:
                # Fallback method
                result = {'success': False, 'error': 'Exchange does not support order execution'}

            return result

        except Exception as e:
            logger.error(f"❌ [SLICE] Market slice execution failed: {e}")
            return {'success': False, 'error': str(e)}

    async def _execute_limit_slice(self, symbol: str, side: str, amount: float) -> Dict[str, Any]:
        """Execute a limit order slice"""
        try:
            primary_exchange = self.get_primary_exchange()
            if not primary_exchange or primary_exchange not in self.exchanges:
                return {'success': False, 'error': 'No primary exchange available'}

            exchange_client = self.exchanges[primary_exchange]

            # Get current market price for limit order
            ticker = await exchange_client.get_ticker(symbol)
            if not ticker:
                return {'success': False, 'error': 'Could not get market price'}

            # Set limit price slightly better than market
            market_price = float(ticker.get('price', 0))
            if side.lower() == 'buy':
                limit_price = market_price * 0.999  # 0.1% below market
            else:
                limit_price = market_price * 1.001  # 0.1% above market

            # Execute limit order
            if hasattr(exchange_client, 'execute_limit_order'):
                result = await exchange_client.execute_limit_order(symbol, side, amount, limit_price)
            else:
                # Fallback to market order
                result = await exchange_client.execute_order(symbol, side, amount, 'market')

            return result

        except Exception as e:
            logger.error(f"❌ [SLICE] Limit slice execution failed: {e}")
            return {'success': False, 'error': str(e)}

    async def _get_volume_profile(self, symbol: str, timeframe: str) -> Dict[str, float]:
        """Get volume profile for VWAP calculation"""
        try:
            primary_exchange = self.get_primary_exchange()
            if not primary_exchange or primary_exchange not in self.exchanges:
                return {}

            exchange_client = self.exchanges[primary_exchange]

            # Get recent volume data
            if hasattr(exchange_client, 'get_volume_profile'):
                return await exchange_client.get_volume_profile(symbol, timeframe)
            elif hasattr(exchange_client, 'get_klines'):
                klines = await exchange_client.get_klines(symbol, timeframe, limit=60)
                if klines:
                    volume_profile = {}
                    for i, kline in enumerate(klines):
                        volume_profile[f'bucket_{i}'] = float(kline.get('volume', 0))
                    return volume_profile

            return {}

        except Exception as e:
            logger.error(f"❌ [VOLUME] Error getting volume profile: {e}")
            return {}

    async def execute_twap_order(self, symbol: str, side: str, total_amount: float,
                                duration_minutes: int = 30) -> Dict[str, Any]:
        """Execute Time-Weighted Average Price (TWAP) order"""
        try:
            logger.info(f"📈 [TWAP] Starting TWAP order: {side} {total_amount} {symbol} over {duration_minutes}m")

            # Calculate order slicing
            num_slices = min(duration_minutes, 20)  # Max 20 slices
            slice_amount = total_amount / num_slices
            slice_interval = (duration_minutes * 60) / num_slices  # seconds

            executed_orders = []
            total_executed = 0
            total_cost = 0

            for i in range(num_slices):
                try:
                    # Execute slice
                    result = await self._execute_market_slice(symbol, side, slice_amount)

                    if result.get('success'):
                        executed_orders.append(result)
                        total_executed += result.get('amount', 0)
                        total_cost += result.get('cost', 0)

                        logger.debug(f"📊 [TWAP] Slice {i+1}/{num_slices} executed: {result.get('amount', 0)}")

                    # Wait for next slice (except last one)
                    if i < num_slices - 1:
                        await asyncio.sleep(slice_interval)

                except Exception as e:
                    logger.error(f"❌ [TWAP] Error executing slice {i+1}: {e}")

            avg_price = total_cost / total_executed if total_executed > 0 else 0

            return {
                'success': total_executed > 0,
                'order_type': 'TWAP',
                'symbol': symbol,
                'side': side,
                'total_amount': total_amount,
                'executed_amount': total_executed,
                'average_price': avg_price,
                'total_cost': total_cost,
                'slices_executed': len(executed_orders),
                'execution_time_minutes': duration_minutes
            }

        except Exception as e:
            logger.error(f"❌ [TWAP] TWAP order execution failed: {e}")
            return {'success': False, 'error': str(e)}

    async def execute_vwap_order(self, symbol: str, side: str, total_amount: float) -> Dict[str, Any]:
        """Execute Volume-Weighted Average Price (VWAP) order"""
        try:
            logger.info(f"📊 [VWAP] Starting VWAP order: {side} {total_amount} {symbol}")

            # Get volume profile for the last hour
            volume_profile = await self._get_volume_profile(symbol, '1h')

            if not volume_profile:
                # Fallback to regular market order
                return await self._execute_market_slice(symbol, side, total_amount)

            # Calculate VWAP-weighted slices
            total_volume = sum(volume_profile.values())
            executed_orders = []
            total_executed = 0
            total_cost = 0

            for time_bucket, volume in volume_profile.items():
                volume_weight = volume / total_volume
                slice_amount = total_amount * volume_weight

                if slice_amount > 0.001:  # Minimum slice size
                    result = await self._execute_market_slice(symbol, side, slice_amount)

                    if result.get('success'):
                        executed_orders.append(result)
                        total_executed += result.get('amount', 0)
                        total_cost += result.get('cost', 0)

            avg_price = total_cost / total_executed if total_executed > 0 else 0

            return {
                'success': total_executed > 0,
                'order_type': 'VWAP',
                'symbol': symbol,
                'side': side,
                'total_amount': total_amount,
                'executed_amount': total_executed,
                'average_price': avg_price,
                'total_cost': total_cost,
                'slices_executed': len(executed_orders)
            }

        except Exception as e:
            logger.error(f"❌ [VWAP] VWAP order execution failed: {e}")
            return {'success': False, 'error': str(e)}

    async def execute_iceberg_order(self, symbol: str, side: str, total_amount: float,
                                  visible_amount: float) -> Dict[str, Any]:
        """Execute Iceberg order (large order with small visible portions)"""
        try:
            logger.info(f"🧊 [ICEBERG] Starting iceberg order: {side} {total_amount} {symbol} "
                       f"(visible: {visible_amount})")

            remaining_amount = total_amount
            executed_orders = []
            total_executed = 0
            total_cost = 0

            while remaining_amount > 0.001:
                # Calculate current slice size
                current_slice = min(visible_amount, remaining_amount)

                # Execute visible portion
                result = await self._execute_limit_slice(symbol, side, current_slice)

                if result.get('success'):
                    executed_orders.append(result)
                    executed_amount = result.get('amount', 0)
                    total_executed += executed_amount
                    total_cost += result.get('cost', 0)
                    remaining_amount -= executed_amount

                    logger.debug(f"🧊 [ICEBERG] Slice executed: {executed_amount}, remaining: {remaining_amount}")
                else:
                    logger.warning(f"⚠️ [ICEBERG] Slice execution failed, stopping")
                    break

                # Small delay between slices
                await asyncio.sleep(1)

            avg_price = total_cost / total_executed if total_executed > 0 else 0

            return {
                'success': total_executed > 0,
                'order_type': 'ICEBERG',
                'symbol': symbol,
                'side': side,
                'total_amount': total_amount,
                'executed_amount': total_executed,
                'average_price': avg_price,
                'total_cost': total_cost,
                'slices_executed': len(executed_orders)
            }

        except Exception as e:
            logger.error(f"❌ [ICEBERG] Iceberg order execution failed: {e}")
            return {'success': False, 'error': str(e)}

    async def initialize(self, components: Dict) -> bool:
        """Initialize exchange manager with available components"""
        try:
            logger.info("🚀 [EXCHANGE-MGR] Initializing enhanced exchange manager...")
            
            # Initialize all available exchanges independently
            active_exchanges = []

            # Initialize Bybit (High priority trading exchange)
            bybit_success = await self._initialize_bybit(components)
            if bybit_success:
                active_exchanges.append('bybit')

            # Initialize Coinbase (Primary for capital management, secondary for trading)
            coinbase_success = await self._initialize_coinbase(components)
            if coinbase_success:
                active_exchanges.append('coinbase')

            # Initialize other exchanges if available
            binance_success = await self._initialize_binance(components)
            if binance_success:
                active_exchanges.append('binance')

            phantom_success = await self._initialize_phantom(components)
            if phantom_success:
                active_exchanges.append('phantom')

            # Coinbase-Primary mode regardless of API status
            self.capital_management_mode = 'coinbase_primary'

            if not active_exchanges:
                logger.error("❌ [EXCHANGE-MGR] No exchanges available for trading")
                return False

            logger.info(f"✅ [EXCHANGE-MGR] Coinbase-Primary mode with {len(active_exchanges)} active exchanges: {active_exchanges}")

            # Start monitoring for any inactive exchanges
            if not coinbase_success or len(active_exchanges) < 4:  # Monitor if any exchange is missing
                await self._start_monitoring()

            return True
            
        except Exception as e:
            logger.error(f"❌ [EXCHANGE-MGR] Initialization failed: {e}")
            return False
    
    async def _initialize_bybit(self, components: Dict) -> bool:
        """Initialize Bybit exchange - FIXED CLIENT NAME"""
        try:
            # Check for both possible client names
            bybit_client = None
            if 'bybit_client_fixed' in components:
                bybit_client = components['bybit_client_fixed']
            elif 'bybit_client' in components:
                bybit_client = components['bybit_client']

            if not bybit_client:
                logger.warning("⚠️ [BYBIT] Client not available in components")
                return False
            
            # Test connection
            test_result = await self._test_exchange_connection(bybit_client, 'bybit')
            
            if test_result:
                self.exchanges['bybit'] = bybit_client
                self.exchange_status['bybit'] = ExchangeStatus(
                    name='bybit',
                    active=True,
                    last_test=datetime.now(),
                    error_count=0,
                    role='PRIMARY',
                    priority=1
                )
                
                # Get balance
                try:
                    balance = await bybit_client.get_balance('USDT')
                    self.exchange_status['bybit'].balance_usd = balance
                    logger.info(f"✅ [BYBIT] Primary exchange ready - Balance: ${balance:.2f}")
                except Exception as e:
                    logger.warning(f"⚠️ [BYBIT] Could not get balance: {e}")
                
                return True
            else:
                logger.error("❌ [BYBIT] Connection test failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ [BYBIT] Initialization failed: {e}")
            return False
    
    async def _initialize_coinbase(self, components: Dict) -> bool:
        """Initialize Coinbase exchange - FIXED CLIENT NAME"""
        try:
            # Check for both possible client names
            coinbase_client = None
            if 'coinbase_enhanced_client' in components:
                coinbase_client = components['coinbase_enhanced_client']
            elif 'coinbase_client' in components:
                coinbase_client = components['coinbase_client']

            if not coinbase_client:
                logger.warning("⚠️ [COINBASE] Client not available in components")
                return False
            
            # Test connection
            test_result = await self._test_exchange_connection(coinbase_client, 'coinbase')
            
            if test_result:
                self.exchanges['coinbase'] = coinbase_client
                self.exchange_status['coinbase'] = ExchangeStatus(
                    name='coinbase',
                    active=True,
                    last_test=datetime.now(),
                    error_count=0,
                    role='SECONDARY',
                    priority=2,
                    api_restored=True
                )
                
                logger.info("✅ [COINBASE] Secondary exchange ready")
                return True
            else:
                logger.warning("⚠️ [COINBASE] Connection test failed - will monitor for restoration")
                self.exchange_status['coinbase'] = ExchangeStatus(
                    name='coinbase',
                    active=False,
                    last_test=datetime.now(),
                    error_count=1,
                    role='SECONDARY',
                    priority=2,
                    api_restored=False,
                    monitoring_enabled=True
                )
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ [COINBASE] Initialization failed: {e}")
            return False

    async def _initialize_binance(self, components: Dict) -> bool:
        """Initialize Binance exchange if available"""
        try:
            if 'binance_client' not in components:
                logger.debug("🔍 [BINANCE] Client not available in components")
                return False

            binance_client = components['binance_client']

            # Test connection
            test_result = await self._test_exchange_connection(binance_client, 'binance')

            if test_result:
                self.exchanges['binance'] = binance_client
                self.exchange_status['binance'] = ExchangeStatus(
                    name='binance',
                    active=True,
                    last_test=datetime.now(),
                    error_count=0,
                    role='TRADING',
                    priority=2
                )

                logger.info("✅ [BINANCE] Trading exchange ready")
                return True
            else:
                logger.debug("🔍 [BINANCE] Connection test failed")
                return False

        except Exception as e:
            logger.debug(f"🔍 [BINANCE] Initialization failed: {e}")
            return False

    async def _initialize_phantom(self, components: Dict) -> bool:
        """Initialize Phantom wallet if available"""
        try:
            if 'phantom_client' not in components:
                logger.debug("🔍 [PHANTOM] Client not available in components")
                return False

            phantom_client = components['phantom_client']

            # Test connection
            test_result = await self._test_exchange_connection(phantom_client, 'phantom')

            if test_result:
                self.exchanges['phantom'] = phantom_client
                self.exchange_status['phantom'] = ExchangeStatus(
                    name='phantom',
                    active=True,
                    last_test=datetime.now(),
                    error_count=0,
                    role='SOLANA_TRADING',
                    priority=4
                )

                logger.info("✅ [PHANTOM] Solana trading ready")
                return True
            else:
                logger.debug("🔍 [PHANTOM] Connection test failed")
                return False

        except Exception as e:
            logger.debug(f"🔍 [PHANTOM] Initialization failed: {e}")
            return False
    
    async def _test_exchange_connection(self, exchange_client, exchange_name: str) -> bool:
        """Test exchange connection - FIXED ASYNC/SYNC HANDLING"""
        try:
            if hasattr(exchange_client, 'test_connection'):
                # Check if test_connection is async or sync - FIXED
                test_method = exchange_client.test_connection
                if asyncio.iscoroutinefunction(test_method):
                    result = await exchange_client.test_connection()
                else:
                    # Synchronous method - call directly
                    result = exchange_client.test_connection()

                # Ensure we return a boolean
                return bool(result)

            elif hasattr(exchange_client, 'get_ticker'):
                ticker = await exchange_client.get_ticker('BTCUSDT')  # Use Bybit format
                return ticker is not None
            elif hasattr(exchange_client, 'get_balance'):
                # Try to get balance as a connection test
                balance = await exchange_client.get_balance('USDT')
                return balance is not None
            else:
                logger.warning(f"⚠️ [{exchange_name.upper()}] No test method available")
                return True  # Assume working if no test method

        except Exception as e:
            logger.warning(f"⚠️ [{exchange_name.upper()}] Connection test failed: {e}")
            return False
    
    async def _start_monitoring(self):
        """Start background monitoring for exchange restoration"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("🔍 [MONITOR] Started exchange monitoring")
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.monitoring_active:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                logger.info("🔍 [MONITOR] Performing exchange health check...")
                
                # Check all exchanges
                for exchange_name, status in self.exchange_status.items():
                    if not status.active and status.monitoring_enabled:
                        await self._check_exchange_restoration(exchange_name)
                
                # Update last health check
                self.last_health_check = datetime.now()
                
            except Exception as e:
                logger.error(f"❌ [MONITOR] Monitoring error: {e}")
                await asyncio.sleep(60)  # Short delay on error
    
    async def _check_exchange_restoration(self, exchange_name: str):
        """Check if a failed exchange has been restored"""
        try:
            status = self.exchange_status[exchange_name]
            
            if exchange_name == 'coinbase':
                # Try to reinitialize Coinbase
                success = await self._test_coinbase_restoration()
                if success:
                    logger.info("🎉 [MONITOR] Coinbase API access RESTORED!")
                    status.active = True
                    status.api_restored = True
                    status.error_count = 0
                    status.last_test = datetime.now()
                    
                    # Update capital management mode
                    if self.capital_management_mode == 'bybit_only':
                        self.capital_management_mode = 'dual'
                        logger.info("🔄 [MONITOR] Switched to dual-exchange mode")
                else:
                    logger.debug("🔍 [MONITOR] Coinbase still not available")
                    
        except Exception as e:
            logger.error(f"❌ [MONITOR] Error checking {exchange_name} restoration: {e}")
    
    async def _test_coinbase_restoration(self) -> bool:
        """Test if Coinbase API has been restored"""
        try:
            # This would test Coinbase API endpoints
            # For now, return False to simulate ongoing API issues
            return False
            
        except Exception as e:
            logger.debug(f"Coinbase restoration test failed: {e}")
            return False
    
    def get_active_exchanges(self) -> List[str]:
        """Get list of currently active exchanges"""
        return [name for name, status in self.exchange_status.items() if status.active]
    
    def get_primary_exchange(self) -> Optional[str]:
        """Get the primary exchange for trading"""
        active_exchanges = self.get_active_exchanges()
        
        if 'bybit' in active_exchanges:
            return 'bybit'
        elif 'coinbase' in active_exchanges:
            return 'coinbase'
        else:
            return None
    
    def get_exchange_for_symbol(self, symbol: str) -> Optional[str]:
        """
        Get the best exchange for a specific symbol using Coinbase-Primary routing
        Returns the optimal exchange regardless of Coinbase API status
        """
        active_exchanges = self.get_active_exchanges()

        if not active_exchanges:
            return None

        # Route based on symbol and available exchanges
        base_currency = symbol.split('-')[0]

        # Exchange priority for different asset types
        if base_currency == 'SOL':
            # Solana: Prefer Phantom > Bybit > Binance > Coinbase
            for exchange in ['phantom', 'bybit', 'binance', 'coinbase']:
                if exchange in active_exchanges:
                    return exchange
        elif base_currency in ['BTC', 'ETH']:
            # Major cryptos: Prefer Bybit > Binance > Coinbase > Phantom
            for exchange in ['bybit', 'binance', 'coinbase', 'phantom']:
                if exchange in active_exchanges:
                    return exchange
        else:
            # Other assets: Prefer Bybit > Binance > Coinbase
            for exchange in ['bybit', 'binance', 'coinbase']:
                if exchange in active_exchanges:
                    return exchange

        # Fallback: Return any available exchange
        return active_exchanges[0]

    def get_all_trading_exchanges(self) -> List[str]:
        """Get all exchanges available for trading (not just primary)"""
        active_exchanges = self.get_active_exchanges()

        # Filter out exchanges that are not suitable for active trading
        trading_exchanges = []
        for exchange in active_exchanges:
            if exchange in self.exchange_status:
                status = self.exchange_status[exchange]
                # Include all active exchanges for trading
                if status.active and status.consecutive_failures < self.max_consecutive_failures:
                    trading_exchanges.append(exchange)

        return trading_exchanges
    
    async def record_trade_result(self, exchange_name: str, success: bool):
        """Record the result of a trade execution"""
        if exchange_name in self.exchange_status:
            status = self.exchange_status[exchange_name]
            
            if success:
                status.consecutive_failures = 0
                status.last_successful_trade = datetime.now()
            else:
                status.consecutive_failures += 1
                status.error_count += 1
                
                # Disable exchange if too many consecutive failures
                if status.consecutive_failures >= self.max_consecutive_failures:
                    logger.warning(f"⚠️ [EXCHANGE-MGR] Disabling {exchange_name} due to consecutive failures")
                    status.active = False
                    
                    # Update capital management mode
                    await self._update_capital_management_mode()
    
    async def _update_capital_management_mode(self):
        """Update capital management mode based on active exchanges"""
        active_exchanges = self.get_active_exchanges()
        
        if 'bybit' in active_exchanges and 'coinbase' in active_exchanges:
            self.capital_management_mode = 'dual'
        elif 'bybit' in active_exchanges:
            self.capital_management_mode = 'bybit_only'
        elif 'coinbase' in active_exchanges:
            self.capital_management_mode = 'coinbase_only'
        else:
            logger.error("❌ [EXCHANGE-MGR] No active exchanges available!")
    
    def get_status_report(self) -> Dict[str, Any]:
        """Get comprehensive status report"""
        return {
            'capital_management_mode': self.capital_management_mode,
            'active_exchanges': self.get_active_exchanges(),
            'primary_exchange': self.get_primary_exchange(),
            'monitoring_active': self.monitoring_active,
            'last_health_check': self.last_health_check,
            'exchange_status': {
                name: {
                    'active': status.active,
                    'role': status.role,
                    'balance_usd': float(status.balance_usd),
                    'consecutive_failures': status.consecutive_failures,
                    'last_successful_trade': status.last_successful_trade
                }
                for name, status in self.exchange_status.items()
            }
        }
    
    async def shutdown(self):
        """Shutdown the exchange manager"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 [EXCHANGE-MGR] Shutdown complete")

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status for professional monitoring"""
        active_exchanges = self.get_active_exchanges()

        return {
            'professional_mode': True,
            'institutional_grade': True,
            'capital_management_mode': self.capital_management_mode,
            'coinbase_primary_mode': self.coinbase_primary_mode,
            'active_exchanges': active_exchanges,
            'total_exchanges': len(self.exchange_status),
            'primary_exchange': self.get_primary_exchange(),
            'monitoring_active': self.monitoring_active,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
            'market_regime': self.market_regime.value,
            'professional_features': {
                'multi_timeframe_analysis': True,
                'arbitrage_detection': True,
                'advanced_order_types': ['TWAP', 'VWAP', 'ICEBERG'],
                'dynamic_position_sizing': True,
                'risk_management': True
            },
            'performance_metrics': {
                'arbitrage_opportunities_found': len(self.arbitrage_opportunities),
                'trade_history_size': len(self.trade_history),
                'performance_window_size': len(self.performance_window)
            },
            'exchange_details': {
                name: {
                    'active': status.active,
                    'role': status.role,
                    'priority': status.priority,
                    'balance_usd': float(status.balance_usd),
                    'consecutive_failures': status.consecutive_failures,
                    'last_successful_trade': status.last_successful_trade.isoformat() if status.last_successful_trade else None,
                    'latency_ms': status.latency_ms,
                    'uptime_percentage': status.uptime_percentage,
                    'trade_success_rate': status.trade_success_rate,
                    'supported_order_types': status.supported_order_types,
                    'liquidity_score': status.liquidity_score,
                    'data_quality_score': status.data_quality_score
                }
                for name, status in self.exchange_status.items()
            }
        }

# Maintain backward compatibility with existing code
EnhancedExchangeManager = ProfessionalExchangeManager
