#!/usr/bin/env python3
"""
Quick Balance Parsing Fix Test
Test the fixed decimal conversion for balance parsing
"""

import os
import sys
import asyncio
import logging

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_balance_parsing_fix():
    """Test the fixed balance parsing"""
    try:
        logger.info("[BALANCE-FIX-TEST] Testing fixed balance parsing")
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Get Bybit credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            logger.error("[BALANCE-FIX-TEST] Bybit credentials not found")
            return False
        
        # Initialize client
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        
        if not client.session:
            logger.error("[BALANCE-FIX-TEST] Failed to initialize client")
            return False
        
        logger.info("[BALANCE-FIX-TEST] Client initialized successfully")
        
        # Test 1: Get individual USDT balance
        logger.info("[TEST 1] Testing individual USDT balance...")
        usdt_balance = await client.get_balance('USDT')
        logger.info(f"[TEST 1] USDT balance: {usdt_balance}")
        
        # Test 2: Get all available balances
        logger.info("[TEST 2] Testing all available balances...")
        all_balances = await client.get_all_available_balances()
        logger.info(f"[TEST 2] Found {len(all_balances)} currencies with balances:")
        for currency, balance in all_balances.items():
            logger.info(f"[TEST 2]   {currency}: {balance:.6f}")
        
        # Test 3: Test multi-currency balance verification
        logger.info("[TEST 3] Testing multi-currency balance verification...")
        if len(all_balances) > 0:
            # Test with BTCUSDT
            balance_result = await client.find_sufficient_balance_for_trade(
                'BTCUSDT', 'buy', 10.0, 100000.0  # $10 at $100k BTC price
            )
            
            logger.info(f"[TEST 3] Balance verification result:")
            logger.info(f"[TEST 3]   Sufficient: {balance_result['sufficient']}")
            if balance_result['sufficient']:
                logger.info(f"[TEST 3]   Currency: {balance_result['currency']}")
                logger.info(f"[TEST 3]   Available: {balance_result['available_balance']:.6f}")
                logger.info(f"[TEST 3]   Conversion needed: {balance_result.get('conversion_needed', False)}")
            else:
                logger.info(f"[TEST 3]   Error: {balance_result.get('error', 'Unknown')}")
        
        # Determine success
        success = len(all_balances) > 0 or float(usdt_balance) > 0
        
        if success:
            logger.info("[BALANCE-FIX-TEST] SUCCESS - Balance parsing fix working!")
            logger.info(f"[BALANCE-FIX-TEST] Detected balances in {len(all_balances)} currencies")
        else:
            logger.error("[BALANCE-FIX-TEST] FAILED - Still no balances detected")
        
        return success
        
    except Exception as e:
        logger.error(f"[BALANCE-FIX-TEST] Test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("BALANCE PARSING FIX TEST")
    print("Testing the fixed decimal conversion for balance parsing")
    
    # Run the test
    result = asyncio.run(test_balance_parsing_fix())
    
    if result:
        print("\nSUCCESS: Balance parsing fix working!")
        print("Multi-currency system should now detect available balances!")
    else:
        print("\nFAILED: Balance parsing still has issues!")
        print("Further investigation required!")
    
    sys.exit(0 if result else 1)
