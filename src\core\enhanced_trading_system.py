#!/usr/bin/env python3
"""
Enhanced Trading System Integration
Main integration point for the enhanced data infrastructure and component system
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import traceback

# Import enhanced components
from .enhanced_system_integration import EnhancedSystemIntegration
from .execution_results_validator import ExecutionResultsValidator, ValidationResult
from ..testing.enhanced_integration_tests import EnhancedIntegrationTester

logger = logging.getLogger(__name__)

class EnhancedTradingSystem:
    """
    Enhanced Trading System with Professional Infrastructure
    
    Features:
    - Robust component discovery and management
    - Multi-source data aggregation with validation
    - Comprehensive health monitoring
    - Execution results validation
    - Automatic recovery mechanisms
    - Real-time performance monitoring
    """
    
    def __init__(self):
        self.integration_system: Optional[EnhancedSystemIntegration] = None
        self.initialized = False
        self.external_components: Dict[str, Any] = {}
        
        logger.info("🚀 [ENHANCED-SYSTEM] Enhanced trading system initialized")
    
    async def initialize_enhanced_system(self, external_components: Optional[Dict[str, Any]] = None) -> bool:
        """
        Initialize the enhanced trading system
        
        Args:
            external_components: External components from main.py (self.components)
            
        Returns:
            True if initialization successful
        """
        try:
            logger.info("🔧 [ENHANCED-SYSTEM] Initializing enhanced trading infrastructure...")
            
            # Store external components
            if external_components:
                self.external_components = external_components
                logger.info(f"📦 [ENHANCED-SYSTEM] Integrating {len(external_components)} external components")
            
            # Initialize integration system
            self.integration_system = EnhancedSystemIntegration()
            success = await self.integration_system.initialize(external_components)
            
            if success:
                self.initialized = True
                logger.info("✅ [ENHANCED-SYSTEM] Enhanced trading system initialized successfully")
                
                # Log system status
                await self._log_system_status()
                
                return True
            else:
                logger.error("❌ [ENHANCED-SYSTEM] Enhanced system initialization failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Initialization error: {e}")
            logger.error(traceback.format_exc())
            return False
    
    async def _log_system_status(self):
        """Log current system status"""
        try:
            if not self.integration_system:
                return
            
            status = self.integration_system.system_status
            logger.info(f"📊 [ENHANCED-SYSTEM] System Status:")
            logger.info(f"  • Operational: {status.is_operational}")
            logger.info(f"  • Active Components: {status.active_components}")
            logger.info(f"  • Healthy Components: {status.healthy_components}")
            logger.info(f"  • Data Sources: {status.data_sources_available}")
            logger.info(f"  • Component Registry: {status.component_registry_status}")
            logger.info(f"  • Health Monitor: {status.health_monitor_status}")
            logger.info(f"  • Data Aggregator: {status.data_aggregator_status}")
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error logging system status: {e}")
    
    async def get_enhanced_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get enhanced market data with multi-source aggregation
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Enhanced market data with validation and quality metrics
        """
        try:
            if not self.integration_system:
                logger.warning("⚠️ [ENHANCED-SYSTEM] Integration system not initialized")
                return None
            
            # Get aggregated price data
            price_data = await self.integration_system.get_aggregated_price_with_validation(symbol)
            
            if price_data:
                logger.debug(f"📈 [ENHANCED-SYSTEM] Enhanced data for {symbol}: "
                           f"Price=${price_data['price']:.2f}, Sources={price_data['source_count']}, "
                           f"Confidence={price_data['confidence']:.2f}")
            
            return price_data
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error getting enhanced market data for {symbol}: {e}")
            return None
    
    def validate_execution_result(self, execution_result: Dict[str, Any]) -> ValidationResult:
        """
        Validate execution result with enhanced validation
        
        Args:
            execution_result: Execution result to validate
            
        Returns:
            Comprehensive validation result
        """
        try:
            if not self.integration_system:
                logger.warning("⚠️ [ENHANCED-SYSTEM] Integration system not initialized")
                # Fallback to basic validation
                validator = ExecutionResultsValidator()
                return validator.validate(execution_result)
            
            return self.integration_system.validate_execution_result(execution_result)
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error validating execution result: {e}")
            # Return error validation result
            error_result = ValidationResult(is_valid=False)
            error_result.add_issue(
                field="validation",
                severity="critical",
                message=f"Validation exception: {str(e)}"
            )
            return error_result
    
    def get_healthy_exchange_clients(self) -> List[str]:
        """Get list of healthy exchange clients"""
        try:
            if not self.integration_system:
                return []
            
            from .component_registry import ComponentType
            return self.integration_system.get_healthy_components(ComponentType.EXCHANGE_CLIENT)
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error getting healthy exchange clients: {e}")
            return []
    
    def get_component(self, name: str) -> Optional[Any]:
        """Get a component by name"""
        try:
            if not self.integration_system:
                return self.external_components.get(name)
            
            return self.integration_system.get_component(name)
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error getting component {name}: {e}")
            return None
    
    async def get_system_health_report(self) -> Dict[str, Any]:
        """Get comprehensive system health report"""
        try:
            if not self.integration_system:
                return {
                    'error': 'Integration system not initialized',
                    'timestamp': datetime.now().isoformat()
                }
            
            return await self.integration_system.get_system_health_report()
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error getting health report: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def run_integration_tests(self) -> Dict[str, Any]:
        """Run comprehensive integration tests"""
        try:
            logger.info("🧪 [ENHANCED-SYSTEM] Running integration tests...")
            
            tester = EnhancedIntegrationTester()
            results = await tester.run_comprehensive_tests(self.external_components)
            
            # Log test summary
            summary = results.get('summary', {})
            success_rate = summary.get('overall_success_rate', 0)
            status = summary.get('status', 'UNKNOWN')
            
            logger.info(f"🧪 [ENHANCED-SYSTEM] Integration tests completed: {status}")
            logger.info(f"  • Success Rate: {success_rate:.1f}%")
            logger.info(f"  • Total Tests: {summary.get('total_tests', 0)}")
            logger.info(f"  • Passed: {summary.get('total_passed', 0)}")
            logger.info(f"  • Failed: {summary.get('total_failed', 0)}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error running integration tests: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def create_standard_execution_result(self, status: str, action: str, symbol: str,
                                       amount: Union[float, int], exchange: str,
                                       **kwargs) -> Dict[str, Any]:
        """Create a standardized execution result"""
        try:
            if not self.integration_system:
                # Fallback to basic creation
                validator = ExecutionResultsValidator()
                return validator.create_standard_result(status, action, symbol, amount, exchange, **kwargs)
            
            return self.integration_system.execution_validator.create_standard_result(
                status, action, symbol, amount, exchange, **kwargs
            )
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error creating standard result: {e}")
            return {
                'status': 'error',
                'error': f"Failed to create result: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }
    
    def create_error_result(self, error_message: str, **context) -> Dict[str, Any]:
        """Create a standardized error result"""
        try:
            if not self.integration_system:
                # Fallback to basic creation
                validator = ExecutionResultsValidator()
                return validator.create_error_result(error_message, **context)
            
            return self.integration_system.execution_validator.create_error_result(error_message, **context)
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error creating error result: {e}")
            return {
                'status': 'error',
                'error': f"Failed to create error result: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }
    
    def is_operational(self) -> bool:
        """Check if the enhanced system is operational"""
        try:
            if not self.integration_system:
                return False
            
            return self.integration_system.is_operational()
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error checking operational status: {e}")
            return False
    
    async def shutdown(self):
        """Gracefully shutdown the enhanced system"""
        try:
            logger.info("🛑 [ENHANCED-SYSTEM] Shutting down enhanced trading system...")
            
            if self.integration_system:
                await self.integration_system.shutdown()
                self.integration_system = None
            
            self.initialized = False
            
            logger.info("✅ [ENHANCED-SYSTEM] Enhanced system shutdown completed")
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error during shutdown: {e}")
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the enhanced system"""
        try:
            if not self.integration_system:
                return {'error': 'System not initialized'}
            
            health_report = await self.integration_system.get_system_health_report()
            
            # Extract key performance metrics
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'system_operational': self.integration_system.is_operational(),
                'component_health_rate': 0.0,
                'data_source_availability': 0.0,
                'avg_response_time': 0.0,
                'error_rate': 0.0
            }
            
            # Calculate component health rate
            component_summary = health_report.get('component_summary', {})
            total_components = component_summary.get('total', 0)
            healthy_components = component_summary.get('healthy', 0)
            
            if total_components > 0:
                metrics['component_health_rate'] = (healthy_components / total_components) * 100
            
            # Calculate data source availability
            data_sources = component_summary.get('data_sources', 0)
            metrics['data_source_availability'] = data_sources
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ [ENHANCED-SYSTEM] Error getting performance metrics: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# Global instance for easy access
enhanced_trading_system = EnhancedTradingSystem()

async def initialize_enhanced_infrastructure(external_components: Optional[Dict[str, Any]] = None) -> bool:
    """
    Initialize the enhanced trading infrastructure
    
    Args:
        external_components: External components from main.py
        
    Returns:
        True if initialization successful
    """
    return await enhanced_trading_system.initialize_enhanced_system(external_components)

def get_enhanced_system() -> EnhancedTradingSystem:
    """Get the global enhanced trading system instance"""
    return enhanced_trading_system
