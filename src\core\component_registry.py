#!/usr/bin/env python3
"""
Enhanced Component Registry for Professional Trading System
Provides robust component discovery, health validation, and fallback mechanisms
"""

import asyncio
import logging
import time
import inspect
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Type, Callable, Union, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import traceback
from pathlib import Path

logger = logging.getLogger(__name__)

class ComponentStatus(Enum):
    """Component health status enumeration"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    INITIALIZING = "initializing"
    UNKNOWN = "unknown"
    DISABLED = "disabled"

class ComponentType(Enum):
    """Component type classification"""
    EXCHANGE_CLIENT = "exchange_client"
    DATA_SOURCE = "data_source"
    NEURAL_COMPONENT = "neural_component"
    TRADING_ENGINE = "trading_engine"
    RISK_MANAGER = "risk_manager"
    MONITOR = "monitor"
    VALIDATOR = "validator"
    CRAWLER = "crawler"
    AGGREGATOR = "aggregator"
    EXECUTOR = "executor"

@dataclass
class ComponentHealth:
    """Comprehensive component health metrics"""
    status: ComponentStatus = ComponentStatus.UNKNOWN
    last_check: Optional[datetime] = None
    error_count: int = 0
    consecutive_failures: int = 0
    last_error: Optional[str] = None
    response_time_ms: float = 0.0
    uptime_percentage: float = 100.0
    success_rate: float = 100.0
    
    # Performance metrics
    avg_response_time: float = 0.0
    max_response_time: float = 0.0
    min_response_time: float = float('inf')
    total_requests: int = 0
    successful_requests: int = 0
    
    # Timestamps
    first_seen: Optional[datetime] = None
    last_success: Optional[datetime] = None
    last_failure: Optional[datetime] = None
    
    # Additional metadata
    version: Optional[str] = None
    capabilities: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    
    def update_success(self, response_time_ms: float = 0.0):
        """Update metrics after successful operation"""
        now = datetime.now()
        self.status = ComponentStatus.HEALTHY
        self.last_check = now
        self.last_success = now
        self.consecutive_failures = 0
        self.response_time_ms = response_time_ms
        
        # Update performance metrics
        self.total_requests += 1
        self.successful_requests += 1
        self.success_rate = (self.successful_requests / self.total_requests) * 100
        
        # Update response time metrics
        if response_time_ms > 0:
            if self.avg_response_time == 0:
                self.avg_response_time = response_time_ms
            else:
                self.avg_response_time = (self.avg_response_time + response_time_ms) / 2
            
            self.max_response_time = max(self.max_response_time, response_time_ms)
            self.min_response_time = min(self.min_response_time, response_time_ms)
    
    def update_failure(self, error_message: str):
        """Update metrics after failed operation"""
        now = datetime.now()
        self.last_check = now
        self.last_failure = now
        self.last_error = error_message
        self.error_count += 1
        self.consecutive_failures += 1
        
        # Update performance metrics
        self.total_requests += 1
        if self.total_requests > 0:
            self.success_rate = (self.successful_requests / self.total_requests) * 100
        
        # Determine status based on consecutive failures
        if self.consecutive_failures >= 5:
            self.status = ComponentStatus.FAILED
        elif self.consecutive_failures >= 2:
            self.status = ComponentStatus.DEGRADED
        else:
            self.status = ComponentStatus.HEALTHY

@dataclass
class ComponentMetadata:
    """Component metadata and configuration"""
    name: str
    component_type: ComponentType
    instance: Any
    priority: int = 50  # 0-100, higher is more important
    required: bool = False
    auto_restart: bool = True
    health_check_interval: int = 300  # seconds
    timeout: int = 30  # seconds
    max_retries: int = 3
    fallback_components: List[str] = field(default_factory=list)
    
    # Discovery metadata
    module_path: Optional[str] = None
    class_name: Optional[str] = None
    initialization_args: Dict[str, Any] = field(default_factory=dict)
    initialization_kwargs: Dict[str, Any] = field(default_factory=dict)
    
    # Health check configuration
    health_check_method: Optional[str] = None
    custom_health_check: Optional[Callable] = None
    
    # Performance requirements
    max_response_time_ms: float = 5000.0
    min_success_rate: float = 95.0

class ComponentRegistry:
    """
    Enhanced Component Registry with Discovery and Health Management
    
    Features:
    - Automatic component discovery and registration
    - Comprehensive health monitoring and validation
    - Intelligent fallback mechanisms
    - Performance tracking and optimization
    - Real-time component status reporting
    """
    
    def __init__(self):
        self.components: Dict[str, ComponentMetadata] = {}
        self.health_status: Dict[str, ComponentHealth] = {}
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Discovery configuration
        self.discovery_paths = [
            'src.exchanges',
            'src.data_feeds',
            'src.neural',
            'src.trading',
            'src.monitoring',
            'src.quantum_trading'
        ]
        
        # Health monitoring
        self.health_check_interval = 60  # seconds
        self.component_timeouts = defaultdict(lambda: 30)  # seconds
        
        # Performance tracking
        self.performance_history = defaultdict(lambda: deque(maxlen=1000))
        self.alert_thresholds = {
            'response_time_ms': 5000,
            'success_rate': 95.0,
            'consecutive_failures': 3
        }
        
        # Event callbacks
        self.event_callbacks = defaultdict(list)
        
        logger.info("🔧 [REGISTRY] Enhanced component registry initialized")
    
    async def initialize(self) -> bool:
        """Initialize the component registry"""
        try:
            logger.info("🚀 [REGISTRY] Initializing component registry...")
            
            # Start health monitoring
            await self.start_monitoring()
            
            logger.info("✅ [REGISTRY] Component registry initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ [REGISTRY] Failed to initialize: {e}")
            return False
    
    async def discover_and_register_components(self) -> Dict[str, bool]:
        """Discover and register all available components"""
        results = {}
        
        try:
            logger.info("🔍 [REGISTRY] Starting component discovery...")
            
            # Discover exchange clients
            exchange_results = await self._discover_exchange_clients()
            results.update(exchange_results)
            
            # Discover data sources
            data_source_results = await self._discover_data_sources()
            results.update(data_source_results)
            
            # Discover neural components
            neural_results = await self._discover_neural_components()
            results.update(neural_results)
            
            # Discover trading components
            trading_results = await self._discover_trading_components()
            results.update(trading_results)
            
            successful = sum(1 for success in results.values() if success)
            total = len(results)
            
            logger.info(f"✅ [REGISTRY] Component discovery completed: {successful}/{total} successful")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ [REGISTRY] Component discovery failed: {e}")
            return results
    
    async def register_component(self, name: str, instance: Any, 
                                component_type: ComponentType,
                                **kwargs) -> bool:
        """Register a component with the registry"""
        try:
            # Create metadata
            metadata = ComponentMetadata(
                name=name,
                component_type=component_type,
                instance=instance,
                **kwargs
            )
            
            # Create health status
            health = ComponentHealth(
                status=ComponentStatus.INITIALIZING,
                first_seen=datetime.now()
            )
            
            # Store component
            self.components[name] = metadata
            self.health_status[name] = health
            
            # Perform initial health check
            await self._perform_health_check(name)
            
            logger.info(f"✅ [REGISTRY] Registered component: {name} ({component_type.value})")
            
            # Trigger registration event
            await self._trigger_event('component_registered', name, metadata)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [REGISTRY] Failed to register {name}: {e}")
            return False

    async def _discover_exchange_clients(self) -> Dict[str, bool]:
        """Discover and register exchange clients"""
        results = {}

        try:
            # Exchange client patterns to discover
            exchange_patterns = [
                ('bybit_client', 'src.exchanges.bybit_client_fixed', 'BybitClientFixed'),
                ('coinbase_client', 'src.exchanges.coinbase_enhanced', 'CoinbaseEnhancedClient'),
                ('binance_client', 'src.exchanges.binance_client', 'BinanceClient'),
                ('bybit_session', 'src.exchanges.bybit_session', 'BybitSession'),
                ('bybit_validator', 'src.exchanges.bybit_order_validator', 'BybitOrderValidator')
            ]

            for name, module_path, class_name in exchange_patterns:
                try:
                    # Try to import and instantiate
                    module = __import__(module_path, fromlist=[class_name])
                    component_class = getattr(module, class_name)

                    # Check if already instantiated elsewhere
                    instance = None
                    if hasattr(self, 'external_components'):
                        instance = self.external_components.get(name)

                    if not instance:
                        # Create new instance with safe defaults
                        instance = component_class()

                    # Register with high priority
                    success = await self.register_component(
                        name=name,
                        instance=instance,
                        component_type=ComponentType.EXCHANGE_CLIENT,
                        priority=90,
                        required=True,
                        health_check_method='test_connection',
                        max_response_time_ms=10000
                    )

                    results[name] = success

                except Exception as e:
                    logger.debug(f"Could not discover {name}: {e}")
                    results[name] = False

            return results

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Exchange client discovery failed: {e}")
            return results

    async def _discover_data_sources(self) -> Dict[str, bool]:
        """Discover and register data sources"""
        results = {}

        try:
            # Data source patterns to discover
            data_source_patterns = [
                ('market_data_aggregator', 'src.data_feeds.market_data', 'MarketDataAggregator'),
                ('binance_feed', 'src.data_feeds.binance_feed', 'BinanceDataFeed'),
                ('coinbase_feed', 'src.data_feeds.coinbase_feed', 'CoinbaseDataFeed'),
                ('internet_crawler', 'src.data_feeds.internet_crawler', 'InternetCrawler'),
                ('data_validator', 'src.data_feeds.data_validator', 'DataValidator'),
                ('websocket_manager', 'src.data_feeds.websocket_manager', 'WebSocketManager'),
                ('cache_manager', 'src.data_feeds.cache', 'CacheManager'),
                ('monitoring_service', 'src.data_feeds.monitoring', 'DataFeedMonitor')
            ]

            for name, module_path, class_name in data_source_patterns:
                try:
                    # Try to import and instantiate
                    module = __import__(module_path, fromlist=[class_name])
                    component_class = getattr(module, class_name)

                    # Create instance with appropriate configuration
                    if name == 'market_data_aggregator':
                        instance = component_class({'binance': {}, 'coinbase': {}})
                    elif name == 'internet_crawler':
                        instance = component_class()
                    else:
                        instance = component_class()

                    # Register with medium priority
                    success = await self.register_component(
                        name=name,
                        instance=instance,
                        component_type=ComponentType.DATA_SOURCE,
                        priority=70,
                        required=False,
                        health_check_method='get_health_status' if hasattr(instance, 'get_health_status') else None,
                        max_response_time_ms=5000
                    )

                    results[name] = success

                except Exception as e:
                    logger.debug(f"Could not discover {name}: {e}")
                    results[name] = False

            return results

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Data source discovery failed: {e}")
            return results

    async def _discover_neural_components(self) -> Dict[str, bool]:
        """Discover and register neural components"""
        results = {}

        try:
            # Neural component patterns to discover
            neural_patterns = [
                ('rl_agent_manager', 'src.neural.rl_agent', 'RLAgentManager'),
                ('hybrid_agent', 'src.neural.hybrid_agent', 'HybridTradingAgent'),
                ('price_predictor', 'src.neural.predictors', 'AdvancedPricePredictor'),
                ('market_anomaly_detector', 'src.neural.market_monitor', 'MarketAnomalyDetector'),
                ('lstm_processor', 'src.neural.lstm_processor', 'LSTMTradingProcessor'),
                ('circuit_breaker', 'src.neural.market_monitor', 'CircuitBreaker'),
                ('liquidity_analyzer', 'src.neural.liquidity', 'LiquidityAnalyzer'),
                ('arbitrage_detector', 'src.neural.arbitrage', 'CrossVenueArbitrageDetector'),
                ('enhanced_risk_predictor', 'src.neural.enhanced_risk_predictor', 'EnhancedRiskPredictor'),
                ('enhanced_profit_predictor', 'src.neural.enhanced_profit_predictor', 'EnhancedProfitPredictor'),
                ('advanced_neural_strategy', 'src.neural.advanced_neural_strategy', 'AdvancedNeuralStrategy')
            ]

            for name, module_path, class_name in neural_patterns:
                try:
                    # Try to import and instantiate
                    module = __import__(module_path, fromlist=[class_name])
                    component_class = getattr(module, class_name)

                    # Create instance with safe defaults
                    if name == 'hybrid_agent':
                        config = {
                            'state_dim': 100,
                            'action_dim': 3,
                            'learning_rate': 0.001,
                            'batch_size': 32
                        }
                        instance = component_class(config)
                    elif name == 'circuit_breaker':
                        instance = component_class(volatility_threshold=0.50, price_drop_limit=0.20)
                    elif name == 'arbitrage_detector':
                        instance = component_class(venues=['coinbase', 'bybit'], min_spread=0.0005)
                    else:
                        instance = component_class()

                    # Register with medium priority
                    success = await self.register_component(
                        name=name,
                        instance=instance,
                        component_type=ComponentType.NEURAL_COMPONENT,
                        priority=60,
                        required=False,
                        max_response_time_ms=2000  # Neural components should be fast
                    )

                    results[name] = success

                except Exception as e:
                    logger.debug(f"Could not discover {name}: {e}")
                    results[name] = False

            return results

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Neural component discovery failed: {e}")
            return results

    async def _discover_trading_components(self) -> Dict[str, bool]:
        """Discover and register trading components"""
        results = {}

        try:
            # Trading component patterns to discover
            trading_patterns = [
                ('enhanced_exchange_manager', 'src.trading.enhanced_exchange_manager', 'ProfessionalExchangeManager'),
                ('enhanced_signal_generator', 'src.trading.enhanced_signal_generator', 'EnhancedSignalGenerator'),
                ('enhanced_capital_manager', 'src.trading.enhanced_capital_manager', 'EnhancedCapitalManager'),
                ('high_speed_executor', 'src.trading.high_speed_executor', 'HighSpeedExecutor'),
                ('smart_order_router', 'src.trading.smart_order_router', 'SmartOrderRouter'),
                ('trade_logger', 'src.trading.trade_logger', 'EnhancedTradeLogger'),
                ('real_money_validator', 'src.trading.real_money_validator', 'RealMoneyValidator')
            ]

            for name, module_path, class_name in trading_patterns:
                try:
                    # Try to import and instantiate
                    module = __import__(module_path, fromlist=[class_name])
                    component_class = getattr(module, class_name)

                    # Create instance with safe defaults
                    instance = component_class()

                    # Register with high priority for trading components
                    success = await self.register_component(
                        name=name,
                        instance=instance,
                        component_type=ComponentType.TRADING_ENGINE,
                        priority=80,
                        required=False,
                        max_response_time_ms=3000
                    )

                    results[name] = success

                except Exception as e:
                    logger.debug(f"Could not discover {name}: {e}")
                    results[name] = False

            return results

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Trading component discovery failed: {e}")
            return results

    async def start_monitoring(self) -> bool:
        """Start component health monitoring"""
        try:
            if self.monitoring_active:
                logger.warning("🔍 [REGISTRY] Monitoring already active")
                return True

            self.monitoring_active = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())

            logger.info("🔍 [REGISTRY] Component health monitoring started")
            return True

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Failed to start monitoring: {e}")
            return False

    async def stop_monitoring(self):
        """Stop component health monitoring"""
        try:
            self.monitoring_active = False

            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
                self.monitoring_task = None

            logger.info("🛑 [REGISTRY] Component health monitoring stopped")

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Error stopping monitoring: {e}")

    async def _monitoring_loop(self):
        """Main monitoring loop"""
        try:
            while self.monitoring_active:
                try:
                    # Perform health checks on all components
                    await self._perform_all_health_checks()

                    # Check for alerts
                    await self._check_alerts()

                    # Update performance metrics
                    await self._update_performance_metrics()

                    # Sleep until next check
                    await asyncio.sleep(self.health_check_interval)

                except Exception as e:
                    logger.error(f"❌ [REGISTRY] Error in monitoring loop: {e}")
                    await asyncio.sleep(30)  # Shorter sleep on error

        except asyncio.CancelledError:
            logger.debug("🔍 [REGISTRY] Monitoring loop cancelled")
        except Exception as e:
            logger.error(f"❌ [REGISTRY] Monitoring loop failed: {e}")

    async def _perform_all_health_checks(self):
        """Perform health checks on all registered components"""
        try:
            tasks = []
            for component_name in self.components.keys():
                task = asyncio.create_task(self._perform_health_check(component_name))
                tasks.append(task)

            # Execute all health checks in parallel
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Error performing health checks: {e}")

    async def _perform_health_check(self, component_name: str) -> bool:
        """Perform health check on a specific component"""
        try:
            if component_name not in self.components:
                return False

            metadata = self.components[component_name]
            health = self.health_status[component_name]

            start_time = time.time()

            # Perform health check based on component type
            success = False
            error_message = None

            try:
                # Try custom health check method first
                if metadata.custom_health_check:
                    success = await metadata.custom_health_check(metadata.instance)
                elif metadata.health_check_method:
                    # Try named method on instance
                    method = getattr(metadata.instance, metadata.health_check_method, None)
                    if method:
                        if asyncio.iscoroutinefunction(method):
                            result = await method()
                        else:
                            result = method()
                        success = bool(result)
                    else:
                        # Method doesn't exist, assume healthy if instance exists
                        success = metadata.instance is not None
                else:
                    # Default health check - just verify instance exists
                    success = metadata.instance is not None

            except Exception as e:
                success = False
                error_message = str(e)

            # Calculate response time
            response_time_ms = (time.time() - start_time) * 1000

            # Update health status
            if success:
                health.update_success(response_time_ms)
            else:
                health.update_failure(error_message or "Health check failed")

            # Log significant status changes
            if health.consecutive_failures == 1 and not success:
                logger.warning(f"⚠️ [REGISTRY] Component {component_name} health check failed: {error_message}")
            elif health.consecutive_failures == 0 and success and health.error_count > 0:
                logger.info(f"✅ [REGISTRY] Component {component_name} health restored")

            return success

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Error checking health of {component_name}: {e}")
            return False

    async def _check_alerts(self):
        """Check for alert conditions and trigger notifications"""
        try:
            for component_name, health in self.health_status.items():
                metadata = self.components.get(component_name)
                if not metadata:
                    continue

                # Check response time alerts
                if health.avg_response_time > self.alert_thresholds['response_time_ms']:
                    await self._trigger_alert('high_response_time', component_name, {
                        'response_time': health.avg_response_time,
                        'threshold': self.alert_thresholds['response_time_ms']
                    })

                # Check success rate alerts
                if health.success_rate < self.alert_thresholds['success_rate']:
                    await self._trigger_alert('low_success_rate', component_name, {
                        'success_rate': health.success_rate,
                        'threshold': self.alert_thresholds['success_rate']
                    })

                # Check consecutive failures
                if health.consecutive_failures >= self.alert_thresholds['consecutive_failures']:
                    await self._trigger_alert('consecutive_failures', component_name, {
                        'consecutive_failures': health.consecutive_failures,
                        'threshold': self.alert_thresholds['consecutive_failures']
                    })

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Error checking alerts: {e}")

    async def _update_performance_metrics(self):
        """Update performance metrics for all components"""
        try:
            for component_name, health in self.health_status.items():
                # Store performance history
                self.performance_history[component_name].append({
                    'timestamp': datetime.now(),
                    'response_time': health.response_time_ms,
                    'success_rate': health.success_rate,
                    'status': health.status.value
                })

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Error updating performance metrics: {e}")

    async def _trigger_alert(self, alert_type: str, component_name: str, data: Dict[str, Any]):
        """Trigger an alert for a component"""
        try:
            alert = {
                'type': alert_type,
                'component': component_name,
                'timestamp': datetime.now(),
                'data': data
            }

            logger.warning(f"🚨 [REGISTRY] Alert: {alert_type} for {component_name}: {data}")

            # Trigger event callbacks
            await self._trigger_event('alert', component_name, alert)

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Error triggering alert: {e}")

    async def _trigger_event(self, event_type: str, component_name: str, data: Any):
        """Trigger event callbacks"""
        try:
            callbacks = self.event_callbacks.get(event_type, [])
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(component_name, data)
                    else:
                        callback(component_name, data)
                except Exception as e:
                    logger.error(f"❌ [REGISTRY] Error in event callback: {e}")

        except Exception as e:
            logger.error(f"❌ [REGISTRY] Error triggering event: {e}")

    def get_component(self, name: str) -> Optional[Any]:
        """Get a component instance by name"""
        metadata = self.components.get(name)
        return metadata.instance if metadata else None

    def get_healthy_components(self, component_type: Optional[ComponentType] = None) -> List[str]:
        """Get list of healthy component names, optionally filtered by type"""
        healthy = []

        for name, health in self.health_status.items():
            if health.status == ComponentStatus.HEALTHY:
                if component_type is None:
                    healthy.append(name)
                else:
                    metadata = self.components.get(name)
                    if metadata and metadata.component_type == component_type:
                        healthy.append(name)

        return healthy

    def get_component_status(self, name: str) -> Optional[ComponentHealth]:
        """Get health status for a specific component"""
        return self.health_status.get(name)

    def get_all_component_status(self) -> Dict[str, Dict[str, Any]]:
        """Get comprehensive status report for all components"""
        status_report = {}

        for name, health in self.health_status.items():
            metadata = self.components.get(name)

            status_report[name] = {
                'type': metadata.component_type.value if metadata else 'unknown',
                'status': health.status.value,
                'priority': metadata.priority if metadata else 0,
                'required': metadata.required if metadata else False,
                'last_check': health.last_check.isoformat() if health.last_check else None,
                'error_count': health.error_count,
                'consecutive_failures': health.consecutive_failures,
                'success_rate': health.success_rate,
                'avg_response_time': health.avg_response_time,
                'last_error': health.last_error,
                'uptime_percentage': health.uptime_percentage,
                'capabilities': health.capabilities
            }

        return status_report
