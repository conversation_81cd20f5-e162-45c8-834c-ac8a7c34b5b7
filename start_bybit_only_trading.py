#!/usr/bin/env python3
"""
Start Bybit-Only Trading - IMMEDIATE EXECUTION
CRITICAL: This bypasses Coinbase completely and executes real trades on Bybit only
"""

import os
import sys
import asyncio
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def setup_bybit_only_environment():
    """Setup environment for Bybit-only trading"""
    print("🎯 [BYBIT-ONLY] Setting up Bybit-only trading environment...")
    
    # Force Bybit-only mode
    os.environ["BYBIT_ONLY_MODE"] = "true"
    os.environ["COINBASE_ENABLED"] = "false"
    os.environ["COINBASE_DISABLED"] = "true"
    os.environ["PRIMARY_EXCHANGE"] = "bybit"
    os.environ["SECONDARY_EXCHANGE"] = "none"
    os.environ["FORCE_BYBIT_TRADING"] = "true"
    
    # Force live trading mode
    os.environ["LIVE_TRADING"] = "true"
    os.environ["REAL_MONEY_TRADING"] = "true"
    os.environ["DEMO_MODE"] = "false"
    os.environ["DRY_RUN"] = "false"
    os.environ["TRADING_MODE"] = "live"
    os.environ["SANDBOX"] = "false"
    os.environ["TESTNET"] = "false"
    os.environ["ENVIRONMENT"] = "production"
    
    print("✅ [BYBIT-ONLY] Environment configured for Bybit-only live trading")

def test_bybit_connection():
    """Test Bybit connection and verify trading readiness"""
    print("🔧 [BYBIT-TEST] Testing Bybit connection...")
    
    try:
        from pybit.unified_trading import HTTP
        from dotenv import load_dotenv
        
        load_dotenv()
        
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        testnet = os.getenv('BYBIT_TESTNET', 'false').lower() == 'true'
        
        if testnet:
            print("❌ [BYBIT-TEST] CRITICAL: Testnet mode detected")
            return False
        
        # Initialize session with timestamp sync
        session = HTTP(api_key=api_key, api_secret=api_secret, testnet=False, recv_window=10000)
        
        # Test connection
        account_info = session.get_account_info()
        if account_info.get('retCode') != 0:
            print(f"❌ [BYBIT-TEST] Connection failed: {account_info}")
            return False
        
        print("✅ [BYBIT-TEST] Connection successful")
        
        # Get balance
        balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
        if balance_response.get('retCode') == 0:
            balance = float(balance_response['result']['list'][0]['coin'][0]['walletBalance'])
            print(f"💰 [BYBIT-TEST] USDT Balance: ${balance:.2f}")
            
            if balance >= 15.0:
                print("✅ [BYBIT-TEST] Sufficient balance for trading")
                return True
            else:
                print(f"⚠️ [BYBIT-TEST] Low balance: ${balance:.2f} (minimum $15 recommended)")
                return True  # Still allow trading with low balance
        else:
            print(f"❌ [BYBIT-TEST] Balance check failed: {balance_response}")
            return False
            
    except Exception as e:
        print(f"❌ [BYBIT-TEST] Connection test failed: {e}")
        return False

def execute_immediate_test_trade():
    """Execute an immediate test trade to verify system is working"""
    print("🚨 [TEST-TRADE] Executing immediate test trade...")
    
    try:
        from pybit.unified_trading import HTTP
        from dotenv import load_dotenv
        
        load_dotenv()
        
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        session = HTTP(api_key=api_key, api_secret=api_secret, testnet=False, recv_window=10000)
        
        # Get current balance
        balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
        if balance_response.get('retCode') != 0:
            print(f"❌ [TEST-TRADE] Cannot get balance: {balance_response}")
            return False
        
        balance = float(balance_response['result']['list'][0]['coin'][0]['walletBalance'])
        print(f"💰 [TEST-TRADE] Current balance: ${balance:.2f} USDT")
        
        # Calculate trade amount (use $15 or 20% of balance, whichever is smaller)
        trade_amount = min(15.0, balance * 0.20)
        
        if trade_amount < 15.0:
            print(f"⚠️ [TEST-TRADE] Insufficient balance for minimum trade: ${trade_amount:.2f}")
            return False
        
        print(f"📊 [TEST-TRADE] Trade amount: ${trade_amount:.2f} USDT")
        
        # Execute BUY order for BTC
        print("🚨 [TEST-TRADE] Executing REAL BUY order for BTCUSDT...")
        
        buy_order = session.place_order(
            category="spot",
            symbol="BTCUSDT",
            side="Buy",
            orderType="Market",
            qty=f"{trade_amount:.2f}",
            isLeverage=0,
            orderFilter="Order"
        )
        
        if buy_order.get('retCode') == 0:
            order_id = buy_order['result']['orderId']
            print(f"✅ [TEST-TRADE] BUY order executed successfully!")
            print(f"📋 [TEST-TRADE] Order ID: {order_id}")
            print(f"💰 [TEST-TRADE] Amount: ${trade_amount:.2f} USDT")
            print(f"📈 [TEST-TRADE] Symbol: BTCUSDT")
            
            # Wait for order to settle
            time.sleep(3)
            
            # Check new balance
            new_balance_response = session.get_wallet_balance(accountType="UNIFIED", coin="USDT")
            if new_balance_response.get('retCode') == 0:
                new_balance = float(new_balance_response['result']['list'][0]['coin'][0]['walletBalance'])
                balance_change = balance - new_balance
                print(f"💰 [TEST-TRADE] New balance: ${new_balance:.2f} USDT")
                print(f"📊 [TEST-TRADE] Balance change: ${balance_change:.2f} USDT")
                
                if balance_change > 1.0:
                    print("✅ [TEST-TRADE] REAL MONEY TRADING CONFIRMED - Balance changed!")
                    return True
                else:
                    print("⚠️ [TEST-TRADE] Small balance change detected")
                    return True
            
            return True
        else:
            print(f"❌ [TEST-TRADE] BUY order failed: {buy_order}")
            return False
            
    except Exception as e:
        print(f"❌ [TEST-TRADE] Test trade failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def start_main_trading_system():
    """Start the main trading system in Bybit-only mode"""
    print("🚀 [MAIN-SYSTEM] Starting main trading system...")
    
    try:
        # Import and run the main trading system
        import main
        
        # The main system will now run in Bybit-only mode due to environment variables
        await main.main()
        
    except Exception as e:
        print(f"❌ [MAIN-SYSTEM] Main trading system failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution"""
    print("=" * 60)
    print("🎯 BYBIT-ONLY TRADING SYSTEM")
    print("🚨 IMMEDIATE REAL MONEY TRADING")
    print("🚫 COINBASE DISABLED (401 ERRORS)")
    print("=" * 60)
    
    # Step 1: Setup environment
    setup_bybit_only_environment()
    
    # Step 2: Test Bybit connection
    if not test_bybit_connection():
        print("❌ [FAILED] Bybit connection test failed")
        return False
    
    # Step 3: Execute immediate test trade
    print("\n🚨 [IMMEDIATE] Executing test trade to verify system...")
    if not execute_immediate_test_trade():
        print("❌ [FAILED] Test trade failed")
        return False
    
    print("\n✅ [SUCCESS] Test trade completed - system verified!")
    print("🚀 [STARTING] Starting continuous trading system...")
    
    # Step 4: Start main trading system
    try:
        asyncio.run(start_main_trading_system())
    except KeyboardInterrupt:
        print("\n🛑 [STOPPED] Trading stopped by user")
    except Exception as e:
        print(f"\n❌ [ERROR] Trading system error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚨 WARNING: This will execute REAL MONEY trades on Bybit")
    print("🚨 WARNING: Coinbase is disabled due to 401 authentication errors")
    print("🚨 WARNING: Press Ctrl+C to stop at any time")
    
    # Give user a moment to cancel
    print("\n⏳ Starting in 5 seconds...")
    for i in range(5, 0, -1):
        print(f"⏳ {i}...")
        time.sleep(1)
    
    print("\n🚀 STARTING BYBIT-ONLY TRADING...")
    
    success = main()
    if success:
        print("\n✅ BYBIT-ONLY TRADING COMPLETED")
    else:
        print("\n❌ BYBIT-ONLY TRADING FAILED")
    
    sys.exit(0 if success else 1)
