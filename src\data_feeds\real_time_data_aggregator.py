#!/usr/bin/env python3
"""
Real-Time Data Aggregation Infrastructure
Comprehensive multi-source data collection for live trading operations
"""

import asyncio
import aiohttp
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import statistics
from decimal import Decimal

logger = logging.getLogger(__name__)

class DataSourceStatus(Enum):
    """Data source status enumeration"""
    ACTIVE = "active"
    DEGRADED = "degraded"
    FAILED = "failed"
    TIMEOUT = "timeout"
    RATE_LIMITED = "rate_limited"

@dataclass
class RealTimeDataPoint:
    """Real-time data point structure"""
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    source: str
    bid: Optional[float] = None
    ask: Optional[float] = None
    high_24h: Optional[float] = None
    low_24h: Optional[float] = None
    change_24h: Optional[float] = None
    confidence: float = 1.0
    latency_ms: float = 0.0

@dataclass
class AggregatedMarketData:
    """Aggregated market data from multiple sources"""
    symbol: str
    consensus_price: float
    price_range: Tuple[float, float]
    weighted_price: float
    confidence_score: float
    source_count: int
    timestamp: datetime
    individual_sources: List[RealTimeDataPoint] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class RealTimeDataAggregator:
    """
    Comprehensive real-time data aggregation from multiple sources
    
    Features:
    - Exchange APIs (Bybit, Coinbase, Binance)
    - Alternative APIs (CoinGecko, DEXScreener, CoinMarketCap)
    - Web crawlers for price discovery
    - News and sentiment crawlers
    - Intelligent data routing and validation
    """
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.data_cache: Dict[str, AggregatedMarketData] = {}
        self.cache_ttl = 30  # seconds
        
        # Data source configurations
        self.data_sources = {
            'bybit_api': {
                'url': 'https://api.bybit.com/v5/market/tickers',
                'priority': 1.0,
                'timeout': 5,
                'status': DataSourceStatus.ACTIVE
            },
            'coinbase_api': {
                'url': 'https://api.exchange.coinbase.com/products/{symbol}/ticker',
                'priority': 0.9,
                'timeout': 5,
                'status': DataSourceStatus.ACTIVE
            },
            'binance_api': {
                'url': 'https://api.binance.com/api/v3/ticker/24hr',
                'priority': 0.8,
                'timeout': 5,
                'status': DataSourceStatus.ACTIVE
            },
            'coingecko_api': {
                'url': 'https://api.coingecko.com/api/v3/simple/price',
                'priority': 0.6,
                'timeout': 10,
                'status': DataSourceStatus.ACTIVE
            },
            'dexscreener_api': {
                'url': 'https://api.dexscreener.com/latest/dex/tokens/{token}',
                'priority': 0.5,
                'timeout': 10,
                'status': DataSourceStatus.ACTIVE
            }
        }
        
        # Performance tracking
        self.source_performance = defaultdict(lambda: {
            'response_time': 0.0,
            'success_rate': 100.0,
            'error_count': 0,
            'total_requests': 0,
            'last_success': None
        })
        
        # Rate limiting
        self.rate_limiters = defaultdict(lambda: deque(maxlen=60))  # 60 requests per minute
        
        logger.info("🔗 [DATA-AGGREGATOR] Real-time data aggregator initialized")
    
    async def initialize(self) -> bool:
        """Initialize the data aggregator"""
        try:
            # Create HTTP session with optimized settings
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=20,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                connector=connector,
                headers={
                    'User-Agent': 'AutoGPT-Trader/1.0',
                    'Accept': 'application/json'
                }
            )
            
            logger.info("✅ [DATA-AGGREGATOR] Initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ [DATA-AGGREGATOR] Failed to initialize: {e}")
            return False
    
    async def get_comprehensive_market_data(self, symbol: str) -> Optional[AggregatedMarketData]:
        """Get comprehensive market data from all available sources"""
        try:
            # Check cache first
            if symbol in self.data_cache:
                cached_data = self.data_cache[symbol]
                age = (datetime.now() - cached_data.timestamp).total_seconds()
                if age < self.cache_ttl:
                    logger.debug(f"📊 [CACHE-HIT] Using cached data for {symbol} (age: {age:.1f}s)")
                    return cached_data
            
            # Fetch from all available sources
            source_data = await self._fetch_from_all_sources(symbol)
            
            if not source_data:
                logger.warning(f"⚠️ [NO-DATA] No data available for {symbol}")
                return None
            
            # Aggregate and validate data
            aggregated = self._aggregate_market_data(symbol, source_data)
            
            # Cache the result
            self.data_cache[symbol] = aggregated
            
            logger.info(f"📊 [DATA-SUCCESS] {symbol}: ${aggregated.consensus_price:.2f} "
                       f"from {aggregated.source_count} sources (confidence: {aggregated.confidence_score:.2f})")
            
            return aggregated
            
        except Exception as e:
            logger.error(f"❌ [DATA-ERROR] Error getting market data for {symbol}: {e}")
            return None
    
    async def _fetch_from_all_sources(self, symbol: str) -> List[RealTimeDataPoint]:
        """Fetch data from all available sources in parallel"""
        tasks = []
        
        for source_name, config in self.data_sources.items():
            if config['status'] == DataSourceStatus.ACTIVE and self._check_rate_limit(source_name):
                task = asyncio.create_task(
                    self._fetch_from_source(source_name, config, symbol)
                )
                tasks.append(task)
        
        # Execute all requests in parallel with timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=15.0
            )
        except asyncio.TimeoutError:
            logger.warning(f"⏰ [TIMEOUT] Data fetching timed out for {symbol}")
            results = []
        
        # Filter successful results
        data_points = []
        for result in results:
            if isinstance(result, RealTimeDataPoint):
                data_points.append(result)
            elif isinstance(result, Exception):
                logger.debug(f"🔍 [SOURCE-ERROR] {result}")
        
        return data_points
    
    async def _fetch_from_source(self, source_name: str, config: Dict[str, Any], symbol: str) -> Optional[RealTimeDataPoint]:
        """Fetch data from a specific source"""
        start_time = time.time()
        
        try:
            # Update rate limiter
            self.rate_limiters[source_name].append(time.time())
            
            # Prepare URL
            url = self._prepare_url(config['url'], symbol)
            
            # Make request
            async with self.session.get(url, timeout=config['timeout']) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Parse response based on source
                    data_point = self._parse_response(source_name, data, symbol)
                    
                    if data_point:
                        # Calculate latency
                        latency_ms = (time.time() - start_time) * 1000
                        data_point.latency_ms = latency_ms
                        
                        # Update performance metrics
                        self._update_source_performance(source_name, True, latency_ms)
                        
                        return data_point
                else:
                    logger.warning(f"⚠️ [HTTP-ERROR] {source_name}: HTTP {response.status}")
                    self._update_source_performance(source_name, False)
                    
        except asyncio.TimeoutError:
            logger.warning(f"⏰ [TIMEOUT] {source_name} timed out for {symbol}")
            self._update_source_performance(source_name, False)
        except Exception as e:
            logger.debug(f"🔍 [SOURCE-ERROR] {source_name}: {e}")
            self._update_source_performance(source_name, False)
        
        return None
    
    def _prepare_url(self, url_template: str, symbol: str) -> str:
        """Prepare URL with symbol substitution"""
        try:
            # Handle different symbol formats
            symbol_formats = {
                'symbol': symbol,
                'token': symbol.replace('USDT', '').replace('USD', ''),
                'base': symbol.split('USDT')[0] if 'USDT' in symbol else symbol.split('USD')[0],
                'quote': 'USDT' if 'USDT' in symbol else 'USD'
            }
            
            # Replace placeholders
            url = url_template
            for key, value in symbol_formats.items():
                url = url.replace(f'{{{key}}}', value)
            
            return url
            
        except Exception as e:
            logger.error(f"❌ [URL-ERROR] Error preparing URL: {e}")
            return url_template
    
    def _parse_response(self, source_name: str, data: Dict[str, Any], symbol: str) -> Optional[RealTimeDataPoint]:
        """Parse API response into standardized data point"""
        try:
            if source_name == "bybit_api":
                return self._parse_bybit_response(data, symbol)
            elif source_name == "coinbase_api":
                return self._parse_coinbase_response(data, symbol)
            elif source_name == "binance_api":
                return self._parse_binance_response(data, symbol)
            elif source_name == "coingecko_api":
                return self._parse_coingecko_response(data, symbol)
            elif source_name == "dexscreener_api":
                return self._parse_dexscreener_response(data, symbol)
            else:
                logger.warning(f"⚠️ [UNKNOWN-SOURCE] {source_name}")
                return None
                
        except Exception as e:
            logger.error(f"❌ [PARSE-ERROR] {source_name}: {e}")
            return None
    
    def _parse_bybit_response(self, data: Dict[str, Any], symbol: str) -> Optional[RealTimeDataPoint]:
        """Parse Bybit API response"""
        try:
            result = data.get('result', {})
            if isinstance(result, dict) and 'list' in result:
                for item in result['list']:
                    if item.get('symbol') == symbol:
                        return RealTimeDataPoint(
                            symbol=symbol,
                            price=float(item['lastPrice']),
                            volume=float(item.get('volume24h', 0)),
                            timestamp=datetime.now(),
                            source="bybit",
                            bid=float(item.get('bid1Price', 0)),
                            ask=float(item.get('ask1Price', 0)),
                            high_24h=float(item.get('highPrice24h', 0)),
                            low_24h=float(item.get('lowPrice24h', 0)),
                            change_24h=float(item.get('price24hPcnt', 0))
                        )
            return None
        except Exception as e:
            logger.error(f"❌ [BYBIT-PARSE] Error: {e}")
            return None
    
    def _parse_coinbase_response(self, data: Dict[str, Any], symbol: str) -> Optional[RealTimeDataPoint]:
        """Parse Coinbase API response"""
        try:
            return RealTimeDataPoint(
                symbol=symbol,
                price=float(data['price']),
                volume=float(data.get('volume', 0)),
                timestamp=datetime.now(),
                source="coinbase",
                bid=float(data.get('bid', 0)),
                ask=float(data.get('ask', 0))
            )
        except Exception as e:
            logger.error(f"❌ [COINBASE-PARSE] Error: {e}")
            return None
    
    def _parse_binance_response(self, data: Dict[str, Any], symbol: str) -> Optional[RealTimeDataPoint]:
        """Parse Binance API response"""
        try:
            if isinstance(data, list):
                for item in data:
                    if item.get('symbol') == symbol:
                        return RealTimeDataPoint(
                            symbol=symbol,
                            price=float(item['lastPrice']),
                            volume=float(item['volume']),
                            timestamp=datetime.now(),
                            source="binance",
                            high_24h=float(item.get('highPrice', 0)),
                            low_24h=float(item.get('lowPrice', 0)),
                            change_24h=float(item.get('priceChangePercent', 0))
                        )
            return None
        except Exception as e:
            logger.error(f"❌ [BINANCE-PARSE] Error: {e}")
            return None
    
    def _parse_coingecko_response(self, data: Dict[str, Any], symbol: str) -> Optional[RealTimeDataPoint]:
        """Parse CoinGecko API response"""
        try:
            token = symbol.replace('USDT', '').replace('USD', '').lower()
            if token in data:
                price_data = data[token]
                return RealTimeDataPoint(
                    symbol=symbol,
                    price=float(price_data.get('usd', 0)),
                    volume=0,
                    timestamp=datetime.now(),
                    source="coingecko",
                    confidence=0.8  # Lower confidence for delayed data
                )
            return None
        except Exception as e:
            logger.error(f"❌ [COINGECKO-PARSE] Error: {e}")
            return None
    
    def _parse_dexscreener_response(self, data: Dict[str, Any], symbol: str) -> Optional[RealTimeDataPoint]:
        """Parse DEXScreener API response"""
        try:
            if 'pairs' in data and data['pairs']:
                pair = data['pairs'][0]
                return RealTimeDataPoint(
                    symbol=symbol,
                    price=float(pair.get('priceUsd', 0)),
                    volume=float(pair.get('volume', {}).get('h24', 0)),
                    timestamp=datetime.now(),
                    source="dexscreener",
                    confidence=0.7  # Lower confidence for DEX data
                )
            return None
        except Exception as e:
            logger.error(f"❌ [DEXSCREENER-PARSE] Error: {e}")
            return None

    def _aggregate_market_data(self, symbol: str, data_points: List[RealTimeDataPoint]) -> AggregatedMarketData:
        """Aggregate data points into comprehensive market data"""
        try:
            if not data_points:
                raise ValueError("No data points to aggregate")

            # Calculate weighted consensus price
            total_weight = 0
            weighted_sum = 0

            for point in data_points:
                source_config = self.data_sources.get(point.source, {})
                weight = source_config.get('priority', 0.5) * point.confidence

                weighted_sum += point.price * weight
                total_weight += weight

            consensus_price = weighted_sum / total_weight if total_weight > 0 else 0

            # Calculate price range
            prices = [point.price for point in data_points]
            price_range = (min(prices), max(prices))

            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(data_points, consensus_price)

            # Calculate weighted price (volume-weighted if available)
            weighted_price = self._calculate_volume_weighted_price(data_points, consensus_price)

            return AggregatedMarketData(
                symbol=symbol,
                consensus_price=consensus_price,
                price_range=price_range,
                weighted_price=weighted_price,
                confidence_score=confidence_score,
                source_count=len(data_points),
                timestamp=datetime.now(),
                individual_sources=data_points,
                metadata={
                    'price_spread': price_range[1] - price_range[0],
                    'price_spread_percentage': ((price_range[1] - price_range[0]) / consensus_price * 100) if consensus_price > 0 else 0,
                    'avg_latency_ms': sum(p.latency_ms for p in data_points) / len(data_points)
                }
            )

        except Exception as e:
            logger.error(f"❌ [AGGREGATION-ERROR] Error aggregating data: {e}")
            raise

    def _calculate_confidence_score(self, data_points: List[RealTimeDataPoint], consensus_price: float) -> float:
        """Calculate confidence score based on data consistency"""
        try:
            if len(data_points) < 2:
                return 0.5

            # Calculate price deviation from consensus
            deviations = [abs(point.price - consensus_price) / consensus_price for point in data_points]
            avg_deviation = sum(deviations) / len(deviations)

            # Base confidence on consistency
            consistency_score = max(0.1, 1.0 - (avg_deviation * 5))

            # Boost for more sources
            source_bonus = min(0.3, len(data_points) * 0.1)

            # Factor in individual source confidence
            avg_source_confidence = sum(point.confidence for point in data_points) / len(data_points)

            final_confidence = min(1.0, consistency_score + source_bonus) * avg_source_confidence

            return final_confidence

        except Exception as e:
            logger.error(f"❌ [CONFIDENCE-ERROR] Error calculating confidence: {e}")
            return 0.5

    def _calculate_volume_weighted_price(self, data_points: List[RealTimeDataPoint], fallback_price: float) -> float:
        """Calculate volume-weighted price"""
        try:
            total_volume = sum(point.volume for point in data_points if point.volume > 0)

            if total_volume > 0:
                volume_weighted_sum = sum(point.price * point.volume for point in data_points if point.volume > 0)
                return volume_weighted_sum / total_volume
            else:
                return fallback_price

        except Exception as e:
            logger.error(f"❌ [VWAP-ERROR] Error calculating VWAP: {e}")
            return fallback_price

    def _check_rate_limit(self, source_name: str) -> bool:
        """Check if source is within rate limits"""
        try:
            now = time.time()
            rate_limiter = self.rate_limiters[source_name]

            # Remove old requests (older than 1 minute)
            while rate_limiter and now - rate_limiter[0] > 60:
                rate_limiter.popleft()

            # Check if we're within rate limit (60 requests per minute)
            return len(rate_limiter) < 60

        except Exception as e:
            logger.error(f"❌ [RATE-LIMIT-ERROR] Error checking rate limit: {e}")
            return False

    def _update_source_performance(self, source_name: str, success: bool, latency_ms: float = 0):
        """Update performance metrics for a source"""
        try:
            performance = self.source_performance[source_name]
            performance['total_requests'] += 1

            if success:
                performance['last_success'] = datetime.now()
                if latency_ms > 0:
                    if performance['response_time'] == 0:
                        performance['response_time'] = latency_ms
                    else:
                        performance['response_time'] = (performance['response_time'] + latency_ms) / 2
            else:
                performance['error_count'] += 1

            # Update success rate
            successful_requests = performance['total_requests'] - performance['error_count']
            performance['success_rate'] = (successful_requests / performance['total_requests']) * 100

        except Exception as e:
            logger.error(f"❌ [PERFORMANCE-ERROR] Error updating performance: {e}")

    async def get_multiple_symbols(self, symbols: List[str]) -> Dict[str, Optional[AggregatedMarketData]]:
        """Get market data for multiple symbols"""
        try:
            tasks = []
            for symbol in symbols:
                task = asyncio.create_task(self.get_comprehensive_market_data(symbol))
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            symbol_data = {}
            for symbol, result in zip(symbols, results):
                if isinstance(result, AggregatedMarketData):
                    symbol_data[symbol] = result
                else:
                    symbol_data[symbol] = None
                    if isinstance(result, Exception):
                        logger.error(f"❌ [MULTI-SYMBOL] Error for {symbol}: {result}")

            return symbol_data

        except Exception as e:
            logger.error(f"❌ [MULTI-SYMBOL] Error getting multiple symbols: {e}")
            return {}

    def get_performance_report(self) -> Dict[str, Any]:
        """Get performance report for all sources"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'total_sources': len(self.data_sources),
                'active_sources': sum(1 for s in self.data_sources.values() if s['status'] == DataSourceStatus.ACTIVE),
                'cache_size': len(self.data_cache),
                'sources': {}
            }

            for source_name, performance in self.source_performance.items():
                source_config = self.data_sources.get(source_name, {})
                report['sources'][source_name] = {
                    'status': source_config.get('status', DataSourceStatus.FAILED).value,
                    'priority': source_config.get('priority', 0),
                    'success_rate': performance['success_rate'],
                    'avg_response_time': performance['response_time'],
                    'error_count': performance['error_count'],
                    'total_requests': performance['total_requests'],
                    'last_success': performance['last_success'].isoformat() if performance['last_success'] else None
                }

            return report

        except Exception as e:
            logger.error(f"❌ [REPORT-ERROR] Error generating report: {e}")
            return {'error': str(e)}

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()
                self.session = None

            logger.info("🧹 [DATA-AGGREGATOR] Cleanup completed")

        except Exception as e:
            logger.error(f"❌ [CLEANUP-ERROR] Error during cleanup: {e}")

# Global instance for easy access
real_time_aggregator = RealTimeDataAggregator()

async def get_real_time_market_data(symbol: str) -> Optional[AggregatedMarketData]:
    """Get real-time market data for a symbol"""
    return await real_time_aggregator.get_comprehensive_market_data(symbol)

async def initialize_real_time_data_infrastructure() -> bool:
    """Initialize the real-time data infrastructure"""
    return await real_time_aggregator.initialize()
