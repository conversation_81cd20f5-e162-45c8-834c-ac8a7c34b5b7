#!/usr/bin/env python3
"""
Hybrid Trading System with Automatic Failover
Ensures continuous live trading operations with Bybit as PRIMARY and Coinbase as SECONDARY
"""

import os
import time
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class HybridTradingSystem:
    """
    Robust hybrid trading system with automatic failover capabilities
    - Bybit: PRIMARY exchange (immediate live trading)
    - Coinbase: SECONDARY exchange (when API restored)
    """
    
    def __init__(self):
        self.exchange_status = {
            'bybit': {
                'active': False,
                'last_test': None,
                'error_count': 0,
                'role': 'PRIMARY',
                'priority': 1
            },
            'coinbase': {
                'active': False,
                'last_test': None,
                'error_count': 0,
                'role': 'SECONDARY',
                'priority': 2,
                'api_restored': False,
                'monitoring_enabled': True
            }
        }
        
        self.exchanges = {}
        self.primary_exchange = None
        self.secondary_exchange = None
        self.monitoring_task = None
        self.coinbase_enabled = os.getenv('COINBASE_ENABLED', 'false').lower() == 'true'
        
        # Trading configuration
        self.position_size_percentage = 0.225  # 22.5% of available balance per trade
        self.min_trade_amount = 10.0  # Minimum trade amount in USD
        
        logger.info("🔧 [HYBRID] Hybrid trading system initialized")
        logger.info(f"🎯 [HYBRID] Coinbase enabled: {self.coinbase_enabled}")

    async def initialize(self) -> bool:
        """Initialize hybrid trading system with failover capabilities"""
        logger.info("🚀 [HYBRID] Initializing hybrid trading system...")
        
        # Step 1: Initialize Bybit (PRIMARY - CRITICAL)
        bybit_success = await self._initialize_bybit()
        if not bybit_success:
            logger.error("❌ [HYBRID] CRITICAL: Primary exchange (Bybit) initialization failed")
            return False
        
        # Step 2: Initialize Coinbase (SECONDARY - OPTIONAL)
        coinbase_success = await self._initialize_coinbase()
        if coinbase_success:
            logger.info("✅ [HYBRID] Secondary exchange (Coinbase) ready")
        else:
            logger.warning("⚠️ [HYBRID] Secondary exchange (Coinbase) not available - continuing with Bybit-only")
        
        # Step 3: Set primary exchange
        self.primary_exchange = self.exchanges.get('bybit')
        self.secondary_exchange = self.exchanges.get('coinbase')
        
        # Step 4: Start monitoring service
        if not coinbase_success and self.coinbase_enabled:
            await self._start_coinbase_monitoring()
        
        # Step 5: Validate system readiness
        if self.primary_exchange:
            logger.info("✅ [HYBRID] System ready for live trading")
            logger.info(f"🎯 [HYBRID] Primary: {self.primary_exchange.name}")
            if self.secondary_exchange:
                logger.info(f"🎯 [HYBRID] Secondary: {self.secondary_exchange.name}")
            return True
        else:
            logger.error("❌ [HYBRID] CRITICAL: No exchanges available for trading")
            return False

    async def _initialize_bybit(self) -> bool:
        """Initialize Bybit as PRIMARY exchange"""
        try:
            logger.info("🚀 [BYBIT] Initializing PRIMARY exchange...")
            
            from src.exchanges.bybit_client_fixed import BybitClientFixed
            
            # Get and decrypt credentials
            bybit_api_key = os.getenv('BYBIT_API_KEY')
            bybit_api_secret = os.getenv('BYBIT_API_SECRET')
            
            if not bybit_api_key or not bybit_api_secret:
                logger.error("❌ [BYBIT] CRITICAL: Primary exchange credentials missing")
                return False
            
            # Decrypt if encrypted
            if bybit_api_key.startswith("gAAAAA"):
                from src.utils.cryptpography.hybrid import HybridCrypto
                crypto = HybridCrypto()
                bybit_api_key = crypto.decrypt_value(bybit_api_key)
                bybit_api_secret = crypto.decrypt_value(bybit_api_secret)
                logger.info("🔓 [BYBIT] Credentials decrypted successfully")
            
            # Initialize client
            bybit_client = BybitClientFixed(
                api_key=bybit_api_key,
                api_secret=bybit_api_secret,
                testnet=False  # LIVE TRADING ONLY
            )
            
            # Test connection
            test_result = await self._test_bybit_connection(bybit_client)
            if test_result:
                self.exchanges['bybit'] = bybit_client
                self.exchange_status['bybit']['active'] = True
                self.exchange_status['bybit']['last_test'] = time.time()
                self.exchange_status['bybit']['error_count'] = 0
                logger.info("✅ [BYBIT] PRIMARY exchange ready for live trading")
                return True
            else:
                logger.error("❌ [BYBIT] Connection test failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ [BYBIT] CRITICAL: Primary exchange initialization failed: {e}")
            return False

    async def _initialize_coinbase(self) -> bool:
        """Initialize Coinbase as SECONDARY exchange with enhanced authentication"""
        try:
            logger.info("🔄 [COINBASE] Initializing SECONDARY exchange...")
            
            # Get CDP credentials
            api_key_name = os.getenv('COINBASE_API_KEY_NAME')
            private_key_pem = os.getenv('COINBASE_PRIVATE_KEY')
            
            if not api_key_name or not private_key_pem:
                logger.warning("⚠️ [COINBASE] No CDP credentials found - continuing with Bybit-only")
                return False
            
            # Validate credentials format
            if not api_key_name.startswith("organizations/"):
                logger.error("❌ [COINBASE] Invalid API key format")
                return False
            
            # Extract key ID
            key_id = api_key_name.split("/apiKeys/")[1] if "/apiKeys/" in api_key_name else None
            if not key_id:
                logger.error("❌ [COINBASE] Invalid API key format")
                return False
            
            logger.info(f"🔑 [COINBASE] Using enhanced CDP authentication: {api_key_name[:50]}...")
            
            # Create enhanced Coinbase client wrapper
            from src.exchanges.coinbase_enhanced_client import CoinbaseEnhancedClient
            coinbase_client = CoinbaseEnhancedClient(
                api_key_name=api_key_name,
                private_key_pem=private_key_pem,
                key_id=key_id
            )
            
            # Test connection with enhanced authentication
            test_result = await coinbase_client.test_connection()
            if test_result:
                self.exchanges['coinbase'] = coinbase_client
                self.exchange_status['coinbase']['active'] = True
                self.exchange_status['coinbase']['last_test'] = time.time()
                self.exchange_status['coinbase']['error_count'] = 0
                self.exchange_status['coinbase']['api_restored'] = True
                logger.info("✅ [COINBASE] SECONDARY exchange ready")
                return True
            else:
                logger.warning("⚠️ [COINBASE] API access still restricted - enabling monitoring")
                self.exchange_status['coinbase']['api_restored'] = False
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ [COINBASE] Secondary exchange initialization failed: {e}")
            return False

    async def _test_bybit_connection(self, client) -> bool:
        """Test Bybit connection and validate trading capabilities"""
        try:
            # Test basic connectivity - Bybit client returns Decimal for specific coin
            balance = await client.get_balance("USDT")
            logger.info(f"🔍 [BYBIT] Balance response: {balance}")

            if balance is not None:
                # Convert Decimal to float
                usdt_balance = float(balance)
                logger.info(f"💰 [BYBIT] USDT Balance: {usdt_balance:.2f}")

                # Validate minimum trading balance
                if usdt_balance >= self.min_trade_amount:
                    logger.info("✅ [BYBIT] Sufficient balance for trading")
                    return True
                else:
                    logger.warning(f"⚠️ [BYBIT] Low balance: {usdt_balance:.2f} USDT (min: {self.min_trade_amount})")
                    return True  # Still allow trading with low balance
            else:
                logger.error("❌ [BYBIT] Could not retrieve balance")
                return False

        except Exception as e:
            logger.error(f"❌ [BYBIT] Connection test failed: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def _start_coinbase_monitoring(self):
        """Start background monitoring for Coinbase API restoration"""
        if self.monitoring_task:
            return  # Already running
        
        logger.info("🔍 [MONITOR] Starting Coinbase API restoration monitoring...")
        self.monitoring_task = asyncio.create_task(self._coinbase_monitoring_loop())

    async def _coinbase_monitoring_loop(self):
        """Background monitoring loop for Coinbase API restoration"""
        while True:
            try:
                await asyncio.sleep(1800)  # 30 minutes
                
                if self.exchange_status['coinbase']['api_restored']:
                    logger.info("🔍 [MONITOR] Coinbase API already restored - stopping monitoring")
                    break
                
                logger.info("🔍 [MONITOR] Testing Coinbase API restoration...")
                
                # Test Coinbase API access
                restored = await self._test_coinbase_api_restoration()
                if restored:
                    logger.info("🎉 [MONITOR] Coinbase API access RESTORED!")
                    await self._handle_coinbase_restoration()
                    break
                else:
                    logger.info("🔍 [MONITOR] Coinbase API still restricted - continuing monitoring")
                    
            except Exception as e:
                logger.error(f"❌ [MONITOR] Monitoring error: {e}")
                await asyncio.sleep(300)  # 5 minutes on error

    async def _test_coinbase_api_restoration(self) -> bool:
        """Test if Coinbase API access has been restored"""
        try:
            import jwt
            import requests
            from cryptography.hazmat.primitives import serialization
            
            # Get credentials
            api_key_name = os.getenv('COINBASE_API_KEY_NAME')
            private_key_pem = os.getenv('COINBASE_PRIVATE_KEY')
            
            if not api_key_name or not private_key_pem:
                return False
            
            # Load private key
            private_key = serialization.load_pem_private_key(
                private_key_pem.encode('utf-8'),
                password=None
            )
            
            # Extract key ID
            key_id = api_key_name.split("/apiKeys/")[1] if "/apiKeys/" in api_key_name else None
            
            # Test accounts endpoint (previously failing with 401)
            now = int(time.time())
            payload = {
                'iss': 'cdp',
                'nbf': now,
                'exp': now + 120,
                'sub': api_key_name,
                'uri': 'GET /api/v3/brokerage/accounts'
            }
            
            token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': key_id})
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json',
                'User-Agent': 'AutoGPT-Trader/1.0'
            }
            
            response = requests.get(
                'https://api.coinbase.com/api/v3/brokerage/accounts',
                headers=headers,
                timeout=15
            )
            
            if response.status_code == 200:
                logger.info("✅ [MONITOR] Coinbase accounts endpoint now returns 200 OK!")
                return True
            else:
                logger.debug(f"🔍 [MONITOR] Coinbase still returns {response.status_code}")
                return False
                
        except Exception as e:
            logger.debug(f"🔍 [MONITOR] Coinbase test error: {e}")
            return False

    async def _handle_coinbase_restoration(self):
        """Handle Coinbase API restoration"""
        try:
            logger.info("🎉 [HYBRID] Coinbase API access restored - reinitializing...")
            
            # Reinitialize Coinbase
            coinbase_success = await self._initialize_coinbase()
            if coinbase_success:
                self.secondary_exchange = self.exchanges.get('coinbase')
                self.exchange_status['coinbase']['api_restored'] = True
                logger.info("✅ [HYBRID] Coinbase secondary exchange now available")
                
                # Send notification (could be email, webhook, etc.)
                await self._send_restoration_notification()
            else:
                logger.warning("⚠️ [HYBRID] Coinbase reinitializtion failed despite API restoration")
                
        except Exception as e:
            logger.error(f"❌ [HYBRID] Error handling Coinbase restoration: {e}")

    async def _send_restoration_notification(self):
        """Send notification about Coinbase API restoration"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message = f"🎉 Coinbase API access restored at {timestamp}. Secondary exchange now available for trading."
            
            # Log the notification
            logger.info(f"📧 [NOTIFICATION] {message}")
            
            # Could add email, webhook, or other notification methods here
            
        except Exception as e:
            logger.error(f"❌ [NOTIFICATION] Failed to send restoration notification: {e}")

    def get_exchange_status(self) -> Dict[str, Any]:
        """Get current status of all exchanges"""
        status = {
            'primary_exchange': self.primary_exchange.name if self.primary_exchange else None,
            'secondary_exchange': self.secondary_exchange.name if self.secondary_exchange else None,
            'exchanges': self.exchange_status.copy(),
            'coinbase_enabled': self.coinbase_enabled,
            'monitoring_active': self.monitoring_task is not None and not self.monitoring_task.done()
        }
        
        # Add last test timestamps in human readable format
        for exchange, data in status['exchanges'].items():
            if data['last_test']:
                data['last_test_human'] = datetime.fromtimestamp(data['last_test']).strftime("%Y-%m-%d %H:%M:%S")
        
        return status

    async def execute_trade(self, symbol: str, side: str, amount: float, order_type: str = "market") -> Dict[str, Any]:
        """Execute trade with automatic failover"""
        try:
            # Use primary exchange (Bybit)
            if self.primary_exchange and self.exchange_status['bybit']['active']:
                logger.info(f"💰 [TRADE] Executing {side} {amount} {symbol} on PRIMARY (Bybit)")
                result = await self.primary_exchange.execute_order(symbol, side, amount, order_type)
                
                if result.get('success'):
                    logger.info("✅ [TRADE] Primary exchange execution successful")
                    return result
                else:
                    logger.warning("⚠️ [TRADE] Primary exchange execution failed - checking secondary")
            
            # Fallback to secondary exchange (Coinbase) if available
            if self.secondary_exchange and self.exchange_status['coinbase']['active']:
                logger.info(f"💰 [TRADE] Executing {side} {amount} {symbol} on SECONDARY (Coinbase)")
                result = await self.secondary_exchange.execute_order(symbol, side, amount, order_type)
                
                if result.get('success'):
                    logger.info("✅ [TRADE] Secondary exchange execution successful")
                    return result
                else:
                    logger.error("❌ [TRADE] Secondary exchange execution also failed")
            
            # No exchanges available
            logger.error("❌ [TRADE] No exchanges available for trading")
            return {
                'success': False,
                'error': 'NO_EXCHANGES_AVAILABLE',
                'message': 'No active exchanges available for trade execution'
            }
            
        except Exception as e:
            logger.error(f"❌ [TRADE] Trade execution error: {e}")
            return {
                'success': False,
                'error': 'EXECUTION_ERROR',
                'message': f'Trade execution failed: {str(e)}'
            }

    async def get_available_balance(self, currency: str = "USDT") -> float:
        """Get available balance from primary exchange"""
        try:
            if self.primary_exchange and self.exchange_status['bybit']['active']:
                # Bybit client returns Decimal for specific currency
                balance = await self.primary_exchange.get_balance(currency)
                logger.info(f"🔍 [BALANCE] Raw balance response: {balance}")

                if balance is not None:
                    # Convert Decimal to float
                    balance_float = float(balance)
                    logger.info(f"💰 [BALANCE] {currency} Balance: {balance_float:.2f}")
                    return balance_float
                else:
                    logger.warning(f"⚠️ [BALANCE] {currency} balance is None")
                    return 0.0

            logger.warning(f"⚠️ [BALANCE] Could not get {currency} balance from primary exchange")
            return 0.0

        except Exception as e:
            logger.error(f"❌ [BALANCE] Error getting balance: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return 0.0

    def calculate_position_size(self, available_balance: float) -> float:
        """Calculate position size based on available balance (20-25% rule)"""
        position_size = available_balance * self.position_size_percentage
        
        # Ensure minimum trade amount
        if position_size < self.min_trade_amount:
            if available_balance >= self.min_trade_amount:
                position_size = self.min_trade_amount
            else:
                logger.warning(f"⚠️ [POSITION] Insufficient balance for minimum trade: {available_balance:.2f}")
                return 0.0
        
        logger.info(f"📊 [POSITION] Position size: {position_size:.2f} ({self.position_size_percentage*100:.1f}% of {available_balance:.2f})")
        return position_size

    async def shutdown(self):
        """Shutdown hybrid trading system"""
        logger.info("🔄 [HYBRID] Shutting down hybrid trading system...")
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("✅ [HYBRID] Hybrid trading system shutdown complete")
