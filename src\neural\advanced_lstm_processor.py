"""
Advanced LSTM Trading Processor with Attention Mechanisms
Professional-grade neural architecture for institutional trading
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import os
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
from collections import deque

logger = logging.getLogger(__name__)

@dataclass
class AttentionWeights:
    """Attention weights for interpretability"""
    temporal_weights: torch.Tensor
    feature_weights: torch.Tensor
    cross_attention_weights: Optional[torch.Tensor] = None

class MultiHeadAttention(nn.Module):
    """Multi-head attention mechanism for LSTM outputs"""
    
    def __init__(self, d_model: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_k)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        batch_size, seq_len = query.size(0), query.size(1)
        
        # Linear transformations and reshape
        Q = self.w_q(query).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        
        # Attention
        attention_output, attention_weights = self._attention(Q, K, V, mask)
        
        # Concatenate heads
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        
        # Final linear transformation
        output = self.w_o(attention_output)
        
        return output, attention_weights
    
    def _attention(self, Q: torch.Tensor, K: torch.Tensor, V: torch.Tensor,
                   mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        # Scaled dot-product attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights

class PositionalEncoding(nn.Module):
    """Positional encoding for transformer-like processing"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x + self.pe[:x.size(0), :]

class AdvancedLSTMProcessor(nn.Module):
    """
    Advanced LSTM processor with attention mechanisms and transformer integration
    Features:
    - Bidirectional LSTM layers
    - Multi-head attention
    - Positional encoding
    - Residual connections
    - Layer normalization
    - Ensemble capabilities
    """
    
    def __init__(self, 
                 input_size: int = 20,
                 hidden_size: int = 128,
                 num_layers: int = 3,
                 num_heads: int = 8,
                 output_size: int = 1,
                 dropout: float = 0.2,
                 bidirectional: bool = True,
                 use_attention: bool = True,
                 use_positional_encoding: bool = True,
                 device: str = 'cpu'):
        super().__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.output_size = output_size
        self.dropout = dropout
        self.bidirectional = bidirectional
        self.use_attention = use_attention
        self.use_positional_encoding = use_positional_encoding
        self.device = device
        
        # Calculate actual LSTM output size
        lstm_output_size = hidden_size * (2 if bidirectional else 1)
        
        # Input projection
        self.input_projection = nn.Linear(input_size, hidden_size)
        
        # Positional encoding
        if use_positional_encoding:
            self.pos_encoding = PositionalEncoding(hidden_size)
        
        # Bidirectional LSTM layers
        self.lstm_layers = nn.ModuleList()
        for i in range(num_layers):
            layer_input_size = hidden_size if i == 0 else lstm_output_size
            self.lstm_layers.append(
                nn.LSTM(
                    layer_input_size,
                    hidden_size,
                    batch_first=True,
                    dropout=dropout if i < num_layers - 1 else 0,
                    bidirectional=bidirectional
                )
            )
        
        # Layer normalization
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(lstm_output_size) for _ in range(num_layers)
        ])
        
        # Multi-head attention
        if use_attention:
            self.attention = MultiHeadAttention(lstm_output_size, num_heads, dropout)
            self.attention_norm = nn.LayerNorm(lstm_output_size)
        
        # Feature attention for interpretability
        self.feature_attention = nn.Sequential(
            nn.Linear(lstm_output_size, lstm_output_size // 2),
            nn.ReLU(),
            nn.Linear(lstm_output_size // 2, 1),
            nn.Sigmoid()
        )
        
        # Output layers
        self.output_projection = nn.Sequential(
            nn.Linear(lstm_output_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, output_size)
        )
        
        # Ensemble components
        self.ensemble_weights = nn.Parameter(torch.ones(3) / 3)  # LSTM, Attention, Feature weights
        
        # Performance tracking
        self.training_history = deque(maxlen=1000)
        self.attention_weights_history = deque(maxlen=100)
        
        self._init_weights()
        self.to(device)
        
    def _init_weights(self):
        """Initialize weights with Xavier/He initialization"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'lstm' in name:
                    # LSTM weights
                    for weight in param.chunk(4, 0):
                        nn.init.xavier_uniform_(weight)
                elif len(param.shape) >= 2:
                    nn.init.xavier_uniform_(param)
                else:
                    nn.init.uniform_(param, -0.1, 0.1)
            elif 'bias' in name:
                nn.init.constant_(param, 0)
    
    def forward(self, x: torch.Tensor, return_attention: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, AttentionWeights]]:
        batch_size, seq_len, _ = x.shape
        
        # Input projection
        x = self.input_projection(x)
        
        # Positional encoding
        if self.use_positional_encoding:
            x = self.pos_encoding(x.transpose(0, 1)).transpose(0, 1)
        
        # LSTM layers with residual connections
        lstm_outputs = []
        current_input = x
        
        for i, (lstm_layer, layer_norm) in enumerate(zip(self.lstm_layers, self.layer_norms)):
            lstm_out, _ = lstm_layer(current_input)
            lstm_out = layer_norm(lstm_out)
            
            # Residual connection (if dimensions match)
            if i > 0 and current_input.size(-1) == lstm_out.size(-1):
                lstm_out = lstm_out + current_input
            
            lstm_outputs.append(lstm_out)
            current_input = lstm_out
        
        # Final LSTM output
        lstm_final = lstm_outputs[-1]
        
        # Multi-head attention
        attention_output = lstm_final
        attention_weights = None
        
        if self.use_attention:
            attention_output, attention_weights = self.attention(
                lstm_final, lstm_final, lstm_final
            )
            attention_output = self.attention_norm(attention_output + lstm_final)
        
        # Feature attention for interpretability
        feature_weights = self.feature_attention(attention_output)
        attended_features = attention_output * feature_weights
        
        # Ensemble combination
        lstm_contribution = lstm_final[:, -1, :] * self.ensemble_weights[0]
        attention_contribution = attention_output[:, -1, :] * self.ensemble_weights[1]
        feature_contribution = attended_features[:, -1, :] * self.ensemble_weights[2]
        
        ensemble_output = lstm_contribution + attention_contribution + feature_contribution
        
        # Final prediction
        output = self.output_projection(ensemble_output)
        
        if return_attention:
            attention_info = AttentionWeights(
                temporal_weights=attention_weights,
                feature_weights=feature_weights,
                cross_attention_weights=None
            )
            return output, attention_info
        
        return output

    async def predict_with_confidence(self, x: torch.Tensor,
                                    num_samples: int = 10) -> Tuple[torch.Tensor, torch.Tensor]:
        """Predict with uncertainty estimation using Monte Carlo dropout"""
        self.train()  # Enable dropout for uncertainty estimation

        predictions = []
        attention_weights_list = []

        with torch.no_grad():
            for _ in range(num_samples):
                if self.use_attention:
                    pred, attention_info = self.forward(x, return_attention=True)
                    attention_weights_list.append(attention_info.temporal_weights)
                else:
                    pred = self.forward(x)
                predictions.append(pred)

        predictions = torch.stack(predictions)
        mean_prediction = predictions.mean(dim=0)
        uncertainty = predictions.std(dim=0)

        self.eval()  # Return to evaluation mode

        return mean_prediction, uncertainty

    async def adaptive_learning(self, x: torch.Tensor, y: torch.Tensor,
                              learning_rate: float = 0.001,
                              adaptation_steps: int = 5) -> Dict[str, float]:
        """Adaptive learning with meta-learning principles"""
        try:
            # Create optimizer for adaptation
            optimizer = torch.optim.Adam(self.parameters(), lr=learning_rate)
            criterion = nn.MSELoss()

            losses = []

            for step in range(adaptation_steps):
                optimizer.zero_grad()

                # Forward pass with attention
                if self.use_attention:
                    pred, attention_info = self.forward(x, return_attention=True)
                    # Store attention weights for analysis
                    self.attention_weights_history.append(attention_info)
                else:
                    pred = self.forward(x)

                loss = criterion(pred, y)
                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.parameters(), max_norm=1.0)

                optimizer.step()
                losses.append(loss.item())

                logger.debug(f"Adaptation step {step + 1}/{adaptation_steps}, Loss: {loss.item():.6f}")

            # Calculate adaptation metrics
            adaptation_metrics = {
                'initial_loss': losses[0],
                'final_loss': losses[-1],
                'improvement': (losses[0] - losses[-1]) / losses[0] if losses[0] > 0 else 0,
                'convergence_rate': self._calculate_convergence_rate(losses)
            }

            # Store training history
            self.training_history.append({
                'timestamp': len(self.training_history),
                'losses': losses,
                'metrics': adaptation_metrics
            })

            return adaptation_metrics

        except Exception as e:
            logger.error(f"Error in adaptive learning: {e}")
            return {'error': str(e)}

    def _calculate_convergence_rate(self, losses: List[float]) -> float:
        """Calculate convergence rate from loss history"""
        if len(losses) < 2:
            return 0.0

        # Calculate rate of loss decrease
        loss_deltas = [losses[i] - losses[i+1] for i in range(len(losses)-1)]
        avg_delta = sum(loss_deltas) / len(loss_deltas)

        return max(0.0, avg_delta / losses[0]) if losses[0] > 0 else 0.0

    def get_attention_analysis(self) -> Dict[str, Any]:
        """Analyze attention patterns for interpretability"""
        try:
            if not self.attention_weights_history:
                return {'error': 'No attention weights available'}

            # Aggregate attention weights
            temporal_weights = []
            feature_weights = []

            for attention_info in self.attention_weights_history:
                if attention_info.temporal_weights is not None:
                    temporal_weights.append(attention_info.temporal_weights.cpu().numpy())
                if attention_info.feature_weights is not None:
                    feature_weights.append(attention_info.feature_weights.cpu().numpy())

            analysis = {}

            if temporal_weights:
                temporal_array = np.array(temporal_weights)
                analysis['temporal_attention'] = {
                    'mean_weights': temporal_array.mean(axis=0).tolist(),
                    'std_weights': temporal_array.std(axis=0).tolist(),
                    'max_attention_position': int(temporal_array.mean(axis=0).argmax()),
                    'attention_entropy': float(-np.sum(temporal_array.mean(axis=0) *
                                                     np.log(temporal_array.mean(axis=0) + 1e-8)))
                }

            if feature_weights:
                feature_array = np.array(feature_weights)
                analysis['feature_attention'] = {
                    'mean_weights': feature_array.mean(axis=0).tolist(),
                    'std_weights': feature_array.std(axis=0).tolist(),
                    'most_important_features': np.argsort(feature_array.mean(axis=0))[-5:].tolist()
                }

            return analysis

        except Exception as e:
            logger.error(f"Error in attention analysis: {e}")
            return {'error': str(e)}

    def get_ensemble_weights(self) -> Dict[str, float]:
        """Get current ensemble component weights"""
        weights = F.softmax(self.ensemble_weights, dim=0)
        return {
            'lstm_weight': float(weights[0]),
            'attention_weight': float(weights[1]),
            'feature_weight': float(weights[2])
        }

    def save_model(self, path: str):
        """Save model with metadata"""
        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)

            save_dict = {
                'model_state_dict': self.state_dict(),
                'architecture_config': {
                    'input_size': self.input_size,
                    'hidden_size': self.hidden_size,
                    'num_layers': self.num_layers,
                    'num_heads': self.num_heads,
                    'output_size': self.output_size,
                    'dropout': self.dropout,
                    'bidirectional': self.bidirectional,
                    'use_attention': self.use_attention,
                    'use_positional_encoding': self.use_positional_encoding
                },
                'training_history': list(self.training_history),
                'ensemble_weights': self.ensemble_weights.data
            }

            torch.save(save_dict, path)
            logger.info(f"Advanced LSTM model saved to {path}")

        except Exception as e:
            logger.error(f"Error saving model: {e}")

    def load_model(self, path: str):
        """Load model with metadata"""
        try:
            if os.path.exists(path):
                checkpoint = torch.load(path, map_location=self.device)

                self.load_state_dict(checkpoint['model_state_dict'])

                if 'training_history' in checkpoint:
                    self.training_history.extend(checkpoint['training_history'])

                if 'ensemble_weights' in checkpoint:
                    self.ensemble_weights.data = checkpoint['ensemble_weights']

                logger.info(f"Advanced LSTM model loaded from {path}")

            else:
                logger.warning(f"Model file not found at {path}, using initialized model")

        except Exception as e:
            logger.error(f"Error loading model: {e}")
