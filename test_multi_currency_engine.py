#!/usr/bin/env python3
"""
Test Multi-Currency Trading Engine

This script tests the advanced multi-currency trading engine to ensure
it can properly discover currencies, find trading opportunities, and
execute trades across multiple currencies and exchanges.
"""

import asyncio
import logging
import sys
import os
from decimal import Decimal

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_multi_currency_engine():
    """Test the multi-currency trading engine functionality"""
    try:
        logger.info("🧪 [TEST] Starting multi-currency trading engine test...")
        
        # Import required modules
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine, TradingMode
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            logger.error("❌ [TEST] Missing Bybit API credentials")
            return False
        
        # Initialize exchange clients
        logger.info("🔧 [TEST] Initializing exchange clients...")
        
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        
        if not bybit_client.session:
            logger.error("❌ [TEST] Failed to initialize Bybit client")
            return False
        
        exchange_clients = {
            'bybit': bybit_client
        }
        
        # Initialize multi-currency trading engine
        logger.info("🚀 [TEST] Initializing multi-currency trading engine...")
        
        engine = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={
                'max_position_per_currency': 0.3,
                'min_liquidity_threshold': 1000.0,
                'max_correlation_exposure': 0.7
            }
        )
        
        # Test 1: Initialize engine
        logger.info("🧪 [TEST-1] Testing engine initialization...")
        await engine.initialize()
        
        if len(engine.supported_currencies) == 0:
            logger.error("❌ [TEST-1] No currencies discovered")
            return False
        
        logger.info(f"✅ [TEST-1] Discovered {len(engine.supported_currencies)} currencies")
        logger.info(f"✅ [TEST-1] Currencies: {sorted(list(engine.supported_currencies))}")
        
        # Test 2: Currency discovery
        logger.info("🧪 [TEST-2] Testing currency discovery...")
        
        if 'USDT' not in engine.supported_currencies:
            logger.warning("⚠️ [TEST-2] USDT not found in supported currencies")
        
        major_currencies = {'BTC', 'ETH', 'SOL', 'USDT'}
        found_major = major_currencies.intersection(engine.supported_currencies)
        
        logger.info(f"✅ [TEST-2] Found major currencies: {found_major}")
        
        # Test 3: Trading pair discovery
        logger.info("🧪 [TEST-3] Testing trading pair discovery...")
        
        total_pairs = 0
        for exchange_name, pairs in engine.active_pairs.items():
            total_pairs += len(pairs)
            logger.info(f"✅ [TEST-3] {exchange_name}: {len(pairs)} trading pairs")
            
            # Show some example pairs
            example_pairs = list(pairs.keys())[:5]
            logger.info(f"✅ [TEST-3] Example pairs: {example_pairs}")
        
        if total_pairs == 0:
            logger.error("❌ [TEST-3] No trading pairs discovered")
            return False
        
        logger.info(f"✅ [TEST-3] Total trading pairs: {total_pairs}")
        
        # Test 4: Balance checking
        logger.info("🧪 [TEST-4] Testing balance checking...")
        
        total_balance_value = Decimal('0')
        for exchange_name, balances in engine.currency_balances.items():
            exchange_value = Decimal('0')
            for currency, balance in balances.items():
                if balance > 0:
                    usd_value = await engine._get_usd_value(currency, balance, exchange_name)
                    exchange_value += usd_value
                    logger.info(f"✅ [TEST-4] {currency}: {balance:.6f} = ${usd_value:.2f}")
            
            total_balance_value += exchange_value
            logger.info(f"✅ [TEST-4] {exchange_name} total value: ${exchange_value:.2f}")
        
        logger.info(f"✅ [TEST-4] Total portfolio value: ${total_balance_value:.2f}")
        
        # Test 5: Trading opportunity discovery
        logger.info("🧪 [TEST-5] Testing trading opportunity discovery...")
        
        opportunities = await engine.find_trading_opportunities()
        
        if not opportunities:
            logger.warning("⚠️ [TEST-5] No trading opportunities found")
        else:
            logger.info(f"✅ [TEST-5] Found {len(opportunities)} trading opportunities")
            
            # Show details of top opportunities
            for i, opp in enumerate(opportunities[:3]):
                logger.info(f"✅ [TEST-5] Opportunity {i+1}:")
                logger.info(f"  - Strategy: {opp.strategy}")
                logger.info(f"  - Pair: {opp.pair.symbol}")
                logger.info(f"  - Side: {opp.side}")
                logger.info(f"  - Amount: {opp.amount:.6f}")
                logger.info(f"  - Expected Profit: {opp.expected_profit:.2f}")
                logger.info(f"  - Confidence: {opp.confidence:.2f}")
                logger.info(f"  - Risk Score: {opp.risk_score:.2f}")
        
        # Test 6: Execution validation (without actual execution)
        logger.info("🧪 [TEST-6] Testing execution validation...")
        
        if opportunities:
            test_opportunity = opportunities[0]
            validation_result = await engine._validate_execution(test_opportunity)
            
            if validation_result['valid']:
                logger.info("✅ [TEST-6] Execution validation passed")
            else:
                logger.warning(f"⚠️ [TEST-6] Execution validation failed: {validation_result['reason']}")
        else:
            logger.info("ℹ️ [TEST-6] No opportunities to validate")
        
        # Test 7: Multi-currency mode verification
        logger.info("🧪 [TEST-7] Testing multi-currency mode verification...")
        
        if engine.trading_mode == TradingMode.MULTI_CURRENCY:
            logger.info("✅ [TEST-7] Engine is in multi-currency mode")
        else:
            logger.warning(f"⚠️ [TEST-7] Engine is in {engine.trading_mode} mode")
        
        # Test 8: Currency switching capability
        logger.info("🧪 [TEST-8] Testing currency switching capability...")
        
        # Check if engine can handle insufficient USDT scenario
        usdt_balance = engine.currency_balances.get('bybit', {}).get('USDT', 0)
        
        if usdt_balance < 10:  # Less than $10 USDT
            logger.info(f"✅ [TEST-8] Low USDT balance detected: ${usdt_balance:.2f}")
            
            # Check if engine found alternative currency opportunities
            alt_currency_opportunities = [
                opp for opp in opportunities 
                if opp.pair.base != 'USDT' or opp.side == 'sell'
            ]
            
            if alt_currency_opportunities:
                logger.info(f"✅ [TEST-8] Found {len(alt_currency_opportunities)} alternative currency opportunities")
            else:
                logger.warning("⚠️ [TEST-8] No alternative currency opportunities found")
        else:
            logger.info(f"✅ [TEST-8] Sufficient USDT balance: ${usdt_balance:.2f}")
        
        # Summary
        logger.info("📊 [TEST-SUMMARY] Multi-currency trading engine test results:")
        logger.info(f"  - Currencies discovered: {len(engine.supported_currencies)}")
        logger.info(f"  - Trading pairs: {total_pairs}")
        logger.info(f"  - Portfolio value: ${total_balance_value:.2f}")
        logger.info(f"  - Trading opportunities: {len(opportunities)}")
        logger.info(f"  - Engine mode: {engine.trading_mode}")
        
        logger.info("✅ [TEST] Multi-currency trading engine test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST] Multi-currency engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    try:
        logger.info("🚀 Starting multi-currency trading engine tests...")
        
        success = await test_multi_currency_engine()
        
        if success:
            logger.info("✅ All tests passed!")
            return 0
        else:
            logger.error("❌ Some tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
