#!/usr/bin/env python3
"""
Test Bybit balance retrieval to see if that's where the hang occurs
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_bybit_balance():
    """Test Bybit balance retrieval"""
    try:
        print("🔧 [TEST] Testing Bybit balance retrieval...")
        
        # Import Bybit client
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Get credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            print("❌ [TEST] Missing Bybit credentials")
            return False
        
        print("✅ [TEST] Credentials available")
        
        # Initialize client
        print("🔧 [TEST] Initializing Bybit client...")
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        print("✅ [TEST] Bybit client created")
        
        # Test individual balance calls
        print("💰 [TEST] Testing individual balance calls...")
        try:
            usdt_balance = await bybit_client.get_balance('USDT')
            print(f"✅ [TEST] USDT balance: {usdt_balance}")
        except Exception as e:
            print(f"❌ [TEST] USDT balance error: {e}")
            return False
        
        # Test the problematic method
        print("💰 [TEST] Testing get_all_available_balances() - this might hang...")
        try:
            # Add a timeout to prevent infinite hang
            all_balances = await asyncio.wait_for(
                bybit_client.get_all_available_balances(), 
                timeout=30.0
            )
            print(f"✅ [TEST] All balances retrieved: {len(all_balances)} currencies")
            for currency, balance in all_balances.items():
                if balance > 0:
                    print(f"  💰 {currency}: {balance}")
        except asyncio.TimeoutError:
            print("❌ [TEST] get_all_available_balances() timed out after 30 seconds")
            return False
        except Exception as e:
            print(f"❌ [TEST] get_all_available_balances() error: {e}")
            return False
        
        print("🎉 [TEST] All balance tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ [TEST] Error in balance test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_bybit_balance())
    sys.exit(0 if success else 1)
