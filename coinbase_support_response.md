# Complete Code Sample and Request Details for Coinbase Support

Hi Rishabh,

Here's the complete code sample and exact request details you requested:

## 1. Complete Code Sample

```python
#!/usr/bin/env python3
import os
import time
import jwt
import json
import requests
from cryptography.hazmat.primitives import serialization

def create_jwt_token(api_key_name, private_key_pem, key_id, method, endpoint):
    """Create JWT token exactly as per Coinbase documentation"""
    
    # Load private key
    private_key = serialization.load_pem_private_key(
        private_key_pem.encode('utf-8'),
        password=None
    )
    
    # Create JWT payload - exactly as per your docs
    now = int(time.time())
    payload = {
        'iss': 'cdp',                    # Issuer: Coinbase Developer Platform
        'nbf': now,                      # Not before: current timestamp
        'exp': now + 120,                # Expires: 2 minutes from now
        'sub': api_key_name,             # Subject: full API key name
        'uri': f'{method} {endpoint}'    # URI: HTTP method + endpoint path
    }
    
    # Create JWT headers - exactly as per your docs
    headers = {
        'alg': 'ES256',                  # Algorithm: ECDSA with SHA-256
        'kid': key_id,                   # Key ID
        'typ': 'JWT'                     # Token type
    }
    
    # Generate JWT token
    token = jwt.encode(
        payload,
        private_key,
        algorithm='ES256',
        headers=headers
    )
    
    return token

def make_api_request(endpoint, method='GET'):
    """Make API request with JWT authentication"""
    
    # Credentials
    api_key_name = "organizations/7405b51f-cfea-4f54-a52d-02838b5cb217/apiKeys/b71fc94b-f040-4d88-9435-7ee897421f33"
    key_id = "b71fc94b-f040-4d88-9435-7ee897421f33"
    private_key_pem = "[REDACTED_FOR_SECURITY]"  # EC private key in PEM format
    
    # Generate JWT token
    jwt_token = create_jwt_token(api_key_name, private_key_pem, key_id, method, endpoint)
    
    # Create request headers
    request_headers = {
        'Authorization': f'Bearer {jwt_token}',
        'Content-Type': 'application/json'
    }
    
    # Make the API request
    url = f'https://api.coinbase.com{endpoint}'
    
    response = requests.request(
        method=method,
        url=url,
        headers=request_headers,
        timeout=10
    )
    
    return response

# Test both endpoints
public_response = make_api_request('/api/v3/brokerage/time', 'GET')
private_response = make_api_request('/api/v3/brokerage/accounts', 'GET')
```

## 2. Exact Request Details

### Working Public Endpoint Request:
```
GET https://api.coinbase.com/api/v3/brokerage/time
Headers:
  Authorization: Bearer eyJhbGciOiJFUzI1NiIsImtpZCI6ImI3MWZjOTRiLWYwNDAtNGQ4OC05NDM1LTdlZTg5NzQyMWYzMyIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.example_signature
  Content-Type: application/json

Response: 200 OK
```

### Failing Private Endpoint Request:
```
GET https://api.coinbase.com/api/v3/brokerage/accounts
Headers:
  Authorization: Bearer eyJhbGciOiJFUzI1NiIsImtpZCI6ImI3MWZjOTRiLWYwNDAtNGQ4OC05NDM1LTdlZTg5NzQyMWYzMyIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************.example_signature
  Content-Type: application/json

Response: 401 Unauthorized
```

## 3. JWT Token Breakdown

### Header:
```json
{
  "alg": "ES256",
  "kid": "b71fc94b-f040-4d88-9435-7ee897421f33",
  "typ": "JWT"
}
```

### Payload (for accounts endpoint):
```json
{
  "iss": "cdp",
  "nbf": **********,
  "exp": **********,
  "sub": "organizations/7405b51f-cfea-4f54-a52d-02838b5cb217/apiKeys/b71fc94b-f040-4d88-9435-7ee897421f33",
  "uri": "GET /api/v3/brokerage/accounts"
}
```

## 4. Key Observations

1. **Identical Code**: The exact same JWT generation code works for public endpoints but fails for private endpoints
2. **Valid Signature**: The ES256 signature is mathematically valid (proven by public endpoint success)
3. **Correct Format**: JWT structure matches your documentation precisely
4. **Multiple Keys Tested**: Same behavior across 3 different regenerated API keys
5. **Permissions Configured**: API key shows "View, Trade, Transfer" permissions in dashboard

## 5. Environment Details

- **Python Version**: 3.11.x
- **JWT Library**: PyJWT 2.8.0
- **Cryptography Library**: cryptography 41.0.x
- **Request Method**: Python requests library
- **IP Address**: ************ (Belgium)
- **Account Status**: Fully verified (email, phone, identity)

## 6. Request for Investigation

Since the technical implementation is proven correct by the working public endpoint, could you please investigate:

1. **Account-level permissions**: Are there hidden restrictions on private API access?
2. **Regional limitations**: Any Belgium/EU specific restrictions?
3. **Compliance flags**: Any pending reviews or restrictions?
4. **Backend permission sync**: Mismatch between dashboard permissions and actual backend access?

The issue appears to be account/permission-related rather than technical implementation.

Thanks for your assistance!

Best regards,
Stijn Herman
<EMAIL>
