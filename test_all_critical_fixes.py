#!/usr/bin/env python3
"""
Comprehensive Test for All Critical Fixes
Tests Coinbase authentication, Bybit connectivity, strategy diversification, and automatic execution
"""

import os
import sys
import asyncio
import traceback
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Load environment
from dotenv import load_dotenv
load_dotenv()

class CriticalFixesValidator:
    """Comprehensive validator for all critical fixes"""
    
    def __init__(self):
        self.test_results = {}
        self.components = {}
        
    async def run_comprehensive_tests(self):
        """Run all critical fix tests"""
        print("🔧 [VALIDATOR] Starting comprehensive critical fixes validation...")
        print(f"🕒 [VALIDATOR] Test started at: {datetime.now()}")
        
        # Test 1: Coinbase API Authentication
        await self._test_coinbase_authentication()
        
        # Test 2: Bybit Connectivity
        await self._test_bybit_connectivity()
        
        # Test 3: Strategy Diversification
        await self._test_strategy_diversification()
        
        # Test 4: Intelligent Strategy Selection
        await self._test_intelligent_strategy_selection()
        
        # Test 5: Automatic Execution (No User Prompts)
        await self._test_automatic_execution()
        
        # Test 6: Integration Test
        await self._test_system_integration()
        
        # Generate final report
        self._generate_test_report()
        
        return self._calculate_overall_success()
    
    async def _test_coinbase_authentication(self):
        """Test Coinbase API authentication with fixed JWT implementation"""
        print("\n📋 [TEST-1] Testing Coinbase API Authentication...")
        
        try:
            # Test the fixed Coinbase client
            from src.exchanges.coinbase_client import CoinbaseClient
            
            # Get credentials
            api_key_name = os.getenv('COINBASE_API_KEY_NAME')
            private_key = os.getenv('COINBASE_PRIVATE_KEY')
            
            if not api_key_name or not private_key:
                self.test_results['coinbase_auth'] = {
                    'status': 'SKIPPED',
                    'reason': 'Credentials not available',
                    'details': 'COINBASE_API_KEY_NAME or COINBASE_PRIVATE_KEY not found'
                }
                print("⚠️ [TEST-1] Skipped - Coinbase credentials not available")
                return
            
            # Test JWT creation
            client = CoinbaseClient(api_key_name, private_key)
            
            # Test JWT token creation (this should not fail with the fix)
            try:
                headers = client._get_headers('GET', '/api/v3/brokerage/accounts')
                if 'Authorization' in headers and 'Bearer' in headers['Authorization']:
                    print("✅ [TEST-1] JWT token creation successful")
                    jwt_test_passed = True
                else:
                    print("❌ [TEST-1] JWT token creation failed - no Bearer token")
                    jwt_test_passed = False
            except Exception as e:
                print(f"❌ [TEST-1] JWT token creation failed: {e}")
                jwt_test_passed = False
            
            # Test API call (if possible)
            api_test_passed = False
            try:
                # This would make an actual API call - be careful with rate limits
                # For now, just test that the method exists and can be called
                if hasattr(client, 'get_accounts'):
                    print("✅ [TEST-1] API methods available")
                    api_test_passed = True
                else:
                    print("❌ [TEST-1] API methods not available")
            except Exception as e:
                print(f"⚠️ [TEST-1] API test warning: {e}")
                api_test_passed = True  # Don't fail on API limits
            
            self.test_results['coinbase_auth'] = {
                'status': 'PASSED' if jwt_test_passed and api_test_passed else 'FAILED',
                'jwt_creation': jwt_test_passed,
                'api_methods': api_test_passed,
                'details': 'Fixed JWT implementation with ES256 and proper headers'
            }
            
            if jwt_test_passed and api_test_passed:
                print("✅ [TEST-1] Coinbase authentication fix validated")
            else:
                print("❌ [TEST-1] Coinbase authentication fix failed")
                
        except Exception as e:
            self.test_results['coinbase_auth'] = {
                'status': 'ERROR',
                'error': str(e),
                'details': traceback.format_exc()
            }
            print(f"❌ [TEST-1] Coinbase authentication test error: {e}")
    
    async def _test_bybit_connectivity(self):
        """Test Bybit connectivity"""
        print("\n📋 [TEST-2] Testing Bybit Connectivity...")
        
        try:
            from src.exchanges.bybit_client_fixed import BybitClientFixed
            
            api_key = os.getenv('BYBIT_API_KEY')
            api_secret = os.getenv('BYBIT_API_SECRET')
            
            if not api_key or not api_secret:
                self.test_results['bybit_connectivity'] = {
                    'status': 'SKIPPED',
                    'reason': 'Credentials not available'
                }
                print("⚠️ [TEST-2] Skipped - Bybit credentials not available")
                return
            
            # Test client creation
            client = BybitClientFixed(api_key, api_secret, testnet=False)
            
            # Test connection
            connection_result = client.test_connection()
            
            # Test balance retrieval
            balance_result = await client.get_balance()
            
            success = connection_result and balance_result is not None
            
            self.test_results['bybit_connectivity'] = {
                'status': 'PASSED' if success else 'FAILED',
                'connection_test': connection_result,
                'balance_test': balance_result is not None,
                'balance_amount': float(balance_result) if balance_result else 0
            }
            
            if success:
                print(f"✅ [TEST-2] Bybit connectivity validated - Balance: ${float(balance_result):.2f}")
            else:
                print("❌ [TEST-2] Bybit connectivity failed")
                
        except Exception as e:
            self.test_results['bybit_connectivity'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"❌ [TEST-2] Bybit connectivity test error: {e}")
    
    async def _test_strategy_diversification(self):
        """Test strategy diversification implementation"""
        print("\n📋 [TEST-3] Testing Strategy Diversification...")
        
        try:
            # Test strategy availability
            from src.strategies.momentum import MomentumStrategy
            from src.strategies.mean_reversion import MeanReversionStrategy
            from src.strategies.adaptive_neural_strategy import AdaptiveNeuralStrategy
            from src.strategies.neural_strategy_manager import NeuralStrategyManager
            
            strategies_available = True
            print("✅ [TEST-3] All strategy classes importable")
            
            # Test neural strategy manager
            strategy_config = {
                'momentum': {'lookback_period': 20, 'min_confidence_threshold': 0.6},
                'mean_reversion': {'lookback_period': 50, 'min_confidence_threshold': 0.6},
                'adaptive_neural': {'learning_rate': 0.001, 'min_confidence_threshold': 0.6}
            }
            
            manager = NeuralStrategyManager(strategy_config)
            manager_initialized = manager is not None
            
            available_strategies = list(manager.strategies.keys())
            strategy_count = len(available_strategies)
            
            print(f"✅ [TEST-3] Neural strategy manager initialized with {strategy_count} strategies")
            print(f"✅ [TEST-3] Available strategies: {available_strategies}")
            print(f"✅ [TEST-3] Active strategy: {manager.active_strategy}")
            
            # Test strategy activation patch
            try:
                from src.strategies.strategy_activation_patch import get_advanced_strategy_activator
                patch_available = True
                print("✅ [TEST-3] Strategy activation patch available")
            except ImportError:
                patch_available = False
                print("⚠️ [TEST-3] Strategy activation patch not found")
            
            self.test_results['strategy_diversification'] = {
                'status': 'PASSED' if strategies_available and manager_initialized else 'FAILED',
                'strategies_available': strategies_available,
                'manager_initialized': manager_initialized,
                'strategy_count': strategy_count,
                'available_strategies': available_strategies,
                'patch_available': patch_available
            }
            
            if strategies_available and manager_initialized:
                print("✅ [TEST-3] Strategy diversification validated")
            else:
                print("❌ [TEST-3] Strategy diversification failed")
                
        except Exception as e:
            self.test_results['strategy_diversification'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"❌ [TEST-3] Strategy diversification test error: {e}")
    
    async def _test_intelligent_strategy_selection(self):
        """Test intelligent strategy selection system"""
        print("\n📋 [TEST-4] Testing Intelligent Strategy Selection...")
        
        try:
            from src.strategies.intelligent_strategy_selector import create_intelligent_strategy_selector
            from src.strategies.intelligent_strategy_integration import get_intelligent_trading_orchestrator
            
            # Create mock strategies
            mock_strategies = {
                'momentum': None,
                'mean_reversion': None,
                'adaptive_neural': None
            }
            
            # Test strategy selector creation
            selector = create_intelligent_strategy_selector(mock_strategies)
            selector_created = selector is not None
            
            # Test orchestrator
            components = {}
            orchestrator = get_intelligent_trading_orchestrator(components)
            orchestrator_created = orchestrator is not None
            
            # Test market regime detection
            from src.strategies.intelligent_strategy_selector import MarketRegimeDetector
            regime_detector = MarketRegimeDetector()
            
            mock_market_data = {
                'price_data': {
                    'bybit': {
                        'BTC-USDT': {'price': 105000, 'volume': 1000000, 'change_24h': 0.02}
                    }
                }
            }
            
            market_conditions = await regime_detector.detect_regime(mock_market_data)
            regime_detection_works = market_conditions is not None
            
            print(f"✅ [TEST-4] Strategy selector created: {selector_created}")
            print(f"✅ [TEST-4] Orchestrator created: {orchestrator_created}")
            print(f"✅ [TEST-4] Regime detection works: {regime_detection_works}")
            print(f"✅ [TEST-4] Detected regime: {market_conditions.regime.value if market_conditions else 'None'}")
            
            self.test_results['intelligent_strategy_selection'] = {
                'status': 'PASSED' if all([selector_created, orchestrator_created, regime_detection_works]) else 'FAILED',
                'selector_created': selector_created,
                'orchestrator_created': orchestrator_created,
                'regime_detection': regime_detection_works,
                'detected_regime': market_conditions.regime.value if market_conditions else None
            }
            
            if all([selector_created, orchestrator_created, regime_detection_works]):
                print("✅ [TEST-4] Intelligent strategy selection validated")
            else:
                print("❌ [TEST-4] Intelligent strategy selection failed")
                
        except Exception as e:
            self.test_results['intelligent_strategy_selection'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"❌ [TEST-4] Intelligent strategy selection test error: {e}")
    
    async def _test_automatic_execution(self):
        """Test that user prompts have been removed"""
        print("\n📋 [TEST-5] Testing Automatic Execution (No User Prompts)...")
        
        try:
            # Check if backup files exist (indicating prompts were removed)
            backup_files = list(Path(".").glob("*.backup"))
            prompts_removed = len(backup_files) > 0
            
            # Test that main.py doesn't contain input() calls
            main_py_path = Path("main.py")
            if main_py_path.exists():
                with open(main_py_path, 'r', encoding='utf-8') as f:
                    main_content = f.read()
                
                # Check for remaining input() calls
                remaining_inputs = main_content.count('input(')
                no_remaining_prompts = remaining_inputs == 0
            else:
                no_remaining_prompts = False
            
            # Check batch files
            batch_files_fixed = True
            for batch_file in ["MASTER_LIVE_TRADING_LAUNCHER.bat", "LAUNCH_LIVE_TRADING.ps1"]:
                if Path(batch_file).exists():
                    with open(batch_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    if 'confirmation=YES' not in content and 'confirmation = "YES"' not in content:
                        batch_files_fixed = False
            
            self.test_results['automatic_execution'] = {
                'status': 'PASSED' if prompts_removed and no_remaining_prompts and batch_files_fixed else 'FAILED',
                'backup_files_count': len(backup_files),
                'remaining_input_calls': remaining_inputs if 'remaining_inputs' in locals() else 0,
                'batch_files_fixed': batch_files_fixed
            }
            
            if prompts_removed and no_remaining_prompts and batch_files_fixed:
                print(f"✅ [TEST-5] Automatic execution validated - {len(backup_files)} files modified")
            else:
                print("❌ [TEST-5] Automatic execution validation failed")
                
        except Exception as e:
            self.test_results['automatic_execution'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"❌ [TEST-5] Automatic execution test error: {e}")
    
    async def _test_system_integration(self):
        """Test overall system integration"""
        print("\n📋 [TEST-6] Testing System Integration...")
        
        try:
            # Test that main.py can be imported without errors
            import main
            main_importable = True
            print("✅ [TEST-6] Main system importable")
            
            # Test that critical components can be initialized
            components_available = hasattr(main, 'ComprehensiveLiveTradingSystem')
            
            # Test that the system has the required methods
            if components_available:
                system_class = main.ComprehensiveLiveTradingSystem
                required_methods = [
                    'initialize_comprehensive_system',
                    '_init_trading_engines',
                    '_init_neural_quantum_systems'
                ]
                
                methods_available = all(hasattr(system_class, method) for method in required_methods)
            else:
                methods_available = False
            
            self.test_results['system_integration'] = {
                'status': 'PASSED' if main_importable and components_available and methods_available else 'FAILED',
                'main_importable': main_importable,
                'components_available': components_available,
                'methods_available': methods_available
            }
            
            if main_importable and components_available and methods_available:
                print("✅ [TEST-6] System integration validated")
            else:
                print("❌ [TEST-6] System integration failed")
                
        except Exception as e:
            self.test_results['system_integration'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"❌ [TEST-6] System integration test error: {e}")
    
    def _generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*60)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASSED')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAILED')
        error_tests = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        skipped_tests = sum(1 for result in self.test_results.values() if result['status'] == 'SKIPPED')
        
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"🔥 Errors: {error_tests}")
        print(f"⚠️ Skipped: {skipped_tests}")
        print(f"📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n📋 DETAILED RESULTS:")
        for test_name, result in self.test_results.items():
            status_emoji = {
                'PASSED': '✅',
                'FAILED': '❌',
                'ERROR': '🔥',
                'SKIPPED': '⚠️'
            }
            
            print(f"{status_emoji[result['status']]} {test_name.upper()}: {result['status']}")
            
            if result['status'] == 'ERROR':
                print(f"   Error: {result.get('error', 'Unknown error')}")
            elif result['status'] == 'SKIPPED':
                print(f"   Reason: {result.get('reason', 'Unknown reason')}")
    
    def _calculate_overall_success(self) -> bool:
        """Calculate overall test success"""
        critical_tests = ['bybit_connectivity', 'strategy_diversification', 'automatic_execution', 'system_integration']
        
        critical_passed = all(
            self.test_results.get(test, {}).get('status') in ['PASSED', 'SKIPPED']
            for test in critical_tests
        )
        
        return critical_passed

async def main():
    """Main test execution"""
    print("=" * 60)
    print("🔧 CRITICAL FIXES COMPREHENSIVE VALIDATION")
    print("🚨 TESTING ALL IMPLEMENTED FIXES")
    print("=" * 60)
    
    validator = CriticalFixesValidator()
    
    try:
        success = await validator.run_comprehensive_tests()
        
        if success:
            print("\n🎉 [SUCCESS] All critical fixes validated successfully!")
            print("✅ [RESULT] Trading system is ready for deployment")
            return 0
        else:
            print("\n❌ [FAILED] Some critical fixes failed validation")
            print("❌ [RESULT] Review failed tests before deployment")
            return 1
            
    except Exception as e:
        print(f"\n💥 [CRITICAL-ERROR] Validation failed: {e}")
        print(f"💥 [TRACEBACK] {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Validation stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Validation system error: {e}")
        sys.exit(1)
