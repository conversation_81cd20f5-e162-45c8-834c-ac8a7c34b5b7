#!/usr/bin/env python3
"""
Generate Ed25519 Private Key for Coinbase API
==============================================

This script generates a new Ed25519 private key to match the Ed25519 signature
algorithm selected for the Coinbase API key (6548b65d-0ea5-40f0-b2ac-c0c89ca1bcd2).

The current private key uses secp256r1 (ECDSA) curve, but the API key was created
with Ed25519 signature algorithm, causing authentication failures.
"""

import os
import sys
from pathlib import Path
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ed25519
from dotenv import load_dotenv

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def generate_ed25519_keypair():
    """Generate a new Ed25519 private/public key pair"""
    
    print("🔑 Generating Ed25519 Key Pair")
    print("=" * 40)
    
    # Generate Ed25519 private key
    private_key = ed25519.Ed25519PrivateKey.generate()
    public_key = private_key.public_key()
    
    print("✅ Generated Ed25519 private key")
    print("✅ Generated Ed25519 public key")
    
    # Serialize private key to PEM format
    private_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    # Serialize public key to PEM format
    public_pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    
    print("✅ Serialized keys to PEM format")
    
    return private_pem, public_pem

def backup_existing_keys():
    """Backup existing ECDSA keys before replacing"""
    
    print("\n📦 Backing up existing ECDSA keys...")
    
    backup_dir = Path("backup_ecdsa_keys")
    backup_dir.mkdir(exist_ok=True)
    
    # Files to backup
    key_files = [
        "src/utils/cryptography/private.pem",
        "src/utils/cryptography/public.pem",
        "private.pem",
        "public.pem"
    ]
    
    backed_up = []
    for key_file in key_files:
        if Path(key_file).exists():
            backup_path = backup_dir / f"{Path(key_file).name}.backup"
            Path(key_file).rename(backup_path)
            backed_up.append(f"{key_file} -> {backup_path}")
            print(f"✅ Backed up {key_file}")
    
    if backed_up:
        print(f"✅ Backed up {len(backed_up)} existing key files")
        return True
    else:
        print("ℹ️ No existing key files found to backup")
        return False

def save_new_keys(private_pem, public_pem):
    """Save the new Ed25519 keys to the expected locations"""
    
    print("\n💾 Saving new Ed25519 keys...")
    
    # Ensure directories exist
    crypto_dir = Path("src/utils/cryptography")
    crypto_dir.mkdir(parents=True, exist_ok=True)
    
    # Save keys to both locations (for compatibility)
    key_locations = [
        ("src/utils/cryptography/private.pem", private_pem),
        ("src/utils/cryptography/public.pem", public_pem),
        ("private.pem", private_pem),
        ("public.pem", public_pem)
    ]
    
    for location, key_data in key_locations:
        with open(location, 'wb') as f:
            f.write(key_data)
        print(f"✅ Saved {location}")
    
    print("✅ All Ed25519 keys saved successfully")

def verify_key_format(private_pem):
    """Verify the generated key is Ed25519 format"""
    
    print("\n🔍 Verifying key format...")
    
    try:
        # Load the private key to verify it's Ed25519
        private_key = serialization.load_pem_private_key(
            private_pem,
            password=None
        )
        
        if isinstance(private_key, ed25519.Ed25519PrivateKey):
            print("✅ Verified: Key is Ed25519 format")
            print(f"✅ Key size: 256 bits (Ed25519 standard)")
            return True
        else:
            print(f"❌ Error: Key is {type(private_key)} instead of Ed25519")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying key: {e}")
        return False

def main():
    """Generate new Ed25519 keys for Coinbase API authentication"""
    
    print("🔐 Ed25519 Key Generation for Coinbase API")
    print("=" * 50)
    print("📋 API Key: 6548b65d-0ea5-40f0-b2ac-c0c89ca1bcd2")
    print("📋 Algorithm: Ed25519 (as configured in Coinbase)")
    print("📋 Purpose: Replace ECDSA keys to match API key signature algorithm")
    print()
    
    try:
        # Step 1: Generate new Ed25519 key pair
        private_pem, public_pem = generate_ed25519_keypair()
        
        # Step 2: Verify the key format
        if not verify_key_format(private_pem):
            print("❌ Key verification failed")
            return False
        
        # Step 3: Backup existing keys
        backup_existing_keys()
        
        # Step 4: Save new keys
        save_new_keys(private_pem, public_pem)
        
        print("\n🎉 SUCCESS! Ed25519 keys generated and installed")
        print("=" * 50)
        print("✅ New Ed25519 private key matches API key signature algorithm")
        print("✅ Existing ECDSA keys backed up to backup_ecdsa_keys/")
        print("✅ Ready to test Coinbase API authentication")
        print()
        print("Next steps:")
        print("1. Run debug_jwt_token.py to test the new Ed25519 authentication")
        print("2. The signature algorithm mismatch should now be resolved")
        print("3. You should see successful API calls instead of 401 errors")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating keys: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
