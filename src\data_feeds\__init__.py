<<<<<<< Updated upstream
=======
"""
Data Feeds Module - Enhanced Production System
Provides comprehensive market data feeds from multiple cryptocurrency exchanges
with advanced caching, monitoring, streaming, and data validation
"""

from .binance_feed import BinanceDataFeed
from .coinbase_feed import CoinbaseDataFeed
from .market_data import MarketDataAggregator, MarketDataStreamer, AlphaFactory
from .cache import CacheManager, MemoryCache
from .monitoring import DataFeedMonitor, MetricsCollector
from .data_validator import DataValidator as DataFeedValidator
from .websocket_manager import WebSocketManager, StreamingDataRouter, DataFeedsDashboard

# Real-time data infrastructure
try:
    from .real_time_data_aggregator import (
        RealTimeDataAggregator,
        AggregatedMarketData,
        RealTimeDataPoint,
        DataSourceStatus,
        real_time_aggregator,
        get_real_time_market_data,
        initialize_real_time_data_infrastructure
    )

    from .intelligent_data_router import (
        IntelligentDataRouter,
        RoutingStrategy,
        SourceMetrics,
        intelligent_router,
        get_intelligent_router
    )

    from .web_crawling_infrastructure import (
        CryptoPriceCrawler,
        CryptoNewsCrawler,
        CrawledData,
        NewsItem,
        CrawlerStatus,
        price_crawler,
        news_crawler,
        initialize_web_crawling_infrastructure,
        get_crawled_price_data,
        get_crypto_news
    )

    REAL_TIME_INFRASTRUCTURE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ [DATA-FEEDS] Real-time infrastructure not available: {e}")
    REAL_TIME_INFRASTRUCTURE_AVAILABLE = False

__all__ = [
    'BinanceDataFeed',
    'CoinbaseDataFeed',
    'MarketDataAggregator',
    'MarketDataStreamer',
    'AlphaFactory',
    'CacheManager',
    'MemoryCache',
    'DataFeedMonitor',
    'MetricsCollector',
    'DataFeedValidator',
    'WebSocketManager',
    'StreamingDataRouter',
    'DataFeedsDashboard'
]

# Add real-time infrastructure to exports if available
if REAL_TIME_INFRASTRUCTURE_AVAILABLE:
    __all__.extend([
        # Real-time data aggregator
        'RealTimeDataAggregator',
        'AggregatedMarketData',
        'RealTimeDataPoint',
        'DataSourceStatus',
        'real_time_aggregator',
        'get_real_time_market_data',
        'initialize_real_time_data_infrastructure',

        # Intelligent data router
        'IntelligentDataRouter',
        'RoutingStrategy',
        'SourceMetrics',
        'intelligent_router',
        'get_intelligent_router',

        # Web crawling infrastructure
        'CryptoPriceCrawler',
        'CryptoNewsCrawler',
        'CrawledData',
        'NewsItem',
        'CrawlerStatus',
        'price_crawler',
        'news_crawler',
        'initialize_web_crawling_infrastructure',
        'get_crawled_price_data',
        'get_crypto_news'
    ])
>>>>>>> Stashed changes
