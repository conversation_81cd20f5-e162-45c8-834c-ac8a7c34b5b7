#!/usr/bin/env python3
"""
Trading Performance Optimizer
Enterprise-grade performance optimization for trading operations
"""

import time
import asyncio
import logging
import statistics
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from collections import deque
import threading

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    execution_times: deque = field(default_factory=lambda: deque(maxlen=100))
    api_response_times: deque = field(default_factory=lambda: deque(maxlen=100))
    order_success_rate: float = 0.0
    average_execution_time: float = 0.0
    average_api_response_time: float = 0.0
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    cache_hit_rate: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0

class TradingPerformanceOptimizer:
    """Enterprise-grade trading performance optimization system"""
    
    def __init__(self):
        # Performance tracking
        self.metrics = PerformanceMetrics()
        self.performance_lock = threading.RLock()
        
        # Optimization settings
        self.optimization_enabled = True
        self.auto_optimization = True
        self.performance_threshold_ms = 1000  # 1 second threshold
        self.cache_enabled = True
        self.batch_processing_enabled = True
        
        # Caching system
        self.price_cache = {}
        self.balance_cache = {}
        self.market_data_cache = {}
        self.cache_ttl_seconds = 5  # 5 second cache TTL
        self.cache_stats = {'hits': 0, 'misses': 0}
        
        # Batch processing
        self.batch_queue = deque()
        self.batch_size = 10
        self.batch_timeout_ms = 100
        
        # Connection pooling
        self.connection_pool_size = 5
        self.connection_timeout = 30
        
        logger.info("[PERFORMANCE-OPTIMIZER] Trading performance optimizer initialized")
    
    def start_operation_timer(self) -> float:
        """Start timing an operation"""
        return time.time()
    
    def end_operation_timer(self, start_time: float, operation_type: str = "general") -> float:
        """End timing an operation and record metrics"""
        try:
            execution_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            with self.performance_lock:
                self.metrics.execution_times.append(execution_time)
                self.metrics.total_operations += 1
                
                # Update average execution time
                if self.metrics.execution_times:
                    self.metrics.average_execution_time = statistics.mean(self.metrics.execution_times)
                
                # Log slow operations
                if execution_time > self.performance_threshold_ms:
                    logger.warning(f"[PERFORMANCE] Slow {operation_type} operation: {execution_time:.1f}ms")
                
                # Auto-optimization trigger
                if self.auto_optimization and execution_time > self.performance_threshold_ms * 2:
                    self._trigger_auto_optimization(operation_type, execution_time)
            
            return execution_time
            
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error recording operation timing: {e}")
            return 0.0
    
    def record_api_response_time(self, response_time_ms: float):
        """Record API response time"""
        try:
            with self.performance_lock:
                self.metrics.api_response_times.append(response_time_ms)
                
                if self.metrics.api_response_times:
                    self.metrics.average_api_response_time = statistics.mean(self.metrics.api_response_times)
                    
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error recording API response time: {e}")
    
    def record_operation_result(self, success: bool):
        """Record operation success/failure"""
        try:
            with self.performance_lock:
                if success:
                    self.metrics.successful_operations += 1
                else:
                    self.metrics.failed_operations += 1
                
                # Update success rate
                total_ops = self.metrics.successful_operations + self.metrics.failed_operations
                if total_ops > 0:
                    self.metrics.order_success_rate = (self.metrics.successful_operations / total_ops) * 100
                    
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error recording operation result: {e}")
    
    def get_cached_data(self, cache_type: str, key: str) -> Optional[Any]:
        """Get data from cache if available and not expired"""
        try:
            if not self.cache_enabled:
                return None
            
            cache_dict = getattr(self, f"{cache_type}_cache", {})
            
            if key in cache_dict:
                data, timestamp = cache_dict[key]
                
                # Check if cache is still valid
                if time.time() - timestamp < self.cache_ttl_seconds:
                    self.cache_stats['hits'] += 1
                    
                    # Update cache hit rate
                    total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
                    self.metrics.cache_hit_rate = (self.cache_stats['hits'] / total_requests) * 100
                    
                    return data
                else:
                    # Cache expired, remove it
                    del cache_dict[key]
            
            self.cache_stats['misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error getting cached data: {e}")
            return None
    
    def set_cached_data(self, cache_type: str, key: str, data: Any):
        """Set data in cache with timestamp"""
        try:
            if not self.cache_enabled:
                return
            
            cache_dict = getattr(self, f"{cache_type}_cache", {})
            cache_dict[key] = (data, time.time())
            
            # Limit cache size to prevent memory issues
            max_cache_size = 1000
            if len(cache_dict) > max_cache_size:
                # Remove oldest entries
                oldest_keys = sorted(cache_dict.keys(), 
                                   key=lambda k: cache_dict[k][1])[:len(cache_dict) - max_cache_size + 100]
                for old_key in oldest_keys:
                    del cache_dict[old_key]
                    
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error setting cached data: {e}")
    
    def clear_cache(self, cache_type: Optional[str] = None):
        """Clear cache data"""
        try:
            if cache_type:
                cache_dict = getattr(self, f"{cache_type}_cache", {})
                cache_dict.clear()
                logger.info(f"[PERFORMANCE] Cleared {cache_type} cache")
            else:
                # Clear all caches
                self.price_cache.clear()
                self.balance_cache.clear()
                self.market_data_cache.clear()
                logger.info("[PERFORMANCE] Cleared all caches")
                
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error clearing cache: {e}")
    
    def add_to_batch_queue(self, operation: Dict):
        """Add operation to batch processing queue"""
        try:
            if not self.batch_processing_enabled:
                return False
            
            self.batch_queue.append({
                'operation': operation,
                'timestamp': time.time()
            })
            
            # Process batch if it's full or timeout reached
            if (len(self.batch_queue) >= self.batch_size or 
                self._should_process_batch_timeout()):
                self._process_batch_queue()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error adding to batch queue: {e}")
            return False
    
    def _should_process_batch_timeout(self) -> bool:
        """Check if batch should be processed due to timeout"""
        try:
            if not self.batch_queue:
                return False
            
            oldest_item = self.batch_queue[0]
            time_since_oldest = (time.time() - oldest_item['timestamp']) * 1000
            
            return time_since_oldest >= self.batch_timeout_ms
            
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error checking batch timeout: {e}")
            return False
    
    def _process_batch_queue(self):
        """Process the batch queue"""
        try:
            if not self.batch_queue:
                return
            
            batch_items = list(self.batch_queue)
            self.batch_queue.clear()
            
            logger.info(f"[PERFORMANCE] Processing batch of {len(batch_items)} operations")
            
            # Group operations by type for efficient processing
            operation_groups = {}
            for item in batch_items:
                op_type = item['operation'].get('type', 'unknown')
                if op_type not in operation_groups:
                    operation_groups[op_type] = []
                operation_groups[op_type].append(item['operation'])
            
            # Process each group
            for op_type, operations in operation_groups.items():
                logger.debug(f"[PERFORMANCE] Processing {len(operations)} {op_type} operations")
                # Here you would implement specific batch processing logic
                # For now, we just log the batch processing
                
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error processing batch queue: {e}")
    
    def _trigger_auto_optimization(self, operation_type: str, execution_time: float):
        """Trigger automatic optimization for slow operations"""
        try:
            logger.info(f"[PERFORMANCE] Auto-optimization triggered for {operation_type} "
                       f"(execution time: {execution_time:.1f}ms)")
            
            # Implement specific optimizations based on operation type
            if operation_type == "api_call":
                self._optimize_api_calls()
            elif operation_type == "balance_check":
                self._optimize_balance_checks()
            elif operation_type == "price_fetch":
                self._optimize_price_fetching()
            else:
                self._apply_general_optimizations()
                
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error in auto-optimization: {e}")
    
    def _optimize_api_calls(self):
        """Optimize API call performance"""
        try:
            # Increase cache TTL for API calls
            if self.cache_ttl_seconds < 10:
                self.cache_ttl_seconds = min(self.cache_ttl_seconds + 2, 10)
                logger.info(f"[PERFORMANCE] Increased cache TTL to {self.cache_ttl_seconds}s")
            
            # Enable batch processing if not already enabled
            if not self.batch_processing_enabled:
                self.batch_processing_enabled = True
                logger.info("[PERFORMANCE] Enabled batch processing")
                
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error optimizing API calls: {e}")
    
    def _optimize_balance_checks(self):
        """Optimize balance check performance"""
        try:
            # Increase balance cache TTL
            balance_cache_ttl = getattr(self, 'balance_cache_ttl', self.cache_ttl_seconds)
            if balance_cache_ttl < 15:
                self.balance_cache_ttl = min(balance_cache_ttl + 3, 15)
                logger.info(f"[PERFORMANCE] Increased balance cache TTL to {self.balance_cache_ttl}s")
                
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error optimizing balance checks: {e}")
    
    def _optimize_price_fetching(self):
        """Optimize price fetching performance"""
        try:
            # Increase price cache TTL
            price_cache_ttl = getattr(self, 'price_cache_ttl', self.cache_ttl_seconds)
            if price_cache_ttl < 8:
                self.price_cache_ttl = min(price_cache_ttl + 2, 8)
                logger.info(f"[PERFORMANCE] Increased price cache TTL to {self.price_cache_ttl}s")
                
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error optimizing price fetching: {e}")
    
    def _apply_general_optimizations(self):
        """Apply general performance optimizations"""
        try:
            # Reduce batch timeout for faster processing
            if self.batch_timeout_ms > 50:
                self.batch_timeout_ms = max(self.batch_timeout_ms - 10, 50)
                logger.info(f"[PERFORMANCE] Reduced batch timeout to {self.batch_timeout_ms}ms")
            
            # Increase batch size for better throughput
            if self.batch_size < 20:
                self.batch_size = min(self.batch_size + 2, 20)
                logger.info(f"[PERFORMANCE] Increased batch size to {self.batch_size}")
                
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error applying general optimizations: {e}")
    
    def get_performance_report(self) -> Dict:
        """Get comprehensive performance report"""
        try:
            with self.performance_lock:
                return {
                    'execution_metrics': {
                        'average_execution_time_ms': self.metrics.average_execution_time,
                        'average_api_response_time_ms': self.metrics.average_api_response_time,
                        'total_operations': self.metrics.total_operations,
                        'successful_operations': self.metrics.successful_operations,
                        'failed_operations': self.metrics.failed_operations,
                        'success_rate_percentage': self.metrics.order_success_rate
                    },
                    'cache_metrics': {
                        'cache_hit_rate_percentage': self.metrics.cache_hit_rate,
                        'cache_hits': self.cache_stats['hits'],
                        'cache_misses': self.cache_stats['misses'],
                        'cache_ttl_seconds': self.cache_ttl_seconds
                    },
                    'optimization_settings': {
                        'optimization_enabled': self.optimization_enabled,
                        'auto_optimization': self.auto_optimization,
                        'cache_enabled': self.cache_enabled,
                        'batch_processing_enabled': self.batch_processing_enabled,
                        'batch_size': self.batch_size,
                        'batch_timeout_ms': self.batch_timeout_ms
                    },
                    'performance_thresholds': {
                        'performance_threshold_ms': self.performance_threshold_ms,
                        'cache_ttl_seconds': self.cache_ttl_seconds
                    }
                }
                
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error generating performance report: {e}")
            return {}
    
    def reset_metrics(self):
        """Reset performance metrics"""
        try:
            with self.performance_lock:
                self.metrics = PerformanceMetrics()
                self.cache_stats = {'hits': 0, 'misses': 0}
                logger.info("[PERFORMANCE] Performance metrics reset")
                
        except Exception as e:
            logger.error(f"[PERFORMANCE] Error resetting metrics: {e}")

# Global performance optimizer instance
performance_optimizer = TradingPerformanceOptimizer()
