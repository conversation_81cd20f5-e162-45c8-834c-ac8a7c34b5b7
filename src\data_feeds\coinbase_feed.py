from coinbase.rest import RESTClient
import numpy as np
import base64
import hashlib
import hmac
import time

class CoinbaseFeed:
    def __init__(self, api_key: str, secret_key: str):
        self.client = RESTClient(api_key=api_key, secret_key=secret_key)
        
    async def get_historical_data(self, product_id: str, granularity: int=3600, limit: int=300):
        """Get historical candles (granularity in seconds)"""
        resp = self.client.get_product_candles(
            product_id=product_id,
            granularity=str(granularity),
            limit=str(limit)
        )
        return {
            "prices": np.array([float(c['close']) for c in resp['candles']]),
            "volumes": np.array([float(c['volume']) for c in resp['candles']])
        }
        def get_prices(self, product_id: str) -> list[float]:
            candles = self.client.get_product_candles(product_id, "ONE_HOUR")
        return [float(c["close"]) for c in candles["candles"]]

from binance import AsyncClient

class BinanceDataFeed:
    def __init__(self, api_key: str, api_secret: str):
        self.client = AsyncClient(api_key, api_secret)
    
    async def get_historical_data(self, symbol: str, interval: str, limit: int):
        return await self.client.get_klines(
            symbol=symbol,
            interval=interval,
            limit=limit
        )        