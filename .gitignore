# Python
__pycache__/
*.py[cod]
*.pyc
.python-version
*.egg-info/
*.egg
dist/
build/
.venv/
venv/

# Environment and Configuration Files
.env
.env.*
!.env.example
!.env.template
/config/config.json
/config/secrets.*

# Data and Logs
/data/
/logs/
*.log

# Testing
.pytest_cache/

# Poetry
poetry.lock

# IDE
.vscode/
.idea/
*.swp

# System
.DS_Store
Thumbs.db

# Docker
docker-compose.db.yml
secure_storage/
/backend/.env
/backend/config/secrets.*

# Vault and Security Files
.env.vault
*.hcl
vault.log
vault.zip
vault/vault.exe
<<<<<<< Updated upstream

echo "vault.crt" >> .gitignore
echo "vault.csr" >> .gitignore
echo "vault.key" >> .gitignore
.env
=======
vault.csr
vault.crt
vault.key
secret.key
.fernet_key

# Credential Files
*credentials.txt
*_credentials.txt
Credentials.txt
api_keys.txt

# Certificate Files
ca.key
ca.crt
ca.srl
private.pem
public.pem
*.pem
*.key
*.crt
*.csr

# Security Audit Files
security_audit.log
>>>>>>> Stashed changes
