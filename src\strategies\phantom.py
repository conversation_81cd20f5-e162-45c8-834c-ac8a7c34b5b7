from solana.rpc.async_api import AsyncClient
from solana.keypair import Keypair
from cryptography.fernet import <PERSON>rne<PERSON>
from config import Config

class PhantomWallet:
    def __init__(self):
        self.cipher = Fernet(Config.ENCRYPTION_KEY)
        self.keypair = Keypair.from_secret_key(
            self.cipher.decrypt(Config.PHANTOM_PRIVATE_KEY.encode())
        )
        self.client = AsyncClient("https://api.mainnet-beta.solana.com")
    
    async def get_balance(self) -> float:
        balance = await self.client.get_balance(self.keypair.public_key)
        return balance["result"]["value"] / 1_000_000_000  # SOL balance

from jsonschema import Draft7Validator

class ConfigManager:
    def __init__(self):
        self.validator = Draft7Validator(self._load_schema())
        
    def validate(self, config: dict) -> list:
        """Return list of errors instead of raising"""
        return list(self.validator.iter_errors(config))