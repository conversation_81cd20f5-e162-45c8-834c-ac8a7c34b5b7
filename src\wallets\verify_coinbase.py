import asyncio
from .coinbase import SecureCoinbaseClient
from .security_monitor import audit_system

async def test_connection():
    client = SecureCoinbaseClient()
    try:
        # Test balance check instead of transaction
        balance = await client.client.get_balances()
        print(f"✅ Connection successful. Org balance: {balance}")
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
    finally:
        audit_system()

if __name__ == "__main__":
    asyncio.run(test_connection())