"""
Professional Trading Integration Module
Integrates all professional-grade enhancements into a unified trading system
"""

import logging
import asyncio
from typing import Dict, Optional
from decimal import Decimal

# Import professional-grade components
from src.core.engine import InstitutionalTradingEngine
from src.capital_manager import ProfessionalCapitalManager
from src.capital_allocator import ProfessionalCapitalAllocator
from src.data_analysis.advanced_data_utilization import AdvancedDataUtilizationEngine

logger = logging.getLogger(__name__)

class ProfessionalTradingSystem:
    """
    Unified professional trading system that integrates all enhanced components
    """
    
    def __init__(self, config: Dict, wallet_manager, profit_tracker):
        self.config = config
        self.wallet_manager = wallet_manager
        self.profit_tracker = profit_tracker
        
        # Initialize professional-grade components
        self.trading_engine = InstitutionalTradingEngine(config)
        self.capital_manager = ProfessionalCapitalManager(
            wallet_manager, profit_tracker
        )
        self.capital_allocator = ProfessionalCapitalAllocator(
            profit_tracker, wallet_manager, self.capital_manager
        )
        self.data_utilization_engine = AdvancedDataUtilizationEngine()
        
        # System state
        self.is_running = False
        self.performance_metrics = {}
        
        logger.info("Professional Trading System initialized with all enhanced components")
    
    async def execute_professional_trade_cycle(self, symbol: str, market_data: Dict, 
                                             sentiment_data: Optional[Dict] = None) -> Dict:
        """
        Execute a complete professional trading cycle using all enhanced components
        """
        try:
            logger.info(f"Executing professional trade cycle for {symbol}")
            
            # 1. Advanced data utilization and analysis
            comprehensive_data = {
                'price_feeds': market_data,
                'sentiment_analysis': sentiment_data or {},
                'technical_indicators': market_data.get('technical', {}),
                'volume_analysis': market_data.get('volume_history', []),
                'order_books': market_data.get('order_book', {})
            }
            
            data_analysis = await self.data_utilization_engine.process_comprehensive_market_data(
                symbol, comprehensive_data
            )
            
            # 2. Professional trading engine analysis
            engine_analysis = await self.trading_engine.execute_professional_trade(
                symbol, market_data, sentiment_data
            )
            
            # 3. Capital management optimization
            optimal_position_size = await self.capital_manager.calculate_optimal_position_size(
                symbol, market_data, engine_analysis.get('confidence', 0.5)
            )
            
            # 4. Risk assessment
            portfolio_risk = await self.capital_manager.calculate_portfolio_risk()
            
            # 5. Synthesize final decision
            final_decision = self._synthesize_professional_decision(
                symbol, data_analysis, engine_analysis, optimal_position_size, portfolio_risk
            )
            
            # 6. Execute trade if conditions are met
            if final_decision['action'] in ['buy', 'sell'] and final_decision['confidence'] > 0.7:
                execution_result = await self._execute_professional_trade(
                    symbol, final_decision, optimal_position_size
                )
                final_decision['execution'] = execution_result
            
            # 7. Update performance metrics
            self._update_performance_metrics(symbol, final_decision)
            
            return final_decision
            
        except Exception as e:
            logger.error(f"Error in professional trade cycle for {symbol}: {e}")
            return {
                'symbol': symbol,
                'action': 'hold',
                'confidence': 0.0,
                'error': str(e)
            }
    
    def _synthesize_professional_decision(self, symbol: str, data_analysis: Dict, 
                                        engine_analysis: Dict, position_size: Decimal, 
                                        portfolio_risk: Dict) -> Dict:
        """Synthesize final decision from all professional analyses"""
        try:
            # Extract key metrics
            data_action = data_analysis.get('action', 'hold')
            data_confidence = data_analysis.get('confidence', 0.5)
            engine_action = engine_analysis.get('action', 'hold')
            engine_confidence = engine_analysis.get('confidence', 0.5)
            
            # Weight the decisions (data analysis 60%, engine analysis 40%)
            if data_action == engine_action:
                # Both agree
                final_action = data_action
                final_confidence = (data_confidence * 0.6 + engine_confidence * 0.4) * 1.2  # Boost for agreement
            elif data_action == 'hold' or engine_action == 'hold':
                # One suggests hold
                final_action = 'hold'
                final_confidence = max(data_confidence, engine_confidence) * 0.8
            else:
                # Disagreement
                final_action = 'hold'
                final_confidence = (data_confidence + engine_confidence) / 2 * 0.6  # Reduce for disagreement
            
            # Risk adjustment
            total_risk = portfolio_risk.get('total_risk', 0.5)
            if total_risk > 0.7:
                final_confidence *= 0.7  # Reduce confidence in high-risk environment
            
            # Position size adjustment
            risk_adjusted_position = float(position_size) * final_confidence
            
            return {
                'symbol': symbol,
                'action': final_action,
                'confidence': min(1.0, final_confidence),
                'position_size': Decimal(str(risk_adjusted_position)),
                'data_analysis': data_analysis,
                'engine_analysis': engine_analysis,
                'portfolio_risk': portfolio_risk,
                'reasoning': self._generate_decision_reasoning(
                    data_analysis, engine_analysis, portfolio_risk
                )
            }
            
        except Exception as e:
            logger.error(f"Error synthesizing professional decision: {e}")
            return {
                'symbol': symbol,
                'action': 'hold',
                'confidence': 0.5,
                'position_size': Decimal('0.01')
            }
    
    def _generate_decision_reasoning(self, data_analysis: Dict, engine_analysis: Dict, 
                                   portfolio_risk: Dict) -> list:
        """Generate human-readable reasoning for the decision"""
        reasoning = []
        
        # Data analysis reasoning
        data_reasoning = data_analysis.get('reasoning', [])
        if data_reasoning:
            reasoning.extend([f"Data: {r}" for r in data_reasoning])
        
        # Engine analysis reasoning
        if 'analysis_summary' in engine_analysis:
            summary = engine_analysis['analysis_summary']
            reasoning.append(f"Engine: Trend strength {summary.get('trend_strength', 0.5):.2f}")
        
        # Risk considerations
        risk_level = portfolio_risk.get('risk_level', 'MODERATE')
        reasoning.append(f"Portfolio risk: {risk_level}")
        
        return reasoning
    
    async def _execute_professional_trade(self, symbol: str, decision: Dict, 
                                        position_size: Decimal) -> Dict:
        """Execute the professional trade"""
        try:
            logger.info(f"Executing professional trade: {decision['action']} {symbol}")
            
            # This would integrate with the actual trading execution system
            # For now, return a simulated execution result
            execution_result = {
                'status': 'success',
                'symbol': symbol,
                'action': decision['action'],
                'amount': str(position_size),
                'timestamp': 'simulated',
                'execution_price': 'market',
                'fees': '0.001'
            }
            
            logger.info(f"Professional trade executed: {execution_result}")
            return execution_result
            
        except Exception as e:
            logger.error(f"Error executing professional trade: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _update_performance_metrics(self, symbol: str, decision: Dict):
        """Update performance metrics"""
        try:
            if symbol not in self.performance_metrics:
                self.performance_metrics[symbol] = {
                    'total_decisions': 0,
                    'buy_decisions': 0,
                    'sell_decisions': 0,
                    'hold_decisions': 0,
                    'avg_confidence': 0.0,
                    'executions': 0
                }
            
            metrics = self.performance_metrics[symbol]
            metrics['total_decisions'] += 1
            
            action = decision.get('action', 'hold')
            if action == 'buy':
                metrics['buy_decisions'] += 1
            elif action == 'sell':
                metrics['sell_decisions'] += 1
            else:
                metrics['hold_decisions'] += 1
            
            # Update average confidence
            confidence = decision.get('confidence', 0.5)
            metrics['avg_confidence'] = (
                (metrics['avg_confidence'] * (metrics['total_decisions'] - 1) + confidence) /
                metrics['total_decisions']
            )
            
            if 'execution' in decision and decision['execution'].get('status') == 'success':
                metrics['executions'] += 1
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    async def start_professional_trading_system(self):
        """Start the professional trading system"""
        try:
            logger.info("Starting professional trading system...")
            self.is_running = True
            
            # Start capital allocator
            capital_allocator_task = asyncio.create_task(
                self.capital_allocator.live_rebalance()
            )
            
            logger.info("Professional trading system started successfully")
            
            # Keep the system running
            await capital_allocator_task
            
        except Exception as e:
            logger.error(f"Error starting professional trading system: {e}")
            self.is_running = False
    
    async def stop_professional_trading_system(self):
        """Stop the professional trading system"""
        try:
            logger.info("Stopping professional trading system...")
            self.is_running = False
            
            # Close trading engine
            if hasattr(self.trading_engine, 'shutdown'):
                await self.trading_engine.shutdown()
            
            logger.info("Professional trading system stopped")
            
        except Exception as e:
            logger.error(f"Error stopping professional trading system: {e}")
    
    def get_system_status(self) -> Dict:
        """Get comprehensive system status"""
        try:
            return {
                'is_running': self.is_running,
                'performance_metrics': self.performance_metrics,
                'capital_efficiency': asyncio.run(
                    self.capital_manager.get_capital_efficiency_metrics()
                ) if self.capital_manager else {},
                'allocation_summary': self.capital_allocator.get_allocation_summary() if self.capital_allocator else {}
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}
