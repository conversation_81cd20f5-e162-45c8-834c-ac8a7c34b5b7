# backend/src/core/main.py
import asyncio
import signal
import logging
import json
import os
import platform
from decimal import Decimal
from pathlib import Path
from typing import List, Dict, Optional, Any
import aiohttp
import numpy as np

# Configure absolute imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
ROOT_DIR = Path(__file__).parent.parent.parent

# Strategy imports
from src.strategies.hft_momentum import HFTMomentumStrategy
from src.utils.config import load_config, validate_config
from src.core.performance import LivePerformanceTracker
from src.utils.microstructure import OrderBookAnalyzer
from src.utils.exceptions import TradingHaltedException

# Constants
MAX_CONCURRENT_ORDERS = 100
LATENCY_THRESHOLD = 0.001  # 1ms

class LiveTradingEngine:
    def __init__(self, config_path: str = 'config/hft_config.json'):
        self.config = self._load_and_validate_config(config_path)
        self.logger = self._setup_logging()
        self.strategy = self._init_strategy()
        self.performance_tracker = LivePerformanceTracker()
        self.active = True
        self.circuit_breaker = False
        self._init_components()

    def _init_components(self):
        """Initialize all system components"""
        self.session = aiohttp.ClientSession()
        self.order_book = OrderBookAnalyzer(
            depth=self.config['order_book']['depth'],
            refresh_interval=self.config['order_book']['refresh_interval']
        )
        self.redis = self._init_redis()
        self._register_signal_handlers()

    def _init_strategy(self) -> HFTMomentumStrategy:
        """Initialize HFT Momentum strategy"""
        return HFTMomentumStrategy({
            'alpha': self.config['strategy']['alpha'],
            'risk_multiplier': self.config['strategy']['risk_multiplier'],
            'max_position': self.config['risk']['max_position'],
            'min_spread': self.config['strategy']['min_spread'],
            'base_size': self.config['execution']['base_size']
        })

    def _init_redis(self):
        """Initialize Redis connection"""
        return Redis(
            host=self.config['redis']['host'],
            port=self.config['redis']['port'],
            decode_responses=True
        )

    async def run(self):
        """Main trading loop"""
        self.logger.info("Starting HFT trading engine")
        try:
            while self.active:
                start_time = time.perf_counter()
                
                # Market data pipeline
                raw_data = await self._fetch_market_data()
                processed_data = await self._process_market_data(raw_data)
                
                # Signal generation
                signals = await self.strategy.generate_signals(processed_data)
                validated_signals = await self._validate_signals(signals)
                
                # Order execution
                if validated_signals:
                    execution_results = await self._execute_orders(validated_signals)
                    await self._handle_execution_results(execution_results)
                
                # System monitoring
                await self._monitor_system_health()
                self._log_latency(time.perf_counter() - start_time)
                
                await asyncio.sleep(self.config['execution']['interval'])
                
        except TradingHaltedException as e:
            self.logger.critical(f"Trading halted: {str(e)}")
        finally:
            await self.graceful_shutdown()

    async def _fetch_market_data(self) -> Dict:
        """Fetch real-time market data"""
        try:
            async with self.session.get(
                self.config['data_feed']['url'],
                headers={'X-API-KEY': os.getenv('MARKET_DATA_KEY')}
            ) as response:
                return await response.json()
        except Exception as e:
            self.logger.error(f"Data fetch failed: {str(e)}")
            raise TradingHaltedException("Market data unavailable")

    async def _process_market_data(self, raw_data: Dict) -> Dict:
        """Process raw market data for strategy consumption"""
        processed = {}
        for symbol, data in raw_data.items():
            try:
                processed[symbol] = {
                    'bid': float(data['bid']),
                    'ask': float(data['ask']),
                    'volume': float(data['volume']),
                    'timestamp': data['timestamp']
                }
            except KeyError as e:
                self.logger.warning(f"Invalid data format for {symbol}: {str(e)}")
        return processed

    async def _validate_signals(self, signals: List[TradeSignal]) -> List[TradeSignal]:
        """Validate generated trading signals"""
        valid_signals = []
        for signal in signals:
            if await self._validate_single_signal(signal):
                valid_signals.append(signal)
        return valid_signals

    async def _validate_single_signal(self, signal: TradeSignal) -> bool:
        """Validate individual trade signal"""
        checks = [
            self._check_liquidity(signal.symbol, float(signal.amount)),
            self._check_position_limits(float(signal.amount)),
            self._check_risk_limits(signal)
        ]
        results = await asyncio.gather(*checks)
        return all(results)

    async def _execute_orders(self, signals: List[TradeSignal]):
        """Execute batch orders with HFT optimizations"""
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_ORDERS)
        async with semaphore:
            tasks = [self._execute_single_order(signal) for signal in signals]
            return await asyncio.gather(*tasks, return_exceptions=True)

    async def _execute_single_order(self, signal: TradeSignal):
        """Execute single order with error handling"""
        try:
            order = {
                'symbol': signal.symbol,
                'side': signal.side,
                'price': str(signal.price),
                'amount': str(signal.amount),
                'type': OrderType.LIMIT,
                'time_in_force': 'IOC'
            }
            return await self.strategy.executor.execute_order(order)
        except Exception as e:
            self.logger.error(f"Order failed: {str(e)}")
            return e

    async def _monitor_system_health(self):
        """Monitor critical system metrics"""
        health_checks = [
            self._check_latency(),
            self._check_memory_usage(),
            self._check_network_health()
        ]
        results = await asyncio.gather(*health_checks)
        if not all(results):
            await self._trigger_circuit_breaker()

    async def graceful_shutdown(self):
        """Orderly shutdown procedure"""
        self.logger.info("Initiating graceful shutdown")
        self.active = False
        
        try:
            await self.strategy.executor.cancel_all_orders()
            await self.session.close()
            self.redis.close()
            await self.redis.wait_closed()
        except Exception as e:
            self.logger.error(f"Shutdown error: {str(e)}")
        
        self.logger.info("Shutdown complete")

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )

    # Windows-specific setup
    if platform.system() == 'Windows':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    try:
        engine = LiveTradingEngine()
        asyncio.run(engine.run())
    except KeyboardInterrupt:
        logging.info("Operator initiated shutdown")
    except Exception as e:
        logging.critical(f"Fatal error: {str(e)}")