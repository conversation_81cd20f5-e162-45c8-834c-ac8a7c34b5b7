"""
ENTERPRISE-GRADE ENDLESS LOOP VALIDATION SYSTEM
Ensures continuous 24/7 operation with robust health monitoring
"""

import asyncio
import logging
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import json
import os
import signal
import sys

logger = logging.getLogger(__name__)

@dataclass
class LoopHealthMetrics:
    """Health metrics for endless loop validation"""
    loop_id: str
    start_time: datetime
    last_iteration: datetime
    total_iterations: int
    avg_iteration_time: float
    error_count: int
    success_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float
    is_healthy: bool
    uptime_seconds: float

@dataclass
class SystemHealthSnapshot:
    """Comprehensive system health snapshot"""
    timestamp: datetime
    total_memory_gb: float
    available_memory_gb: float
    memory_usage_percent: float
    cpu_usage_percent: float
    disk_usage_percent: float
    network_connections: int
    active_threads: int
    loop_health: Dict[str, LoopHealthMetrics]
    overall_health_score: float

class EndlessLoopValidator:
    """Enterprise-grade endless loop validation and monitoring system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.monitored_loops = {}
        self.health_history = deque(maxlen=1000)
        self.alert_thresholds = {
            'max_iteration_time': config.get('max_iteration_time', 60.0),  # seconds
            'min_success_rate': config.get('min_success_rate', 0.95),
            'max_memory_usage': config.get('max_memory_usage', 80.0),  # percent
            'max_cpu_usage': config.get('max_cpu_usage', 90.0),  # percent
            'max_error_rate': config.get('max_error_rate', 0.05)
        }
        
        # Recovery mechanisms
        self.auto_recovery_enabled = config.get('auto_recovery_enabled', True)
        self.recovery_callbacks = {}
        
        # Monitoring state
        self.monitoring_active = False
        self.monitoring_task = None
        self.validation_interval = config.get('validation_interval', 10.0)  # seconds
        
        # Performance tracking
        self.performance_metrics = defaultdict(list)
        
        # System resources
        self.process = psutil.Process()
        
        logger.info("🔄 [LOOP-VALIDATOR] Endless loop validator initialized")
    
    def register_loop(self, loop_id: str, loop_function: Callable, 
                     recovery_callback: Optional[Callable] = None) -> bool:
        """Register a loop for monitoring"""
        try:
            if loop_id in self.monitored_loops:
                logger.warning(f"⚠️ [LOOP-VALIDATOR] Loop {loop_id} already registered")
                return False
            
            self.monitored_loops[loop_id] = {
                'function': loop_function,
                'start_time': datetime.now(),
                'last_iteration': datetime.now(),
                'iteration_count': 0,
                'error_count': 0,
                'iteration_times': deque(maxlen=100),
                'is_running': False,
                'task': None,
                'recovery_callback': recovery_callback
            }
            
            if recovery_callback:
                self.recovery_callbacks[loop_id] = recovery_callback
            
            logger.info(f"✅ [LOOP-VALIDATOR] Registered loop: {loop_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [LOOP-VALIDATOR] Error registering loop {loop_id}: {e}")
            return False
    
    async def start_monitoring(self) -> bool:
        """Start the endless loop monitoring system"""
        try:
            if self.monitoring_active:
                logger.warning("⚠️ [LOOP-VALIDATOR] Monitoring already active")
                return True
            
            self.monitoring_active = True
            
            # Start monitoring task
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            # Start all registered loops
            for loop_id, loop_data in self.monitored_loops.items():
                await self._start_loop(loop_id)
            
            logger.info("🚀 [LOOP-VALIDATOR] Endless loop monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"❌ [LOOP-VALIDATOR] Error starting monitoring: {e}")
            return False
    
    async def stop_monitoring(self) -> bool:
        """Stop the endless loop monitoring system"""
        try:
            self.monitoring_active = False
            
            # Stop all loops
            for loop_id in list(self.monitored_loops.keys()):
                await self._stop_loop(loop_id)
            
            # Cancel monitoring task
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("🛑 [LOOP-VALIDATOR] Endless loop monitoring stopped")
            return True
            
        except Exception as e:
            logger.error(f"❌ [LOOP-VALIDATOR] Error stopping monitoring: {e}")
            return False
    
    async def _start_loop(self, loop_id: str) -> bool:
        """Start a specific monitored loop"""
        try:
            loop_data = self.monitored_loops[loop_id]
            
            if loop_data['is_running']:
                logger.warning(f"⚠️ [LOOP-VALIDATOR] Loop {loop_id} already running")
                return True
            
            # Create wrapper function for monitoring
            async def monitored_loop():
                while self.monitoring_active:
                    try:
                        iteration_start = time.time()
                        
                        # Execute the loop function
                        if asyncio.iscoroutinefunction(loop_data['function']):
                            await loop_data['function']()
                        else:
                            loop_data['function']()
                        
                        # Update metrics
                        iteration_time = time.time() - iteration_start
                        loop_data['iteration_times'].append(iteration_time)
                        loop_data['iteration_count'] += 1
                        loop_data['last_iteration'] = datetime.now()
                        
                        logger.debug(f"🔄 [LOOP-VALIDATOR] {loop_id} iteration completed in {iteration_time:.3f}s")
                        
                        # Small delay to prevent CPU overload
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        loop_data['error_count'] += 1
                        logger.error(f"❌ [LOOP-VALIDATOR] Error in loop {loop_id}: {e}")
                        
                        # Attempt recovery if enabled
                        if self.auto_recovery_enabled:
                            await self._attempt_loop_recovery(loop_id, e)
                        
                        # Wait before retrying
                        await asyncio.sleep(5.0)
            
            # Start the loop task
            loop_data['task'] = asyncio.create_task(monitored_loop(), name=f"loop_{loop_id}")
            loop_data['is_running'] = True
            
            logger.info(f"🚀 [LOOP-VALIDATOR] Started loop: {loop_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [LOOP-VALIDATOR] Error starting loop {loop_id}: {e}")
            return False
    
    async def _stop_loop(self, loop_id: str) -> bool:
        """Stop a specific monitored loop"""
        try:
            loop_data = self.monitored_loops[loop_id]
            
            if not loop_data['is_running']:
                return True
            
            # Cancel the loop task
            if loop_data['task']:
                loop_data['task'].cancel()
                try:
                    await loop_data['task']
                except asyncio.CancelledError:
                    pass
            
            loop_data['is_running'] = False
            loop_data['task'] = None
            
            logger.info(f"🛑 [LOOP-VALIDATOR] Stopped loop: {loop_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [LOOP-VALIDATOR] Error stopping loop {loop_id}: {e}")
            return False
    
    async def _monitoring_loop(self):
        """Main monitoring loop for health validation"""
        try:
            while self.monitoring_active:
                try:
                    # Collect system health metrics
                    health_snapshot = await self._collect_health_metrics()
                    
                    # Validate loop health
                    validation_results = await self._validate_loop_health()
                    
                    # Check for issues and trigger recovery
                    await self._check_health_issues(health_snapshot, validation_results)
                    
                    # Store health history
                    self.health_history.append(health_snapshot)
                    
                    # Log health status
                    self._log_health_status(health_snapshot)
                    
                    # Wait before next check
                    await asyncio.sleep(self.validation_interval)
                    
                except Exception as e:
                    logger.error(f"❌ [LOOP-VALIDATOR] Error in monitoring loop: {e}")
                    await asyncio.sleep(10)  # Wait longer on error
                    
        except asyncio.CancelledError:
            logger.info("🔄 [LOOP-VALIDATOR] Monitoring loop cancelled")
        except Exception as e:
            logger.error(f"❌ [LOOP-VALIDATOR] Fatal error in monitoring loop: {e}")
    
    async def _collect_health_metrics(self) -> SystemHealthSnapshot:
        """Collect comprehensive system health metrics"""
        try:
            # System metrics
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            disk = psutil.disk_usage('/')
            
            # Process metrics
            process_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            
            # Network connections
            connections = len(psutil.net_connections())
            
            # Thread count
            thread_count = threading.active_count()
            
            # Loop health metrics
            loop_health = {}
            for loop_id, loop_data in self.monitored_loops.items():
                loop_health[loop_id] = self._calculate_loop_health(loop_id, loop_data)
            
            # Calculate overall health score
            overall_health = self._calculate_overall_health_score(loop_health, cpu_percent, memory.percent)
            
            return SystemHealthSnapshot(
                timestamp=datetime.now(),
                total_memory_gb=memory.total / 1024 / 1024 / 1024,
                available_memory_gb=memory.available / 1024 / 1024 / 1024,
                memory_usage_percent=memory.percent,
                cpu_usage_percent=cpu_percent,
                disk_usage_percent=disk.percent,
                network_connections=connections,
                active_threads=thread_count,
                loop_health=loop_health,
                overall_health_score=overall_health
            )
            
        except Exception as e:
            logger.error(f"❌ [LOOP-VALIDATOR] Error collecting health metrics: {e}")
            return SystemHealthSnapshot(
                timestamp=datetime.now(),
                total_memory_gb=0.0,
                available_memory_gb=0.0,
                memory_usage_percent=0.0,
                cpu_usage_percent=0.0,
                disk_usage_percent=0.0,
                network_connections=0,
                active_threads=0,
                loop_health={},
                overall_health_score=0.0
            )
