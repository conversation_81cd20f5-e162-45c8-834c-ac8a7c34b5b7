#!/usr/bin/env python3
"""
Test Coinbase API access levels and permissions
"""

import os
import sys
import time
import requests
import jwt
from cryptography.hazmat.primitives import serialization
from dotenv import load_dotenv

# Load environment
load_dotenv()

# Add project root to path
sys.path.append('.')

from src.utils.cryptography.secure_credentials import decrypt_value

def load_private_key(private_key_pem):
    """Load private key and detect algorithm"""
    try:
        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )
        
        # Detect algorithm based on key type
        if hasattr(private_key, 'curve'):
            return 'ES256', private_key
        else:
            return 'RS256', private_key
            
    except Exception as e:
        print(f"❌ Error loading private key: {e}")
        return None, None

def make_authenticated_request(endpoint, method='GET', api_key_name=None, private_key=None, key_id=None, algorithm='ES256'):
    """Make authenticated request to Coinbase API"""
    try:
        now = int(time.time())
        
        # Create JWT payload
        payload = {
            'iss': 'cdp',
            'nbf': now,
            'exp': now + 120,
            'sub': api_key_name,
            'uri': f'{method.upper()} {endpoint}',
            'aud': ['retail_rest_api_proxy']
        }

        # Create JWT token
        token = jwt.encode(
            payload,
            private_key,
            algorithm=algorithm,
            headers={
                'kid': key_id,
                'typ': 'JWT',
                'alg': algorithm
            }
        )

        # Make request
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
            'User-Agent': 'AutoGPT-Trader/1.0',
            'Accept': 'application/json'
        }

        response = requests.get(f"https://api.coinbase.com{endpoint}", headers=headers, timeout=30)
        
        print(f"  📡 {endpoint}: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"  ✅ Success: {len(str(data))} chars response")
                return True, data
            except:
                print(f"  ✅ Success: Non-JSON response")
                return True, response.text
        else:
            print(f"  ❌ Error: {response.text[:100]}")
            return False, response.text
            
    except Exception as e:
        print(f"  ❌ Exception: {e}")
        return False, str(e)

def main():
    print("🔍 COINBASE API ACCESS LEVEL TEST")
    print("=" * 50)
    
    # Get credentials
    try:
        api_key_name = decrypt_value(os.getenv('COINBASE_API_KEY_NAME'))
        private_key_pem = decrypt_value(os.getenv('COINBASE_PRIVATE_KEY_PEM'))
        key_id = decrypt_value(os.getenv('COINBASE_KEY_ID'))
        
        print(f"🔑 API Key: {api_key_name[:50]}...")
        print(f"🔑 Key ID: {key_id}")
        
    except Exception as e:
        print(f"❌ Failed to load credentials: {e}")
        return False
    
    # Load private key
    algorithm, private_key = load_private_key(private_key_pem)
    if not private_key:
        return False
        
    print(f"🔐 Algorithm: {algorithm}")
    
    # Test endpoints
    endpoints = [
        '/api/v3/brokerage/time',
        '/api/v3/brokerage/accounts',
        '/api/v3/brokerage/products',
        '/api/v3/brokerage/orders/historical',
        '/api/v3/brokerage/portfolios'
    ]
    
    print(f"\n📊 Testing {len(endpoints)} endpoints...")
    
    results = {}
    for endpoint in endpoints:
        print(f"\n🔍 Testing {endpoint}...")
        success, data = make_authenticated_request(
            endpoint, 'GET', api_key_name, private_key, key_id, algorithm
        )
        results[endpoint] = success
    
    # Analyze results
    print(f"\n📈 RESULTS SUMMARY:")
    print("=" * 30)
    
    successful = sum(results.values())
    total = len(results)
    
    for endpoint, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {endpoint}")
    
    print(f"\n📊 Success Rate: {successful}/{total} ({successful/total*100:.1f}%)")
    
    # Determine access level
    if results.get('/api/v3/brokerage/accounts', False):
        print("\n🎯 ACCESS LEVEL: FULL")
        print("✅ Account access available")
        print("✅ Trade execution should be possible")
        print("✅ Can serve as PRIMARY exchange")
    elif results.get('/api/v3/brokerage/products', False):
        print("\n🎯 ACCESS LEVEL: LIMITED")
        print("⚠️ Product data only")
        print("❌ No account access")
    elif results.get('/api/v3/brokerage/time', False):
        print("\n🎯 ACCESS LEVEL: MINIMAL")
        print("⚠️ Time endpoint only")
        print("❌ No trading capabilities")
    else:
        print("\n🎯 ACCESS LEVEL: NONE")
        print("❌ No API access")
    
    return successful > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
