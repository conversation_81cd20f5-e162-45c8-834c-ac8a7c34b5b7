#!/usr/bin/env python3
"""
Coinbase API Authentication Fix
Comprehensive solution for 401 Unauthorized errors
"""

import os
import sys
import time
import requests
import jwt
from cryptography.hazmat.primitives import serialization

# Add project root to path
sys.path.append('.')

# Load environment
from dotenv import load_dotenv
load_dotenv()

from src.utils.cryptography.secure_credentials import decrypt_value

def test_api_key_format():
    """Test different API key formats"""
    print("=== TESTING API KEY FORMATS ===")
    
    # Get credentials
    encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
    encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
    
    api_key_name = decrypt_value(encrypted_api_key)
    private_key_pem = decrypt_value(encrypted_private_key)
    
    print(f"Current API Key: {api_key_name}")
    
    # Extract components
    if '/apiKeys/' in api_key_name:
        key_id = api_key_name.split('/apiKeys/')[1]
        org_id = api_key_name.split('/')[1]
        print(f"Organization ID: {org_id}")
        print(f"Key ID: {key_id}")
    else:
        print("❌ Invalid API key format")
        return False
    
    # Test different authentication methods
    test_methods = [
        ("CDP Format", api_key_name, key_id),
        ("Key ID Only", key_id, key_id),
        ("Legacy Format", f"organizations/{org_id}/apiKeys/{key_id}", key_id)
    ]
    
    for method_name, api_key, kid in test_methods:
        print(f"\n--- Testing {method_name} ---")
        success = test_authentication(api_key, private_key_pem, kid)
        if success:
            print(f"✅ {method_name} WORKS!")
            return True
        else:
            print(f"❌ {method_name} failed")
    
    return False

def test_authentication(api_key: str, private_key_pem: str, key_id: str) -> bool:
    """Test authentication with specific parameters"""
    try:
        # Load private key
        private_key = serialization.load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )
        
        # Create JWT with different payload formats
        now = int(time.time())
        
        # Test different JWT payload formats
        payloads = [
            {
                'iss': 'cdp',
                'nbf': now,
                'exp': now + 120,
                'sub': api_key,
                'uri': 'GET /api/v3/brokerage/accounts'
            },
            {
                'iss': 'coinbase-cloud',
                'nbf': now,
                'exp': now + 120,
                'sub': api_key,
                'uri': 'GET /api/v3/brokerage/accounts'
            },
            {
                'iss': 'cdp',
                'nbf': now,
                'exp': now + 120,
                'sub': key_id,
                'uri': 'GET /api/v3/brokerage/accounts'
            }
        ]
        
        for i, payload in enumerate(payloads):
            print(f"  Testing payload format {i+1}...")
            
            # Generate token
            token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': key_id})
            
            # Test API call
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                'https://api.coinbase.com/api/v3/brokerage/accounts',
                headers=headers,
                timeout=10
            )
            
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"    ✅ Success with payload format {i+1}!")
                return True
            elif response.status_code == 401:
                print(f"    ❌ 401 Unauthorized")
            else:
                print(f"    ❌ Error: {response.text[:100]}")
        
        return False
        
    except Exception as e:
        print(f"    ❌ Exception: {e}")
        return False

def test_permissions():
    """Test different API endpoints to identify permission issues"""
    print("\n=== TESTING API PERMISSIONS ===")
    
    # Get credentials
    encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
    encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
    
    api_key_name = decrypt_value(encrypted_api_key)
    private_key_pem = decrypt_value(encrypted_private_key)
    key_id = api_key_name.split('/apiKeys/')[1] if '/apiKeys/' in api_key_name else api_key_name
    
    # Load private key
    private_key = serialization.load_pem_private_key(
        private_key_pem.encode('utf-8'),
        password=None
    )
    
    # Test different endpoints
    endpoints = [
        ("Public Products", "GET", "/api/v3/brokerage/products", False),
        ("Time", "GET", "/api/v3/brokerage/time", False),
        ("Accounts", "GET", "/api/v3/brokerage/accounts", True),
        ("Orders", "GET", "/api/v3/brokerage/orders/historical/batch", True),
        ("Portfolios", "GET", "/api/v3/brokerage/portfolios", True)
    ]
    
    for name, method, endpoint, requires_auth in endpoints:
        print(f"\nTesting {name} ({endpoint})...")
        
        if requires_auth:
            # Create JWT
            now = int(time.time())
            payload = {
                'iss': 'cdp',
                'nbf': now,
                'exp': now + 120,
                'sub': api_key_name,
                'uri': f'{method} {endpoint}'
            }
            
            token = jwt.encode(payload, private_key, algorithm='ES256', headers={'kid': key_id})
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
        else:
            headers = {'Content-Type': 'application/json'}
        
        try:
            response = requests.get(f'https://api.coinbase.com{endpoint}', headers=headers, timeout=10)
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  ✅ {name} works!")
            elif response.status_code == 401:
                print(f"  ❌ {name} - 401 Unauthorized")
            elif response.status_code == 403:
                print(f"  ❌ {name} - 403 Forbidden (permission denied)")
            else:
                print(f"  ❌ {name} - {response.status_code}: {response.text[:100]}")
                
        except Exception as e:
            print(f"  ❌ {name} - Exception: {e}")

def check_ip_whitelist():
    """Check current IP and provide guidance"""
    print("\n=== IP ADDRESS CHECK ===")
    
    try:
        # Get current public IP
        response = requests.get('https://api.ipify.org', timeout=5)
        current_ip = response.text.strip()
        print(f"Current IP: {current_ip}")
        
        expected_ip = "************"
        if current_ip == expected_ip:
            print("✅ IP matches expected address")
        else:
            print(f"⚠️ IP differs from expected: {expected_ip}")
            print("   This could cause authentication failures if IP restrictions are enabled")
            
    except Exception as e:
        print(f"❌ Could not determine IP: {e}")

def generate_new_credentials_guide():
    """Generate guide for creating new API credentials"""
    print("\n=== NEW CREDENTIALS GUIDE ===")
    print("If authentication continues to fail, generate new API credentials:")
    print()
    print("1. Go to: https://cloud.coinbase.com/access/api")
    print("2. Click 'Create API Key'")
    print("3. Select permissions:")
    print("   ✅ wallet:accounts:read")
    print("   ✅ wallet:buys:create")
    print("   ✅ wallet:sells:create")
    print("   ✅ wallet:trades:read")
    print("   ✅ wallet:transactions:read")
    print("4. Add IP restriction: ************ (if using VPN, add VPN IP)")
    print("5. Download the JSON file")
    print("6. Update environment variables with new credentials")
    print()
    print("Key format should be: organizations/{org_id}/apiKeys/{key_id}")

def main():
    print("=== COINBASE API AUTHENTICATION FIX ===")
    print()
    
    # Test current IP
    check_ip_whitelist()
    
    # Test API key formats
    if test_api_key_format():
        print("\n✅ Found working authentication method!")
        return True
    
    # Test permissions
    test_permissions()
    
    # Provide guidance
    generate_new_credentials_guide()
    
    print("\n❌ Authentication fix unsuccessful")
    print("Manual intervention required - see guidance above")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
