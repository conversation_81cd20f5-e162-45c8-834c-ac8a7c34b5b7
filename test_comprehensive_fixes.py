#!/usr/bin/env python3
"""
Comprehensive test script to verify all critical fixes
"""

import asyncio
import sys
import os
from decimal import Decimal

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from trading.enhanced_signal_generator import EnhancedSignalGenerator
from strategies.adaptive_neural_strategy import AdaptiveNeuralStrategy
from data_feeds.internet_crawler import InternetCrawler

class MockMarketContext:
    """Mock market context for testing"""
    
    def __init__(self):
        self.price_data = {
            'bybit': {
                'BTCUSDT': {'price': 50000.0, 'volume': 1000.0}
            }
        }
        self.technical_indicators = {
            'volatility': 0.03,
            'rsi': 55.0,
            'macd': 0.1,
            'macd_signal': 0.05
        }
        self.sentiment_data = {
            'overall': 0.2
        }

class MockExchangeManager:
    """Mock exchange manager for testing"""
    
    def __init__(self):
        self.exchanges = {
            'bybit_client_fixed': MockBybitClient()
        }

class MockBybitClient:
    """Mock Bybit client for testing"""
    
    async def get_balance(self, currency):
        """Return mock balance"""
        if currency == 'USDT':
            return Decimal('63.34')
        return Decimal('0')
    
    def get_all_trading_pairs(self):
        """Return mock trading pairs"""
        return {
            'trading_pairs': {
                'BTCUSDT': {
                    'base_currency': 'BTC',
                    'quote_currency': 'USDT',
                    'status': 'trading',
                    'min_order_qty': 0.000048,
                    'min_order_amt': 5.0,
                    'symbol_bybit': 'BTCUSDT',
                    'symbol_user': 'BTC-USDT'
                },
                'ETHUSDT': {
                    'base_currency': 'ETH',
                    'quote_currency': 'USDT',
                    'status': 'trading',
                    'min_order_qty': 0.002,
                    'min_order_amt': 5.0,
                    'symbol_bybit': 'ETHUSDT',
                    'symbol_user': 'ETH-USDT'
                }
            }
        }

async def test_comprehensive_fixes():
    """Test all critical fixes comprehensively"""
    print("🧪 COMPREHENSIVE TESTING OF CRITICAL FIXES")
    print("=" * 60)
    
    success_count = 0
    total_tests = 6
    
    # Test 1: Symbol Discovery and Format Conversion
    print("\n1. Testing Symbol Discovery and Format Conversion...")
    try:
        mock_exchange_manager = MockExchangeManager()
        signal_generator = EnhancedSignalGenerator(mock_exchange_manager)
        
        symbols = await signal_generator._get_symbols_for_exchange('bybit')
        print(f"   ✅ Discovered symbols: {symbols}")
        
        if len(symbols) >= 2 and 'BTCUSDT' in symbols:
            success_count += 1
            print("   ✅ Symbol discovery working correctly")
        else:
            print("   ❌ Symbol discovery failed")
    except Exception as e:
        print(f"   ❌ Symbol discovery error: {e}")
    
    # Test 2: Position Sizing (20-25% of balance)
    print("\n2. Testing Position Sizing (20-25% of $63.34)...")
    try:
        position_size = await signal_generator._calculate_minimum_position_size('BTCUSDT', 50000.0, 'bybit')
        usd_value = float(position_size) * 50000.0
        target_min = 63.34 * 0.20  # $12.67
        target_max = 63.34 * 0.25  # $15.84
        
        print(f"   Position size: {position_size:.6f} BTC")
        print(f"   USD value: ${usd_value:.2f}")
        print(f"   Target range: ${target_min:.2f} - ${target_max:.2f}")
        
        if target_min <= usd_value <= target_max * 1.1:  # Allow 10% tolerance
            success_count += 1
            print("   ✅ Position sizing working correctly")
        else:
            print("   ❌ Position sizing outside target range")
    except Exception as e:
        print(f"   ❌ Position sizing error: {e}")
    
    # Test 3: Neural Strategy Data Structure
    print("\n3. Testing Neural Strategy Data Structure...")
    try:
        strategy = AdaptiveNeuralStrategy()
        mock_context = MockMarketContext()
        
        # Test _should_adapt method
        should_adapt = strategy._should_adapt()
        print(f"   Should adapt: {should_adapt}")
        
        # Test position size calculation
        from strategies.adaptive_neural_strategy import SignalData
        mock_signal = SignalData(
            symbol='BTCUSDT',
            exchange='bybit',
            action='BUY',
            amount=Decimal('0.001'),
            confidence=0.7,
            reasoning='Test signal',
            timestamp=None,
            price=Decimal('50000'),
            stop_loss=None,
            take_profit=None
        )
        
        position_size = await strategy.calculate_position_size(mock_signal, mock_context)
        print(f"   Neural strategy position size: {position_size}")
        
        success_count += 1
        print("   ✅ Neural strategy data structure working correctly")
    except Exception as e:
        print(f"   ❌ Neural strategy error: {e}")
    
    # Test 4: Signal Generation
    print("\n4. Testing Signal Generation...")
    try:
        signals = await signal_generator._generate_time_based_signals_for_exchange('bybit')
        print(f"   Generated {len(signals)} signals")
        
        for signal_id, signal in signals.items():
            print(f"   Signal: {signal['action']} {signal['symbol']} - ${signal['usd_value']:.2f}")
        
        if len(signals) > 0:
            success_count += 1
            print("   ✅ Signal generation working correctly")
        else:
            print("   ❌ No signals generated")
    except Exception as e:
        print(f"   ❌ Signal generation error: {e}")
    
    # Test 5: Internet Crawler Graceful Fallback
    print("\n5. Testing Internet Crawler Graceful Fallback...")
    try:
        crawler = InternetCrawler()
        
        # Test fallback sentiment
        fallback_sentiment = crawler.get_fallback_sentiment()
        print(f"   Fallback sentiment: {fallback_sentiment}")
        
        # Test graceful sentiment retrieval
        sentiment = await crawler.get_real_time_sentiment()
        print(f"   Real-time sentiment: {sentiment}")
        
        success_count += 1
        print("   ✅ Internet crawler graceful fallback working correctly")
    except Exception as e:
        print(f"   ❌ Internet crawler error: {e}")
    
    # Test 6: Balance Detection
    print("\n6. Testing Balance Detection...")
    try:
        balances = await signal_generator._get_exchange_balances('bybit')
        print(f"   Detected balances: {balances}")
        
        if 'USDT' in balances and balances['USDT'] > 60:
            success_count += 1
            print("   ✅ Balance detection working correctly")
        else:
            print("   ❌ Balance detection failed")
    except Exception as e:
        print(f"   ❌ Balance detection error: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print(f"COMPREHENSIVE TEST RESULTS: {success_count}/{total_tests} tests passed")
    
    if success_count >= 5:  # Allow 1 failure
        print("🎉 SUCCESS: Critical fixes are working!")
        print("✅ System should now generate signals and execute trades")
        return True
    else:
        print("❌ FAILURE: Critical issues remain")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_comprehensive_fixes())
    sys.exit(0 if success else 1)
