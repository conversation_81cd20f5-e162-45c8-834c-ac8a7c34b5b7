import psutil
import platform
import logging
from fastapi import FastAP<PERSON>
from typing import Optional, Dict
from pkcs11 import LibHSM
import yubihsm

logger = logging.getLogger('monitoring.hardware')

class HardwareSecurityEngine:
    def __init__(self):
        self.hsm = LibHSM('/usr/lib/libsofthsm2.so')
        self.session = self.hsm.open_session(token='trading-token')
        
    def secure_sign(self, data):
        private_key = self.session.get_key(
            yubihsm.OBJECT.ASYMMETRIC_KEY,
            key_id=0x0001
        )
        return private_key.sign(data)
        
    def _init_gpu(self):
        try:
            import GPUtil
            self.gpu_available = True
        except ImportError:
            logger.warning("GPU monitoring not available")

    def get_status(self) -> Dict[str, Optional[float]]:
        status = {
            'cpu_load': psutil.cpu_percent(interval=1),
            'memory_usage': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'gpu_load': None,
            'network_latency': self._check_latency(),
            'system_uptime': psutil.boot_time(),
            'alerts': []
        }

        if self.gpu_available:
            try:
                import GPUtil
                gpus = GPUtil.getGPUs()
                status['gpu_load'] = gpus[0].load * 100 if gpus else None
            except Exception as e:
                logger.error("GPU monitoring failed: %s", str(e))

        self._check_thresholds(status)
        return status

    def _check_latency(self) -> float:
        try:
            # Use Cloudflare DNS for reliable latency check
            target = "1.1.1.1"
            res = psutil.net_io_counters()
            return psutil.net_if_stats()[target].isup  # Simplified example
        except Exception:
            return 9999.9

    def _check_thresholds(self, status: Dict):
        alerts = []
        thresholds = config['monitoring']['thresholds']
        
        if status['cpu_load'] > thresholds['cpu']:
            alerts.append(f"High CPU ({status['cpu_load']}%)")
        if status['memory_usage'] > thresholds['memory']:
            alerts.append(f"High Memory ({status['memory_usage']}%)")
        if status['disk_usage'] > thresholds['disk']:
            alerts.append(f"High Disk ({status['disk_usage']}%)")
        if status['network_latency'] > thresholds['network']:
            alerts.append(f"Network Latency ({status['network_latency']}ms)")
        
        status['alerts'] = alerts

app = FastAPI()
monitor = HardwareMonitor()

@app.get("/status")
async def get_status():
    return monitor.get_status()