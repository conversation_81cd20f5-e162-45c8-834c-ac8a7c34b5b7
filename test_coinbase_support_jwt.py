#!/usr/bin/env python3
"""
Test Coinbase API with the EXACT JWT implementation provided by Coinbase support
"""

import jwt
from cryptography.hazmat.primitives import serialization
import time
import secrets
import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_credentials():
    """Get and decrypt Coinbase credentials"""
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        # Get encrypted credentials
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        if not encrypted_api_key or not encrypted_private_key:
            print("❌ Missing encrypted credentials in .env file")
            return None, None
        
        # Decrypt credentials
        api_key = decrypt_value(encrypted_api_key)
        private_key_pem = decrypt_value(encrypted_private_key)
        
        return api_key, private_key_pem
        
    except Exception as e:
        print(f"❌ Error loading credentials: {e}")
        return None, None

def build_jwt(uri, key_name, key_secret):
    """Build JWT using EXACT implementation from Coinbase support"""
    private_key_bytes = key_secret.encode('utf-8')
    private_key = serialization.load_pem_private_key(private_key_bytes, password=None)
    jwt_payload = {
        'sub': key_name,
        'iss': "cdp",
        'nbf': int(time.time()),
        'exp': int(time.time()) + 120,
        'uri': uri,
    }
    jwt_token = jwt.encode(
        jwt_payload,
        private_key,
        algorithm='ES256',
        headers={'kid': key_name, 'nonce': secrets.token_hex()},
    )
    return jwt_token

def test_coinbase_support_jwt():
    """Test Coinbase API with support's exact JWT implementation"""
    
    print("🚀 [COINBASE-SUPPORT-JWT] Testing with EXACT support implementation...")
    
    # Get credentials
    key_name, key_secret = get_credentials()
    if not key_name or not key_secret:
        print("❌ Failed to load credentials")
        return False
    
    print(f"✅ [CREDENTIALS] API Key: {key_name[:50]}...")
    
    # Test parameters from support
    request_method = "GET"
    request_host = "api.coinbase.com"
    request_path = "/api/v3/brokerage/accounts"
    
    # Build URI exactly as support specified
    uri = f"{request_method} {request_host}{request_path}"
    print(f"📋 [URI] {uri}")
    
    # Build JWT using support's exact function
    try:
        jwt_token = build_jwt(uri, key_name, key_secret)
        print(f"✅ [JWT] Token generated: {jwt_token[:50]}...")
    except Exception as e:
        print(f"❌ [JWT] Token generation failed: {e}")
        return False
    
    # Make request exactly as support specified
    try:
        headers = {
            'Authorization': f'Bearer {jwt_token}',
            'Content-Type': 'application/json'
        }
        
        full_url = f"https://{request_host}{request_path}"
        print(f"📋 [REQUEST] {full_url}")
        
        response = requests.get(full_url, headers=headers, timeout=30)
        
        print(f"📊 [RESPONSE] Status: {response.status_code}")
        print(f"📊 [RESPONSE] Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ [SUCCESS] API call successful!")
            print(f"📊 [DATA] Response: {data}")
            return True
        else:
            print(f"❌ [ERROR] API call failed")
            print(f"📊 [ERROR] Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ [REQUEST] Request failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 COINBASE SUPPORT JWT TEST")
    print("=" * 60)
    
    success = test_coinbase_support_jwt()
    
    print("=" * 60)
    if success:
        print("✅ COINBASE SUPPORT JWT SUCCESSFUL!")
        print("✅ Coinbase API authentication WORKING")
        print("✅ Ready to integrate into main system")
    else:
        print("❌ COINBASE SUPPORT JWT FAILED")
        print("❌ Need to investigate further")
    print("=" * 60)
