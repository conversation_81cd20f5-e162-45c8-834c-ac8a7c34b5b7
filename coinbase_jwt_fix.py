#!/usr/bin/env python3
"""
Coinbase JWT Authentication Fix

Based on Coinbase support's working code, this implements the correct JWT authentication
for Coinbase Advanced API with ES256 algorithm, 'kid' and 'nonce' headers.

Replace the existing JWT authentication in your Coinbase client with this implementation.
"""

import jwt
from cryptography.hazmat.primitives import serialization
import time
import secrets
import http.client

def build_coinbase_jwt(key_name: str, key_secret: str, request_method: str, request_host: str, request_path: str) -> str:
    """
    Build JWT token for Coinbase Advanced API authentication
    
    Args:
        key_name: Your Coinbase API key name (organizations/.../apiKeys/...)
        key_secret: Your Coinbase private key (-----BEGIN EC PRIVATE KEY-----...)
        request_method: HTTP method (GET, POST, etc.)
        request_host: API host (api.coinbase.com)
        request_path: API path (/api/v3/brokerage/accounts)
    
    Returns:
        JWT token string
    """
    # Load the private key
    private_key_bytes = key_secret.encode('utf-8')
    private_key = serialization.load_pem_private_key(private_key_bytes, password=None)
    
    # Build the URI for the JWT payload
    uri = f"{request_method} {request_host}{request_path}"
    
    # Create JWT payload
    jwt_payload = {
        'sub': key_name,
        'iss': "cdp",
        'nbf': int(time.time()),
        'exp': int(time.time()) + 120,  # 2 minutes expiration
        'uri': uri,
    }
    
    # Create JWT token with required headers
    jwt_token = jwt.encode(
        jwt_payload,
        private_key,
        algorithm='ES256',
        headers={
            'kid': key_name,
            'nonce': secrets.token_hex()
        }
    )
    
    return jwt_token

def test_coinbase_authentication(key_name: str, key_secret: str):
    """
    Test Coinbase authentication with the provided credentials
    """
    try:
        # Build JWT for accounts endpoint
        jwt_token = build_coinbase_jwt(
            key_name=key_name,
            key_secret=key_secret,
            request_method="GET",
            request_host="api.coinbase.com",
            request_path="/api/v3/brokerage/accounts"
        )
        
        print(f"JWT Token: {jwt_token}")
        
        # Make the API request
        conn = http.client.HTTPSConnection("api.coinbase.com")
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {jwt_token}"
        }
        
        conn.request("GET", "/api/v3/brokerage/accounts", "", headers)
        response = conn.getresponse()
        data = response.read()
        
        print(f"Status: {response.status}")
        print(f"Response: {data.decode('utf-8')}")
        
        if response.status == 200:
            print("✅ Coinbase authentication successful!")
            return True
        else:
            print(f"❌ Coinbase authentication failed: {response.status}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Coinbase authentication: {e}")
        return False

# Example usage:
if __name__ == "__main__":
    # Replace with your actual credentials
    KEY_NAME = "organizations/....../apiKeys/......"
    KEY_SECRET = """-----BEGIN EC PRIVATE KEY-----
...your private key here...
-----END EC PRIVATE KEY-----"""
    
    test_coinbase_authentication(KEY_NAME, KEY_SECRET)
