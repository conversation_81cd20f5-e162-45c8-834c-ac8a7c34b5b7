#!/usr/bin/env python3
"""
Test script to verify real-time balance integrity fixes
Ensures no hardcoded balance values are used in trading decisions
"""

import asyncio
import sys
import os
import logging
from decimal import Decimal

# Add project root to path
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_bybit_balance_integrity():
    """Test Bybit client for real-time balance integrity"""
    print("🔍 Testing Bybit real-time balance integrity...")
    
    try:
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Initialize client
        client = BybitClientFixed()
        
        # Test 1: Real-time balance fetch
        print("\n📊 Test 1: Real-time balance fetch")
        usdt_balance = await client.get_balance('USDT')
        print(f"💰 Real-time USDT balance: ${usdt_balance:.2f}")
        
        if usdt_balance > 0:
            print("✅ Successfully fetched real-time USDT balance")
        else:
            print("⚠️ USDT balance is zero or unavailable")
        
        # Test 2: Position sizing logic with real balance
        print("\n📊 Test 2: Position sizing logic")
        if float(usdt_balance) >= 10.0:
            strategy = await client.determine_optimal_order_strategy('BTCUSDT', 'buy', 0.001)
            print(f"📈 Strategy result: {strategy.get('strategy', 'unknown')}")
            print(f"📈 Reason: {strategy.get('reason', 'No reason provided')}")
            
            # Verify proper position sizing
            if 'amount' in strategy:
                calculated_amount = strategy['amount']
                position_percentage = (calculated_amount * float(usdt_balance)) / float(usdt_balance) * 100
                print(f"📊 Calculated trade amount: ${calculated_amount:.2f}")
                print(f"📊 Position percentage: {position_percentage:.1f}%")
                
                if 20 <= position_percentage <= 30:
                    print("✅ Position sizing within expected range (20-30%)")
                else:
                    print(f"⚠️ Position sizing outside expected range: {position_percentage:.1f}%")
            
            if strategy.get('strategy') == 'buy':
                print("✅ BUY strategy correctly approved with sufficient funds")
            elif strategy.get('strategy') == 'sell_crypto':
                print("✅ Correctly switched to SELL strategy")
            elif strategy.get('strategy') == 'api_error':
                print("❌ API error - real-time data unavailable")
            else:
                print(f"⚠️ Unexpected strategy: {strategy.get('strategy')}")
        else:
            print(f"⚠️ Insufficient USDT balance (${usdt_balance:.2f}) for testing position sizing")
        
        # Test 3: Verify no hardcoded values in crypto holdings
        print("\n📊 Test 3: Crypto holdings verification")
        crypto_holdings = await client.get_crypto_holdings_for_trading()
        
        if crypto_holdings:
            print(f"💎 Found {len(crypto_holdings)} crypto holdings:")
            for crypto, data in crypto_holdings.items():
                print(f"   {crypto}: {data['available']:.6f} (${data['value_usd']:.2f})")
            print("✅ Crypto holdings fetched from real-time API")
        else:
            print("⚠️ No crypto holdings found or API unavailable")
        
        return True
        
    except Exception as e:
        print(f"❌ Bybit test failed: {e}")
        return False

async def test_signal_generator_integrity():
    """Test enhanced signal generator for hardcoded balance elimination"""
    print("\n🔍 Testing Enhanced Signal Generator integrity...")
    
    try:
        from src.trading.enhanced_signal_generator import EnhancedSignalGenerator
        from src.trading.enhanced_exchange_manager import EnhancedExchangeManager
        
        # Initialize components
        exchange_manager = EnhancedExchangeManager()
        signal_generator = EnhancedSignalGenerator(exchange_manager)
        
        # Test real-time balance method
        print("📊 Testing real-time balance method...")
        
        # This should fail-fast if real-time data is unavailable
        try:
            balance = await signal_generator._get_real_time_balance('USDT', 'bybit')
            print(f"💰 Real-time balance method returned: ${balance:.2f}")
            print("✅ Real-time balance method working correctly")
        except Exception as balance_error:
            print(f"⚠️ Real-time balance method failed (expected if no exchange connection): {balance_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Signal generator test failed: {e}")
        return False

def test_hardcoded_value_elimination():
    """Test that hardcoded balance values have been eliminated"""
    print("\n🔍 Testing hardcoded value elimination...")
    
    # Check for hardcoded values in key files (excluding legitimate user-confirmed balances)
    hardcoded_patterns = [
        '0.027579', '1.375641', '25.79', '34.72',
        '0.0003794', '17.53789'
    ]
    
    files_to_check = [
        'src/exchanges/bybit_client_fixed.py',
        'src/trading/enhanced_signal_generator.py',
        'main.py'
    ]
    
    issues_found = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern in hardcoded_patterns:
                    if pattern in content and 'CRITICAL FIX' not in content[content.find(pattern):content.find(pattern)+100]:
                        issues_found.append(f"{file_path}: Found hardcoded value {pattern}")
            except Exception as e:
                print(f"⚠️ Could not check {file_path}: {e}")
    
    if issues_found:
        print("❌ Hardcoded values still found:")
        for issue in issues_found:
            print(f"   {issue}")
        return False
    else:
        print("✅ No hardcoded balance values found in key files")
        return True

async def main():
    """Run all integrity tests"""
    print("🚀 Starting Real-Time Balance Integrity Tests")
    print("=" * 60)
    
    results = []
    
    # Test 1: Bybit client integrity
    results.append(await test_bybit_balance_integrity())
    
    # Test 2: Signal generator integrity  
    results.append(await test_signal_generator_integrity())
    
    # Test 3: Hardcoded value elimination
    results.append(test_hardcoded_value_elimination())
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("🎉 Real-time balance integrity verified!")
        print("💰 System ready for real money trading with live API data")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("⚠️ System requires additional fixes before real money trading")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
