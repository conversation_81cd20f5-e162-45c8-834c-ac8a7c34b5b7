[tool.poetry]
name = "autogpt-trader"
version = "0.1.0"
description = "Multi-exchange Crypto Trading Bot with AI Strategies"
authors = ["<PERSON><PERSON><PERSON>"]
readme = "README.md"
packages = [{ include = "src", from = "." }]

[tool.poetry.dependencies]
python = "^3.10"
aiohttp = "^3.9.0"
aiosqlite = "^0.19.0"
python-dotenv = "^1.0.0"
python-binance = "^1.0.19"
coinbase = "^2.1.0"
bybit = "^0.2.12"
pandas = "^2.0.0"
numpy = "^1.24.0"
ta-lib = "^0.4.0"
ccxt = "^4.1.59"
pydantic = "^2.0.0"
loguru = "^0.7.0"
schedule = "^1.2.0"
pyyaml = "^6.0"
requests = "^2.31.0"
websockets = "^11.0.0"
uvicorn = "^0.29.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.torch.dependencies]
torch = { version = "2.0.1", extras = ["cu118"] }  # Official PyTorch package
torchvision = { version = "0.15.2", extras = ["cu118"] }
torchaudio = { version = "2.0.2", extras = ["cu118"] }

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.23.0"
pytest-mock = "^3.11.1"
mypy = "^1.4.0"
black = "^23.7.0"
flake8 = "^6.0.0"
isort = "^5.12.0"

[tool.poetry.group.test.dependencies]
pytest-cov = "^4.1.0"
tox = "^4.6.0"

[tool.pytest.ini_options]
asyncio_mode = "auto"
python_files = "test_*.py"
testpaths = ["backend/tests"]